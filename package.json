{"name": "core", "version": "0.0.1", "private": true, "scripts": {"db:stop": "docker compose down", "db:start": "docker compose up", "dev": "BROWSER='Brave Browser' vite dev", "build": "vite build", "preview": "vite preview", "test": "npm run test:integration && npm run test:unit", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check", "check:watch": "svelte-kit sync && svelte-check --ignore '../worker' --no-tsconfig", "lint": "prettier --check . && eslint .", "format": "prettier --write .", "test:integration": "playwright test", "test:unit": "vitest", "deploy-web": "wrangler pages deploy .svelte-kit/cloudflare --project-name=upfront --commit-dirty=true --env .env.production --branch main", "deploy": "run-s build deploy-web"}, "devDependencies": {"@ai-sdk/openai": "^2.0.7", "@axiomhq/js": "^1.3.1", "@better-auth-kit/app-invite": "^0.1.2", "@eslint/compat": "^1.3.2", "@eslint/js": "^9.33.0", "@fullcalendar/common": "^5.11.5", "@fullcalendar/core": "^6.1.18", "@fullcalendar/daygrid": "^6.1.18", "@fullcalendar/interaction": "^6.1.18", "@fullcalendar/list": "^6.1.18", "@fullcalendar/multimonth": "^6.1.18", "@fullcalendar/timegrid": "^6.1.18", "@internationalized/date": "^3.8.2", "@kitql/helpers": "^0.8.13", "@kitql/internals": "^0.10.5", "@kripod/uuidv7": "^0.3.4", "@lucide/svelte": "^0.539.0", "@mastra/client-js": "^0.10.20", "@mastra/core": "^0.13.1", "@mastra/fastembed": "^0.10.2", "@mastra/loggers": "^0.10.6", "@mastra/memory": "^0.12.1", "@mastra/pg": "^0.13.3", "@nerdfolio/remult-better-auth": "^0.3.3", "@number-flow/svelte": "^0.3.9", "@oslojs/crypto": "^1.0.1", "@passwordless-id/webauthn": "^2.3.1", "@playwright/test": "^1.54.2", "@plunk/node": "^3.0.3", "@poppanator/sveltekit-svg": "^5.0.1", "@react-email/components": "^0.5.0", "@react-email/render": "^1.2.0", "@restatedev/restate-sdk": "^1.8.1", "@restatedev/restate-sdk-clients": "^1.8.1", "@rivetkit/actor": "^0.9.9", "@rivetkit/core": "^0.9.9", "@rivetkit/framework-base": "^0.9.9", "@signaldb/core": "^1.7.0", "@signaldb/devtools": "1.0.0-beta.4", "@signaldb/indexeddb": "^1.1.0", "@signaldb/svelte": "^1.1.1", "@signaldb/sync": "^1.3.1", "@sveltejs/adapter-auto": "^6.0.2", "@sveltejs/adapter-static": "^3.0.9", "@sveltejs/kit": "^2.27.3", "@sveltejs/vite-plugin-svelte": "^6.1.1", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.11", "@tanstack/table-core": "^8.21.3", "@types/eslint": "^9.6.1", "@types/gravatar": "^1.8.6", "@types/js-cookie": "^3.0.6", "@types/lodash-es": "^4.17.12", "@types/pg": "^8.15.5", "@types/react": "latest", "@types/react-dom": "latest", "@typescript-eslint/eslint-plugin": "^8.39.0", "@typescript-eslint/parser": "^8.39.0", "ai": "5.0.8", "apigen-ts": "^1.2.1", "autoprefixer": "^10.4.21", "bits-ui": "^2.9.2", "clsx": "^2.1.1", "date-fns": "^4.1.0", "decimal.js": "^10.6.0", "dotenv-cli": "10.0.0", "embla-carousel-svelte": "^8.6.0", "eslint": "^9.33.0", "eslint-config-prettier": "^10.1.8", "eslint-config-turbo": "^2.5.5", "eslint-plugin-neverthrow": "^1.1.4", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-svelte": "^3.11.0", "esm-env": "^1.2.2", "formsnap": "^2.0.1", "generate-password-ts": "^1.6.5", "globals": "^16.3.0", "gravatar-gen": "^1.0.2", "gravatar-url": "^4.0.1", "hono": "^4.9.0", "http-status-codes": "^2.3.0", "import": "^0.0.6", "isomorphic-dompurify": "^2.26.0", "jose": "^6.0.12", "js-cookie": "^3.0.5", "jsx-email": "^2.7.2", "layerchart": "2.0.0-next.10", "linq-extensions": "^1.0.4", "lodash-es": "^4.17.21", "mastra": "^0.10.20", "mode-watcher": "^1.1.0", "npm-run-all": "^4.1.5", "paneforge": "1.0.2", "pg": "^8.16.3", "postcss": "^8.5.6", "postgres": "^3.4.7", "prettier": "^3.6.2", "prettier-plugin-svelte": "^3.4.0", "prettier-plugin-tailwindcss": "^0.6.14", "react": "^19.1.1", "react-dom": "^19.1.1", "remult": "latest", "runed": "^0.31.1", "superjson": "^2.2.2", "svelte": "^5.38.0", "svelte-breadcrumbs": "^2.0.1", "svelte-check": "^4.3.1", "svelte-inview": "^4.0.4", "svelte-meta-tags": "^4.4.0", "svelte-motion": "^0.12.2", "svelte-radix": "^2.0.1", "svelte-sonner": "^1.0.5", "svelte-tel-input": "^3.6.0", "sveltekit-flash-message": "^2.4.6", "sveltekit-i18n": "^2.4.2", "sveltekit-superactions": "^0.8.0", "sveltekit-superforms": "^2.27.1", "sveltekit-top-loader": "^0.1.0", "sveltekit-view-transition": "^0.5.3", "tailwind-merge": "^3.3.1", "tailwind-scrollbar-hide": "^4.0.0", "tailwind-variants": "^2.1.0", "tailwindcss": "4.1.11", "tsx": "^4.20.3", "tw-animate-css": "^1.3.6", "type-fest": "^4.41.0", "typescript": "^5.9.2", "typescript-eslint": "^8.39.0", "valibot": "^1.1.0", "vaul-svelte": "1.0.0-next.7", "vite": "^7.1.1", "vite-plugin-devtools-json": "^0.4.1", "vite-plugin-kit-routes": "^1.0.2", "vite-plugin-remove-console": "^2.2.0", "vitest": "^3.2.4", "zod": "^4.0.16"}, "type": "module", "packageManager": "pnpm@10.14.0", "dependencies": {"better-auth": "^1.3.4", "d3-scale": "^4.0.2", "d3-shape": "^3.2.0", "ollama-ai-provider": "^1.2.0", "uuidv7": "^1.0.2"}}