/**
 * Role Controller
 * CRUD operations for system roles
 */

import { Allow, BackendMethod, remult } from 'remult'
import { Role } from '../models'
import { RolesType, rolesTypes, type RoleType } from '../types'

export class RoleController {
  @BackendMethod({ allowed: RolesType.Admin })
  static async findAll() {
    const repo = remult.repo(Role)
    return await repo.find({
      orderBy: { role: 'asc' }
    })
  }

  @BackendMethod({ allowed: RolesType.Admin })
  static async findByRole(role: RoleType) {
    const repo = remult.repo(Role)
    return await repo.findFirst({ role })
  }

  @BackendMethod({ allowed: RolesType.Admin })
  static async insert(roleData: Partial<Role>) {
    const repo = remult.repo(Role)
    return await repo.insert(roleData)
  }

  @BackendMethod({ allowed: RolesType.Admin })
  static async update(id: string, roleData: Partial<Role>) {
    const repo = remult.repo(Role)
    return await repo.update(id, roleData)
  }

  @BackendMethod({ allowed: RolesType.Admin })
  static async delete(roleData: Role) {
    const repo = remult.repo(Role)
    return await repo.delete(roleData)
  }

  @BackendMethod({ allowed: RolesType.Admin })
  static async deleteByRole(role: RoleType) {
    const repo = remult.repo(Role)
    const item = await repo.findFirst({ role })
    if (item) {
      return await repo.delete(item)
    }
    throw new Error('Role not found')
  }

  @BackendMethod({ allowed: RolesType.Admin })
  static async seed() {
    const repo = remult.repo(Role)
    const defaultRoles = rolesTypes

    const results: Role[] = []
    for (const roleName of defaultRoles) {
      const existing = await repo.findFirst({ role: roleName })
      if (!existing) {
        const created = await repo.insert({ role: roleName })
        results.push(created)
      }
    }

    return results
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async getAvailableRoles() {
    const repo = remult.repo(Role)
    const roles = await repo.find({
      orderBy: { role: 'asc' }
    })

    // Return role hierarchy information
    return roles.map(r => ({
      role: r.role,
      displayName: r.role.split('-').map(word =>
        word.charAt(0).toUpperCase() + word.slice(1)
      ).join(' '),
      level: getRoleLevel(r.role)
    }))
  }
}

function getRoleLevel(role: string): number {
  const levels: { [key: string]: number } = {
    [RolesType.Admin]: 100,
    [RolesType.OrgAdmin]: 75,
    [RolesType.TeamLocationAdmin]: 50,
    [RolesType.TeamMember]: 25,
    [RolesType.User]: 10,
    'guest': 1
  }
  return levels[role] || 0
}
