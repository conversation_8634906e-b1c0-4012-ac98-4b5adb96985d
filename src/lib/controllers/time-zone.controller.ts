/**
 * TimeZone Controller
 * CRUD operations for timezone reference data
 */

import { Allow, BackendMethod, remult } from 'remult'
import { TimeZone } from '../models'

export class TimeZoneController {
  @BackendMethod({ allowed: true })
  static async findAll() {
    const repo = remult.repo(TimeZone)
    return await repo.find({
      orderBy: { timeZoneName: 'asc' }
    })
  }

  @BackendMethod({ allowed: true })
  static async findById(id: string) {
    const repo = remult.repo(TimeZone)
    return await repo.findId(id)
  }

  @BackendMethod({ allowed: true })
  static async findByName(name: string) {
    const repo = remult.repo(TimeZone)
    return await repo.findFirst({ timeZoneName: name })
  }

  @BackendMethod({ allowed: true })
  static async findByOffset(offsetHours: number) {
    const repo = remult.repo(TimeZone)
    return await repo.find({
      where: { offsetHours },
      orderBy: { timeZoneName: 'asc' }
    })
  }

  @BackendMethod({ allowed: true })
  static async findByRegion(region: string) {
    const repo = remult.repo(TimeZone)
    return await repo.find({
      where: { timeZoneName: { $contains: region } },
      orderBy: { timeZoneName: 'asc' }
    })
  }

  @BackendMethod({ allowed: true })
  static async findCommon() {
    const repo = remult.repo(TimeZone)
    const commonTimezones = [
      'UTC',
      'America/New_York',
      'America/Chicago',
      'America/Denver',
      'America/Los_Angeles',
      'Europe/London',
      'Europe/Paris',
      'Europe/Berlin',
      'Asia/Tokyo',
      'Asia/Shanghai',
      'Australia/Sydney'
    ]

    return await repo.find({
      where: { timeZoneName: { $in: commonTimezones } },
      orderBy: { timeZoneName: 'asc' }
    })
  }

  @BackendMethod({ allowed: 'admin' })
  static async insert(timezone: Partial<TimeZone>) {
    const repo = remult.repo(TimeZone)
    return await repo.insert(timezone)
  }

  @BackendMethod({ allowed: 'admin' })
  static async update(id: string, timezone: Partial<TimeZone>) {
    const repo = remult.repo(TimeZone)
    return await repo.update(id, timezone)
  }

  @BackendMethod({ allowed: 'admin' })
  static async delete(timezone: TimeZone) {
    const repo = remult.repo(TimeZone)
    return await repo.delete(timezone)
  }

  @BackendMethod({ allowed: 'admin' })
  static async deleteById(id: string) {
    const repo = remult.repo(TimeZone)
    const item = await repo.findId(id)
    if (item) {
      return await repo.delete(item)
    }
    throw new Error('Timezone not found')
  }

  @BackendMethod({ allowed: 'admin' })
  static async seed() {
    const repo = remult.repo(TimeZone)

    // Common timezones with their UTC offsets
    const timezones = [
      { timeZoneName: 'UTC', offsetHours: 0 },
      { timeZoneName: 'America/New_York', offsetHours: -5 }, // EST (UTC-5)
      { timeZoneName: 'America/Chicago', offsetHours: -6 }, // CST (UTC-6)
      { timeZoneName: 'America/Denver', offsetHours: -7 }, // MST (UTC-7)
      { timeZoneName: 'America/Los_Angeles', offsetHours: -8 }, // PST (UTC-8)
      { timeZoneName: 'America/Toronto', offsetHours: -5 }, // EST (UTC-5)
      { timeZoneName: 'America/Vancouver', offsetHours: -8 }, // PST (UTC-8)
      { timeZoneName: 'Europe/London', offsetHours: 0 }, // GMT (UTC+0)
      { timeZoneName: 'Europe/Paris', offsetHours: 1 }, // CET (UTC+1)
      { timeZoneName: 'Europe/Berlin', offsetHours: 1 }, // CET (UTC+1)
      { timeZoneName: 'Europe/Rome', offsetHours: 1 }, // CET (UTC+1)
      { timeZoneName: 'Europe/Madrid', offsetHours: 1 }, // CET (UTC+1)
      { timeZoneName: 'Europe/Amsterdam', offsetHours: 1 }, // CET (UTC+1)
      { timeZoneName: 'Europe/Stockholm', offsetHours: 1 }, // CET (UTC+1)
      { timeZoneName: 'Europe/Moscow', offsetHours: 3 }, // MSK (UTC+3)
      { timeZoneName: 'Asia/Tokyo', offsetHours: 9 }, // JST (UTC+9)
      { timeZoneName: 'Asia/Shanghai', offsetHours: 8 }, // CST (UTC+8)
      { timeZoneName: 'Asia/Hong_Kong', offsetHours: 8 }, // HKT (UTC+8)
      { timeZoneName: 'Asia/Singapore', offsetHours: 8 }, // SGT (UTC+8)
      { timeZoneName: 'Asia/Seoul', offsetHours: 9 }, // KST (UTC+9)
      { timeZoneName: 'Asia/Mumbai', offsetHours: 5.5 }, // IST (UTC+5:30)
      { timeZoneName: 'Asia/Dubai', offsetHours: 4 }, // GST (UTC+4)
      { timeZoneName: 'Australia/Sydney', offsetHours: 10 }, // AEST (UTC+10)
      { timeZoneName: 'Australia/Melbourne', offsetHours: 10 }, // AEST (UTC+10)
      { timeZoneName: 'Australia/Perth', offsetHours: 8 }, // AWST (UTC+8)
      { timeZoneName: 'Pacific/Auckland', offsetHours: 12 }, // NZST (UTC+12)
      { timeZoneName: 'America/Sao_Paulo', offsetHours: -3 }, // BRT (UTC-3)
      { timeZoneName: 'America/Mexico_City', offsetHours: -6 }, // CST (UTC-6)
      { timeZoneName: 'Africa/Cairo', offsetHours: 2 }, // EET (UTC+2)
      { timeZoneName: 'Africa/Johannesburg', offsetHours: 2 }, // SAST (UTC+2)
    ]

    const results: TimeZone[] = []
    for (const tz of timezones) {
      const existing = await repo.findFirst({ timeZoneName: tz.timeZoneName })
      if (!existing) {
        const created = await repo.insert(tz)
        results.push(created)
      }
    }

    return results
  }

  @BackendMethod({ allowed: true })
  static async search(query: string) {
    const repo = remult.repo(TimeZone)
    return await repo.find({
      where: {
        timeZoneName: { $contains: query }
      },
      orderBy: { timeZoneName: 'asc' }
    })
  }

  @BackendMethod({ allowed: true })
  static async getByCurrentTime() {
    const repo = remult.repo(TimeZone)
    const now = new Date()
    const currentOffset = -now.getTimezoneOffset() / 60 // Convert minutes to hours and flip sign

    return await repo.find({
      where: { offsetHours: currentOffset },
      orderBy: { timeZoneName: 'asc' }
    })
  }

  @BackendMethod({ allowed: true })
  static async findPositiveOffsets() {
    const repo = remult.repo(TimeZone)
    return await repo.find({
      where: { offsetHours: { $gt: 0 } },
      orderBy: { offsetHours: 'asc' }
    })
  }

  @BackendMethod({ allowed: true })
  static async findNegativeOffsets() {
    const repo = remult.repo(TimeZone)
    return await repo.find({
      where: { offsetHours: { $lt: 0 } },
      orderBy: { offsetHours: 'desc' }
    })
  }

  @BackendMethod({ allowed: 'admin' })
  static async bulkInsert(timezones: Partial<TimeZone>[]) {
    const repo = remult.repo(TimeZone)
    const results: TimeZone[] = []

    for (const tz of timezones) {
      if (tz.timeZoneName) {
        const existing = await repo.findFirst({ timeZoneName: tz.timeZoneName })
        if (!existing) {
          const created = await repo.insert(tz)
          results.push(created)
        }
      }
    }

    return results
  }

  @BackendMethod({ allowed: 'admin' })
  static async updateOffset(name: string, offsetHours: number) {
    const repo = remult.repo(TimeZone)
    const item = await repo.findFirst({ timeZoneName: name })

    if (item) {
      return await repo.update(item.id, {
        offsetHours
      })
    }
    throw new Error('Timezone not found')
  }
}
