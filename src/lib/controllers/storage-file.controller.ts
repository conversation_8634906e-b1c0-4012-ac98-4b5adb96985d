/**
 * StorageFile Controller
 * CRUD operations for storage files
 */

import { Allow, BackendMethod, remult } from 'remult'
import { StorageFile } from '../models'

export class StorageFileController {
  @BackendMethod({ allowed: Allow.authenticated })
  static async findAll() {
    const repo = remult.repo(StorageFile)
    return await repo.find({
      include: {
        bucket: true
      },
      orderBy: { createdAt: 'desc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findById(id: string) {
    const repo = remult.repo(StorageFile)
    return await repo.findId(id, {
      include: {
        bucket: true
      }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findByBucket(bucketId: string) {
    const repo = remult.repo(StorageFile)
    return await repo.find({
      where: { bucketId },
      include: {
        bucket: true
      },
      orderBy: { createdAt: 'desc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findUploaded() {
    const repo = remult.repo(StorageFile)
    return await repo.find({
      where: { isUploaded: true },
      include: {
        bucket: true
      },
      orderBy: { createdAt: 'desc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findPending() {
    const repo = remult.repo(StorageFile)
    return await repo.find({
      where: { isUploaded: false },
      include: {
        bucket: true
      },
      orderBy: { createdAt: 'desc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findByUser(uploadedByUserId: string) {
    const repo = remult.repo(StorageFile)
    return await repo.find({
      where: { uploadedByUserId },
      include: {
        bucket: true
      },
      orderBy: { createdAt: 'desc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findByMimeType(mimeType: string) {
    const repo = remult.repo(StorageFile)
    return await repo.find({
      where: { mimeType },
      include: {
        bucket: true
      },
      orderBy: { createdAt: 'desc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findImages() {
    const repo = remult.repo(StorageFile)
    return await repo.find({
      where: {
        mimeType: { $contains: 'image/' },
        isUploaded: true
      },
      include: {
        bucket: true
      },
      orderBy: { createdAt: 'desc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findDocuments() {
    const repo = remult.repo(StorageFile)
    return await repo.find({
      where: {
        $or: [
          { mimeType: { $contains: 'application/pdf' } },
          { mimeType: { $contains: 'application/msword' } },
          { mimeType: { $contains: 'application/vnd.openxmlformats-officedocument' } },
          { mimeType: { $contains: 'text/' } }
        ],
        isUploaded: true
      },
      include: {
        bucket: true
      },
      orderBy: { createdAt: 'desc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async insert(file: Partial<StorageFile>) {
    const repo = remult.repo(StorageFile)

    // Set uploader to current user if not specified
    if (!file.uploadedByUserId && remult.user) {
      file.uploadedByUserId = remult.user.id
    }

    return await repo.insert(file)
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async update(id: string, file: Partial<StorageFile>) {
    const repo = remult.repo(StorageFile)

    // Check if user can update this file
    const existingFile = await repo.findId(id)
    if (!existingFile) {
      throw new Error('File not found')
    }

    if (!remult.user?.roles?.includes('admin') && existingFile.uploadedByUserId !== remult.user?.id) {
      throw new Error('You can only update your own files')
    }

    return await repo.update(id, file)
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async delete(file: StorageFile) {
    const repo = remult.repo(StorageFile)

    // Check if user can delete this file
    if (!remult.user?.roles?.includes('admin') && file.uploadedByUserId !== remult.user?.id) {
      throw new Error('You can only delete your own files')
    }

    return await repo.delete(file)
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async deleteById(id: string) {
    const repo = remult.repo(StorageFile)
    const item = await repo.findId(id)
    if (item) {
      // Check permissions
      if (!remult.user?.roles?.includes('admin') && item.uploadedByUserId !== remult.user?.id) {
        throw new Error('You can only delete your own files')
      }
      return await repo.delete(item)
    }
    throw new Error('Storage file not found')
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async markAsUploaded(id: string, fileInfo: {
    size?: number
    mimeType?: string
    etag?: string
  }) {
    const repo = remult.repo(StorageFile)
    const item = await repo.findId(id)
    if (item) {
      // Check permissions
      if (!remult.user?.roles?.includes('admin') && item.uploadedByUserId !== remult.user?.id) {
        throw new Error('You can only update your own files')
      }

      return await repo.save({
        ...item,
        isUploaded: true,
        ...fileInfo
      })
    }
    throw new Error('Storage file not found')
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async updateMetadata(id: string, metadata: any) {
    const repo = remult.repo(StorageFile)
    const item = await repo.findId(id)
    if (item) {
      // Check permissions
      if (!remult.user?.roles?.includes('admin') && item.uploadedByUserId !== remult.user?.id) {
        throw new Error('You can only update your own files')
      }

      return await repo.save({
        ...item,
        metadata: { ...item.metadata, ...metadata }
      })
    }
    throw new Error('Storage file not found')
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async search(query: string) {
    const repo = remult.repo(StorageFile)
    return await repo.find({
      where: {
        $or: [
          { name: { $contains: query } },
          { mimeType: { $contains: query } }
        ],
        isUploaded: true
      },
      include: {
        bucket: true
      },
      orderBy: { createdAt: 'desc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async getStats(bucketId?: string) {
    const repo = remult.repo(StorageFile)
    const whereClause = bucketId ? { bucketId } : {}

    const files = await repo.find({
      where: whereClause
    })

    const totalFiles = files.length
    const uploadedFiles = files.filter(f => f.isUploaded).length
    const totalSize = files.reduce((sum, f) => sum + (f.size || 0), 0)

    // Group by mime type
    const mimeTypes: { [key: string]: number } = {}
    files.forEach(f => {
      if (f.mimeType) {
        const category = f.mimeType.split('/')[0]
        mimeTypes[category] = (mimeTypes[category] || 0) + 1
      }
    })

    return {
      totalFiles,
      uploadedFiles,
      pendingFiles: totalFiles - uploadedFiles,
      totalSize,
      averageFileSize: totalFiles > 0 ? Math.round(totalSize / totalFiles) : 0,
      mimeTypeDistribution: mimeTypes
    }
  }

  @BackendMethod({ allowed: 'admin' })
  static async bulkDelete(fileIds: string[]) {
    const repo = remult.repo(StorageFile)
    const results: string[] = []

    for (const id of fileIds) {
      const item = await repo.findId(id)
      if (item) {
        await repo.delete(item)
        results.push(id)
      }
    }

    return results
  }

  @BackendMethod({ allowed: 'admin' })
  static async cleanupPendingFiles(olderThanDays: number = 7) {
    const repo = remult.repo(StorageFile)
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays)

    const pendingFiles = await repo.find({
      where: {
        isUploaded: false,
        createdAt: { $lt: cutoffDate }
      }
    })

    const results: string[] = []
    for (const file of pendingFiles) {
      await repo.delete(file)
      results.push(file.id)
    }

    return results
  }
}
