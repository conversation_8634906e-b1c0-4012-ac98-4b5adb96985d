/**
 * OrgBankDetail Controller
 * CRUD operations for organization bank details
 */

import { Allow, BackendMethod, remult } from 'remult'
import { OrgBankDetail } from '../models'

export class OrgBankDetailController {
  @BackendMethod({ allowed: Allow.authenticated })
  static async findAll() {
    const repo = remult.repo(OrgBankDetail)
    return await repo.find({
      include: {
        organization: true
      },
      orderBy: { createdAt: 'desc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findById(id: string) {
    const repo = remult.repo(OrgBankDetail)
    return await repo.findId(id, {
      include: {
        organization: true
      }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findByOrganization(organizationId: string) {
    const repo = remult.repo(OrgBankDetail)
    return await repo.find({
      where: { organizationId },
      include: {
        organization: true
      },
      orderBy: { createdAt: 'desc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findByBankName(bankName: string) {
    const repo = remult.repo(OrgBankDetail)
    return await repo.find({
      where: { bankName: { $contains: bankName } },
      include: {
        organization: true
      },
      orderBy: { bankName: 'asc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findByAccountType(accountType: 'Check' | 'Savings' | 'Investment') {
    const repo = remult.repo(OrgBankDetail)
    return await repo.find({
      where: { accountType },
      include: {
        organization: true
      },
      orderBy: { bankName: 'asc' }
    })
  }

  @BackendMethod({ allowed: ['admin', 'organisation-admin'] })
  static async insert(bankDetail: Partial<OrgBankDetail>) {
    const repo = remult.repo(OrgBankDetail)

    // Check permissions
    if (!remult.user?.roles?.includes('admin')) {
      const userOrganizationId = remult.user?.organizationId
      if (userOrganizationId !== bankDetail.organizationId) {
        throw new Error('You can only create bank details for your own organization')
      }
    }

    return await repo.insert(bankDetail)
  }

  @BackendMethod({ allowed: ['admin', 'organisation-admin'] })
  static async update(id: string, bankDetail: Partial<OrgBankDetail>) {
    const repo = remult.repo(OrgBankDetail)

    // Check permissions
    if (!remult.user?.roles?.includes('admin')) {
      const existingBankDetail = await repo.findId(id)
      if (!existingBankDetail) {
        throw new Error('Bank detail not found')
      }

      const userOrganizationId = remult.user?.organizationId
      if (userOrganizationId !== existingBankDetail.organizationId) {
        throw new Error('You can only update bank details for your own organization')
      }
    }

    return await repo.update(id, bankDetail)
  }

  @BackendMethod({ allowed: ['admin', 'organisation-admin'] })
  static async delete(bankDetail: OrgBankDetail) {
    const repo = remult.repo(OrgBankDetail)

    // Check permissions
    if (!remult.user?.roles?.includes('admin')) {
      const userOrganizationId = remult.user?.organizationId
      if (userOrganizationId !== bankDetail.organizationId) {
        throw new Error('You can only delete bank details for your own organization')
      }
    }

    return await repo.delete(bankDetail)
  }

  @BackendMethod({ allowed: ['admin', 'organisation-admin'] })
  static async deleteById(id: string) {
    const repo = remult.repo(OrgBankDetail)
    const item = await repo.findId(id)
    if (item) {
      // Check permissions
      if (!remult.user?.roles?.includes('admin')) {
        const userOrganizationId = remult.user?.organizationId
        if (userOrganizationId !== item.organizationId) {
          throw new Error('You can only delete bank details for your own organization')
        }
      }
      return await repo.delete(item)
    }
    throw new Error('Bank detail not found')
  }

  @BackendMethod({ allowed: ['admin', 'organisation-admin'] })
  static async updateAccountInfo(id: string, accountInfo: {
    accountHolderName?: string
    accountNumber?: string
    routingNumber?: string
    swiftCode?: string
    iban?: string
  }) {
    const repo = remult.repo(OrgBankDetail)
    const item = await repo.findId(id)
    if (item) {
      // Check permissions
      if (!remult.user?.roles?.includes('admin')) {
        const userOrganizationId = remult.user?.organizationId
        if (userOrganizationId !== item.organizationId) {
          throw new Error('You can only modify bank details for your own organization')
        }
      }

      return await repo.save({
        ...item,
        ...accountInfo
      })
    }
    throw new Error('Bank detail not found')
  }

  @BackendMethod({ allowed: ['admin', 'organisation-admin'] })
  static async updateBankInfo(id: string, bankInfo: {
    bankName?: string
    bankAddress?: string
    bankCity?: string
    bankState?: string
    bankCountry?: string
    bankPostalCode?: string
  }) {
    const repo = remult.repo(OrgBankDetail)
    const item = await repo.findId(id)
    if (item) {
      // Check permissions
      if (!remult.user?.roles?.includes('admin')) {
        const userOrganizationId = remult.user?.organizationId
        if (userOrganizationId !== item.organizationId) {
          throw new Error('You can only modify bank details for your own organization')
        }
      }

      return await repo.save({
        ...item,
        ...bankInfo
      })
    }
    throw new Error('Bank detail not found')
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async getStats(organizationId?: string) {
    const repo = remult.repo(OrgBankDetail)
    const whereClause = organizationId ? { organizationId } : {}

    const bankDetails = await repo.find({
      where: whereClause
    })

    // Group by account type
    const accountTypeStats: { [key: string]: number } = {}
    bankDetails.forEach(bd => {
      if (bd.accountType) {
        accountTypeStats[bd.accountType] = (accountTypeStats[bd.accountType] || 0) + 1
      }
    })

    // Group by country
    const countryStats: { [key: string]: number } = {}
    bankDetails.forEach(bd => {
      if (bd.bankCountry) {
        countryStats[bd.bankCountry] = (countryStats[bd.bankCountry] || 0) + 1
      }
    })

    return {
      totalBankDetails: bankDetails.length,
      accountTypeDistribution: accountTypeStats,
      countryDistribution: countryStats
    }
  }

  @BackendMethod({ allowed: ['admin', 'organisation-admin'] })
  static async validateAccountNumber(id: string) {
    const repo = remult.repo(OrgBankDetail)
    const item = await repo.findId(id)
    if (item) {
      // Check permissions
      if (!remult.user?.roles?.includes('admin')) {
        const userOrganizationId = remult.user?.organizationId
        if (userOrganizationId !== item.organizationId) {
          throw new Error('You can only validate bank details for your own organization')
        }
      }

      // Here you would implement actual bank account validation logic
      // For now, just return a mock response
      const isValid = item.accountNumber && item.accountNumber.length >= 8

      return {
        isValid,
        message: isValid ? 'Account number format is valid' : 'Invalid account number format',
        accountNumber: item.accountNumber,
        bankName: item.bankName,
        timestamp: new Date()
      }
    }
    throw new Error('Bank detail not found')
  }

  @BackendMethod({ allowed: 'admin' })
  static async getSupportedAccountTypes() {
    return [
      { value: 'checking', label: 'Checking Account', description: 'Standard checking account' },
      { value: 'savings', label: 'Savings Account', description: 'Standard savings account' },
      { value: 'business_checking', label: 'Business Checking', description: 'Business checking account' },
      { value: 'business_savings', label: 'Business Savings', description: 'Business savings account' },
      { value: 'money_market', label: 'Money Market', description: 'Money market account' },
      { value: 'certificate_deposit', label: 'Certificate of Deposit', description: 'CD account' }
    ]
  }

  @BackendMethod({ allowed: ['admin', 'organisation-admin'] })
  static async maskSensitiveData(bankDetail: OrgBankDetail) {
    // Return bank detail with masked sensitive information
    return {
      ...bankDetail,
      accountNumber: bankDetail.accountNumber ?
        '****' + bankDetail.accountNumber.slice(-4) : undefined,
      routingNumber: bankDetail.routingNumber ?
        '****' + bankDetail.routingNumber.slice(-4) : undefined,
      swiftCode: bankDetail.swiftCode ?
        '****' + bankDetail.swiftCode.slice(-4) : undefined,
      iban: bankDetail.iban ?
        '****' + bankDetail.iban.slice(-4) : undefined
    }
  }
}
