/**
 * UserRole Controller
 * CRUD operations for user role assignments
 */

import { Allow, BackendMethod, remult } from 'remult'
import { UserRole } from '../models'
import { RolesType } from '../types'

export class UserRoleController {
  @BackendMethod({ allowed: RolesType.Admin })
  static async findAll() {
    const repo = remult.repo(UserRole)
    return await repo.find({
      include: {
        user: true
      },
      orderBy: { createdAt: 'desc' }
    })
  }

  @BackendMethod({ allowed: RolesType.Admin })
  static async findById(id: string) {
    const repo = remult.repo(UserRole)
    return await repo.findId(id, {
      include: {
        user: true
      }
    })
  }

  @BackendMethod({ allowed: RolesType.Admin })
  static async findByUser(userId: string) {
    const repo = remult.repo(UserRole)
    return await repo.find({
      where: { userId },
      include: {
        user: true,

      },
      orderBy: { createdAt: 'desc' }
    })
  }

  @BackendMethod({ allowed: RolesType.Admin })
  static async findByRole(role: string) {
    const repo = remult.repo(UserRole)
    return await repo.find({
      where: { role },
      include: {
        user: true,

      },
      orderBy: { createdAt: 'desc' }
    })
  }

  @BackendMethod({ allowed: RolesType.Admin })
  static async insert(userRole: Partial<UserRole>) {
    const repo = remult.repo(UserRole)
    return await repo.insert(userRole)
  }

  @BackendMethod({ allowed: RolesType.Admin })
  static async update(id: string, userRole: Partial<UserRole>) {
    const repo = remult.repo(UserRole)
    return await repo.update(id, userRole)
  }

  @BackendMethod({ allowed: RolesType.Admin })
  static async delete(userRole: UserRole) {
    const repo = remult.repo(UserRole)
    return await repo.delete(userRole)
  }

  @BackendMethod({ allowed: RolesType.Admin })
  static async deleteById(id: string) {
    const repo = remult.repo(UserRole)
    const item = await repo.findId(id)
    if (item) {
      return await repo.delete(item)
    }
    throw new Error('User role not found')
  }

  @BackendMethod({ allowed: RolesType.Admin })
  static async assignRole(userId: string, role: string) {
    const repo = remult.repo(UserRole)

    // Check if assignment already exists
    const existing = await repo.findFirst({
      userId,
      role
    })

    if (existing) {
      throw new Error('User already has this role')
    }

    return await repo.insert({
      userId,
      role
    })
  }

  @BackendMethod({ allowed: RolesType.Admin })
  static async removeRole(userId: string, role: string) {
    const repo = remult.repo(UserRole)
    const item = await repo.findFirst({
      userId,
      role
    })

    if (item) {
      return await repo.delete(item)
    }
    throw new Error('User role assignment not found')
  }

  @BackendMethod({ allowed: RolesType.Admin })
  static async getUserRoles(userId: string) {
    const repo = remult.repo(UserRole)
    const userRoles = await repo.find({
      where: { userId },
      include: {

      }
    })

    return userRoles.map(ur => ur.role)
  }

  @BackendMethod({ allowed: RolesType.Admin })
  static async bulkAssignRole(userIds: string[], role: string) {
    const repo = remult.repo(UserRole)
    const results: UserRole[] = []

    for (const userId of userIds) {
      // Check if assignment already exists
      const existing = await repo.findFirst({
        userId,
        role
      })

      if (!existing) {
        const created = await repo.insert({
          userId,
          role
        })
        results.push(created)
      }
    }

    return results
  }

  @BackendMethod({ allowed: RolesType.Admin })
  static async bulkRemoveRole(userIds: string[], role: string) {
    const repo = remult.repo(UserRole)
    const results: string[] = []

    for (const userId of userIds) {
      const item = await repo.findFirst({
        userId,
        role
      })

      if (item) {
        await repo.delete(item)
        results.push(userId)
      }
    }

    return results
  }

  @BackendMethod({ allowed: RolesType.Admin })
  static async replaceUserRoles(userId: string, roles: string[]) {
    const repo = remult.repo(UserRole)

    // Remove all existing roles for user
    const existingRoles = await repo.find({
      where: { userId }
    })

    for (const existingRole of existingRoles) {
      await repo.delete(existingRole)
    }

    // Add new roles
    const results: UserRole[] = []
    for (const role of roles) {
      const created = await repo.insert({
        userId,
        role
      })
      results.push(created)
    }

    return results
  }

  @BackendMethod({ allowed: RolesType.Admin })
  static async getRoleStats() {
    const repo = remult.repo(UserRole)
    const userRoles = await repo.find()

    // Group by role
    const roleStats: { [key: string]: number } = {}
    userRoles.forEach(ur => {
      roleStats[ur.role] = (roleStats[ur.role] || 0) + 1
    })

    return {
      totalAssignments: userRoles.length,
      roleDistribution: roleStats,
      uniqueUsers: new Set(userRoles.map(ur => ur.userId)).size
    }
  }
}
