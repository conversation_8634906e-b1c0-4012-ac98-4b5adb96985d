/**
 * OrgPaymentProvider Controller
 * CRUD operations for organization payment providers
 */

import { OrgPaymentProvider } from '../models'
import { Allow, BackendMethod, remult } from 'remult'


export class OrgPaymentProviderController {
  @BackendMethod({ allowed: Allow.authenticated })
  static async findAll() {
    const repo = remult.repo(OrgPaymentProvider)
    return await repo.find({
      include: {
        organization: true
      },
      orderBy: { createdAt: 'desc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findById(id: string) {
    const repo = remult.repo(OrgPaymentProvider)
    return await repo.findId(id, {
      include: {
        organization: true
      }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findByOrganization(organizationId: string) {
    const repo = remult.repo(OrgPaymentProvider)
    return await repo.find({
      where: { organizationId },
      include: {
        organization: true
      },
      orderBy: { createdAt: 'desc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findByProvider(providerName: string) {
    const repo = remult.repo(OrgPaymentProvider)
    return await repo.find({
      where: { providerName },
      include: {
        organization: true
      },
      orderBy: { createdAt: 'desc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findActive() {
    const repo = remult.repo(OrgPaymentProvider)
    return await repo.find({
      where: { isActive: true },
      include: {
        organization: true
      },
      orderBy: { createdAt: 'desc' }
    })
  }

  @BackendMethod({ allowed: ['admin', 'organisation-admin'] })
  static async insert(paymentProvider: Partial<OrgPaymentProvider>) {
    const repo = remult.repo(OrgPaymentProvider)

    // Check permissions
    if (!remult.user?.roles?.includes('admin')) {
      const userOrganizationId = remult.user?.organizationId
      if (userOrganizationId !== paymentProvider.organizationId) {
        throw new Error('You can only create payment providers for your own organization')
      }
    }

    return await repo.insert(paymentProvider)
  }

  @BackendMethod({ allowed: ['admin', 'organisation-admin'] })
  static async update(id: string, paymentProvider: Partial<OrgPaymentProvider>) {
    const repo = remult.repo(OrgPaymentProvider)

    // Check permissions
    if (!remult.user?.roles?.includes('admin')) {
      const existingPaymentProvider = await repo.findId(id)
      if (!existingPaymentProvider) {
        throw new Error('Payment provider not found')
      }

      const userOrganizationId = remult.user?.organizationId
      if (userOrganizationId !== existingPaymentProvider.organizationId) {
        throw new Error('You can only update payment providers for your own organization')
      }
    }

    return await repo.update(id, paymentProvider)
  }

  @BackendMethod({ allowed: ['admin', 'organisation-admin'] })
  static async delete(paymentProvider: OrgPaymentProvider) {
    const repo = remult.repo(OrgPaymentProvider)

    // Check permissions
    if (!remult.user?.roles?.includes('admin')) {
      const userOrganizationId = remult.user?.organizationId
      if (userOrganizationId !== paymentProvider.organizationId) {
        throw new Error('You can only delete payment providers for your own organization')
      }
    }

    return await repo.delete(paymentProvider)
  }

  @BackendMethod({ allowed: ['admin', 'organisation-admin'] })
  static async deleteById(id: string) {
    const repo = remult.repo(OrgPaymentProvider)
    const item = await repo.findId(id)
    if (item) {
      // Check permissions
      if (!remult.user?.roles?.includes('admin')) {
        const userOrganizationId = remult.user?.organizationId
        if (userOrganizationId !== item.organizationId) {
          throw new Error('You can only delete payment providers for your own organization')
        }
      }
      return await repo.delete(item)
    }
    throw new Error('Payment provider not found')
  }

  @BackendMethod({ allowed: ['admin', 'organisation-admin'] })
  static async toggleActive(id: string) {
    const repo = remult.repo(OrgPaymentProvider)
    const item = await repo.findId(id)
    if (item) {
      // Check permissions
      if (!remult.user?.roles?.includes('admin')) {
        const userOrganizationId = remult.user?.organizationId
        if (userOrganizationId !== item.organizationId) {
          throw new Error('You can only modify payment providers for your own organization')
        }
      }

      return await repo.save({
        ...item,
        isActive: !item.isActive
      })
    }
    throw new Error('Payment provider not found')
  }

  @BackendMethod({ allowed: ['admin', 'organisation-admin'] })
  static async updateCredentials(id: string, credentials: {
    publicKey?: string
    secretKey?: string
    webhookSecret?: string
    merchantId?: string
  }) {
    const repo = remult.repo(OrgPaymentProvider)
    const item = await repo.findId(id)
    if (item) {
      // Check permissions
      if (!remult.user?.roles?.includes('admin')) {
        const userOrganizationId = remult.user?.organizationId
        if (userOrganizationId !== item.organizationId) {
          throw new Error('You can only modify payment providers for your own organization')
        }
      }

      return await repo.save({
        ...item,
        ...credentials
      })
    }
    throw new Error('Payment provider not found')
  }

  @BackendMethod({ allowed: ['admin', 'organisation-admin'] })
  static async testConnection(id: string) {
    const repo = remult.repo(OrgPaymentProvider)
    const item = await repo.findId(id)
    if (item) {
      // Check permissions
      if (!remult.user?.roles?.includes('admin')) {
        const userOrganizationId = remult.user?.organizationId
        if (userOrganizationId !== item.organizationId) {
          throw new Error('You can only test payment providers for your own organization')
        }
      }

      // Here you would implement actual payment provider testing logic
      // For now, just return a mock response
      return {
        success: true,
        message: 'Connection test successful',
        provider: item.providerName,
        timestamp: new Date()
      }
    }
    throw new Error('Payment provider not found')
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async getStats(organizationId?: string) {
    const repo = remult.repo(OrgPaymentProvider)
    const whereClause = organizationId ? { organizationId } : {}

    const providers = await repo.find({
      where: whereClause
    })

    // Group by provider type
    const providerStats: { [key: string]: number } = {}
    providers.forEach(p => {
      providerStats[p.providerName] = (providerStats[p.providerName] || 0) + 1
    })

    return {
      totalProviders: providers.length,
      activeProviders: providers.filter(p => p.isActive).length,
      providerDistribution: providerStats
    }
  }

  @BackendMethod({ allowed: 'admin' })
  static async getSupportedProviders() {
    return [
      { name: 'stripe', displayName: 'Stripe', description: 'Online payment processing' },
      { name: 'paypal', displayName: 'PayPal', description: 'PayPal payment gateway' },
      { name: 'square', displayName: 'Square', description: 'Square payment processing' },
      { name: 'razorpay', displayName: 'Razorpay', description: 'Indian payment gateway' },
      { name: 'flutterwave', displayName: 'Flutterwave', description: 'African payment gateway' },
      { name: 'mollie', displayName: 'Mollie', description: 'European payment gateway' }
    ]
  }
}
