

// ==================== CONTROLLER COLLECTIONS ====================

// Core business controllers
export const coreBusinessControllers = [
  'CalendarBlockController',
  'AppointmentTypeController',
  'AppointmentController',
  'StorageBucketController',
  'StorageFileController'
] as const

// User management controllers
export const userManagementControllers = [
  'GenderController',
  'RoleController',
  'UserRoleController',
  'UserController'
] as const

// Organization controllers
export const organizationControllers = [
  'OrganizationController',
  'TeamController',
  'TeamMemberController',
  'MemberController',
  'InvitationController'
] as const

// Time and scheduling controllers
export const timeSchedulingControllers = [
  'TimeZoneController',
  'WorkdaySettingController'
] as const

// ==================== ALL CONTROLLERS ARRAY ====================

import { CalendarBlockController } from './calendar-block.controller'
import { AppointmentTypeController } from './appointment-type.controller'
import { AppointmentController } from './appointment.controller'
import { StorageBucketController } from './storage-bucket.controller'
import { StorageFileController } from './storage-file.controller'
import { GenderController } from './gender.controller'
import { RoleController } from './role.controller'
import { UserRoleController } from './user-role.controller'
import { UserController } from './user.controller'
import { OrganizationController } from './organization.controller'
import { TeamController } from './team.controller'
import { TeamMemberController } from './team-member.controller'
import { MemberController } from './member.controller'
import { InvitationController } from './invitation.controller'
import { TimeZoneController } from './time-zone.controller'
import { WorkdaySettingController } from './workday-setting.controller'

/**
 * Array of all controllers for easy registration
 * Use this in your Remult configuration
 */
export const controllers = [
  // Core Business
  CalendarBlockController,
  AppointmentTypeController,
  AppointmentController,
  StorageBucketController,
  StorageFileController,

  // User Management
  GenderController,
  RoleController,
  UserRoleController,
  UserController,

  // Organization Management
  OrganizationController,
  TeamController,
  TeamMemberController,
  MemberController,
  InvitationController,

  // Time & Scheduling
  TimeZoneController,
  WorkdaySettingController,

]

// ==================== CONTROLLER GROUPS ====================

/**
 * Controllers grouped by business domain for selective registration
 */
export const controllerGroups = {
  core: [
    CalendarBlockController,
    AppointmentTypeController,
    AppointmentController,
    StorageBucketController,
    StorageFileController
  ],

  users: [
    GenderController,
    RoleController,
    UserRoleController,
    UserController
  ],

  organization: [
    OrganizationController,
    TeamController,
    MemberController,
    InvitationController
  ],

  scheduling: [
    TimeZoneController,
    WorkdaySettingController
  ]
}

// ==================== UTILITY TYPES ====================

/**
 * Union type of all controller names
 */
export type ControllerName =
  | typeof coreBusinessControllers[number]
  | typeof userManagementControllers[number]
  | typeof organizationControllers[number]
  | typeof timeSchedulingControllers[number]
  | 'TasksController'

/**
 * Controller group names
 */
export type ControllerGroup = keyof typeof controllerGroups
