import { RolesType, type DefaultError, type MethodCallbacks } from '../types'
import { isUserInRole } from '../utils'

export const adminOnly = (remult) => {
      return isUserInRole(remult.user as any, RolesType.Admin)
}
export const orgAdminOnly = (remult) => {
      return isUserInRole(remult.user as any, RolesType.OrgAdmin) || adminOnly(remult)
}

export const teamAdminOnly = (remult) => {
      return isUserInRole(remult.user as any, RolesType.TeamLocationAdmin) || orgAdminOnly(remult) || adminOnly(remult)
}

export const orgAdminOrOwnerOnly = (remult,
      id: string
) => {
      return remult.user?.id === id || orgAdminOnly(remult)
}

export const actionLoading = async (callBacks: MethodCallbacks<any, any>, loading: boolean) => {
      if (callBacks.onLoading) {
            await callBacks.onLoading(loading)
      }
}
export const actionSuccess = async (result: any, callBacks: MethodCallbacks<any, any>) => {
      await actionLoading(callBacks, false)
      if (callBacks.onSuccess) {
            await callBacks.onSuccess(result)
      }
      return { data: result }
}
export const actionError = async (error: DefaultError | any, callBacks: MethodCallbacks<any, any>) => {
      if (callBacks.onError) {
            await callBacks.onError(error)
      }
      return { error }
}