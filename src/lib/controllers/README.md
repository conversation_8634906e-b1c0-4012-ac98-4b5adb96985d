# Remult Controllers

This directory contains Remult controller files with backend methods for CRUD operations on entities. Each controller provides a clean API layer with proper authorization and business logic.

## 📁 File Structure

Each controller is in its own file following the pattern `{entity-name}-controller.ts`:

### Core Business Controllers

- `calendar-block-controller.ts` - Calendar scheduling blocks management
- `appointment-type-controller.ts` - Appointment types configuration
- `appointment-controller.ts` - Individual appointment management
- `storage-bucket-controller.ts` - File storage bucket management (Admin only)
- `storage-file-controller.ts` - Individual file management

### User & Role Management Controllers

- `user-controller.ts` - User accounts and profiles
- `role-controller.ts` - System roles management (Admin only)
- `gender-controller.ts` - Gender reference data

### Organization & Team Management Controllers

- `organization-controller.ts` - Organization management
- `team-controller.ts` - Team management within organizations
- `member-controller.ts` - Organization/team membership
- `invitation-controller.ts` - Invitation system for joining organizations/teams

### Time & Scheduling Controllers

- `time-zone-controller.ts` - Timezone reference data

### Existing Controllers

- `task.ts` - Task management (existing example)

## 🚀 Usage

### Import Individual Controllers

```typescript
import { UserController } from './controllers/user-controller'
import { OrganizationController } from './controllers/organization-controller'
import { AppointmentController } from './controllers/appointment-controller'
```

### Import Multiple Controllers

```typescript
import {
  UserController,
  OrganizationController,
  TeamController,
} from './controllers'
```

### Import All Controllers

```typescript
import { controllers } from './controllers'

// Use in Remult configuration
app.use(
  remultExpress({
    controllers,
    // ... other config
  })
)
```

### Import Controller Groups

```typescript
import { controllerGroups } from './controllers'

// Use specific groups
app.use(
  remultExpress({
    controllers: [...controllerGroups.core, ...controllerGroups.users],
    // ... other config
  })
)
```

## 🔐 Authorization Patterns

### Public Access

```typescript
@BackendMethod({ allowed: true })
static async findAll() { /* ... */ }
```

### Authenticated Users

```typescript
@BackendMethod({ allowed: Allow.authenticated })
static async findAll() { /* ... */ }
```

### Admin Only

```typescript
@BackendMethod({ allowed: RolesType.Admin })
static async insert() { /* ... */ }
```

### Role-based Access

```typescript
@BackendMethod({ allowed: [RolesType.Admin, RolesType.TeamLocationAdmin] })
static async update() { /* ... */ }
```

## 📋 Common CRUD Operations

### Basic CRUD Pattern

```typescript
export class EntityController {
  @BackendMethod({ allowed: Allow.authenticated })
  static async findAll() {
    const repo = remult.repo(Entity)
    return await repo.find({
      include: {
        /* relations */
      },
      orderBy: { createdAt: 'desc' },
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findById(id: string) {
    const repo = remult.repo(Entity)
    return await repo.findId(id, {
      include: {
        /* relations */
      },
    })
  }

  @BackendMethod({ allowed: ['admin', 'team-admin'] })
  static async insert(entity: Partial<Entity>) {
    const repo = remult.repo(Entity)
    return await repo.insert(entity)
  }

  @BackendMethod({ allowed: [RolesType.Admin, RolesType.TeamLocationAdmin] })
  static async update(entity: Entity) {
    const repo = remult.repo(Entity)
    return await repo.save(entity)
  }

  @BackendMethod({ allowed: RolesType.Admin })
  static async delete(entity: Entity) {
    const repo = remult.repo(Entity)
    return await repo.delete(entity)
  }
}
```

## 🔍 Advanced Query Methods

### Filtering and Search

```typescript
@BackendMethod({ allowed: Allow.authenticated })
static async findByOrganization(organizationId: string) {
  const repo = remult.repo(Entity)
  return await repo.find({
    where: { organizationId },
    include: { /* relations */ },
    orderBy: { name: 'asc' }
  })
}

@BackendMethod({ allowed: Allow.authenticated })
static async search(query: string) {
  const repo = remult.repo(Entity)
  return await repo.find({
    where: {
      $or: [
        { name: { $contains: query } },
        { email: { $contains: query } }
      ]
    },
    orderBy: { name: 'asc' }
  })
}
```

### Date Range Queries

```typescript
@BackendMethod({ allowed: Allow.authenticated })
static async findByDateRange(startDate: Date, endDate: Date) {
  const repo = remult.repo(Entity)
  return await repo.find({
    where: {
      createdAt: { $gte: startDate, $lte: endDate }
    },
    orderBy: { createdAt: 'desc' }
  })
}
```

### Statistics and Aggregation

```typescript
@BackendMethod({ allowed: Allow.authenticated })
static async getStats(organizationId?: string) {
  const repo = remult.repo(Entity)
  const entities = await repo.find({
    where: organizationId ? { organizationId } : {}
  })

  return {
    totalCount: entities.length,
    activeCount: entities.filter(e => e.isActive).length,
    // ... other stats
  }
}
```

## 🛡️ Permission Checking

### Organization-based Permissions

```typescript
@BackendMethod({ allowed: [RolesType.Admin, RolesType.TeamLocationAdmin] })
static async update(entity: Entity) {
  const repo = remult.repo(Entity)

  // Check permissions
  if (!remult.user?.roles?.includes(RolesType.Admin)) {
    const userOrganizationId = remult.user?.organizationId
    if (userOrganizationId !== entity.organizationId) {
      throw new Error('You can only update entities in your own organization')
    }
  }

  return await repo.save(entity)
}
```

### User-based Permissions

```typescript
@BackendMethod({ allowed: Allow.authenticated })
static async update(entity: Entity) {
  const repo = remult.repo(Entity)

  // Only allow users to update their own entities unless they're admin
  if (remult.user?.id !== entity.userId && !remult.user?.roles?.includes('admin')) {
    throw new Error('You can only update your own entities')
  }

  return await repo.save(entity)
}
```

## 🔄 Bulk Operations

```typescript
@BackendMethod({ allowed: RolesType.Admin })
static async bulkUpdate(entityIds: string[], updateData: Partial<Entity>) {
  const repo = remult.repo(Entity)
  const results = []

  for (const id of entityIds) {
    const item = await repo.findId(id)
    if (item) {
      const updated = await repo.save({
        ...item,
        ...updateData
      })
      results.push(updated)
    }
  }

  return results
}
```

## 🧹 Soft Delete Pattern

```typescript
@BackendMethod({ allowed: RolesType.Admin })
static async softDelete(id: string) {
  const repo = remult.repo(Entity)
  const item = await repo.findId(id)
  if (item) {
    return await repo.save({
      ...item,
      isActive: false,
      deletedAt: new Date()
    })
  }
  throw new Error('Entity not found')
}

@BackendMethod({ allowed: 'admin' })
static async restore(id: string) {
  const repo = remult.repo(Entity)
  const item = await repo.findId(id)
  if (item) {
    return await repo.save({
      ...item,
      isActive: true,
      deletedAt: undefined
    })
  }
  throw new Error('Entity not found')
}
```

## 📊 Frontend Usage Examples

### React Example

```typescript
import { UserController, OrganizationController } from './controllers'

function UserList() {
  const [users, setUsers] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const loadUsers = async () => {
      try {
        const data = await UserController.findAll()
        setUsers(data)
      } catch (error) {
        console.error('Failed to load users:', error)
      } finally {
        setLoading(false)
      }
    }

    loadUsers()
  }, [])

  const createUser = async (userData) => {
    try {
      const newUser = await UserController.insert(userData)
      setUsers((prev) => [...prev, newUser])
    } catch (error) {
      console.error('Failed to create user:', error)
    }
  }

  // ... render logic
}
```

### Svelte Example

```svelte
<script lang="ts">
  import { onMount } from 'svelte'
  import { AppointmentController } from './controllers'

  let appointments = []
  let loading = true

  onMount(async () => {
    try {
      appointments = await AppointmentController.findUpcoming()
    } catch (error) {
      console.error('Failed to load appointments:', error)
    } finally {
      loading = false
    }
  })

  async function updateAppointmentStatus(id: string, status: string) {
    try {
      await AppointmentController.updateStatus(id, status)
      // Refresh appointments
      appointments = await AppointmentController.findUpcoming()
    } catch (error) {
      console.error('Failed to update appointment:', error)
    }
  }
</script>
```

## 🎯 Benefits

1. **🔒 Security**: Built-in authorization with role-based access control
2. **🎯 Type Safety**: Full TypeScript support with proper typing
3. **🔄 Consistency**: Standardized CRUD operations across all entities
4. **🚀 Performance**: Optimized queries with proper relations and indexing
5. **🧹 Clean Code**: Separation of concerns with business logic in controllers
6. **📊 Rich Queries**: Advanced filtering, searching, and aggregation methods
7. **🛡️ Permission Checking**: Granular access control at the method level
8. **🔄 Bulk Operations**: Efficient batch processing capabilities
