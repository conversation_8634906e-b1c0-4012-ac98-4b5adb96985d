/**
 * AppointmentType Controller
 * CRUD operations for appointment types
 */

import { Allow, BackendMethod, remult } from 'remult'
import { AppointmentType } from '../models'
import { RolesType } from '../types'

export class AppointmentTypeController {
  @BackendMethod({ allowed: Allow.authenticated })
  static async findAll() {
    const repo = remult.repo(AppointmentType)
    return await repo.find({
      include: {
        organization: true,
        timezone: true,
        creator: true
      },
      orderBy: { appointmentDetail: 'asc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findById(id: string) {
    const repo = remult.repo(AppointmentType)
    return await repo.findId(id, {
      include: {
        organization: true,
        timezone: true,
        creator: true,
        appointments: true
      }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findByOrganization(organizationId: string) {
    const repo = remult.repo(AppointmentType)
    return await repo.find({
      where: { organizationId, isActive: true },
      include: {
        organization: true,
        timezone: true,
        creator: true
      },
      orderBy: { appointmentDetail: 'asc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findActive() {
    const repo = remult.repo(AppointmentType)
    return await repo.find({
      where: { isActive: true },
      include: {
        organization: true,
        timezone: true,
        creator: true
      },
      orderBy: { appointmentDetail: 'asc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findByDuration(durationInMinutes: number) {
    const repo = remult.repo(AppointmentType)
    return await repo.find({
      where: { durationInMinutes, isActive: true },
      include: {
        organization: true,
        timezone: true,
        creator: true
      },
      orderBy: { appointmentDetail: 'asc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findRequiringPayment() {
    const repo = remult.repo(AppointmentType)
    return await repo.find({
      where: { requiresUpfrontPayment: true, isActive: true },
      include: {
        organization: true,
        timezone: true,
        creator: true
      },
      orderBy: { appointmentDetail: 'asc' }
    })
  }

  @BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin] })
  static async insert(appointmentType: Partial<AppointmentType>) {
    const repo = remult.repo(AppointmentType)

    // Set creator to current user if not specified
    if (!appointmentType.createdBy && remult.user) {
      appointmentType.createdBy = remult.user.id
    }

    return await repo.insert(appointmentType)
  }

  @BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin] })
  static async update(id: string, appointmentType: Partial<AppointmentType>) {
    const repo = remult.repo(AppointmentType)
    return await repo.update(id, appointmentType)
  }

  @BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin] })
  static async delete(appointmentType: AppointmentType) {
    const repo = remult.repo(AppointmentType)
    return await repo.delete(appointmentType)
  }

  @BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin] })
  static async deleteById(id: string) {
    const repo = remult.repo(AppointmentType)
    const item = await repo.findId(id)
    if (item) {
      return await repo.delete(item)
    }
    throw new Error('Appointment type not found')
  }

  @BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin] })
  static async softDelete(id: string) {
    const repo = remult.repo(AppointmentType)
    const item = await repo.findId(id)
    if (item) {
      return await repo.save({
        ...item,
        isActive: false,
        deletedAt: new Date()
      })
    }
    throw new Error('Appointment type not found')
  }

  @BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin] })
  static async restore(id: string) {
    const repo = remult.repo(AppointmentType)
    const item = await repo.findId(id)
    if (item) {
      return await repo.save({
        ...item,
        isActive: true,
        deletedAt: undefined
      })
    }
    throw new Error('Appointment type not found')
  }

  @BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin] })
  static async toggleActive(id: string) {
    const repo = remult.repo(AppointmentType)
    const item = await repo.findId(id)
    if (item) {
      return await repo.save({
        ...item,
        isActive: !item.isActive
      })
    }
    throw new Error('Appointment type not found')
  }

  @BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin] })
  static async updatePaymentSettings(id: string, requiresUpfrontPayment: boolean, upfrontPaymentAmount?: string) {
    const repo = remult.repo(AppointmentType)
    const item = await repo.findId(id)
    if (item) {
      return await repo.save({
        ...item,
        requiresUpfrontPayment,
        upfrontPaymentAmount: upfrontPaymentAmount || item.upfrontPaymentAmount
      })
    }
    throw new Error('Appointment type not found')
  }
}
