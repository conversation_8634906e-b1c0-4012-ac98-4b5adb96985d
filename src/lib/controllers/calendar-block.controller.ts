/**
 * CalendarBlock Controller
 * CRUD operations for calendar blocks
 */

import { Allow, BackendMethod, remult } from 'remult'
import { CalendarBlock } from '../models'
import { RolesType } from '../types'

export class CalendarBlockController {
  @BackendMethod({ allowed: Allow.authenticated })
  static async findAll() {
    const repo = remult.repo(CalendarBlock)
    return await repo.find({
      include: {
        creator: true,
        organization: true,
        team: true
      },
      orderBy: { startDate: 'desc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findById(id: string) {
    const repo = remult.repo(CalendarBlock)
    return await repo.findId(id, {
      include: {
        creator: true,
        organization: true,
        team: true
      }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findByOrganization(organizationId: string) {
    const repo = remult.repo(CalendarBlock)
    return await repo.find({
      where: { organizationId },
      include: {
        creator: true,
        organization: true,
        team: true
      },
      orderBy: { startDate: 'desc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findByTeam(teamId: string) {
    const repo = remult.repo(CalendarBlock)
    return await repo.find({
      where: { teamId },
      include: {
        creator: true,
        organization: true,
        team: true
      },
      orderBy: { startDate: 'desc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findByDateRange(startDate: Date, endDate: Date) {
    const repo = remult.repo(CalendarBlock)
    return await repo.find({
      where: {
        startDate: { $gte: startDate },
        endDate: { $lte: endDate },
        isActive: true
      },
      include: {
        creator: true,
        organization: true,
        team: true
      },
      orderBy: { startDate: 'asc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async insert(calendarBlock: Partial<CalendarBlock>) {
    const repo = remult.repo(CalendarBlock)

    // Set creator to current user if not specified
    if (!calendarBlock.createdBy && remult.user) {
      calendarBlock.createdBy = remult.user.id
    }

    return await repo.insert(calendarBlock)
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async update(id: string, calendarBlock: Partial<CalendarBlock>) {
    const repo = remult.repo(CalendarBlock)
    return await repo.update(id, calendarBlock)
  }

  @BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin] })
  static async delete(calendarBlock: CalendarBlock) {
    const repo = remult.repo(CalendarBlock)
    return await repo.delete(calendarBlock)
  }

  @BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin] })
  static async deleteById(id: string) {
    const repo = remult.repo(CalendarBlock)
    const item = await repo.findId(id)
    if (item) {
      return await repo.delete(item)
    }
    throw new Error('Calendar block not found')
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async softDelete(id: string) {
    const repo = remult.repo(CalendarBlock)
    const item = await repo.findId(id)
    if (item) {
      return await repo.save({
        ...item,
        isActive: false,
        deletedAt: new Date()
      })
    }
    throw new Error('Calendar block not found')
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async restore(id: string) {
    const repo = remult.repo(CalendarBlock)
    const item = await repo.findId(id)
    if (item) {
      return await repo.save({
        ...item,
        isActive: true,
        deletedAt: undefined
      })
    }
    throw new Error('Calendar block not found')
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findActive() {
    const repo = remult.repo(CalendarBlock)
    return await repo.find({
      where: { isActive: true },
      include: {
        creator: true,
        organization: true,
        team: true
      },
      orderBy: { startDate: 'desc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findDeleted() {
    const repo = remult.repo(CalendarBlock)
    return await repo.find({
      where: { isActive: false },
      include: {
        creator: true,
        organization: true,
        team: true
      },
      orderBy: { deletedAt: 'desc' }
    })
  }
}
