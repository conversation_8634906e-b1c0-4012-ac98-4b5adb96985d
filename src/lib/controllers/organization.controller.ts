/**
 * Organization Controller
 * CRUD operations for organizations
 */

import { Allow, BackendMethod, remult } from 'remult'
import { Organization } from '../models'
import { RolesType } from '../types'

export class OrganizationController {
  @BackendMethod({ allowed: Allow.authenticated })
  static async findAll() {
    const repo = remult.repo(Organization)
    return await repo.find({
      include: {
        members: true,
        teams: true
      },
      orderBy: { name: 'asc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findById(id: string) {
    const repo = remult.repo(Organization)
    return await repo.findId(id, {
      include: {
        members: true,
        invitations: true,
        teams: true,
        calendarBlocks: true,
        appointmentTypes: true,
        appointments: true,
        paymentProvider: true,
        workdaySetting: true,
        bankDetail: true,
        memberInvites: true
      }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findBySlug(slug: string) {
    const repo = remult.repo(Organization)
    return await repo.findFirst({
      slug
    }, {
      include: {
        members: true,
        teams: true
      }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findActive() {
    const repo = remult.repo(Organization)
    return await repo.find({
      where: { isActive: true },
      include: {
        members: true,
        teams: true
      },
      orderBy: { name: 'asc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findByPlan(currentPlan: string) {
    const repo = remult.repo(Organization)
    return await repo.find({
      where: { currentPlan },
      include: {
        members: true,
        teams: true
      },
      orderBy: { name: 'asc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findByBusinessEmail(businessEmail: string) {
    const repo = remult.repo(Organization)
    return await repo.findFirst({
      businessEmail
    }, {
      include: {
        members: true,
        teams: true
      }
    })
  }

  @BackendMethod({ allowed: RolesType.Admin })
  static async insert(organization: Partial<Organization>) {
    const repo = remult.repo(Organization)
    return await repo.insert(organization)
  }

  @BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin] })
  static async update(id: string, organization: Partial<Organization>) {
    const repo = remult.repo(Organization)

    // Check if user has permission to update this organization
    if (!remult.user?.roles?.includes(RolesType.Admin)) {
      // For organisation-admin, check if they belong to this organization
      const userOrganizationId = remult.user?.organizationId
      if (userOrganizationId !== organization.id) {
        throw new Error('You can only update your own organization')
      }
    }

    return await repo.update(id, organization)
  }

  @BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin] })
  static async delete(organization: Organization) {
    const repo = remult.repo(Organization)
    return await repo.delete(organization)
  }

  @BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin] })
  static async deleteById(id: string) {
    const repo = remult.repo(Organization)
    const item = await repo.findId(id)
    if (item) {
      return await repo.delete(item)
    }
    throw new Error('Organization not found')
  }

  @BackendMethod({ allowed: RolesType.Admin })
  static async toggleActive(id: string) {
    const repo = remult.repo(Organization)
    const item = await repo.findId(id)
    if (item) {
      return await repo.save({
        ...item,
        isActive: !item.isActive
      })
    }
    throw new Error('Organization not found')
  }

  @BackendMethod({ allowed: RolesType.Admin })
  static async updatePlan(id: string, currentPlan: string) {
    const repo = remult.repo(Organization)
    const item = await repo.findId(id)
    if (item) {
      return await repo.save({
        ...item,
        currentPlan
      })
    }
    throw new Error('Organization not found')
  }

  @BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin] })
  static async updateBusinessInfo(id: string, businessInfo: {
    businessEmail?: string
    phoneNumber?: string
    businessRegNo?: string
    registrationDate?: Date
  }) {
    const repo = remult.repo(Organization)

    // Check permissions
    if (!remult.user?.roles?.includes(RolesType.Admin)) {
      const userOrganizationId = remult.user?.organizationId
      if (userOrganizationId !== id) {
        throw new Error('You can only update your own organization')
      }
    }

    const item = await repo.findId(id)
    if (item) {
      return await repo.save({
        ...item,
        ...businessInfo
      })
    }
    throw new Error('Organization not found')
  }

  @BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin] })
  static async updateLogo(id: string, logo: string) {
    const repo = remult.repo(Organization)

    // Check permissions
    if (!remult.user?.roles?.includes(RolesType.Admin)) {
      const userOrganizationId = remult.user?.organizationId
      if (userOrganizationId !== id) {
        throw new Error('You can only update your own organization')
      }
    }

    const item = await repo.findId(id)
    if (item) {
      return await repo.save({
        ...item,
        logo
      })
    }
    throw new Error('Organization not found')
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async getStats(id: string) {
    const repo = remult.repo(Organization)
    const org = await repo.findId(id, {
      include: {
        members: true,
        teams: true,
        appointments: true,
        appointmentTypes: true
      }
    })

    if (!org) {
      throw new Error('Organization not found')
    }

    return {
      id: org.id,
      name: org.name,
      memberCount: org.members?.length || 0,
      teamCount: org.teams?.length || 0,
      appointmentCount: org.appointments?.length || 0,
      appointmentTypeCount: org.appointmentTypes?.length || 0,
      currentPlan: org.currentPlan,
      isActive: org.isActive,
      createdAt: org.createdAt
    }
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async search(query: string) {
    const repo = remult.repo(Organization)
    return await repo.find({
      where: {
        $or: [
          { name: { $contains: query } },
          { slug: { $contains: query } },
          { businessEmail: { $contains: query } }
        ],
        isActive: true
      },
      include: {
        members: true,
        teams: true
      },
      orderBy: { name: 'asc' }
    })
  }

  @BackendMethod({ allowed: RolesType.Admin })
  static async findInactive() {
    const repo = remult.repo(Organization)
    return await repo.find({
      where: { isActive: false },
      include: {
        members: true,
        teams: true
      },
      orderBy: { updatedAt: 'desc' }
    })
  }

}
