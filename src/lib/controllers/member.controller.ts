/**
 * Member Controller
 * CRUD operations for organization/team members
 */

import { Allow, BackendMethod, remult } from 'remult'
import { Member } from '../models'
import { RolesType } from '../types'

export class MemberController {
  @BackendMethod({ allowed: Allow.authenticated })
  static async findAll() {
    const repo = remult.repo(Member)
    return await repo.find({
      include: {
        user: true,
        organization: true,
        team: true
      },
      orderBy: { createdAt: 'desc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findById(id: string) {
    const repo = remult.repo(Member)
    return await repo.findId(id, {
      include: {
        user: true,
        organization: true,
        team: true
      }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findByOrganization(organizationId: string) {
    const repo = remult.repo(Member)
    return await repo.find({
      where: { organizationId },
      include: {
        user: true,
        organization: true,
        team: true
      },
      orderBy: { createdAt: 'desc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findByTeam(teamId: string) {
    const repo = remult.repo(Member)
    return await repo.find({
      where: { teamId },
      include: {
        user: true,
        organization: true,
        team: true
      },
      orderBy: { createdAt: 'desc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findByUser(userId: string) {
    const repo = remult.repo(Member)
    return await repo.find({
      where: { userId },
      include: {
        user: true,
        organization: true,
        team: true
      },
      orderBy: { createdAt: 'desc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findByRole(role: string) {
    const repo = remult.repo(Member)
    return await repo.find({
      where: { role },
      include: {
        user: true,
        organization: true,
        team: true
      },
      orderBy: { createdAt: 'desc' }
    })
  }

  @BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin] })
  static async insert(member: Partial<Member>) {
    const repo = remult.repo(Member)

    // Check permissions
    if (!remult.user?.roles?.includes(RolesType.Admin)) {
      const userOrganizationId = remult.user?.organizationId
      if (userOrganizationId !== member.organizationId) {
        throw new Error('You can only add members to your own organization')
      }
    }

    return await repo.insert(member)
  }

  @BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin] })
  static async update(id: string, member: Partial<Member>) {
    const repo = remult.repo(Member)

    // Check permissions
    if (!remult.user?.roles?.includes(RolesType.Admin)) {
      const existingMember = await repo.findId(id)
      if (!existingMember) {
        throw new Error('Member not found')
      }

      const userOrganizationId = remult.user?.organizationId
      if (userOrganizationId !== existingMember.organizationId) {
        throw new Error('You can only update members in your own organization')
      }
    }

    return await repo.update(id, member)
  }

  @BackendMethod({ allowed: RolesType.Admin })
  static async delete(member: Member) {
    const repo = remult.repo(Member)
    return await repo.delete(member)
  }

  @BackendMethod({ allowed: RolesType.Admin })
  static async deleteById(id: string) {
    const repo = remult.repo(Member)
    const item = await repo.findId(id)
    if (item) {
      return await repo.delete(item)
    }
    throw new Error('Member not found')
  }

  @BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin] })
  static async updateRole(id: string, role: string) {
    const repo = remult.repo(Member)
    const item = await repo.findId(id)
    if (item) {
      // Check permissions
      if (!remult.user?.roles?.includes(RolesType.Admin)) {
        const userOrganizationId = remult.user?.organizationId
        if (userOrganizationId !== item.organizationId) {
          throw new Error('You can only update members in your own organization')
        }
      }

      return await repo.save({
        ...item,
        role
      })
    }
    throw new Error('Member not found')
  }

  @BackendMethod({ allowed: ['admin', 'organisation-admin'] })
  static async assignToTeam(id: string, teamId: string) {
    const repo = remult.repo(Member)
    const item = await repo.findId(id)
    if (item) {
      // Check permissions
      if (!remult.user?.roles?.includes('admin')) {
        const userOrganizationId = remult.user?.organizationId
        if (userOrganizationId !== item.organizationId) {
          throw new Error('You can only update members in your own organization')
        }
      }

      return await repo.save({
        ...item,
        teamId
      })
    }
    throw new Error('Member not found')
  }

  @BackendMethod({ allowed: ['admin', 'organisation-admin'] })
  static async removeFromTeam(id: string) {
    const repo = remult.repo(Member)
    const item = await repo.findId(id)
    if (item) {
      // Check permissions
      if (!remult.user?.roles?.includes('admin')) {
        const userOrganizationId = remult.user?.organizationId
        if (userOrganizationId !== item.organizationId) {
          throw new Error('You can only update members in your own organization')
        }
      }

      return await repo.save({
        ...item,
        teamId: undefined
      })
    }
    throw new Error('Member not found')
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async getOrganizationStats(organizationId: string) {
    const repo = remult.repo(Member)
    const members = await repo.find({
      where: { organizationId },
      include: {
        user: true,
        team: true
      }
    })

    // Group by role
    const roleStats: { [key: string]: number } = {}
    members.forEach(m => {
      roleStats[m.role] = (roleStats[m.role] || 0) + 1
    })

    // Group by team
    const teamStats: { [key: string]: number } = {}
    members.forEach(m => {
      if (m.teamId) {
        const teamName = m.team?.name || 'Unknown Team'
        teamStats[teamName] = (teamStats[teamName] || 0) + 1
      }
    })

    return {
      totalMembers: members.length,
      roleDistribution: roleStats,
      teamDistribution: teamStats,
      membersWithoutTeam: members.filter(m => !m.teamId).length
    }
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async getTeamStats(teamId: string) {
    const repo = remult.repo(Member)
    const members = await repo.find({
      where: { teamId },
      include: {
        user: true,
        organization: true
      }
    })

    // Group by role
    const roleStats: { [key: string]: number } = {}
    members.forEach(m => {
      roleStats[m.role] = (roleStats[m.role] || 0) + 1
    })

    return {
      totalMembers: members.length,
      roleDistribution: roleStats
    }
  }

  @BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin] })
  static async bulkUpdateRole(memberIds: string[], role: string) {
    const repo = remult.repo(Member)
    const results: Member[] = []

    for (const id of memberIds) {
      const item = await repo.findId(id)
      if (item) {
        // Check permissions for each member
        if (!remult.user?.roles?.includes(RolesType.Admin)) {
          const userOrganizationId = remult.user?.organizationId
          if (userOrganizationId !== item.organizationId) {
            continue // Skip members not in user's organization
          }
        }

        const updated = await repo.save({
          ...item,
          role
        })
        results.push(updated)
      }
    }

    return results
  }

  @BackendMethod({ allowed: ['admin', 'organisation-admin'] })
  static async bulkAssignToTeam(memberIds: string[], teamId: string) {
    const repo = remult.repo(Member)
    const results: Member[] = []

    for (const id of memberIds) {
      const item = await repo.findId(id)
      if (item) {
        // Check permissions for each member
        if (!remult.user?.roles?.includes('admin')) {
          const userOrganizationId = remult.user?.organizationId
          if (userOrganizationId !== item.organizationId) {
            continue // Skip members not in user's organization
          }
        }

        const updated = await repo.save({
          ...item,
          teamId
        })
        results.push(updated)
      }
    }

    return results
  }
}
