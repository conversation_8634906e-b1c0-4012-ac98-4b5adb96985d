/**
 * Team Controller
 * CRUD operations for teams
 */

import { Allow, BackendMethod, remult } from 'remult'
import { Team } from '../models'
import { RolesType } from '../types'

export class TeamController {
  @BackendMethod({ allowed: Allow.authenticated })
  static async findAll() {
    const repo = remult.repo(Team)
    return await repo.find({
      include: {
        organization: true,
        members: true
      },
      orderBy: { name: 'asc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findById(id: string) {
    const repo = remult.repo(Team)
    return await repo.findId(id, {
      include: {
        organization: true,
        members: true,
        calendarBlocks: true,
        appointments: true
      }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findByOrganization(organizationId: string) {
    const repo = remult.repo(Team)
    return await repo.find({
      where: { organizationId, isActive: true },
      include: {
        organization: true,
        members: true
      },
      orderBy: { name: 'asc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findActive() {
    const repo = remult.repo(Team)
    return await repo.find({
      where: { isActive: true },
      include: {
        organization: true,
        members: true
      },
      orderBy: { name: 'asc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findDefault() {
    const repo = remult.repo(Team)
    return await repo.find({
      where: { isDefault: true, isActive: true },
      include: {
        organization: true,
        members: true
      },
      orderBy: { name: 'asc' }
    })
  }

  @BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin] })
  static async insert(team: Partial<Team>) {
    const repo = remult.repo(Team)

    // Check permissions for organization
    if (!remult.user?.roles?.includes(RolesType.Admin)) {
      const userOrganizationId = remult.user?.organizationId
      if (userOrganizationId !== team.organizationId) {
        throw new Error('You can only create teams in your own organization')
      }
    }

    return await repo.insert(team)
  }

  @BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin] })
  static async update(id: string, team: Partial<Team>) {
    const repo = remult.repo(Team)

    // Check permissions
    if (!remult.user?.roles?.includes(RolesType.Admin)) {
      const existingTeam = await repo.findId(id)
      if (!existingTeam) {
        throw new Error('Team not found')
      }

      const userOrganizationId = remult.user?.organizationId
      if (userOrganizationId !== existingTeam.organizationId) {
        throw new Error('You can only update teams in your own organization')
      }
    }

    return await repo.update(id, team)
  }

  @BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin] })
  static async delete(team: Team) {
    const repo = remult.repo(Team)
    return await repo.delete(team)
  }

  @BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin] })
  static async deleteById(id: string) {
    const repo = remult.repo(Team)
    const item = await repo.findId(id)
    if (item) {
      return await repo.delete(item)
    }
    throw new Error('Team not found')
  }

  @BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin] })
  static async toggleActive(id: string) {
    const repo = remult.repo(Team)
    const item = await repo.findId(id)
    if (item) {
      // Check permissions
      if (!remult.user?.roles?.includes(RolesType.Admin)) {
        const userOrganizationId = remult.user?.organizationId
        if (userOrganizationId !== item.organizationId) {
          throw new Error('You can only modify teams in your own organization')
        }
      }

      return await repo.save({
        ...item,
        isActive: !item.isActive
      })
    }
    throw new Error('Team not found')
  }

  @BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin] })
  static async setAsDefault(id: string) {
    const repo = remult.repo(Team)
    const item = await repo.findId(id)
    if (item) {
      // Check permissions
      if (!remult.user?.roles?.includes(RolesType.Admin)) {
        const userOrganizationId = remult.user?.organizationId
        if (userOrganizationId !== item.organizationId) {
          throw new Error('You can only modify teams in your own organization')
        }
      }

      // First, unset all other teams as default in the same organization
      const allTeams = await repo.find({
        where: { organizationId: item.organizationId, isDefault: true }
      })

      for (const team of allTeams) {
        if (team.id !== id) {
          await repo.save({
            ...team,
            isDefault: false
          })
        }
      }

      // Set this team as default
      return await repo.save({
        ...item,
        isDefault: true
      })
    }
    throw new Error('Team not found')
  }

  @BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin] })
  static async updateLocation(id: string, locationInfo: {
    locationAddress?: string
    locationEmail?: string
    locationPhoneNumber?: string
  }) {
    const repo = remult.repo(Team)
    const item = await repo.findId(id)
    if (item) {
      // Check permissions
      if (!remult.user?.roles?.includes('admin')) {
        const userOrganizationId = remult.user?.organizationId
        if (userOrganizationId !== item.organizationId) {
          throw new Error('You can only modify teams in your own organization')
        }
      }

      return await repo.save({
        ...item,
        ...locationInfo
      })
    }
    throw new Error('Team not found')
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async getStats(id: string) {
    const repo = remult.repo(Team)
    const team = await repo.findId(id, {
      include: {
        organization: true,
        members: true,
        appointments: true
      }
    })

    if (!team) {
      throw new Error('Team not found')
    }

    return {
      id: team.id,
      name: team.name,
      organizationName: team.organization?.name,
      memberCount: team.members?.length || 0,
      appointmentCount: team.appointments?.length || 0,
      isDefault: team.isDefault,
      isActive: team.isActive,
      createdAt: team.createdAt,
      locationAddress: team.locationAddress,
      locationEmail: team.locationEmail,
      locationPhoneNumber: team.locationPhoneNumber
    }
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async search(query: string, organizationId?: string) {
    const repo = remult.repo(Team)
    const whereClause: any = {
      name: { $contains: query },
      isActive: true
    }

    if (organizationId) {
      whereClause.organizationId = organizationId
    }

    return await repo.find({
      where: whereClause,
      include: {
        organization: true,
        members: true
      },
      orderBy: { name: 'asc' }
    })
  }

  @BackendMethod({ allowed: RolesType.Admin })
  static async findInactive() {
    const repo = remult.repo(Team)
    return await repo.find({
      where: { isActive: false },
      include: {
        organization: true,
        members: true
      },
      orderBy: { updatedAt: 'desc' }
    })
  }

  @BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin] })
  static async bulkToggleActive(teamIds: string[], isActive: boolean) {
    const repo = remult.repo(Team)
    const results: Team[] = []

    for (const id of teamIds) {
      const item = await repo.findId(id)
      if (item) {
        // Check permissions for each team
        if (!remult.user?.roles?.includes('admin')) {
          const userOrganizationId = remult.user?.organizationId
          if (userOrganizationId !== item.organizationId) {
            continue // Skip teams not in user's organization
          }
        }

        const updated = await repo.save({
          ...item,
          isActive
        })
        results.push(updated)
      }
    }

    return results
  }
}
