/**
 * Appointment Controller
 * CRUD operations for appointments
 */

import { Allow, BackendMethod, remult } from 'remult'
import { Appointment } from '../models'
import { RolesType } from '../types'

export class AppointmentController {
  @BackendMethod({ allowed: Allow.authenticated })
  static async findAll() {
    const repo = remult.repo(Appointment)
    return await repo.find({
      include: {
        organization: true,
        team: true,
        appointmentType: true,
        updater: true,
        attendee: true
      },
      orderBy: { appointmentDate: 'desc', startTime: 'asc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findById(id: string) {
    const repo = remult.repo(Appointment)
    return await repo.findId(id, {
      include: {
        organization: true,
        team: true,
        appointmentType: true,
        updater: true,
        attendee: true
      }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findByOrganization(organizationId: string) {
    const repo = remult.repo(Appointment)
    return await repo.find({
      where: { organizationId },
      include: {
        organization: true,
        team: true,
        appointmentType: true,
        updater: true,
        attendee: true
      },
      orderBy: { appointmentDate: 'desc', startTime: 'asc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findByTeam(teamId: string) {
    const repo = remult.repo(Appointment)
    return await repo.find({
      where: { teamId },
      include: {
        organization: true,
        team: true,
        appointmentType: true,
        updater: true,
        attendee: true
      },
      orderBy: { appointmentDate: 'desc', startTime: 'asc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findByDateRange(startDate: Date, endDate: Date) {
    const repo = remult.repo(Appointment)
    return await repo.find({
      where: {
        appointmentDate: { $gte: startDate, $lte: endDate }
      },
      include: {
        organization: true,
        team: true,
        appointmentType: true,
        updater: true,
        attendee: true
      },
      orderBy: { appointmentDate: 'asc', startTime: 'asc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findByStatus(appointmentStatus: string) {
    const repo = remult.repo(Appointment)
    return await repo.find({
      where: { appointmentStatus },
      include: {
        organization: true,
        team: true,
        appointmentType: true,
        updater: true,
        attendee: true
      },
      orderBy: { appointmentDate: 'desc', startTime: 'asc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findUpcoming() {
    const repo = remult.repo(Appointment)
    const today = new Date()
    return await repo.find({
      where: {
        appointmentDate: { $gte: today },
        appointmentStatus: { $ne: 'cancelled' }
      },
      include: {
        organization: true,
        team: true,
        appointmentType: true,
        updater: true,
        attendee: true
      },
      orderBy: { appointmentDate: 'asc', startTime: 'asc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findByCustomer(customerEmail: string) {
    const repo = remult.repo(Appointment)
    return await repo.find({
      where: { customerEmail },
      include: {
        organization: true,
        team: true,
        appointmentType: true,
        updater: true,
        attendee: true
      },
      orderBy: { appointmentDate: 'desc', startTime: 'asc' }
    })
  }

  @BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin, RolesType.TeamMember] })
  static async insert(appointment: Partial<Appointment>) {
    const repo = remult.repo(Appointment)

    // Set updater to current user if not specified
    if (!appointment.updatedBy && remult.user) {
      appointment.updatedBy = remult.user.id
    }

    return await repo.insert(appointment)
  }

  @BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin, RolesType.TeamMember] })
  static async update(id: string, appointment: Partial<Appointment>) {
    const repo = remult.repo(Appointment)

    // Update the updatedBy field
    if (remult.user) {
      appointment.updatedBy = remult.user.id
    }

    return await repo.update(id, appointment)
  }

  @BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin] })
  static async delete(appointment: Appointment) {
    const repo = remult.repo(Appointment)
    return await repo.delete(appointment)
  }

  @BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin] })
  static async deleteById(id: string) {
    const repo = remult.repo(Appointment)
    const item = await repo.findId(id)
    if (item) {
      return await repo.delete(item)
    }
    throw new Error('Appointment not found')
  }

  @BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin, RolesType.TeamMember] })
  static async updateStatus(id: string, appointmentStatus: string, appointmentNotes?: string) {
    const repo = remult.repo(Appointment)
    const item = await repo.findId(id)
    if (item) {
      return await repo.save({
        ...item,
        appointmentStatus,
        appointmentNotes: appointmentNotes || item.appointmentNotes,
        updatedBy: remult.user?.id || item.updatedBy
      })
    }
    throw new Error('Appointment not found')
  }

  @BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin, RolesType.TeamMember] })
  static async markAttended(id: string, attendedBy?: string) {
    const repo = remult.repo(Appointment)
    const item = await repo.findId(id)
    if (item) {
      return await repo.save({
        ...item,
        appointmentStatus: 'completed',
        attendedBy: attendedBy || remult.user?.id || item.attendedBy,
        updatedBy: remult.user?.id || item.updatedBy
      })
    }
    throw new Error('Appointment not found')
  }

  @BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin, RolesType.TeamMember] })
  static async updateDepositStatus(id: string, depositPaid: boolean) {
    const repo = remult.repo(Appointment)
    const item = await repo.findId(id)
    if (item) {
      return await repo.save({
        ...item,
        depositPaid,
        updatedBy: remult.user?.id || item.updatedBy
      })
    }
    throw new Error('Appointment not found')
  }
}
