/**
 * StorageBucket Controller
 * CRUD operations for storage buckets (Admin only)
 */

import { Allow, BackendMethod, remult } from 'remult'
import { StorageBucket } from '../models'
import { RolesType } from '../types'

export class StorageBucketController {
  @BackendMethod({ allowed: RolesType.Admin })
  static async findAll() {
    const repo = remult.repo(StorageBucket)
    return await repo.find({
      include: {
        files: true
      },
      orderBy: { createdAt: 'desc' }
    })
  }

  @BackendMethod({ allowed: RolesType.Admin })
  static async findById(id: string) {
    const repo = remult.repo(StorageBucket)
    return await repo.findId(id, {
      include: {
        files: true
      }
    })
  }

  @BackendMethod({ allowed: RolesType.Admin })
  static async findActive() {
    const repo = remult.repo(StorageBucket)
    return await repo.find({
      where: { deletedAt: undefined },
      include: {
        files: true
      },
      orderBy: { createdAt: 'desc' }
    })
  }

  @BackendMethod({ allowed: RolesType.Admin })
  static async insert(bucket: Partial<StorageBucket>) {
    const repo = remult.repo(StorageBucket)
    return await repo.insert(bucket)
  }

  @BackendMethod({ allowed: RolesType.Admin })
  static async update(id: string, bucket: Partial<StorageBucket>) {
    const repo = remult.repo(StorageBucket)
    return await repo.update(id, bucket)
  }

  @BackendMethod({ allowed: RolesType.Admin })
  static async delete(bucket: StorageBucket) {
    const repo = remult.repo(StorageBucket)
    return await repo.delete(bucket)
  }

  @BackendMethod({ allowed: RolesType.Admin })
  static async deleteById(id: string) {
    const repo = remult.repo(StorageBucket)
    const item = await repo.findId(id)
    if (item) {
      return await repo.delete(item)
    }
    throw new Error('Storage bucket not found')
  }

  @BackendMethod({ allowed: RolesType.Admin })
  static async softDelete(id: string) {
    const repo = remult.repo(StorageBucket)
    const item = await repo.findId(id)
    if (item) {
      return await repo.save({
        ...item,
        deletedAt: new Date()
      })
    }
    throw new Error('Storage bucket not found')
  }

  @BackendMethod({ allowed: RolesType.Admin })
  static async restore(id: string) {
    const repo = remult.repo(StorageBucket)
    const item = await repo.findId(id)
    if (item) {
      return await repo.save({
        ...item,
        deletedAt: undefined
      })
    }
    throw new Error('Storage bucket not found')
  }

  @BackendMethod({ allowed: RolesType.Admin })
  static async updateSettings(id: string, settings: {
    downloadExpiration?: number
    minUploadFileSize?: number
    maxUploadFileSize?: number
    cacheControl?: string
    presignedUrlsEnabled?: boolean
  }) {
    const repo = remult.repo(StorageBucket)
    const item = await repo.findId(id)
    if (item) {
      return await repo.save({
        ...item,
        ...settings
      })
    }
    throw new Error('Storage bucket not found')
  }

  @BackendMethod({ allowed: RolesType.Admin })
  static async getStats(id: string) {
    const repo = remult.repo(StorageBucket)
    const bucket = await repo.findId(id, {
      include: {
        files: true
      }
    })

    if (!bucket) {
      throw new Error('Storage bucket not found')
    }

    const files = bucket.files || []
    const totalFiles = files.length
    const uploadedFiles = files.filter(f => f.isUploaded).length
    const totalSize = files.reduce((sum, f) => sum + (f.size || 0), 0)

    return {
      id: bucket.id,
      totalFiles,
      uploadedFiles,
      pendingFiles: totalFiles - uploadedFiles,
      totalSize,
      averageFileSize: totalFiles > 0 ? Math.round(totalSize / totalFiles) : 0,
      settings: {
        downloadExpiration: bucket.downloadExpiration,
        minUploadFileSize: bucket.minUploadFileSize,
        maxUploadFileSize: bucket.maxUploadFileSize,
        cacheControl: bucket.cacheControl,
        presignedUrlsEnabled: bucket.presignedUrlsEnabled
      },
      createdAt: bucket.createdAt,
      updatedAt: bucket.updatedAt,
      deletedAt: bucket.deletedAt
    }
  }

  @BackendMethod({ allowed: RolesType.Admin })
  static async findDeleted() {
    const repo = remult.repo(StorageBucket)
    return await repo.find({
      where: { deletedAt: { $ne: undefined } },
      include: {
        files: true
      },
      orderBy: { deletedAt: 'desc' }
    })
  }

  @BackendMethod({ allowed: RolesType.Admin })
  static async bulkUpdateSettings(bucketIds: string[], settings: {
    downloadExpiration?: number
    minUploadFileSize?: number
    maxUploadFileSize?: number
    cacheControl?: string
    presignedUrlsEnabled?: boolean
  }) {
    const repo = remult.repo(StorageBucket)
    const results: StorageBucket[] = []

    for (const id of bucketIds) {
      const item = await repo.findId(id)
      if (item) {
        const updated = await repo.save({
          ...item,
          ...settings
        })
        results.push(updated)
      }
    }

    return results
  }

  @BackendMethod({ allowed: RolesType.Admin })
  static async createDefault() {
    const repo = remult.repo(StorageBucket)
    return await repo.insert({
      downloadExpiration: 30,
      minUploadFileSize: 1,
      maxUploadFileSize: 50000000, // 50MB
      cacheControl: 'max-age=3600',
      presignedUrlsEnabled: true
    })
  }
}
