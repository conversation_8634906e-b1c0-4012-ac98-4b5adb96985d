/**
 * TeamMember Controller
 * CRUD operations for team member relationships
 */

import { Allow, BackendMethod, remult } from 'remult'
import { TeamMember } from '../models'
import { RolesType } from '../types'

export class TeamMemberController {
  @BackendMethod({ allowed: Allow.authenticated })
  static async findAll() {
    const repo = remult.repo(TeamMember)
    return await repo.find({
      include: {
        team: true,
        user: true
      },
      orderBy: { createdAt: 'desc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findById(id: string) {
    const repo = remult.repo(TeamMember)
    return await repo.findId(id, {
      include: {
        team: true,
        user: true
      }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findByTeam(teamId: string) {
    const repo = remult.repo(TeamMember)
    return await repo.find({
      where: { teamId },
      include: {
        team: true,
        user: true
      },
      orderBy: { createdAt: 'desc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findByUser(userId: string) {
    const repo = remult.repo(TeamMember)
    return await repo.find({
      where: { userId },
      include: {
        team: true,
        user: true
      },
      orderBy: { createdAt: 'desc' }
    })
  }

  @BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin] })
  static async insert(teamMember: Partial<TeamMember>) {
    const repo = remult.repo(TeamMember)

    // Check if the relationship already exists
    const existing = await repo.findFirst({
      teamId: teamMember.teamId,
      userId: teamMember.userId,
      deletedAt: undefined // Only check non-deleted records
    })

    if (existing) {
      throw new Error('User is already a member of this team')
    }

    return await repo.insert(teamMember)
  }

  @BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin] })
  static async update(id: string, teamMember: Partial<TeamMember>) {
    const repo = remult.repo(TeamMember)
    return await repo.update(id, teamMember)
  }

  @BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin] })
  static async delete(id: string) {
    const repo = remult.repo(TeamMember)
    const teamMember = await repo.findId(id)
    if (teamMember) {
      return await repo.delete(teamMember)
    }
    throw new Error('TeamMember not found')
  }

  @BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin] })
  static async softDelete(id: string) {
    const repo = remult.repo(TeamMember)
    const teamMember = await repo.findId(id)
    if (teamMember) {
      return await repo.save({
        ...teamMember,
        deletedAt: new Date()
      })
    }
    throw new Error('TeamMember not found')
  }

  @BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin] })
  static async restore(id: string) {
    const repo = remult.repo(TeamMember)
    const teamMember = await repo.findId(id)
    if (teamMember) {
      return await repo.save({
        ...teamMember,
        deletedAt: undefined
      })
    }
    throw new Error('TeamMember not found')
  }

  @BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin] })
  static async addUserToTeam(userId: string, teamId: string) {
    return await this.insert({ userId, teamId })
  }

  @BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin] })
  static async removeUserFromTeam(userId: string, teamId: string) {
    const repo = remult.repo(TeamMember)
    const teamMember = await repo.findFirst({
      userId,
      teamId,
      deletedAt: undefined
    })

    if (teamMember) {
      return await this.softDelete(teamMember.id)
    }
    throw new Error('User is not a member of this team')
  }
}
