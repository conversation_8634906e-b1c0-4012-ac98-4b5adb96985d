/**
 * User Controller
 * CRUD operations for users
 */

import { Allow, BackendMethod, remult } from 'remult'
import { User } from '../models'
import { RolesType } from '../types'
import { orgAdminOnly } from './controller-helpers'

export class UserController {
  @BackendMethod({ allowed: Allow.authenticated })
  static async findAll() {
    const repo = remult.repo(User)
    return await repo.find({
      include: {
        organization: true,
        team: true,
        userRoles: true
      },
      orderBy: { familyName: 'desc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findById(id: string) {
    const repo = remult.repo(User)
    return await repo.findId(id, {
      include: {
        organization: true,
        team: true,
        userRoles: true
      }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findByEmail(email: string) {
    const repo = remult.repo(User)
    return await repo.findFirst({
      email
    }, {
      include: {
        organization: true,
        team: true,
        userRoles: true
      }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findByOrganization(organizationId: string) {
    const repo = remult.repo(User)
    return await repo.find({
      where: { organizationId },
      include: {
        organization: true,
        team: true,
        userRoles: true
      },
      orderBy: { givenName: 'asc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findByTeam(teamId: string) {
    const repo = remult.repo(User)
    return await repo.find({
      where: { teamId },
      include: {
        organization: true,
        team: true,
        userRoles: true
      },
      orderBy: { givenName: 'asc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findByRole(role: string) {
    const repo = remult.repo(User)
    return await repo.find({
      where: { roles: { $contains: role } },
      include: {
        organization: true,
        team: true,
        userRoles: true
      },
      orderBy: { givenName: 'asc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findActive() {
    const repo = remult.repo(User)
    return await repo.find({
      where: {
        disabled: false,
        banned: false,
        userStatus: 'Active'
      },
      include: {
        organization: true,
        team: true,
        userRoles: true
      },
      orderBy: { givenName: 'asc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findAdmins() {
    const repo = remult.repo(User)
    return await repo.find({
      where: { roles: { $contains: RolesType.Admin } },
      include: {
        organization: true,
        team: true,
        userRoles: true
      },
      orderBy: { givenName: 'asc' }
    })
  }

  @BackendMethod({ allowed: RolesType.Admin })
  static async insert(user: Partial<User>) {
    const repo = remult.repo(User)
    return await repo.insert(user)
  }

  @BackendMethod({ allowed: orgAdminOnly })
  static async update(id: string, user: Partial<User>) {
    const repo = remult.repo(User)

    // Only allow users to update their own profile unless they're admin or org admin
    if (remult.user?.id === id) {
      return await repo.update(id, user)
    } else {
      throw new Error('You can only update your own profile')
    }
  }

  @BackendMethod({ allowed: RolesType.Admin })
  static async delete(user: User) {
    const repo = remult.repo(User)
    return await repo.delete(user)
  }

  @BackendMethod({ allowed: RolesType.Admin })
  static async deleteById(id: string) {
    const repo = remult.repo(User)
    const item = await repo.findId(id)
    if (item) {
      return await repo.delete(item)
    }
    throw new Error('User not found')
  }

  @BackendMethod({ allowed: RolesType.Admin })
  static async toggleDisabled(id: string) {
    const repo = remult.repo(User)
    const item = await repo.findId(id)
    if (item) {
      return await repo.save({
        ...item,
        disabled: !item.disabled
      })
    }
    throw new Error('User not found')
  }

  @BackendMethod({ allowed: RolesType.Admin })
  static async banUser(id: string, banReason: string, banExpires?: string) {
    const repo = remult.repo(User)
    const item = await repo.findId(id)
    if (item) {
      return await repo.save({
        ...item,
        banned: true,
        banReason,
        banExpires,
        userStatus: 'Banned'
      })
    }
    throw new Error('User not found')
  }

  @BackendMethod({ allowed: RolesType.Admin })
  static async unbanUser(id: string) {
    const repo = remult.repo(User)
    const item = await repo.findId(id)
    if (item) {
      return await repo.save({
        ...item,
        banned: false,
        banReason: undefined,
        banExpires: undefined,
        userStatus: 'Active'
      })
    }
    throw new Error('User not found')
  }




  @BackendMethod({ allowed: Allow.authenticated })
  static async updateProfile(id: string, profileData: Partial<User>) {
    const repo = remult.repo(User)

    // Only allow users to update their own profile unless they're admin
    if (remult.user?.id !== id && !remult.user?.roles?.includes('admin')) {
      throw new Error('You can only update your own profile')
    }

    const item = await repo.findId(id)
    if (item) {
      // Restrict which fields can be updated by non-admins
      const allowedFields = ['name', 'givenName', 'familyName', 'displayName', 'phoneNumber', 'avatarUrl', 'locale']
      const updateData: Partial<User> = {}

      if (remult.user?.roles?.includes('admin')) {
        // Admins can update any field
        Object.assign(updateData, profileData)
      } else {
        // Regular users can only update allowed fields
        for (const field of allowedFields) {
          if (field in profileData) {
            (updateData as any)[field] = (profileData as any)[field]
          }
        }
      }

      return await repo.update(id, {
        ...item,
        ...updateData
      })
    }
    throw new Error('User not found')
  }
}
