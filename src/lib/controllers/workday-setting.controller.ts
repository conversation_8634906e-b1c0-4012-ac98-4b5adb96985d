/**
 * WorkdaySetting Controller
 * CRUD operations for workday settings
 */

import { Allow, BackendMethod, remult } from 'remult'
import { WorkdaySetting } from '../models'
import { RolesType, WeekDayNumber, type DayOfWeek } from '../types'

export class WorkdaySettingController {
  @BackendMethod({ allowed: Allow.authenticated })
  static async findAll() {
    const repo = remult.repo(WorkdaySetting)
    return await repo.find({
      include: {
        organization: true
      },
      orderBy: { createdAt: 'desc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findById(id: string) {
    const repo = remult.repo(WorkdaySetting)
    return await repo.findId(id, {
      include: {
        organization: true
      }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findByOrganization(organizationId: string) {
    const repo = remult.repo(WorkdaySetting)
    return await repo.find({
      where: { organizationId },
      include: {
        organization: true
      },
      orderBy: { createdAt: 'desc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findByDayOfWeek(dayOfWeek: DayOfWeek) {
    const repo = remult.repo(WorkdaySetting)
    return await repo.find({
      where: { dayOfWeek },
      include: {
        organization: true
      },
      orderBy: { startTime: 'asc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findActive() {
    const repo = remult.repo(WorkdaySetting)
    return await repo.find({
      where: { isActive: true },
      include: {
        organization: true
      },
      orderBy: { dayOfWeek: 'asc', startTime: 'asc' }
    })
  }

  @BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin] })
  static async insert(workdaySetting: Partial<WorkdaySetting>) {
    const repo = remult.repo(WorkdaySetting)

    // Check permissions
    if (!remult.user?.roles?.includes(RolesType.Admin)) {
      const userOrganizationId = remult.user?.organizationId
      if (userOrganizationId !== workdaySetting.organizationId) {
        throw new Error('You can only create workday settings for your own organization')
      }
    }

    return await repo.insert(workdaySetting)
  }

  @BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin] })
  static async update(id: string, workdaySetting: Partial<WorkdaySetting>) {
    const repo = remult.repo(WorkdaySetting)

    // Check permissions
    if (!remult.user?.roles?.includes(RolesType.Admin)) {
      const existingWorkdaySetting = await repo.findId(id)
      if (!existingWorkdaySetting) {
        throw new Error('Workday setting not found')
      }

      const userOrganizationId = remult.user?.organizationId
      if (userOrganizationId !== existingWorkdaySetting.organizationId) {
        throw new Error('You can only update workday settings for your own organization')
      }
    }

    return await repo.update(id, workdaySetting)
  }

  @BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin] })
  static async delete(workdaySetting: WorkdaySetting) {
    const repo = remult.repo(WorkdaySetting)

    // Check permissions
    if (!remult.user?.roles?.includes(RolesType.Admin)) {
      const userOrganizationId = remult.user?.organizationId
      if (userOrganizationId !== workdaySetting.organizationId) {
        throw new Error('You can only delete workday settings for your own organization')
      }
    }

    return await repo.delete(workdaySetting)
  }

  @BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin] })
  static async deleteById(id: string) {
    const repo = remult.repo(WorkdaySetting)
    const item = await repo.findId(id)
    if (item) {
      // Check permissions
      if (!remult.user?.roles?.includes(RolesType.Admin)) {
        const userOrganizationId = remult.user?.organizationId
        if (userOrganizationId !== item.organizationId) {
          throw new Error('You can only delete workday settings for your own organization')
        }
      }
      return await repo.delete(item)
    }
    throw new Error('Workday setting not found')
  }

  @BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin] })
  static async toggleActive(id: string) {
    const repo = remult.repo(WorkdaySetting)
    const item = await repo.findId(id)
    if (item) {
      // Check permissions
      if (!remult.user?.roles?.includes(RolesType.Admin)) {
        const userOrganizationId = remult.user?.organizationId
        if (userOrganizationId !== item.organizationId) {
          throw new Error('You can only modify workday settings for your own organization')
        }
      }

      return await repo.save({
        ...item,
        isActive: !item.isActive
      })
    }
    throw new Error('Workday setting not found')
  }

  @BackendMethod({ allowed: ['admin', 'organisation-admin', 'team-location-admin'] })
  static async updateHours(id: string, startTime: string, endTime: string) {
    const repo = remult.repo(WorkdaySetting)
    const item = await repo.findId(id)
    if (item) {
      // Check permissions
      if (!remult.user?.roles?.includes('admin')) {
        const userOrganizationId = remult.user?.organizationId
        if (userOrganizationId !== item.organizationId) {
          throw new Error('You can only modify workday settings for your own organization')
        }
      }

      return await repo.save({
        ...item,
        startTime,
        endTime
      })
    }
    throw new Error('Workday setting not found')
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async getWeekSchedule(organizationId: string) {
    const repo = remult.repo(WorkdaySetting)
    const settings = await repo.find({
      where: { organizationId, isActive: true },
      orderBy: { dayOfWeek: 'asc' }
    })

    // Create a week schedule array (0 = Sunday, 6 = Saturday)
    const weekSchedule = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'].map((dayName, index) => ({
      dayOfWeek: index,
      dayName: dayName,
      settings: settings.filter(s => s.dayOfWeek === dayName as DayOfWeek)
    }))

    return weekSchedule
  }

  @BackendMethod({ allowed: ['admin', 'organisation-admin', 'team-location-admin'] })
  static async createWeekSchedule(organizationId: string, schedule: {
    dayOfWeek: DayOfWeek
    weekDay: WeekDayNumber
    startTime: string
    endTime: string
    isActive: boolean
  }[]) {
    const repo = remult.repo(WorkdaySetting)

    // Check permissions
    if (!remult.user?.roles?.includes('admin')) {
      const userOrganizationId = remult.user?.organizationId
      if (userOrganizationId !== organizationId) {
        throw new Error('You can only create workday settings for your own organization')
      }
    }

    const results: WorkdaySetting[] = []
    for (const daySchedule of schedule) {
      const created = await repo.insert({
        organizationId,
        ...daySchedule
      })
      results.push(created)
    }

    return results
  }

  @BackendMethod({ allowed: ['admin', 'organisation-admin', 'team-location-admin'] })
  static async updateWeekSchedule(organizationId: string, schedule: {
    dayOfWeek: DayOfWeek
    weekDay: WeekDayNumber
    startTime: string
    endTime: string
    isActive: boolean
  }[]) {
    const repo = remult.repo(WorkdaySetting)

    // Check permissions
    if (!remult.user?.roles?.includes('admin')) {
      const userOrganizationId = remult.user?.organizationId
      if (userOrganizationId !== organizationId) {
        throw new Error('You can only update workday settings for your own organization')
      }
    }

    // Delete existing settings for this organization
    const existingSettings = await repo.find({
      where: { organizationId }
    })

    for (const setting of existingSettings) {
      await repo.delete(setting)
    }

    // Create new settings
    const results: WorkdaySetting[] = []
    for (const daySchedule of schedule) {
      const created = await repo.insert({
        organizationId,
        ...daySchedule
      })
      results.push(created)
    }

    return results
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async getBusinessHours(organizationId: string) {
    const repo = remult.repo(WorkdaySetting)
    const settings = await repo.find({
      where: { organizationId, isActive: true },
      orderBy: { dayOfWeek: 'asc', startTime: 'asc' }
    })

    // Group by day and find earliest start and latest end
    const businessHours: { [key: string]: { start: string, end: string } } = {}

    settings.forEach(setting => {
      if (!businessHours[setting.dayOfWeek]) {
        businessHours[setting.dayOfWeek] = {
          start: setting.startTime,
          end: setting.endTime
        }

        // Update to earliest start time
        if (setting.startTime < businessHours[setting.dayOfWeek].start) {
          businessHours[setting.dayOfWeek].start = setting.startTime
        }
        // Update to latest end time
        if (setting.endTime > businessHours[setting.dayOfWeek].end) {
          businessHours[setting.dayOfWeek].end = setting.endTime
        }
      }
    })

    return businessHours
  }

  @BackendMethod({ allowed: ['admin', 'organisation-admin', 'team-location-admin'] })
  static async copySchedule(fromOrganizationId: string, toOrganizationId: string) {
    const repo = remult.repo(WorkdaySetting)

    // Check permissions for both organizations
    if (!remult.user?.roles?.includes('admin')) {
      const userOrganizationId = remult.user?.organizationId
      if (userOrganizationId !== fromOrganizationId && userOrganizationId !== toOrganizationId) {
        throw new Error('You can only copy schedules for your own organization')
      }
    }

    const sourceSettings = await repo.find({
      where: { organizationId: fromOrganizationId }
    })

    const results: WorkdaySetting[] = []
    for (const setting of sourceSettings) {
      const copied = await repo.insert({
        organizationId: toOrganizationId,
        dayOfWeek: setting.dayOfWeek,
        startTime: setting.startTime,
        endTime: setting.endTime,
        isActive: setting.isActive
      })
      results.push(copied)
    }

    return results
  }
}
