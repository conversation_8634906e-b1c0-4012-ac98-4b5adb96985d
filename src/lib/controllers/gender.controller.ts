/**
 * Gender Controller
 * CRUD operations for gender reference data
 */

import { BackendMethod, remult } from 'remult'
import { Gender } from '../models'
import { type GenderType, RolesType } from '../types'


export class GenderController {
  @BackendMethod({ allowed: true })
  static async findAll() {
    const repo = remult.repo(Gender)
    return await repo.find({
      orderBy: { name: 'asc' }
    })
  }

  @BackendMethod({ allowed: true })
  static async findByName(name: GenderType) {
    const repo = remult.repo(Gender)
    return await repo.findFirst({ name })
  }

  @BackendMethod({ allowed: RolesType.Admin })
  static async insert(gender: Partial<Gender>) {
    const repo = remult.repo(Gender)
    return await repo.insert(gender)
  }

  @BackendMethod({ allowed: RolesType.Admin })
  static async update(id: string, gender: Partial<Gender>) {
    const repo = remult.repo(Gender)
    return await repo.update(id, gender)
  }

  @BackendMethod({ allowed: RolesType.Admin })
  static async delete(gender: Gender) {
    const repo = remult.repo(Gender)
    return await repo.delete(gender)
  }

  @BackendMethod({ allowed: RolesType.Admin })
  static async deleteByName(name: GenderType) {
    const repo = remult.repo(Gender)
    const item = await repo.findFirst({ name })
    if (item) {
      return await repo.delete(item)
    }
    throw new Error('Gender not found')
  }

  @BackendMethod({ allowed: RolesType.Admin })
  static async seed() {
    const repo = remult.repo(Gender)
    const defaultGenders = [
      'Male',
      'Female',
      'Other'
    ]

    const results: Gender[] = []
    for (const genderName of defaultGenders) {
      const existing = await repo.findFirst({ name: genderName as GenderType })
      if (!existing) {
        const created = await repo.insert({ name: genderName as GenderType })
        results.push(created)
      }
    }

    return results
  }
}
