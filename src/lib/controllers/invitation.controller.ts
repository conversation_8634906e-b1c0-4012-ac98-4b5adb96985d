/**
 * Invitation Controller
 * CRUD operations for organization/team invitations
 */

import { Allow, BackendMethod, remult } from 'remult'
import { Invitation } from '../models'

export class InvitationController {
  @BackendMethod({ allowed: Allow.authenticated })
  static async findAll() {
    const repo = remult.repo(Invitation)
    return await repo.find({
      include: {
        inviter: true,
        organization: true,
        team: true
      },
      orderBy: { createdAt: 'desc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findById(id: string) {
    const repo = remult.repo(Invitation)
    return await repo.findId(id, {
      include: {
        inviter: true,
        organization: true,
        team: true
      }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findByOrganization(organizationId: string) {
    const repo = remult.repo(Invitation)
    return await repo.find({
      where: { organizationId },
      include: {
        inviter: true,
        organization: true,
        team: true
      },
      orderBy: { createdAt: 'desc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findByTeam(teamId: string) {
    const repo = remult.repo(Invitation)
    return await repo.find({
      where: { teamId },
      include: {
        inviter: true,
        organization: true,
        team: true
      },
      orderBy: { createdAt: 'desc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findByEmail(email: string) {
    const repo = remult.repo(Invitation)
    return await repo.find({
      where: { email },
      include: {
        inviter: true,
        organization: true,
        team: true
      },
      orderBy: { createdAt: 'desc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findByStatus(status: string) {
    const repo = remult.repo(Invitation)
    return await repo.find({
      where: { status },
      include: {
        inviter: true,
        organization: true,
        team: true
      },
      orderBy: { createdAt: 'desc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findPending() {
    const repo = remult.repo(Invitation)
    const now = new Date()
    return await repo.find({
      where: {
        status: 'pending',
        expiresAt: { $gt: now }
      },
      include: {
        inviter: true,
        organization: true,
        team: true
      },
      orderBy: { createdAt: 'desc' }
    })
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async findExpired() {
    const repo = remult.repo(Invitation)
    const now = new Date()
    return await repo.find({
      where: {
        status: 'pending',
        expiresAt: { $lt: now }
      },
      include: {
        inviter: true,
        organization: true,
        team: true
      },
      orderBy: { expiresAt: 'desc' }
    })
  }

  @BackendMethod({ allowed: ['admin', 'organisation-admin'] })
  static async insert(invitation: Partial<Invitation>) {
    const repo = remult.repo(Invitation)

    // Set inviter to current user if not specified
    if (!invitation.inviterId && remult.user) {
      invitation.inviterId = remult.user.id
    }

    // Set default expiration if not specified (7 days from now)
    if (!invitation.expiresAt) {
      const expiresAt = new Date()
      expiresAt.setDate(expiresAt.getDate() + 7)
      invitation.expiresAt = expiresAt
    }

    // Check permissions
    if (!remult.user?.roles?.includes('admin')) {
      const userOrganizationId = remult.user?.organizationId
      if (userOrganizationId !== invitation.organizationId) {
        throw new Error('You can only create invitations for your own organization')
      }
    }

    return await repo.insert(invitation)
  }

  @BackendMethod({ allowed: ['admin', 'organisation-admin'] })
  static async update(id: string, invitation: Partial<Invitation>) {
    const repo = remult.repo(Invitation)

    // Check permissions
    if (!remult.user?.roles?.includes('admin')) {
      const existingInvitation = await repo.findId(id)
      if (!existingInvitation) {
        throw new Error('Invitation not found')
      }

      const userOrganizationId = remult.user?.organizationId
      if (userOrganizationId !== existingInvitation.organizationId) {
        throw new Error('You can only update invitations for your own organization')
      }
    }

    return await repo.update(id, invitation)
  }

  @BackendMethod({ allowed: ['admin', 'organisation-admin'] })
  static async delete(invitation: Invitation) {
    const repo = remult.repo(Invitation)

    // Check permissions
    if (!remult.user?.roles?.includes('admin')) {
      const userOrganizationId = remult.user?.organizationId
      if (userOrganizationId !== invitation.organizationId) {
        throw new Error('You can only delete invitations for your own organization')
      }
    }

    return await repo.delete(invitation)
  }

  @BackendMethod({ allowed: ['admin', 'organisation-admin'] })
  static async deleteById(id: string) {
    const repo = remult.repo(Invitation)
    const item = await repo.findId(id)
    if (item) {
      // Check permissions
      if (!remult.user?.roles?.includes('admin')) {
        const userOrganizationId = remult.user?.organizationId
        if (userOrganizationId !== item.organizationId) {
          throw new Error('You can only delete invitations for your own organization')
        }
      }
      return await repo.delete(item)
    }
    throw new Error('Invitation not found')
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async accept(id: string) {
    const repo = remult.repo(Invitation)
    const item = await repo.findId(id)
    if (item) {
      // Check if invitation is still valid
      if (item.expiresAt < new Date()) {
        throw new Error('Invitation has expired')
      }

      if (item.status !== 'pending') {
        throw new Error('Invitation is no longer pending')
      }

      return await repo.save({
        ...item,
        status: 'accepted'
      })
    }
    throw new Error('Invitation not found')
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async decline(id: string) {
    const repo = remult.repo(Invitation)
    const item = await repo.findId(id)
    if (item) {
      return await repo.save({
        ...item,
        status: 'declined'
      })
    }
    throw new Error('Invitation not found')
  }

  @BackendMethod({ allowed: ['admin', 'organisation-admin'] })
  static async cancel(id: string) {
    const repo = remult.repo(Invitation)
    const item = await repo.findId(id)
    if (item) {
      // Check permissions
      if (!remult.user?.roles?.includes('admin')) {
        const userOrganizationId = remult.user?.organizationId
        if (userOrganizationId !== item.organizationId) {
          throw new Error('You can only cancel invitations for your own organization')
        }
      }

      return await repo.save({
        ...item,
        status: 'cancelled'
      })
    }
    throw new Error('Invitation not found')
  }

  @BackendMethod({ allowed: ['admin', 'organisation-admin'] })
  static async resend(id: string, newExpirationDays: number = 7) {
    const repo = remult.repo(Invitation)
    const item = await repo.findId(id)
    if (item) {
      // Check permissions
      if (!remult.user?.roles?.includes('admin')) {
        const userOrganizationId = remult.user?.organizationId
        if (userOrganizationId !== item.organizationId) {
          throw new Error('You can only resend invitations for your own organization')
        }
      }

      const newExpiresAt = new Date()
      newExpiresAt.setDate(newExpiresAt.getDate() + newExpirationDays)

      return await repo.save({
        ...item,
        status: 'pending',
        expiresAt: newExpiresAt
      })
    }
    throw new Error('Invitation not found')
  }

  @BackendMethod({ allowed: Allow.authenticated })
  static async getStats(organizationId?: string) {
    const repo = remult.repo(Invitation)
    const whereClause = organizationId ? { organizationId } : {}

    const invitations = await repo.find({
      where: whereClause
    })

    const now = new Date()
    const statusStats: { [key: string]: number } = {}
    let expiredCount = 0

    invitations.forEach(inv => {
      statusStats[inv.status] = (statusStats[inv.status] || 0) + 1
      if (inv.status === 'pending' && inv.expiresAt < now) {
        expiredCount++
      }
    })

    return {
      totalInvitations: invitations.length,
      statusDistribution: statusStats,
      expiredPendingInvitations: expiredCount
    }
  }

  @BackendMethod({ allowed: 'admin' })
  static async cleanupExpired() {
    const repo = remult.repo(Invitation)
    const now = new Date()

    const expiredInvitations = await repo.find({
      where: {
        status: 'pending',
        expiresAt: { $lt: now }
      }
    })

    const results: Invitation[] = []
    for (const invitation of expiredInvitations) {
      const updated = await repo.save({
        ...invitation,
        status: 'expired'
      })
      results.push(updated)
    }

    return results
  }

  @BackendMethod({ allowed: ['admin', 'organisation-admin'] })
  static async bulkCancel(invitationIds: string[]) {
    const repo = remult.repo(Invitation)
    const results: Invitation[] = []

    for (const id of invitationIds) {
      const item = await repo.findId(id)
      if (item) {
        // Check permissions for each invitation
        if (!remult.user?.roles?.includes('admin')) {
          const userOrganizationId = remult.user?.organizationId
          if (userOrganizationId !== item.organizationId) {
            continue // Skip invitations not in user's organization
          }
        }

        const updated = await repo.save({
          ...item,
          status: 'cancelled'
        })
        results.push(updated)
      }
    }

    return results
  }
}
