import { Gift, Store, ShoppingBag, Megaphone, CreditCard, Wallet, Users, Settings2, Calendar } from '@lucide/svelte'
import * as flashModule from 'sveltekit-flash-message/client'

export const getFlashModule = () => {
	return {
		flashMessage: {
			module: flashModule,
			onError: ({ result, flashMessage }: any) => {
				// Error handling for the flash message:
				// - result is the ActionResult
				// - flashMessage is the flash store (not the status message store)
				const errorMessage = result.error.message
				flashMessage.set(errorMessage, 'error')
			}
		},
		syncFlashMessage: false
	}
}

/**
 * Generates a random password with uppercase, lowercase letters and numbers
 * @param length - Password length (minimum 8 characters for better-auth compatibility)
 * @returns Random password string
 */
export const generateRandomPassword = (length: number = 8): string => {
	// Ensure length is at least 8 characters (better-auth requirement)
	const finalLength = Math.max(length, 8)

	// Character sets
	const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
	const lowercase = 'abcdefghijklmnopqrstuvwxyz'
	const numbers = '0123456789'
	const allChars = uppercase + lowercase + numbers

	let password = ''

	// Ensure at least one character from each set
	password += uppercase[Math.floor(Math.random() * uppercase.length)]
	password += lowercase[Math.floor(Math.random() * lowercase.length)]
	password += numbers[Math.floor(Math.random() * numbers.length)]

	// Fill remaining length with random characters
	for (let i = password.length; i < finalLength; i++) {
		password += allChars[Math.floor(Math.random() * allChars.length)]
	}

	// Shuffle the password to randomize character positions
	return password.split('').sort(() => Math.random() - 0.5).join('')
}

export const menuData = {

	navMain: [
		{
			title: 'Rewards',
			url: '/rewards/',
			icon: Gift,
			items: [
				{
					title: 'Overview',
					url: '/rewards/'
				},
				{
					title: 'Programs',
					url: '/rewards/programs/'
				},
				{
					title: 'Analytics',
					url: '/rewards/analytics/'
				}
			]
		},
		{
			title: 'Appointments',
			url: '/appointments/',
			icon: Calendar,
			items: [
				{
					title: 'Bookings',
					url: '/appointments/'
				},
				{
					title: 'Appointment Types',
					url: '/appointments/appointment-templates/'
				},
				{
					title: 'Calendar Blocks',
					url: '/appointments/calendar-blocks/'
				}
			]
		},
		{
			title: 'AI Chat Store',
			url: '/chat-store',
			icon: Store,
			items: [
				{
					title: 'Chat',
					url: '/chat-store/chat/'
				},
				{
					title: 'Products',
					url: '/chat-store/products/'
				}
			]
		},
		{
			title: 'Laybye Management',
			url: '/laybye/',
			icon: ShoppingBag,
			items: [
				{
					title: 'Manage',
					url: '/laybye/'
				},
				{
					title: 'Orders',
					url: '/laybye/orders/'
				},
				{
					title: 'Payments',
					url: '/laybye/payments/'
				}
			]
		},
		{
			title: 'Campaigns',
			url: '/campaigns/',
			icon: Megaphone,
			items: [
				{
					title: 'Campaigns',
					url: '/campaigns/'
				},
				{
					title: 'Drafts',
					url: '/campaigns/drafts/'
				},
				{
					title: 'Analytics',
					url: '/campaigns/analytics/'
				}
			]
		},
		{
			title: 'Subscriptions',
			url: '/subscriptions/',
			icon: CreditCard,
			items: [
				{
					title: 'Plans',
					url: '/subscriptions'
				},
				{
					title: 'Customers',
					url: '/subscriptions/customers/'
				},
				{
					title: 'Billing',
					url: '/subscriptions/billing/'
				}
			]
		},
		{
			title: 'Wallets',
			url: '/wallets/',
			icon: Wallet,
			items: [
				{
					title: 'Overview',
					url: '/wallet/'
				},
				{
					title: 'Transactions',
					url: '/wallets/transactions/'
				},
				{
					title: 'Settings',
					url: '/wallets/settings/'
				}
			]
		},
		{
			title: 'Memberships',
			url: '/memberships/',
			icon: Users,
			items: [
				{
					title: 'Members',
					url: '/memberships/'
				},
				{
					title: 'Tiers',
					url: '/memberships/tiers'
				},
				{
					title: 'Benefits',
					url: '/memberships/benefits'
				}
			]
		},
		{
			title: 'Settings',
			url: '/settings/',
			icon: Settings2,
			items: [
				{
					title: 'General',
					url: '/settings/'
				},
				{
					title: 'Team Members',
					url: '/settings/staff/'
				},
				{
					title: 'Team Invites',
					url: '/settings/staff-invites/'
				},
				{
					title: 'Team Locations',
					url: '/settings/locations/'
				},
				{
					title: 'Payment Engine',
					url: '/settings/payment-engine/'
				}
			]
		}
	]
}
