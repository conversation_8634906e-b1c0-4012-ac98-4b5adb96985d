# Remult Entity Models

This directory contains Remult entity files organized to eliminate circular dependencies and ensure proper initialization order.

## 📁 Consolidated File Structure

Entities are organized in consolidated files to resolve circular dependencies:

### Individual Entity Files

- `gender.ts` - Gender reference data
- `role.ts` - System roles (admin, team-admin, etc.)
- `account.ts` - OAuth/auth provider accounts
- `sessions.ts` - User sessions
- `verification.ts` - Email/phone verification
- `passkey.ts` - WebAuthn passkeys
- `jwks.ts` - JSON Web Key Sets
- `time-zone.ts` - Timezone reference data
- `user-role.ts` - User-role assignments
- `calendar-block.ts` - Calendar scheduling blocks

### Consolidated Entity Files

- `storage.ts` - StorageBucket, StorageFile (storage entities)
- `user-organization.ts` - User, Organization, Team, Member (core entities)
- `appointments.ts` - AppointmentType, Appointment (appointment entities)
- `invitations.ts` - Invitation, MemberInvite (invitation entities)
- `organization-settings.ts` - OrgPaymentProvider, OrgBankDetail, WorkdaySetting (org config entities)

## 🚀 Usage

### Import Individual Entities

```typescript
// From consolidated files
import { User, Organization } from './models/user-organization'
import { AppointmentType, Appointment } from './models/appointments'
import { StorageBucket, StorageFile } from './models/storage'

// From individual files
import { Gender } from './models/gender'
import { Role } from './models/role'
```

### Import Multiple Entities

```typescript
import { User, Organization, Team } from './models'
```

### Import All Entities

```typescript
import { entities } from './models'

// Use in Remult configuration
app.use(
	remultExpress({
		entities
		// ... other config
	})
)
```

### Import Entity Groups

```typescript
import { entityGroups } from './models'

// Use specific groups
app.use(
	remultExpress({
		entities: [...entityGroups.core, ...entityGroups.users]
		// ... other config
	})
)
```

## 🔗 Relations

Each entity file includes its own relations using forward declarations to avoid circular dependencies:

```typescript
// Forward declarations for relations
import type { Organization } from './organization'
import type { Team } from './team'

@Entity('user')
export class User {
	// ... fields

	// Relations
	@Relations.toOne(() => Organization, 'organizationId')
	organization?: Organization

	@Relations.toOne(() => Team, 'teamId')
	team?: Team
}
```

## 🔐 Security Features

### Authorization Levels

- **Public**: `allowApiCrud: true` (Gender, TimeZone, Role)
- **Authenticated**: `allowApiCrud: Allow.authenticated` (User, CalendarBlock, etc.)
- **Admin Only**: `allowApiCrud: 'admin'` (StorageBucket, OrgPaymentProvider, etc.)
- **Role-based**: `allowApiInsert: ['admin', 'team-admin']` (Appointment, MemberInvite, etc.)
- **No API Access**: `allowApiCrud: false` (Account, Sessions, Verification, Jwks)

### Sensitive Data Protection

Fields containing sensitive information are marked with `includeInApi: false`:

- API keys and secrets
- Account numbers
- Passwords and tokens
- Private keys

## ✅ Validation

Each entity includes appropriate validation:

- **Email validation** for email fields
- **String length validation** for names and required fields
- **Custom business logic** validation where needed

Example:

```typescript
@Fields.string({
  validate: (user) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(user.email)) {
      throw 'Please enter a valid email address'
    }
  }
})
email = ''
```

## 🛠️ Development

### Adding New Entities

1. Create a new file: `packages/shared/models/my-entity.ts`
2. Define the entity with appropriate decorators
3. Add forward declarations for relations
4. Export the entity in `index.ts`
5. Add to the appropriate entity group

### Modifying Existing Entities

1. Edit the individual entity file
2. Update relations in related entities if needed
3. Test the changes with your application

### Entity Relationships

- Use `type` imports for forward declarations to avoid circular dependencies
- Define relations using `@Relations.toOne()` and `@Relations.toMany()`
- Specify foreign key fields when needed

## 📋 Migration from Drizzle

These entities maintain full compatibility with your existing Drizzle schema:

- Same table names and column names
- Compatible data types and constraints
- Preserved foreign key relationships
- Maintained validation rules

You can use both Drizzle and Remult entities in the same application during migration.

## 🔄 Entity Groups

The `index.ts` file provides convenient entity groupings:

```typescript
export const entityGroups = {
	core: [CalendarBlock, AppointmentType, Appointment, StorageBucket, StorageFile],
	users: [Gender, Role, UserRole, User],
	organization: [Organization, Team, Member, Invitation],
	scheduling: [TimeZone, WorkdaySetting],
	financial: [OrgPaymentProvider, OrgBankDetail, MemberInvite],
	auth: [Account, Session, Verification, Passkey, Jwks]
}
```

This allows you to register only the entities you need for specific parts of your application.
