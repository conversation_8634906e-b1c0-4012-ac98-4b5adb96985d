import { <PERSON>ti<PERSON>, Fields, Relations, Allow } from 'remult'
import { genderTypes, RolesType, type GenderType } from '$/lib/types'
import { Organization } from './organization.entity'
import { Team } from './team.entity'
import { UserRole } from './user-role.entity'
import { Member } from './member.entity'
import { CalendarBlock } from './calendar-block.entity'
import { AppointmentType } from './appointment-type.entity'
import { Appointment } from './appointment.entity'
import { Role } from './role.entity'

@Entity('users', {
      dbName: 'auth."users"',
      allowApiCrud: Allow.authenticated,
      allowApiInsert: RolesType.Admin,
      allowApiUpdate: Allow.authenticated, // Users can update their own profiles
      allowApiDelete: RolesType.Admin
})
export class User {
      @Fields.uuid()
      id = ''

      @Fields.string({
            validate: (user: User) => {
                  if (!user.name || user.name.length < 2) {
                        throw 'Name must be at least 2 characters long'
                  }
            }
      })
      name = ''

      @Fields.string({ dbName: 'given_name' })
      givenName = ''

      @Fields.string({ dbName: 'family_name' })
      familyName = ''

      @Fields.string({ dbName: 'display_name' })
      displayName = ''

      @Fields.string({
            validate: (user: User) => {
                  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
                  if (!emailRegex.test(user.email)) {
                        throw 'Please enter a valid email address'
                  }
            }
      })
      email = ''

      @Fields.string({ dbName: 'phone_number' })
      phoneNumber?: string

      @Fields.boolean({ dbName: 'email_verified' })
      emailVerified = false

      @Fields.boolean({ dbName: 'phone_number_verified' })
      phoneNumberVerified = false

      @Fields.string()
      image?: string

      @Fields.string({ dbName: 'avatar_url' })
      avatarUrl = ''

      @Fields.string()
      locale = ''

      @Fields.string({ dbName: 'id_document_number' })
      idDocumentNumber?: string

      @Fields.boolean({ dbName: 'accept_terms' })
      acceptTerms?: boolean

      @Fields.string({ dbName: 'last_seen' })
      lastSeen?: string

      @Fields.boolean()
      disabled = false

      @Fields.json()
      roles: string[] = [];

      @Fields.json()
      metadata?: string

      @Fields.literal(() => genderTypes)
      gender: GenderType = 'Unknown'

      @Fields.string({ dbName: 'organization_id' })
      organizationId?: string

      @Fields.string({ dbName: 'team_id' })
      teamId?: string

      @Fields.string({ dbName: 'user_status' })
      userStatus = 'Active'

      @Fields.string({ dbName: 'user_type' })
      userType?: string

      @Fields.boolean()
      banned = false

      @Fields.string({ dbName: 'ban_reason' })
      banReason?: string

      @Fields.string({ dbName: 'ban_expires' })
      banExpires?: string

      // Date fields
      @Fields.createdAt({
            dbName: 'created_at',
            required: true,
            defaultValue: () => new Date(),
            allowApiUpdate: false
      })
      createdAt!: Date

      @Fields.updatedAt({
            dbName: 'updated_at',
            required: true,
            defaultValue: () => new Date(),
            allowApiUpdate: false
      })
      updatedAt!: Date

      @Fields.date({ dbName: 'deleted_at' })
      deletedAt?: Date

      // Relations - using arrow functions to resolve after entities are defined
      @Relations.toOne(() => Organization, 'organizationId')
      organization?: Organization

      @Relations.toOne(() => Team, 'teamId')
      team?: Team

      @Relations.toOne(() => Role, 'role')
      roleEntity?: Role

      @Relations.toMany(() => UserRole)
      userRoles?: UserRole[]

      @Relations.toMany(() => CalendarBlock, 'createdBy')
      calendarBlocks?: CalendarBlock[]

      @Relations.toMany(() => AppointmentType, 'createdBy')
      appointmentTypes?: AppointmentType[]
}

