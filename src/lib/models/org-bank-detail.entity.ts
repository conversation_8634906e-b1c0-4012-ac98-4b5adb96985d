import { <PERSON>ti<PERSON>, Fields, Relations, Allow } from 'remult'
import { BankAccountTypes, bankAccountTypes, RolesType, type BankAccountType } from '../types'
import { Organization } from './organization.entity'

@Entity('org_bank_details', {
      allowApiCrud: Allow.authenticated,
      allowApiInsert: [RolesType.Admin, RolesType.OrgAdmin],
      allowApiUpdate: [RolesType.Admin, RolesType.OrgAdmin],
      allowApiDelete: [RolesType.Admin, RolesType.OrgAdmin]
})
export class OrgBankDetail {
      @Fields.uuid()
      id = ''

      @Fields.string({ dbName: 'organization_id' })
      organizationId = ''

      @Fields.string({ dbName: 'bank_name' })
      bankName = ''

      @Fields.string({ includeInApi: false, dbName: 'account_number' }) // Sensitive data
      accountNumber = ''

      @Fields.string({ dbName: 'created_at' })
      createdAt = ''

      @Fields.string({ dbName: 'updated_at' })
      updatedAt = ''

      @Fields.string({ dbName: 'swift_code' })
      swiftCode?: string

      @Fields.string({ dbName: 'branch_code' })
      branchCode?: string

      @Fields.string({ dbName: 'account_name' })
      accountName?: string

      @Fields.string({ dbName: 'bank_country' })
      bankCountry?: string

      @Fields.string({ dbName: 'routing_number' })
      routingNumber?: string

      @Fields.string()
      iban?: string

      @Fields.string({ dbName: 'bank_address' })
      bankAddress?: string

      @Fields.literal(() => bankAccountTypes, { dbName: 'account_type' })
      accountType?: BankAccountType = BankAccountTypes.Check

      // Relations
      @Relations.toOne(() => Organization, 'organizationId')
      organization?: Organization
}
