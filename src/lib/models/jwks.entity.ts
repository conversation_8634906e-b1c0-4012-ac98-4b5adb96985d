/**
 * Jwks Entity - Authentication
 */

import { <PERSON>ti<PERSON>, Fields } from 'remult'

@Entity('jwks', {
  dbName: 'auth."jwks"',
  allowApiCrud: false // Sensitive auth data
})
export class Jwks {
  @Fields.uuid()
  id = ''

  @Fields.string({ includeInApi: false, dbName: 'public_key' })
  publicKey = ''

  @Fields.string({ includeInApi: false, dbName: 'private_key' })
  privateKey = ''

  // Date fields
  @Fields.createdAt({
    dbName: 'created_at',
    required: true,
    defaultValue: () => new Date(),
    allowApiUpdate: false
  })
  createdAt!: Date

  @Fields.updatedAt({
    dbName: 'updated_at',
    required: true,
    defaultValue: () => new Date(),
    allowApiUpdate: false
  })
  updatedAt!: Date

  @Fields.date({ dbName: 'deleted_at' })
  deletedAt?: Date
}