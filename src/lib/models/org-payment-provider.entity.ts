import { <PERSON><PERSON><PERSON>, Fields, Relations, Allow } from 'remult'
import { RolesType } from '../types'
import { Organization } from './organization.entity'

@Entity('org_payment_providers', {
      allowApiCrud: Allow.authenticated,
      allowApiInsert: [RolesType.Admin, RolesType.OrgAdmin],
      allowApiUpdate: [RolesType.Admin, RolesType.OrgAdmin],
      allowApiDelete: [RolesType.Admin, RolesType.OrgAdmin]
})
export class OrgPaymentProvider {
      @Fields.uuid()
      id = ''

      @Fields.string({ dbName: 'provider_name' })
      providerName = ''

      @Fields.string({ includeInApi: false, dbName: 'provider_api_key' }) // Sensitive data
      providerApiKey = ''

      @Fields.string({ includeInApi: false, dbName: 'provider_api_secret' }) // Sensitive data
      providerApiSecret = ''

      @Fields.string({ dbName: 'organization_id' })
      organizationId = ''

      @Fields.string({ dbName: 'created_by' })
      createdBy = ''

      @Fields.string({ dbName: 'last_updated_by' })
      lastUpdatedBy?: string

      @Fields.string({ dbName: 'provider_webhook_url' })
      providerWebhookUrl?: string

      @Fields.string({ dbName: 'provider_client_id' })
      providerClientId?: string

      @Fields.string({ dbName: 'provider_api_url' })
      providerApiUrl?: string

      @Fields.boolean({ dbName: 'is_active' })
      isActive = true

      @Fields.createdAt({
            dbName: 'created_at',
            required: true,
            defaultValue: () => new Date(),
            allowApiUpdate: false
      })
      createdAt!: Date
      @Fields.updatedAt({
            dbName: 'updated_at',
            required: true,
            defaultValue: () => new Date(),
            allowApiUpdate: false
      })
      updatedAt!: Date
      @Fields.date({ dbName: 'deleted_at' })
      deletedAt?: Date

      // Relations
      @Relations.toOne(() => Organization, 'organizationId')
      organization?: Organization
}