/**
 * Verification Entity - Authentication
 */

import { <PERSON>ti<PERSON>, Fields } from 'remult'
@Entity('verifications', {
  dbName: 'auth."verifications"',
  allowApiCrud: false // Sensitive auth data
})
export class Verification {
  @Fields.uuid()
  id = ''

  @Fields.string()
  identifier = ''

  @Fields.string()
  value = ''

  @Fields.date({ dbName: 'expires_at' })
  expiresAt!: Date

  // Date fields
  @Fields.createdAt({
    dbName: 'created_at',
    required: true,
    defaultValue: () => new Date(),
    allowApiUpdate: false
  })
  createdAt!: Date

  @Fields.updatedAt({
    dbName: 'updated_at',
    required: true,
    defaultValue: () => new Date(),
    allowApiUpdate: false
  })
  updatedAt!: Date

  @Fields.date({ dbName: 'deleted_at' })
  deletedAt?: Date

}