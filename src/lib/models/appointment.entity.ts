import { <PERSON><PERSON><PERSON>, <PERSON>, Relations, Allow } from 'remult'
import { RolesType } from '$/lib/types'
import { AppointmentType } from './appointment-type.entity'
import { User } from './user.entity'
import { Organization } from './organization.entity'
import { Team } from './team.entity'
import { Member } from './member.entity'


@Entity('appointments', {
      allowApiCrud: Allow.authenticated,
      allowApiInsert: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin, RolesType.TeamMember],
      allowApiUpdate: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin, RolesType.TeamMember],
      allowApiDelete: [RolesType.Admin, RolesType.OrgAdmin]
})
export class Appointment {
      @Fields.uuid()
      id = ''

      @Fields.string({
            dbName: 'customer_name',
            validate: (appointment: any) => {
                  if (!appointment.customerName || appointment.customerName?.length < 2) {
                        throw 'Customer name must be at least 2 characters long'
                  }
            }
      })
      customerName = ''

      @Fields.string({ dbName: 'customer_email' })
      customerEmail?: string

      @Fields.string({ dbName: 'customer_phone_number' })
      customerPhoneNumber?: string

      @Fields.dateOnly({ dbName: 'appointment_date' })
      appointmentDate!: Date

      @Fields.string({ dbName: 'start_time' })
      startTime = ''

      @Fields.string({ dbName: 'end_time' })
      endTime = ''

      @Fields.boolean({ dbName: 'deposit_required' })
      depositRequired = false

      @Fields.boolean({ dbName: 'deposit_paid' })
      depositPaid = false

      @Fields.string({ dbName: 'appointment_status' })
      appointmentStatus = ''

      @Fields.string({ dbName: 'organization_id' })
      organizationId = ''

      @Fields.string({ dbName: 'team_id' })
      teamId = ''

      @Fields.string({ dbName: 'appointment_type_id' })
      appointmentTypeId = ''

      @Fields.string({ dbName: 'updated_by' })
      updatedBy?: string

      @Fields.string({ dbName: 'attended_by' })
      attendedBy?: string

      @Fields.string({ dbName: 'appointment_notes' })
      appointmentNotes?: string

      // Date fields
      @Fields.createdAt({
            dbName: 'created_at',
            required: true,
            defaultValue: () => new Date(),
            allowApiUpdate: false
      })
      createdAt!: Date

      @Fields.updatedAt({
            dbName: 'updated_at',
            required: true,
            defaultValue: () => new Date(),
            allowApiUpdate: false
      })
      updatedAt!: Date

      @Fields.date({ dbName: 'deleted_at' })
      deletedAt?: Date

      // Relations - AppointmentType is already defined above
      @Relations.toOne(() => Organization, 'organizationId')
      organization?: Organization

      @Relations.toOne(() => Team, 'teamId')
      team?: Team

      @Relations.toOne(() => AppointmentType, 'appointmentTypeId')
      appointmentType?: AppointmentType

      @Relations.toOne(() => User, 'updatedBy')
      updater?: User

      @Relations.toOne(() => User, 'attendedBy')
      attendee?: User
}
