/**
 * TimeZone Entity
 */

import { <PERSON>ti<PERSON>, <PERSON>, Relations } from 'remult'

import { RolesType } from '../types'
import { AppointmentType } from './appointment-type.entity'


@Entity('time_zones', {
  allowApiCrud: RolesType.Admin,
  allowApiRead: true
})
export class TimeZone {
  @Fields.uuid()
  id = ''

  @Fields.string({ dbName: 'time_zone_name' })
  timeZoneName = ''

  @Fields.number({ dbName: 'offset_hours' })
  offsetHours = 0

  // Relations
  @Relations.toMany(() => AppointmentType)
  appointmentTypes?: AppointmentType[]

  @Fields.createdAt({
    dbName: 'created_at',
    required: true,
    defaultValue: () => new Date(),
    allowApiUpdate: false
  })
  createdAt!: Date
  @Fields.updatedAt({
    dbName: 'updated_at',
    required: true,
    defaultValue: () => new Date(),
    allowApiUpdate: false
  })
  updatedAt!: Date
  @Fields.date({ dbName: 'deleted_at' })
  deletedAt?: Date
}
