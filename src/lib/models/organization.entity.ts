import { Enti<PERSON>, Fields, Relations, Allow } from 'remult'
import { RolesType } from '../types'
import { Team } from './team.entity'
import { Member } from './member.entity'
import { User } from './user.entity'
import { CalendarBlock } from './calendar-block.entity'
import { AppointmentType } from './appointment-type.entity'
import { Appointment } from './appointment.entity'
import { WorkdaySetting } from './workday-setting.entity'
import { OrgBankDetail } from './org-bank-detail.entity'
import { OrgPaymentProvider } from './org-payment-provider.entity'
import { MemberInvite } from './member-invite.entity'
import { getPubChannel, isOrgAdmin, PermissionActions } from '../utils'
import { Invitation } from './invitation.entity'

@Entity<Organization>('organizations', {
      dbName: 'auth."organizations"',
      allowApiCrud: [RolesType.Admin],
      allowApiInsert: org => isOrgAdmin(org?.id),
      allowApiUpdate: org => isOrgAdmin(org?.id),
      allowApiDelete: org => isOrgAdmin(org?.id),
      saved: (org, { isNew, id }) => {
            if (isNew) {
                  const pub = getPubChannel<Organization>(id.toString(), PermissionActions.CREATE)
                  pub.publish(org)
            }
      }
})
export class Organization {
      @Fields.uuid()
      id = ''

      @Fields.string({
            validate: (org: Organization) => {
                  if (!org.name || org.name.length < 2) {
                        throw 'Organization name must be at least 2 characters long'
                  }
            }
      })
      name = ''

      @Fields.string({
            validate: (org: Organization) => {
                  if (!org.slug || org.slug.length < 2) {
                        throw 'Organization slug must be at least 2 characters long'
                  }
            }
      })
      slug = ''

      @Fields.string()
      logo?: string

      @Fields.string()
      metadata?: string

      @Fields.string({ dbName: 'current_plan' })
      currentPlan = 'Free'

      @Fields.string({
            dbName: 'business_email',
            validate: (org: Organization) => {
                  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
                  if (!emailRegex.test(org.businessEmail)) {
                        throw 'Please enter a valid business email address'
                  }
            }
      })
      businessEmail = ''

      @Fields.string({ dbName: 'phone_number' })
      phoneNumber?: string

      @Fields.string({ dbName: 'business_reg_no' })
      businessRegNo?: string

      @Fields.dateOnly({ dbName: 'registration_date' })
      registrationDate?: Date

      @Fields.boolean({ dbName: 'is_active' })
      isActive = true

      // Date fields
      @Fields.createdAt({
            dbName: 'created_at',
            required: true,
            defaultValue: () => new Date(),
            allowApiUpdate: false
      })
      createdAt!: Date

      @Fields.updatedAt({
            dbName: 'updated_at',
            required: true,
            defaultValue: () => new Date(),
            allowApiUpdate: false
      })
      updatedAt!: Date

      @Fields.date({ dbName: 'deleted_at' })
      deletedAt?: Date

      // Relations - using arrow functions to resolve after entities are defined
      @Relations.toMany(() => Member)
      members?: Member[]

      @Relations.toMany(() => Team)
      teams?: Team[]

      // Forward declarations for entities in other files
      @Relations.toMany(() => Invitation)
      invitations?: Invitation[]

      @Relations.toMany(() => CalendarBlock)
      calendarBlocks?: CalendarBlock[]

      @Relations.toMany(() => AppointmentType)
      appointmentTypes?: AppointmentType[]

      @Relations.toMany(() => Appointment)
      appointments?: Appointment[]

      @Relations.toOne(() => OrgPaymentProvider)
      paymentProvider?: OrgPaymentProvider

      @Relations.toOne(() => WorkdaySetting)
      workdaySetting?: WorkdaySetting

      @Relations.toOne(() => OrgBankDetail)
      bankDetail?: OrgBankDetail

      @Relations.toMany(() => MemberInvite)
      memberInvites?: MemberInvite[]
}
