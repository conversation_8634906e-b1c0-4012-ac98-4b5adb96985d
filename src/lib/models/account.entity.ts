/**
 * Account Entity - Authentication
 */

import { Enti<PERSON>, Fields } from 'remult'

@Entity('accounts', {
  dbName: 'auth."accounts"',
  allowApiCrud: false // Sensitive auth data
})
export class Account {
  @Fields.uuid()
  id = ''

  @Fields.string({ dbName: 'account_id' })
  accountId = ''

  @Fields.string({ dbName: 'provider_id' })
  providerId = ''

  @Fields.string({ dbName: 'user_id' })
  userId = ''

  @Fields.string({ includeInApi: false, dbName: 'access_token' })
  accessToken?: string

  @Fields.string({ includeInApi: false, dbName: 'refresh_token' })
  refreshToken?: string

  @Fields.string({ includeInApi: false, dbName: 'id_token' })
  idToken?: string

  @Fields.date({ dbName: 'access_token_expires_at' })
  accessTokenExpiresAt?: Date

  @Fields.date({ dbName: 'refresh_token_expires_at' })
  refreshTokenExpiresAt?: Date

  @Fields.string()
  scope?: string

  @Fields.string({ includeInApi: false })
  password?: string

  // Date fields
  @Fields.createdAt({
    dbName: 'created_at',
    required: true,
    defaultValue: () => new Date(),
    allowApiUpdate: false
  })
  createdAt!: Date

  @Fields.updatedAt({
    dbName: 'updated_at',
    required: true,
    defaultValue: () => new Date(),
    allowApiUpdate: false
  })
  updatedAt!: Date

  @Fields.date({ dbName: 'deleted_at' })
  deletedAt?: Date
}
