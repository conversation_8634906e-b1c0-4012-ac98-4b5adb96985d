
import { Entity, Fields, Validators } from 'remult'

@Entity('sessions', {
  dbName: 'auth."sessions"',
  allowApiCrud: false // Sensitive auth data
})
export class Session {
  @Fields.uuid()
  id = ''

  @Fields.date({ dbName: 'expires_at' })
  expiresAt!: Date

  @Fields.string({ dbName: 'ip_address' })
  ipAddress?: string

  @Fields.string({ dbName: 'user_agent' })
  userAgent?: string

  @Fields.string({ required: true, validate: Validators.unique() })
  token = ''

  @Fields.string({ dbName: 'user_id' })
  userId = ''

  @Fields.string({ dbName: 'active_organization_id' })
  activeOrganizationId?: string

  @Fields.string({ dbName: 'active_team_id' })
  activeTeamId?: string

  @Fields.string({ dbName: 'impersonated_by' })
  impersonatedBy?: string

  // Date fields
  @Fields.createdAt({
    dbName: 'created_at',
    required: true,
    defaultValue: () => new Date(),
    allowApiUpdate: false
  })
  createdAt!: Date

  @Fields.updatedAt({
    dbName: 'updated_at',
    required: true,
    defaultValue: () => new Date(),
    allowApiUpdate: false
  })
  updatedAt!: Date

  @Fields.date({ dbName: 'deleted_at' })
  deletedAt?: Date
}
