/**
 * Role Entity
 */

import { <PERSON><PERSON><PERSON>, <PERSON> } from 'remult'
import { RolesType, rolesTypes, type RoleType } from '../types'
@Entity('roles', {
  allowApiCrud: RolesType.Admin,
  allowApiRead: true
})
export class Role {
  @Fields.literal(() => rolesTypes)
  role: RoleType = RolesType.User

  @Fields.createdAt({
    dbName: 'created_at',
    required: true,
    defaultValue: () => new Date(),
    allowApiUpdate: false
  })
  createdAt!: Date
  @Fields.updatedAt({
    dbName: 'updated_at',
    required: true,
    defaultValue: () => new Date(),
    allowApiUpdate: false
  })
  updatedAt!: Date
  @Fields.date({ dbName: 'deleted_at' })
  deletedAt?: Date
}
