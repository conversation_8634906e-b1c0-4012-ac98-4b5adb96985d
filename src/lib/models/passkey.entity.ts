/**
 * Passkey Entity - Authentication
 */

import { <PERSON><PERSON><PERSON>, Fields, Allow } from 'remult'


@Entity('passkeys', {
  dbName: 'auth."passkeys"',
  allowApiCrud: Allow.authenticated
})
export class Passkey {
  @Fields.uuid()
  id = ''

  @Fields.string()
  name?: string

  @Fields.string({ dbName: 'public_key' })
  publicKey = ''

  @Fields.string({ dbName: 'user_id' })
  userId = ''

  @Fields.string({ dbName: 'credential_id' })
  credentialID = ''

  @Fields.integer()
  counter = 0

  @Fields.string({ dbName: 'device_type' })
  deviceType = ''

  @Fields.boolean({ dbName: 'backed_up' })
  backedUp = false

  @Fields.string()
  transports?: string

  @Fields.string()
  aaguid?: string

  // Date fields
  @Fields.createdAt({
    dbName: 'created_at',
    required: true,
    defaultValue: () => new Date(),
    allowApiUpdate: false
  })
  createdAt!: Date

  @Fields.updatedAt({
    dbName: 'updated_at',
    required: true,
    defaultValue: () => new Date(),
    allowApiUpdate: false
  })
  updatedAt!: Date

  @Fields.date({ dbName: 'deleted_at' })
  deletedAt?: Date
}
