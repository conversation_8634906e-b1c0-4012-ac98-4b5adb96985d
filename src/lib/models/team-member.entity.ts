import { <PERSON><PERSON><PERSON>, <PERSON>, Relations, Allow } from 'remult'
import { RolesType } from '../types'

@Entity('teamMembers', {
      dbName: 'auth."team_members"',
      allowApiCrud: Allow.authenticated,
      allowApiInsert: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin],
      allowApiUpdate: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin],
      allowApiDelete: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin]
})
export class TeamMember {
      @Fields.uuid()
      id = ''

      @Fields.string({ dbName: 'team_id' })
      teamId = ''

      @Fields.string({ dbName: 'user_id' })
      userId = ''

      @Fields.createdAt({
            dbName: 'created_at',
            required: true,
            defaultValue: () => new Date(),
            allowApiUpdate: false
      })
      createdAt!: Date

      @Fields.updatedAt({
            dbName: 'updated_at',
            required: true,
            defaultValue: () => new Date(),
            allowApiUpdate: false
      })
      updatedAt!: Date

      @Fields.date({ dbName: 'deleted_at' })
      deletedAt?: Date

      // Relations
      @Relations.toOne(() => Team, 'teamId')
      team?: Team

      @Relations.toOne(() => User, 'userId')
      user?: User
}

// Import statements for relations
import { Team } from './team.entity'
import { User } from './user.entity'