import { <PERSON><PERSON><PERSON>, Fields, Relations, Allow } from 'remult'
import { RolesType } from '../types'
import { Role } from './role.entity'
import { Gender } from './gender.entity'
import { User } from './user.entity'
import { Team } from './team.entity'
import { Organization } from './organization.entity'

@Entity('member_invites', {
      dbName: 'auth."member_invites"',
      allowApiCrud: Allow.authenticated,
      allowApiInsert: [RolesType.Admin, RolesType.OrgAdmin],
      allowApiUpdate: [RolesType.Admin, RolesType.OrgAdmin],
      allowApiDelete: [RolesType.Admin, RolesType.OrgAdmin]
})
export class MemberInvite {
      @Fields.uuid()
      id = ''

      @Fields.string({ dbName: 'invited_by' })
      invitedBy = ''

      @Fields.string({
            dbName: 'invitee_email',
            validate: (invite: MemberInvite) => {
                  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
                  if (!emailRegex.test(invite.inviteeEmail)) {
                        throw 'Please enter a valid email address'
                  }
            }
      })
      inviteeEmail = ''

      @Fields.string({ dbName: 'organization_id' })
      organizationId = ''

      @Fields.string({ dbName: 'team_id' })
      teamId = ''

      @Fields.boolean({ dbName: 'is_pending' })
      isPending = true

      @Fields.string({ dbName: 'given_name' })
      givenName = ''

      @Fields.string({ dbName: 'family_name' })
      familyName = ''

      @Fields.string()
      gender?: string

      @Fields.string()
      role = ''

      // Date fields
      @Fields.createdAt({
            dbName: 'created_at',
            required: true,
            defaultValue: () => new Date(),
            allowApiUpdate: false
      })
      createdAt!: Date

      @Fields.updatedAt({
            dbName: 'updated_at',
            required: true,
            defaultValue: () => new Date(),
            allowApiUpdate: false
      })
      updatedAt!: Date

      @Fields.date({ dbName: 'deleted_at' })
      deletedAt?: Date

      // Relations
      @Relations.toOne(() => Organization, 'organizationId')
      organization?: Organization

      @Relations.toOne(() => Team, 'teamId')
      team?: Team

      @Relations.toOne(() => User, 'invitedBy')
      inviter?: User

      @Relations.toOne(() => Gender, 'gender')
      genderEntity?: Gender

      @Relations.toOne(() => Role, 'role')
      roleEntity?: Role
}
