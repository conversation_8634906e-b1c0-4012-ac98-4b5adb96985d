
// ==================== ENTITY COLLECTIONS ====================

// Core business entities
export const coreBusinessEntities = [
  'CalendarBlock',
  'AppointmentType',
  'Appointment',
  'StorageBucket',
  'StorageFile'
] as const

// User management entities
export const userManagementEntities = [
  'Gender',
  'Role',
  'UserRole',
  'User'
] as const

// Organization entities
export const organizationEntities = [
  'Organization',
  'Team',
  'Member',
  'TeamMember',
  'Invitation'
] as const

// Time and scheduling entities
export const timeSchedulingEntities = [
  'TimeZone',
  'WorkdaySetting'
] as const

// Financial entities
export const financialEntities = [
  'OrgPaymentProvider',
  'OrgBankDetail',
  'MemberInvite'
] as const

// Authentication entities
export const authenticationEntities = [
  'Account',
  'Session',
  'Verification',
  'Passkey',
  'Jwks'
] as const

// ==================== ALL ENTITIES ARRAY ====================

// Import all entities from consolidated files to prevent circular dependencies
// Level 1: Base entities with no dependencies
import { Gender } from './gender.entity'
import { Role } from './role.entity'
import { Account } from './account.entity'
import { Session } from './session.entity'
import { Verification } from './verification.entity'
import { Passkey } from './passkey.entity'
import { Jwks } from './jwks.entity'

// Level 2: Independent entities with minimal dependencies
import { TimeZone } from './time-zone.entity'

// Level 3: Individual entity imports (in dependency order)
import { StorageBucket } from './storage-bucket.entity'
import { StorageFile } from './storage-file.entity'
import { User } from './user.entity'
import { Organization } from './organization.entity'
import { Team } from './team.entity'
import { Member } from './member.entity'
import { TeamMember } from './team-member.entity'
import { UserRole } from './user-role.entity'
import { CalendarBlock } from './calendar-block.entity'
import { AppointmentType } from './appointment-type.entity'
import { Appointment } from './appointment.entity'
import { MemberInvite } from './member-invite.entity'
import { OrgPaymentProvider } from './org-payment-provider.entity'
import { OrgBankDetail } from './org-bank-detail.entity'
import { WorkdaySetting } from './workday-setting.entity'
import { Invitation } from './invitation.entity'

/**
 * Array of all Remult entities for easy registration in dependency order
 * Use this in your Remult configuration to prevent circular dependency issues
 */
export const entities = [
  // Level 1: Base entities with no dependencies
  Gender,
  Role,
  Account,
  Session,
  Verification,
  Passkey,
  Jwks,

  // Level 2: Entities with minimal dependencies
  TimeZone,
  StorageBucket,

  // Level 3: Entities depending on Level 1-2
  StorageFile,

  // Level 4: Core entities
  User,
  UserRole,

  // Level 5: Organization entities
  Organization,
  Team,

  // Level 6: Membership entities
  Member,
  TeamMember,
  Invitation,

  // Level 7: Business logic entities
  CalendarBlock,
  AppointmentType,

  // Level 8: Complex entities
  Appointment,
  MemberInvite,
  OrgPaymentProvider,
  OrgBankDetail,
  WorkdaySetting
]

/**
 * Authentication-related entities in dependency order
 * Use this for auth-specific Remult configurations
 */
export const authEntities = {
  // Base auth entities
  Account,
  Session,
  Verification,
  Passkey,
  Jwks,

  // Core entities needed for auth
  User,
  Organization,
  Team,
  Member,
  MemberInvite,
  Invitation,
  TeamMember
}
// ==================== ENTITY GROUPS ====================

/**
 * Entities grouped by business domain for selective registration
 * Each group maintains dependency order within the group
 */
export const entityGroups = {
  // Base entities (no dependencies) - safe to load first
  base: [
    Gender,
    Role,
    TimeZone,
    StorageBucket
  ],

  // Storage entities
  storage: [
    StorageBucket,
    StorageFile
  ],

  // User management entities (in dependency order)
  users: [
    Gender,
    Role,
    User,
    UserRole
  ],

  // Organization entities (in dependency order)
  organization: [
    Organization,
    Team,
    Member,
    Invitation
  ],

  // Core business entities (in dependency order)
  core: [
    CalendarBlock,
    AppointmentType,
    Appointment
  ],

  // Scheduling entities (in dependency order)
  scheduling: [
    TimeZone,
    WorkdaySetting
  ],

  // Financial entities (in dependency order)
  financial: [
    OrgPaymentProvider,
    OrgBankDetail,
    MemberInvite
  ],

  // Authentication entities (in dependency order)
  auth: [
    Account,
    Session,
    Verification,
    Passkey,
    Jwks,
    User,
    Organization,
    Team,
    Member,
    MemberInvite,
    Invitation,
    TeamMember
  ]
}

// ==================== UTILITY TYPES ====================

/**
 * Union type of all entity names
 */
export type EntityName =
  | typeof coreBusinessEntities[number]
  | typeof userManagementEntities[number]
  | typeof organizationEntities[number]
  | typeof timeSchedulingEntities[number]
  | typeof financialEntities[number]
  | typeof authenticationEntities[number]

/**
 * Entity group names
 */
export type EntityGroup = keyof typeof entityGroups
