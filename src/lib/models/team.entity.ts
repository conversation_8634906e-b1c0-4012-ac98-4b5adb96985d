import { <PERSON><PERSON><PERSON>, <PERSON>, Relations, Allow } from 'remult'
import { RolesType } from '../types'
import { Organization } from './organization.entity'
import { Member } from './member.entity'
import { User } from './user.entity'
import { CalendarBlock } from './calendar-block.entity'
import { AppointmentType } from './appointment-type.entity'
import { Appointment } from './appointment.entity'


@Entity('teams', {
      dbName: 'auth."teams"',
      allowApiCrud: Allow.authenticated,
      allowApiInsert: [RolesType.Admin, RolesType.OrgAdmin],
      allowApiUpdate: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin],
      allowApiDelete: [RolesType.Admin, RolesType.OrgAdmin],
      saving: (team: Team) => {
            if (team.name === 'HQ') {
                  team.isDefault = true
            }
      }
})
export class Team {
      @Fields.uuid()
      id = ''

      @Fields.string({
            validate: (team: Team) => {
                  if (!team.name || team.name.length < 2) {
                        throw 'Team name must be at least 2 characters long'
                  }
            }
      })
      name = ''

      @Fields.string({ dbName: 'organization_id' })
      organizationId = ''

      @Fields.boolean({ dbName: 'is_default' })
      isDefault = false

      @Fields.string({ dbName: 'location_address' })
      locationAddress?: string

      @Fields.string({ dbName: 'location_email' })
      locationEmail?: string

      @Fields.string({ dbName: 'location_phone_number' })
      locationPhoneNumber?: string

      @Fields.boolean({ dbName: 'is_active' })
      isActive = true

      @Fields.createdAt({
            dbName: 'created_at',
            required: true,
            defaultValue: () => new Date(),
            allowApiUpdate: false
      })
      createdAt!: Date
      @Fields.updatedAt({
            dbName: 'updated_at',
            required: true,
            defaultValue: () => new Date(),
            allowApiUpdate: false
      })
      updatedAt!: Date
      @Fields.date({ dbName: 'deleted_at' })
      deletedAt?: Date

      // Relations - Organization is already defined above
      @Relations.toOne(() => Organization, 'organizationId')
      organization?: Organization

      @Relations.toMany(() => Member)
      members?: Member[]

      // Forward declarations for entities in other files
      @Relations.toMany(() => CalendarBlock)
      calendarBlocks?: CalendarBlock[]

      @Relations.toMany(() => Appointment)
      appointments?: Appointment[]
}

