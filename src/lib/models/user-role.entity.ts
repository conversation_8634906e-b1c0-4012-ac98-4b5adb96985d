import { Enti<PERSON>, <PERSON>, Relations } from 'remult'
import { RolesType } from '../types'
import { User } from './user.entity'
import { Role } from './role.entity'

@Entity('user_roles', {
      allowApiCrud: RolesType.Admin
})
export class UserRole {
      @Fields.uuid()
      id = ''

      @Fields.string({ dbName: 'user_id' })
      userId = ''

      @Fields.string()
      role = ''

      // Date fields
      @Fields.createdAt({
            dbName: 'created_at',
            required: true,
            defaultValue: () => new Date(),
            allowApiUpdate: false
      })
      createdAt!: Date

      @Fields.updatedAt({
            dbName: 'updated_at',
            required: true,
            defaultValue: () => new Date(),
            allowApiUpdate: false
      })
      updatedAt!: Date

      @Fields.date({ dbName: 'deleted_at' })
      deletedAt?: Date

      // Relations
      @Relations.toOne(() => User, 'userId')
      user?: User

      @Relations.toOne(() => Role, 'role')
      roleEntity?: Role
}