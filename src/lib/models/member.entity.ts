import { <PERSON>ti<PERSON>, Fields, Relations, Allow } from 'remult'
import { RolesType } from '../types'
import { User } from './user.entity'
import { Organization } from './organization.entity'
import { Team } from './team.entity'


@Entity('members', {
      dbName: 'auth."members"',
      allowApiCrud: Allow.authenticated,
      allowApiInsert: [RolesType.Admin, RolesType.OrgAdmin],
      allowApiUpdate: [RolesType.Admin, RolesType.OrgAdmin],
      allowApiDelete: [RolesType.Admin, RolesType.OrgAdmin]
})
export class Member {
      @Fields.uuid()
      id = ''

      @Fields.string({ dbName: 'user_id' })
      userId = ''

      @Fields.string({ dbName: 'organization_id' })
      organizationId = ''

      @Fields.string()
      role = ''

      @Fields.string({ dbName: 'team_id' })
      teamId?: string

      // Date fields
      @Fields.createdAt({
            dbName: 'created_at',
            required: true,
            defaultValue: () => new Date(),
            allowApiUpdate: false
      })
      createdAt!: Date

      @Fields.updatedAt({
            dbName: 'updated_at',
            required: true,
            defaultValue: () => new Date(),
            allowApiUpdate: false
      })
      updatedAt!: Date

      @Fields.date({ dbName: 'deleted_at' })
      deletedAt?: Date

      // Relations - All entities are already defined above
      @Relations.toOne(() => User, 'userId')
      user?: User

      @Relations.toOne(() => Organization, 'organizationId')
      organization?: Organization

      @Relations.toOne(() => Team, 'teamId')
      team?: Team
}
