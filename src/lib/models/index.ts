// ==================== CONSOLIDATED ENTITY EXPORTS ====================
// Entities are exported from consolidated files to eliminate circular dependencies
// and ensure proper initialization order for Remult.js

// Level 1: Base entities with no dependencies
export * from './gender.entity'
export * from './role.entity'
export * from './account.entity'
export * from './session.entity'
export * from './verification.entity'
export * from './passkey.entity'
export * from './jwks.entity'

// Level 2: Independent entities with minimal dependencies
export * from './time-zone.entity'

// Level 3: Individual entity files (in dependency order)
export * from './storage-bucket.entity'      // StorageBucket
export * from './storage-file.entity'        // StorageFile
export * from './user.entity'                // User
export * from './organization.entity'        // Organization
export * from './team.entity'                // Team
export * from './member.entity'              // Member
export * from './team-member.entity'         // TeamMember (depends on User and Team)
export * from './user-role.entity'           // UserRole (depends on User)
export * from './calendar-block.entity'      // CalendarBlock (depends on user-organization entities)
export * from './appointment-type.entity'    // AppointmentType
export * from './appointment.entity'         // Appointment
export * from './invitation.entity'          // Invitation
export * from './member-invite.entity'       // MemberInvite
export * from './org-payment-provider.entity' // OrgPaymentProvider
export * from './org-bank-detail.entity'     // OrgBankDetail
export * from './workday-setting.entity'     // WorkdaySetting

// Level 4: Entity collections and utilities
export * from './mod'
