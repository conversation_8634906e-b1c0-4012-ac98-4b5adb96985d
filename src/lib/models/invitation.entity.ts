import { <PERSON><PERSON><PERSON>, <PERSON>, Relations, Allow } from 'remult'
import { RolesType } from '$/lib/types'
import { Team } from './team.entity'
import { Organization } from './organization.entity'
import { User } from './user.entity'

@Entity('invitations', {
      dbName: 'auth."invitations"',
      allowApiCrud: Allow.authenticated,
      allowApiInsert: [RolesType.Admin, RolesType.OrgAdmin],
      allowApiUpdate: [RolesType.Admin, RolesType.OrgAdmin],
      allowApiDelete: [RolesType.Admin, RolesType.OrgAdmin]
})
export class Invitation {
      @Fields.uuid()
      id = ''

      @Fields.string({
            validate: (invitation: Invitation) => {
                  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
                  if (!emailRegex.test(invitation.email)) {
                        throw 'Please enter a valid email address'
                  }
            }
      })
      email = ''

      @Fields.string({ dbName: 'inviter_id' })
      inviterId = ''

      @Fields.string({ dbName: 'organization_id' })
      organizationId = ''

      @Fields.string()
      role = ''

      @Fields.string()
      status = 'pending'

      @Fields.date({ dbName: 'expires_at' })
      expiresAt!: Date

      @Fields.string({ dbName: 'team_id' })
      teamId?: string

      // Date fields
      @Fields.createdAt({
            dbName: 'created_at',
            required: true,
            defaultValue: () => new Date(),
            allowApiUpdate: false
      })
      createdAt!: Date

      @Fields.updatedAt({
            dbName: 'updated_at',
            required: true,
            defaultValue: () => new Date(),
            allowApiUpdate: false
      })
      updatedAt!: Date

      @Fields.date({ dbName: 'deleted_at' })
      deletedAt?: Date

      // Relations
      @Relations.toOne(() => User, 'inviterId')
      inviter?: User

      @Relations.toOne(() => Organization, 'organizationId')
      organization?: Organization

      @Relations.toOne(() => Team, 'teamId')
      team?: Team
}
