import { <PERSON><PERSON><PERSON>, <PERSON>, Relations, Allow } from 'remult'
import { daysOfTheWeek, DaysOfWeek, RolesType, WeekDayNumber, type DayOfWeek } from '$/lib/types'
import { Organization } from './organization.entity'

@Entity('workday_settings', {
      allowApiCrud: Allow.authenticated,
      allowApiInsert: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin],
      allowApiUpdate: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin],
      allowApiDelete: [RolesType.Admin, RolesType.OrgAdmin]
})
export class WorkdaySetting {
      @Fields.uuid()
      id = ''

      @Fields.literal(() => daysOfTheWeek, { dbName: 'day_of_week' })
      dayOfWeek: DayOfWeek = DaysOfWeek.Monday

      @Fields.number({ dbName: 'week_day' })
      weekDay: WeekDayNumber = WeekDayNumber.Monday

      @Fields.string({ dbName: 'start_time' })
      startTime = ''

      @Fields.string({ dbName: 'end_time' })
      endTime = ''

      @Fields.boolean({ dbName: 'is_day_active' })
      isDayActive = true

      @Fields.string({ dbName: 'created_at' })
      createdAt = ''

      @Fields.string({ dbName: 'updated_at' })
      updatedAt = ''

      @Fields.string({ dbName: 'organization_id' })
      organizationId = ''

      @Fields.string({ dbName: 'timezone_id' })
      timezoneId = ''

      @Fields.boolean({ dbName: 'is_active' })
      isActive = true

      @Fields.string({ dbName: 'last_updated_by' })
      lastUpdatedBy?: string

      @Fields.date({ dbName: 'deleted_at' })
      deletedAt?: Date

      // Relations
      @Relations.toOne(() => Organization, 'organizationId')
      organization?: Organization
}
