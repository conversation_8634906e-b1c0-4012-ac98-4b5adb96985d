import { <PERSON><PERSON><PERSON>, <PERSON>, Relations, Allow } from 'remult'
import { RolesType } from '../types'
import { Organization } from './organization.entity'
import { Team } from './team.entity'
import { User } from './user.entity'

@Entity('calendar_blocks', {
      allowApiCrud: Allow.authenticated,
      allowApiInsert: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin],
      allowApiUpdate: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin],
      allowApiDelete: [RolesType.Admin, RolesType.OrgAdmin]
})
export class CalendarBlock {
      @Fields.uuid()
      id = ''

      @Fields.dateOnly({ dbName: 'start_date' })
      startDate!: Date

      @Fields.dateOnly({ dbName: 'end_date' })
      endDate!: Date

      @Fields.string({ dbName: 'start_time' })
      startTime = ''

      @Fields.string({ dbName: 'end_time' })
      endTime = ''

      @Fields.string({ dbName: 'organization_id' })
      organizationId = ''

      @Fields.string({ dbName: 'team_id' })
      teamId = ''

      @Fields.string({ dbName: 'created_by' })
      createdBy = ''

      @Fields.boolean({ dbName: 'is_active' })
      isActive = true

      // Date fields
      @Fields.createdAt({
            dbName: 'created_at',
            required: true,
            defaultValue: () => new Date(),
            allowApiUpdate: false
      })
      createdAt!: Date

      @Fields.updatedAt({
            dbName: 'updated_at',
            required: true,
            defaultValue: () => new Date(),
            allowApiUpdate: false
      })
      updatedAt!: Date

      @Fields.date({ dbName: 'deleted_at' })
      deletedAt?: Date

      // Relations
      @Relations.toOne(() => User, 'createdBy')
      creator?: User

      @Relations.toOne(() => Organization, 'organizationId')
      organization?: Organization

      @Relations.toOne(() => Team, 'teamId')
      team?: Team
}
