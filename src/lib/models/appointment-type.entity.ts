import { <PERSON><PERSON><PERSON>, <PERSON>, Relations, Allow } from 'remult'
import { RolesType } from '$/lib/types'
import { Organization } from './organization.entity'
import { Appointment } from './appointment.entity'
import { User } from './user.entity'
import { TimeZone } from './time-zone.entity'

@Entity('appointment_types', {
      allowApiCrud: Allow.authenticated,
      allowApiInsert: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin],
      allowApiUpdate: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin],
      allowApiDelete: [RolesType.Admin, RolesType.OrgAdmin]
})
export class AppointmentType {
      @Fields.uuid()
      id = ''

      @Fields.string({ dbName: 'appointment_detail' })
      appointmentDetail = ''

      @Fields.boolean({ dbName: 'is_active' })
      isActive = true

      @Fields.boolean({ dbName: 'requires_upfront_payment' })
      requiresUpfrontPayment = false

      @Fields.boolean({ dbName: 'is_repeatable' })
      isRepeatable = false

      @Fields.integer({ dbName: 'duration_in_minutes' })
      durationInMinutes = 30

      @Fields.string({ dbName: 'upfront_payment_amount' })
      upfrontPaymentAmount = '0'

      @Fields.string({ dbName: 'repeat_type' })
      repeatType?: string

      @Fields.string({ dbName: 'organization_id' })
      organizationId = ''

      @Fields.string({ dbName: 'created_by' })
      createdBy = ''

      @Fields.string({ dbName: 'timezone_id' })
      timezoneId = ''

      // Date fields
      @Fields.createdAt({
            dbName: 'created_at',
            required: true,
            defaultValue: () => new Date(),
            allowApiUpdate: false
      })
      createdAt!: Date

      @Fields.updatedAt({
            dbName: 'updated_at',
            required: true,
            defaultValue: () => new Date(),
            allowApiUpdate: false
      })
      updatedAt!: Date

      @Fields.date({ dbName: 'deleted_at' })
      deletedAt?: Date

      // Relations
      @Relations.toOne(() => Organization, 'organizationId')
      organization?: Organization

      @Relations.toOne(() => TimeZone, 'timezoneId')
      timezone?: TimeZone

      @Relations.toOne(() => User, 'createdBy')
      creator?: User

      @Relations.toMany(() => Appointment)
      appointments?: Appointment[]
}

