import { Entity, Fields, Relations, Allow } from 'remult'
import { RolesType } from '../types'
import { StorageFile } from './storage-file.entity'


@Entity('storage_buckets', {
      allowApiCrud: RolesType.Admin
})
export class StorageBucket {
      @Fields.uuid()
      id = ''

      @Fields.integer({ dbName: 'download_expiration' })
      downloadExpiration = 30

      @Fields.integer({ dbName: 'min_upload_file_size' })
      minUploadFileSize = 1

      @Fields.integer({ dbName: 'max_upload_file_size' })
      maxUploadFileSize = 50000000

      @Fields.string({ dbName: 'cache_control' })
      cacheControl = 'max-age=3600'

      @Fields.boolean({ dbName: 'presigned_urls_enabled' })
      presignedUrlsEnabled = true

      // Date fields
      @Fields.createdAt({
            dbName: 'created_at',
            required: true,
            defaultValue: () => new Date(),
            allowApiUpdate: false
      })
      createdAt!: Date

      @Fields.updatedAt({
            dbName: 'updated_at',
            required: true,
            defaultValue: () => new Date(),
            allowApiUpdate: false
      })
      updatedAt!: Date

      @Fields.date({ dbName: 'deleted_at' })
      deletedAt?: Date

      // Relations - using arrow function to resolve after StorageFile is defined
      @Relations.toMany(() => StorageFile)
      files?: StorageFile[]
}
