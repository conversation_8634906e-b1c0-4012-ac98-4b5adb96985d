import { Entity, Fields, Relations, Allow } from 'remult'
import { StorageBucket } from './storage-bucket.entity'


@Entity('storage_files', {
      allowApiCrud: Allow.authenticated
})
export class StorageFile {
      @Fields.uuid()
      id = ''

      @Fields.string({ dbName: 'bucket_id' })
      bucketId = ''

      @Fields.string()
      name?: string

      @Fields.integer()
      size?: number

      @Fields.string({ dbName: 'mime_type' })
      mimeType?: string

      @Fields.string()
      etag?: string

      @Fields.boolean({ dbName: 'is_uploaded' })
      isUploaded = false

      @Fields.string({ dbName: 'uploaded_by_user_id' })
      uploadedByUserId?: string

      @Fields.json()
      metadata?: any

      // Date fields
      @Fields.createdAt({
            dbName: 'created_at',
            required: true,
            defaultValue: () => new Date(),
            allowApiUpdate: false
      })
      createdAt!: Date

      @Fields.updatedAt({
            dbName: 'updated_at',
            required: true,
            defaultValue: () => new Date(),
            allowApiUpdate: false
      })
      updatedAt!: Date

      @Fields.date({ dbName: 'deleted_at' })
      deletedAt?: Date

      // Relations - StorageBucket is already defined above
      @Relations.toOne(() => StorageBucket, 'bucketId')
      bucket?: StorageBucket
}
