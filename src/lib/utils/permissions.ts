/**
 * Custom roles and permissions for better-auth organization plugin
 * This file defines the access control system for organizations
 */

import { createAccessControl } from "better-auth/plugins/access"
import { RolesType } from '../types'
import 'linq-extensions'

// Define all possible permission actions as constants to avoid spelling mistakes
export const PermissionActions = {
  CREATE: "create",
  READ: "read",
  UPDATE: "update",
  DELETE: "delete",
  MANAGE: "manage",
  CANCEL: "cancel",
  JOIN: "join",
  LEAVE: "leave",
  EXPORT: "export"
} as const

// Type for permission actions
export type PermissionAction = typeof PermissionActions[keyof typeof PermissionActions]

// Define custom resources and actions
export const statement = {
  // Default organization permissions
  organization: [PermissionActions.CREATE, PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.DELETE],
  member: [PermissionActions.CREATE, PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.DELETE],
  invitation: [PermissionActions.CREATE, PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.DELETE, PermissionActions.CANCEL],
  // Custom business resources
  appointment: [PermissionActions.CREATE, PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.DELETE, PermissionActions.MANAGE],
  appointmentType: [PermissionActions.CREATE, PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.DELETE, PermissionActions.MANAGE],
  calendarBlock: [PermissionActions.CREATE, PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.DELETE, PermissionActions.MANAGE],
  teamPayment: [PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.MANAGE],
  orgBankDetail: [PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.MANAGE],
  workdaySetting: [PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.MANAGE],
  // Team-specific permissions
  team: [PermissionActions.CREATE, PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.DELETE, PermissionActions.MANAGE, PermissionActions.JOIN, PermissionActions.LEAVE],
  // Analytics and reporting
  analytics: [PermissionActions.READ, PermissionActions.EXPORT],
  reports: [PermissionActions.READ, PermissionActions.CREATE, PermissionActions.EXPORT],
  // Settings and configuration
  settings: [PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.MANAGE]
} as const

// Create access controller
export const ac = createAccessControl(statement)

// Define custom roles with specific permissions

/**
 * Admin role - System-wide administrator (super admin)
 */
export const admin = ac.newRole({
  // Full organization permissions (admin can update but not delete organizations)
  organization: [PermissionActions.UPDATE],
  member: [PermissionActions.CREATE, PermissionActions.UPDATE, PermissionActions.DELETE],
  invitation: [PermissionActions.CREATE, PermissionActions.CANCEL],

  // Full team permissions
  team: [PermissionActions.CREATE, PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.DELETE, PermissionActions.MANAGE],

  // Full business permissions
  appointment: [PermissionActions.CREATE, PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.DELETE, PermissionActions.MANAGE],
  appointmentType: [PermissionActions.CREATE, PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.DELETE, PermissionActions.MANAGE],
  calendarBlock: [PermissionActions.CREATE, PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.DELETE, PermissionActions.MANAGE],
  teamPayment: [PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.MANAGE],
  orgBankDetail: [PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.MANAGE],
  workdaySetting: [PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.MANAGE],

  // Full analytics and reporting
  analytics: [PermissionActions.READ, PermissionActions.EXPORT],
  reports: [PermissionActions.READ, PermissionActions.CREATE, PermissionActions.EXPORT],

  // Full settings access
  settings: [PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.MANAGE]
})

/**
 * Organisation-admin role - Organization owner/administrator
 * Default role for users creating organizations
 */
export const organisationAdmin = ac.newRole({
  // Organization owner permissions (can update and delete organization)
  organization: [PermissionActions.UPDATE, PermissionActions.DELETE],
  member: [PermissionActions.CREATE, PermissionActions.UPDATE, PermissionActions.DELETE],
  invitation: [PermissionActions.CREATE, PermissionActions.CANCEL],

  // Full team permissions within organization
  team: [PermissionActions.CREATE, PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.DELETE, PermissionActions.MANAGE],

  // Full business permissions within organization
  appointment: [PermissionActions.CREATE, PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.DELETE, PermissionActions.MANAGE],
  appointmentType: [PermissionActions.CREATE, PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.DELETE, PermissionActions.MANAGE],
  calendarBlock: [PermissionActions.CREATE, PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.DELETE, PermissionActions.MANAGE],
  teamPayment: [PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.MANAGE],
  orgBankDetail: [PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.MANAGE],
  workdaySetting: [PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.MANAGE],

  // Full analytics and reporting
  analytics: [PermissionActions.READ, PermissionActions.EXPORT],
  reports: [PermissionActions.READ, PermissionActions.CREATE, PermissionActions.EXPORT],

  // Organization settings access
  settings: [PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.MANAGE]
})

/**
 * Team-admin role - Administrator for a specific team within organization
 */
export const teamAdmin = ac.newRole({
  // Limited organization permissions - can invite to their team
  // member: [PermissionActions.READ, PermissionActions.UPDATE], // Can invite and update members for their team
  // invitation: [PermissionActions.CREATE, PermissionActions.CANCEL], // Can manage invitations for their team

  // Team permissions - limited to their team
  team: [PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.MANAGE], // Cannot create/delete teams, only manage assigned team

  // Business permissions - team-level management
  appointment: [PermissionActions.CREATE, PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.DELETE],
  appointmentType: [PermissionActions.CREATE, PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.DELETE],
  calendarBlock: [PermissionActions.CREATE, PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.DELETE],
  teamPayment: [PermissionActions.READ, PermissionActions.UPDATE], // Limited financial access
  workdaySetting: [PermissionActions.READ, PermissionActions.UPDATE],

  // Team-level analytics and reporting
  analytics: [PermissionActions.READ, PermissionActions.EXPORT],
  reports: [PermissionActions.READ, PermissionActions.CREATE],

  // Limited settings access
  settings: [PermissionActions.READ]
})
const userRole = ac.newRole({
  appointment: [PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.CREATE]
})
/**
 * Member role - Basic team member access
 */
export const memberRole = ac.newRole({
  // No organization management permissions

  // Basic team permissions
  team: [PermissionActions.READ, PermissionActions.JOIN, PermissionActions.LEAVE],

  // Limited business permissions - can manage own work
  appointment: [PermissionActions.CREATE, PermissionActions.READ, PermissionActions.UPDATE], // Can manage their own appointments
  appointmentType: [PermissionActions.READ], // Can view appointment types
  calendarBlock: [PermissionActions.READ], // Can view calendar

  // No financial access
  // No administrative settings access

  // Basic analytics - own data only
  analytics: [PermissionActions.READ]
})

// Export all roles for use in auth configuration
export const roles = {
  [RolesType.Admin]: admin,
  [RolesType.OrgAdmin]: organisationAdmin,
  [RolesType.TeamLocationAdmin]: teamAdmin,
  [RolesType.TeamMember]: memberRole,
  [RolesType.User]: userRole
}

// Helper functions for permission checking

/**
 * Check if a user has permission for a specific action on a resource
 */
export const hasPermission = (
  userRole: string | string[],
  resource: keyof typeof statement,
  action: PermissionAction
): boolean => {
  const userRoles = Array.isArray(userRole) ? userRole : [userRole]

  for (const role of userRoles) {
    const roleDefinition = roles[role as keyof typeof roles]
    if (roleDefinition && resource in roleDefinition.statements) {
      const resourcePermissions = (roleDefinition.statements as any)[resource]
      if (resourcePermissions?.includes(action as any)) {
        return true
      }
    }
  }

  return false
}

/**
 * Get all permissions for a user's roles
 */
export const getUserPermissions = (userRole: string | string[]) => {
  const userRoles = Array.isArray(userRole) ? userRole : [userRole]
  const permissions: Record<string, string[]> = {}

  for (const role of userRoles) {
    const roleDefinition = roles[role as keyof typeof roles]
    if (roleDefinition) {
      Object.entries(roleDefinition.statements).forEach(([resource, actions]) => {
        if (!permissions[resource]) {
          permissions[resource] = []
        }
        permissions[resource] = [...new Set([...permissions[resource], ...actions as any])]
      })
    }
  }

  return permissions
}

/**
 * Check if a role can perform any action on a resource
 */
export const canAccessResource = (
  userRole: string | string[],
  resource: keyof typeof statement
): boolean => {
  const userRoles = Array.isArray(userRole) ? userRole : [userRole]

  for (const role of userRoles) {
    const roleDefinition = roles[role as keyof typeof roles]
    if (roleDefinition && resource in roleDefinition.statements) {
      return true
    }
  }

  return false
}

// Permission action specific functions - one for each PermissionAction
/**
 * Check if a user can create a resource
 */
export const canCreate = (
  userRole: string | string[],
  resource: keyof typeof statement
): boolean => {
  return hasPermission(userRole, resource, PermissionActions.CREATE)
}

/**
 * Check if a user can read a resource
 */
export const canRead = (
  userRole: string | string[],
  resource: keyof typeof statement
): boolean => {
  return hasPermission(userRole, resource, PermissionActions.READ)
}

/**
 * Check if a user can update a resource
 */
export const canUpdate = (
  userRole: string | string[],
  resource: keyof typeof statement
): boolean => {
  return hasPermission(userRole, resource, PermissionActions.UPDATE)
}

/**
 * Check if a user can delete a resource
 */
export const canDelete = (
  userRole: string | string[],
  resource: keyof typeof statement
): boolean => {
  return hasPermission(userRole, resource, PermissionActions.DELETE)
}

/**
 * Check if a user can manage a resource
 */
export const canManage = (
  userRole: string | string[],
  resource: keyof typeof statement
): boolean => {
  return hasPermission(userRole, resource, PermissionActions.MANAGE)
}

/**
 * Check if a user can cancel a resource
 */
export const canCancel = (
  userRole: string | string[],
  resource: keyof typeof statement
): boolean => {
  return hasPermission(userRole, resource, PermissionActions.CANCEL)
}

/**
 * Check if a user can join a resource
 */
export const canJoin = (
  userRole: string | string[],
  resource: keyof typeof statement
): boolean => {
  return hasPermission(userRole, resource, PermissionActions.JOIN)
}

/**
 * Check if a user can leave a resource
 */
export const canLeave = (
  userRole: string | string[],
  resource: keyof typeof statement
): boolean => {
  return hasPermission(userRole, resource, PermissionActions.LEAVE)
}

/**
 * Check if a user can export a resource
 */
export const canExport = (
  userRole: string | string[],
  resource: keyof typeof statement
): boolean => {
  return hasPermission(userRole, resource, PermissionActions.EXPORT)
}

// Role hierarchy for UI display
export const roleHierarchy = [
  { value: 'admin', label: 'System Admin', description: 'Full system control (super admin)' },
  { value: 'organisation-admin', label: 'Organisation Admin', description: 'Full control over organization' },
  { value: 'team-admin', label: 'Team Admin', description: 'Manage specific team within organization' },
  { value: 'member', label: 'Member', description: 'Basic team member access' }
]

export const broadcastEventName = (actorNAme: string, event: PermissionAction) => {
  return `${actorNAme}.${event}`
}
