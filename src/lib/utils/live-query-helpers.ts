import { remult } from 'remult'
import { Organization, Team, User, TeamMember, OrgBankDetail, OrgPaymentProvider, MemberInvite, Appointment, AppointmentType, CalendarBlock, WorkdaySetting } from '$/lib/models'

export function subscribeToOrganization(callback: (org: Organization | undefined) => void): () => void {
      const unsubscribe = remult.repo(Organization)
            .liveQuery({
                  where: { id: remult.user?.organizationId }
            })
            .subscribe((info) => {
                  callback(info.items.firstOrNull() ?? undefined)
            })

      return unsubscribe
}

export function subscribeToTeams(callback: (teams: Team[]) => void): () => void {
      const unsubscribe = remult.repo(Team)
            .liveQuery({
                  where: { organizationId: remult.user?.organizationId },
                  orderBy: { name: 'asc' }
            })
            .subscribe((info) => {
                  callback(info.items)
            })

      return unsubscribe
}

export function subscribeToUsers(callback: (users: User[]) => void): () => void {
      const unsubscribe = remult.repo(User)
            .liveQuery({
                  where: { organizationId: remult.user?.organizationId },
                  orderBy: { email: 'asc' }
            })
            .subscribe((info) => {
                  callback(info.items)
            })

      return unsubscribe
}

export function subscribeToTeamMembers(callback: (members: TeamMember[]) => void): () => void {
      const unsubscribe = remult.repo(TeamMember)
            .liveQuery({
                  where: { teamId: remult.user?.teamId },
                  orderBy: { createdAt: 'desc' }
            })
            .subscribe((info) => {
                  callback(info.items)
            })

      return unsubscribe
}

export function subscribeToOrgBankDetail(callback: (detail: OrgBankDetail | undefined) => void): () => void {
      const unsubscribe = remult.repo(OrgBankDetail)
            .liveQuery({
                  where: { organizationId: remult.user?.organizationId },
                  orderBy: { bankName: 'asc' }
            })
            .subscribe((info) => {
                  callback(info.items.firstOrNull() ?? undefined)
            })

      return unsubscribe
}

export function subscribeToOrgPaymentProvider(callback: (provider: OrgPaymentProvider | undefined) => void): () => void {
      const unsubscribe = remult.repo(OrgPaymentProvider)
            .liveQuery({
                  where: { organizationId: remult.user?.organizationId }
            })
            .subscribe((info) => {
                  callback(info.items.firstOrNull() ?? undefined)
            })

      return unsubscribe
}

export function subscribeToMemberInvites(callback: (invites: MemberInvite[]) => void): () => void {
      const unsubscribe = remult.repo(MemberInvite)
            .liveQuery({
                  where: { organizationId: remult.user?.organizationId },
                  orderBy: { createdAt: 'desc' }
            })
            .subscribe((info) => {
                  callback(info.items)
            })

      return unsubscribe
}

export function subscribeToAppointments(callback: (appointments: Appointment[]) => void): () => void {
      const unsubscribe = remult.repo(Appointment)
            .liveQuery({
                  where: { organizationId: remult.user?.organizationId },
                  orderBy: { createdAt: 'desc' }
            })
            .subscribe((info) => {
                  callback(info.items)
            })

      return unsubscribe
}

export function subscribeToAppointmentTypes(callback: (types: AppointmentType[]) => void): () => void {
      const unsubscribe = remult.repo(AppointmentType)
            .liveQuery({
                  where: { organizationId: remult.user?.organizationId },
                  orderBy: { appointmentDetail: 'asc' }
            })
            .subscribe((info) => {
                  callback(info.items)
            })

      return unsubscribe
}

export function subscribeToCalendarBlocks(callback: (blocks: CalendarBlock[]) => void): () => void {
      const unsubscribe = remult.repo(CalendarBlock)
            .liveQuery({
                  where: { organizationId: remult.user?.organizationId },
                  orderBy: { startDate: 'asc' }
            })
            .subscribe((info) => {
                  callback(info.items)
            })

      return unsubscribe
}

export function subscribeToWorkdaySetting(callback: (setting: WorkdaySetting | undefined) => void): () => void {
      const unsubscribe = remult.repo(WorkdaySetting)
            .liveQuery({
                  where: { organizationId: remult.user?.organizationId }
            })
            .subscribe((info) => {
                  callback(info.items.firstOrNull() ?? undefined)
            })

      return unsubscribe
}

// Helper to get organization name initials
export function getOrgNameInitials(orgName: string | undefined): string {
      return orgName
            ?.split(' ')
            .map((wrd: string) => wrd.charAt(0).toUpperCase())
            .join('') ?? 'NA'
}