import { remult, SubscriptionChannel } from 'remult'
import { RolesType } from '../types'
import type { PermissionAction } from './permissions'

export const isOrgAdmin = (orgId?: string) =>
      !!orgId && remult.isAllowed([RolesType.Admin, RolesType.OrgAdmin]) ||
      orgId === remult.user?.organizationId


export const getPubChannel = <T>(orgId: string, action: PermissionAction) => {
      return new SubscriptionChannel<T>(`org-${action}-${orgId}`)
}