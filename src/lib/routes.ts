/* eslint-disable */
/**
 * This file was generated by 'vite-plugin-kit-routes'
 *
 *      >> DO NOT EDIT THIS FILE MANUALLY <<
 */

/**
 * PAGES
 */
export const PAGES = {
  "_ROOT": `/`,
  "appointments": `/appointments`,
  "appointments_appointment_templates": `/appointments/appointment-templates`,
  "appointments_calendar_blocks": `/appointments/calendar-blocks`,
  "campaigns": `/campaigns`,
  "chat_store": `/chat-store`,
  "invitation_id": (id: (string | number), params?: {  }) => {
    return `/invitation/${id}`
  },
  "laybye": `/laybye`,
  "memberships": `/memberships`,
  "rewards": `/rewards`,
  "settings": `/settings`,
  "settings_invites": `/settings/invites`,
  "settings_locations": `/settings/locations`,
  "settings_payment_engine": `/settings/payment-engine`,
  "settings_staff": `/settings/staff`,
  "settings_staff_invites": `/settings/staff-invites`,
  "subscriptions": `/subscriptions`,
  "wallets": `/wallets`
}

/**
 * SERVERS
 */
export const SERVERS = {
  
}

/**
 * ACTIONS
 */
export const ACTIONS = {
  
}

/**
 * LINKS
 */
export const LINKS = {
  
}

type ParamValue = string | number | boolean | null | undefined

/**
 * Append search params to a string
 */
export const appendSp = (
  sp?: Record<string, ParamValue | ParamValue[]>,
  prefix: '?' | '&' = '?',
) => {
  if (sp === undefined) return ''

  const params = new URLSearchParams()
  const append = (n: string, v: ParamValue) => {
    if (v !== undefined) {
      params.append(n, String(v))
    }
  }

  let anchor = ''
  for (const [name, val] of Object.entries(sp)) {
    if (name === '__KIT_ROUTES_ANCHOR__' && val !== undefined) {
      anchor = `#${val}`
      continue
    }
    if (Array.isArray(val)) {
      for (const v of val) {
        append(name, v)
      }
    } else {
      append(name, val)
    }
  }

  const formatted = params.toString()
  if (formatted || anchor) {
    return `${prefix}${formatted}${anchor}`.replace('?#', '#')
  }
  return ''
}

/**
 * get the current search params
 * 
 * Could be use like this:
 * ```
 * route("/cities", { page: 2 }, { ...currentSP() })
 * ```
 */ 
export const currentSp = () => {
  const params = new URLSearchParams(window.location.search)
  const record: Record<string, string> = {}
  for (const [key, value] of params.entries()) {
    record[key] = value
  }
  return record
}

/**
* Add this type as a generic of the vite plugin `kitRoutes<KIT_ROUTES>`.
*
* Full example:
* ```ts
* import type { KIT_ROUTES } from '$lib/ROUTES'
* import { kitRoutes } from 'vite-plugin-kit-routes'
*
* kitRoutes<KIT_ROUTES>({
*  PAGES: {
*    // here, key of object will be typed!
*  }
* })
* ```
*/
export type KIT_ROUTES = {
  PAGES: { '_ROOT': never, 'appointments': never, 'appointments_appointment_templates': never, 'appointments_calendar_blocks': never, 'campaigns': never, 'chat_store': never, 'invitation_id': 'id', 'laybye': never, 'memberships': never, 'rewards': never, 'settings': never, 'settings_invites': never, 'settings_locations': never, 'settings_payment_engine': never, 'settings_staff': never, 'settings_staff_invites': never, 'subscriptions': never, 'wallets': never }
  SERVERS: Record<string, never>
  ACTIONS: Record<string, never>
  LINKS: Record<string, never>
  Params: { 'id': never }
}
