# Controller Implementation with Method<PERSON><PERSON><PERSON> and Valibot

This document shows how to implement controllers using the `MethodImplementation` utility type with Valibot validation.

## 1. Basic Setup

### Install Dependencies

```bash
pnpm add valibot
```

### Import Required Types

```typescript
// src/lib/controllers/team-member-controller.ts
import { BackendMethod, Allow, remult } from 'remult'
import * as v from 'valibot'
import {
	MethodImplementation,
	CallbackEnabledMethod,
	DefaultError
} from '../types/callback-methods'
import { TeamMember } from '../models/team-member'
import { RolesType } from '../types'
```

## 2. Define Valibot Schemas

```typescript
// Input validation schemas
const AddUserToTeamSchema = v.object({
	userId: v.pipe(
		v.string('User ID must be a string'),
		v.minLength(5, 'User ID must be at least 5 characters'),
		v.regex(/^[a-zA-Z0-9-_]+$/, 'User ID contains invalid characters')
	),
	teamId: v.pipe(
		v.string('Team ID must be a string'),
		v.minLength(5, 'Team ID must be at least 5 characters')
	),
	role: v.optional(v.picklist(['admin', 'member', 'viewer'], 'Invalid role'), 'member')
})

const RemoveUserSchema = v.object({
	userId: v.string('User ID is required'),
	teamId: v.string('Team ID is required')
})

// Type inference from schemas
type AddUserInput = v.InferOutput<typeof AddUserToTeamSchema>
type RemoveUserInput = v.InferOutput<typeof RemoveUserSchema>
```

## 3. Define Custom Error Types

```typescript
interface TeamMemberError extends DefaultError {
	code:
		| 'DUPLICATE_MEMBERSHIP'
		| 'MEMBER_NOT_FOUND'
		| 'TEAM_NOT_FOUND'
		| 'USER_NOT_FOUND'
		| 'VALIDATION_ERROR'
		| 'PERMISSION_DENIED'
}

interface AddUserSuccess {
	teamMember: TeamMember
	message: string
	teamName: string
	userRole: string
}

interface RemoveUserSuccess {
	message: string
	removedMember: TeamMember
	teamName: string
}
```

## 4. Create Method Implementations

### Simple Implementation (Basic CRUD)

```typescript
const addUserImplementation: MethodImplementation<AddUserInput, AddUserSuccess, TeamMemberError> & {
	schema: typeof AddUserToTeamSchema
} = {
	schema: AddUserToTeamSchema,

	// Optional: Additional validation after schema validation
	validate: async (data) => {
		const userExists = await checkUserExists(data.userId)
		const teamExists = await checkTeamExists(data.teamId)

		if (!userExists) throw new Error('User not found')
		if (!teamExists) throw new Error('Team not found')
	},

	// Main business logic
	execute: async (data) => {
		const repo = remult.repo(TeamMember)

		// Check for existing membership
		const existing = await repo.findFirst({
			teamId: data.teamId,
			userId: data.userId,
			deletedAt: undefined
		})

		if (existing) {
			throw new Error('User is already a member of this team')
		}

		// Create team member
		const teamMember = await repo.insert({
			userId: data.userId,
			teamId: data.teamId
		})

		const teamName = await getTeamName(data.teamId)

		return {
			teamMember,
			message: 'User successfully added to team',
			teamName,
			userRole: data.role
		}
	},

	// Optional: Transform result
	transform: (result) => ({
		...result,
		message: `${result.message} with role: ${result.userRole}`
	}),

	// Custom error handling
	handleError: (error) => {
		let code: TeamMemberError['code'] = 'UNKNOWN_ERROR'

		if (v.isValiError(error)) {
			code = 'VALIDATION_ERROR'
			return {
				message: 'Input validation failed',
				code,
				details: v.flatten(error),
				timestamp: new Date()
			}
		}

		if (error.message.includes('already a member')) {
			code = 'DUPLICATE_MEMBERSHIP'
		} else if (error.message.includes('User not found')) {
			code = 'USER_NOT_FOUND'
		} else if (error.message.includes('Team not found')) {
			code = 'TEAM_NOT_FOUND'
		}

		return {
			message: error.message,
			code,
			details: error,
			timestamp: new Date()
		}
	}
}
```

### Complex Implementation (with Async Operations)

```typescript
const removeUserImplementation: MethodImplementation<
	RemoveUserInput,
	RemoveUserSuccess,
	TeamMemberError
> & { schema: typeof RemoveUserSchema } = {
	schema: RemoveUserSchema,

	validate: async (data) => {
		// Check permissions
		const hasPermission = await checkRemovePermission(data.userId, data.teamId)
		if (!hasPermission) {
			throw new Error('Insufficient permissions to remove user')
		}
	},

	execute: async (data) => {
		const repo = remult.repo(TeamMember)

		const teamMember = await repo.findFirst({
			userId: data.userId,
			teamId: data.teamId,
			deletedAt: undefined
		})

		if (!teamMember) {
			throw new Error('User is not a member of this team')
		}

		// Soft delete
		const removedMember = await repo.save({
			...teamMember,
			deletedAt: new Date()
		})

		// Log the action
		await logTeamAction('USER_REMOVED', data.userId, data.teamId)

		const teamName = await getTeamName(data.teamId)

		return {
			message: 'User successfully removed from team',
			removedMember,
			teamName
		}
	},

	handleError: (error) => {
		let code: TeamMemberError['code'] = 'UNKNOWN_ERROR'

		if (v.isValiError(error)) {
			code = 'VALIDATION_ERROR'
		} else if (error.message.includes('not a member')) {
			code = 'MEMBER_NOT_FOUND'
		} else if (error.message.includes('permissions')) {
			code = 'PERMISSION_DENIED'
		}

		return {
			message: error.message,
			code,
			details: error,
			timestamp: new Date()
		}
	}
}
```

## 5. Create Helper Function

```typescript
async function createCallbackMethod<TInput, TSuccess, TError = DefaultError>(
	implementation: MethodImplementation<TInput, TSuccess, TError> & {
		schema?: v.BaseSchema<any, any, any>
	},
	data: TInput,
	callbacks?: {
		onSuccess?: (result: TSuccess) => void | Promise<void>
		onError?: (error: TError) => void | Promise<void>
		onLoading?: (loading: boolean) => void | Promise<void>
		onValidationError?: (errors: v.FlatErrors<any>) => void | Promise<void>
	}
): Promise<{ data?: TSuccess; error?: TError }> {
	try {
		if (callbacks?.onLoading) await callbacks.onLoading(true)

		// Valibot validation
		if (implementation.schema) {
			try {
				data = v.parse(implementation.schema, data) as TInput
			} catch (validationError) {
				if (v.isValiError(validationError)) {
					const flatErrors = v.flatten(validationError)
					if (callbacks?.onValidationError) {
						await callbacks.onValidationError(flatErrors)
					}
				}
				throw validationError
			}
		}

		if (implementation.validate) {
			await implementation.validate(data)
		}

		let result = await implementation.execute(data)

		if (implementation.transform) {
			result = implementation.transform(result)
		}

		if (callbacks?.onSuccess) await callbacks.onSuccess(result)
		if (callbacks?.onLoading) await callbacks.onLoading(false)

		return { data: result }
	} catch (err: any) {
		const error = implementation.handleError
			? implementation.handleError(err)
			: ({
					message: err.message || 'Operation failed',
					code: 'UNKNOWN_ERROR',
					details: err,
					timestamp: new Date()
				} as TError)

		if (callbacks?.onError) await callbacks.onError(error)
		if (callbacks?.onLoading) await callbacks.onLoading(false)

		return { error }
	}
}
```

## 6. Export Controller Methods

```typescript
// Create callback-enabled methods
export const addUserToTeam: CallbackEnabledMethod<
	AddUserInput,
	AddUserSuccess,
	TeamMemberError
> = async (data, callbacks) => {
	return createCallbackMethod(addUserImplementation, data, callbacks)
}

export const removeUserFromTeam: CallbackEnabledMethod<
	RemoveUserInput,
	RemoveUserSuccess,
	TeamMemberError
> = async (data, callbacks) => {
	return createCallbackMethod(removeUserImplementation, data, callbacks)
}

// Controller class
export class TeamMemberController {
	@BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin] })
	static addUserToTeam = addUserToTeam

	@BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin] })
	static removeUserFromTeam = removeUserFromTeam
}
```

## 7. Helper Functions

```typescript
async function checkUserExists(userId: string): Promise<boolean> {
	// Implementation
	return true
}

async function checkTeamExists(teamId: string): Promise<boolean> {
	// Implementation
	return true
}

async function getTeamName(teamId: string): Promise<string> {
	// Implementation
	return `Team ${teamId.slice(-4)}`
}

async function checkRemovePermission(userId: string, teamId: string): Promise<boolean> {
	// Implementation
	return true
}

async function logTeamAction(action: string, userId: string, teamId: string): Promise<void> {
	console.log(`${action}: User ${userId} in Team ${teamId}`)
}
```
