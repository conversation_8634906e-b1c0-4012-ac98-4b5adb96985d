# Svelte Component Usage with MethodImplementation and Valibot

This document shows how to use the callback-enabled controller methods in Svelte components with full type safety and error handling.

## 1. Basic Component Setup

```svelte
<!-- src/routes/(app)/team-management/+page.svelte -->
<script lang="ts">
	import { TeamMemberController } from '$lib/controllers/team-member-controller'
	import { Button } from '$lib/components/ui/button'
	import { Input } from '$lib/components/ui/input'
	import { toast } from 'svelte-sonner'
	import type { ExtractInput, ExtractSuccess, ExtractError } from '$lib/types/callback-methods'

	// Extract types from controller methods for type safety
	type AddUserInput = ExtractInput<typeof TeamMemberController.addUserToTeam>
	type AddUserSuccess = ExtractSuccess<typeof TeamMemberController.addUserToTeam>
	type AddUserError = ExtractError<typeof TeamMemberController.addUserToTeam>

	// Component state
	let isLoading = false
	let validationErrors: Record<string, string[]> = {}
	let formData: AddUserInput = {
		userId: '',
		teamId: '',
		role: 'member'
	}

	// Team members list
	let teamMembers: any[] = []
</script>
```

## 2. Simple Usage with Basic Callbacks

```svelte
<script lang="ts">
	async function handleAddUser() {
		const result = await TeamMemberController.addUserToTeam(formData, {
			onLoading: (loading) => {
				isLoading = loading
			},
			onSuccess: (result) => {
				toast.success(result.message)
				console.log('Added to team:', result.teamName)
				console.log('User role:', result.userRole)

				// Reset form
				formData = { userId: '', teamId: '', role: 'member' }

				// Refresh team members list
				loadTeamMembers()
			},
			onError: (error) => {
				toast.error(error.message)
				console.error('Error code:', error.code)

				// Handle specific error types
				switch (error.code) {
					case 'DUPLICATE_MEMBERSHIP':
						toast.error('User is already a member of this team')
						break
					case 'USER_NOT_FOUND':
						toast.error('User does not exist')
						break
					case 'TEAM_NOT_FOUND':
						toast.error('Team does not exist')
						break
				}
			}
		})

		// Traditional promise handling is still available
		if (result.data) {
			console.log('Success via promise:', result.data.message)
		} else if (result.error) {
			console.log('Error via promise:', result.error.message)
		}
	}
</script>
```

## 3. Advanced Usage with Validation Error Handling

```svelte
<script lang="ts">
	async function handleAddUserWithValidation() {
		// Clear previous validation errors
		validationErrors = {}

		const result = await TeamMemberController.addUserToTeam(formData, {
			onLoading: (loading) => {
				isLoading = loading
			},
			onValidationError: (errors) => {
				// Handle Valibot validation errors
				console.log('Validation errors:', errors)

				// Convert flat errors to component state
				validationErrors = {}

				if (errors.nested?.userId) {
					validationErrors.userId = errors.nested.userId
				}
				if (errors.nested?.teamId) {
					validationErrors.teamId = errors.nested.teamId
				}
				if (errors.nested?.role) {
					validationErrors.role = errors.nested.role
				}

				// Show validation toast
				toast.error('Please fix the validation errors')
			},
			onSuccess: (result) => {
				toast.success(`${result.message} - Role: ${result.userRole}`)

				// Add to local state for immediate UI update
				teamMembers = [
					...teamMembers,
					{
						id: result.teamMember.id,
						userId: formData.userId,
						teamId: formData.teamId,
						role: result.userRole,
						teamName: result.teamName
					}
				]

				// Reset form and errors
				formData = { userId: '', teamId: '', role: 'member' }
				validationErrors = {}
			},
			onError: (error) => {
				// Handle different error types with specific UI feedback
				switch (error.code) {
					case 'VALIDATION_ERROR':
						// Validation errors are handled by onValidationError
						break
					case 'DUPLICATE_MEMBERSHIP':
						toast.error('This user is already a team member', {
							description: 'Choose a different user or team'
						})
						break
					case 'PERMISSION_DENIED':
						toast.error('Access denied', {
							description: "You don't have permission to add users to this team"
						})
						break
					default:
						toast.error('Failed to add user', {
							description: error.message
						})
				}
			}
		})
	}
</script>
```

## 5. Form Component with Real-time Validation

```svelte
<script lang="ts">
	// Real-time validation as user types
	let realTimeErrors: Record<string, string> = {}

	function validateField(field: keyof AddUserInput, value: string) {
		realTimeErrors = { ...realTimeErrors }

		switch (field) {
			case 'userId':
				if (!value) {
					realTimeErrors.userId = 'User ID is required'
				} else if (value.length < 5) {
					realTimeErrors.userId = 'User ID must be at least 5 characters'
				} else if (!/^[a-zA-Z0-9-_]+$/.test(value)) {
					realTimeErrors.userId = 'User ID contains invalid characters'
				} else {
					delete realTimeErrors.userId
				}
				break
			case 'teamId':
				if (!value) {
					realTimeErrors.teamId = 'Team ID is required'
				} else if (value.length < 5) {
					realTimeErrors.teamId = 'Team ID must be at least 5 characters'
				} else {
					delete realTimeErrors.teamId
				}
				break
			case 'role':
				if (value && !['admin', 'member', 'viewer'].includes(value)) {
					realTimeErrors.role = 'Invalid role selected'
				} else {
					delete realTimeErrors.role
				}
				break
		}

		realTimeErrors = realTimeErrors
	}

	// Debounced validation
	let validationTimeout: NodeJS.Timeout
	function debouncedValidation(field: keyof AddUserInput, value: string) {
		clearTimeout(validationTimeout)
		validationTimeout = setTimeout(() => {
			validateField(field, value)
		}, 300)
	}
</script>

<!-- Form Template -->
<form on:submit|preventDefault={handleAddUserWithValidation} class="space-y-4">
	<div>
		<label for="userId" class="block text-sm font-medium">User ID</label>
		<Input
			id="userId"
			bind:value={formData.userId}
			on:input={(e) => debouncedValidation('userId', e.target.value)}
			placeholder="Enter user ID"
			class={realTimeErrors.userId || validationErrors.userId ? 'border-red-500' : ''}
			disabled={isLoading}
		/>
		{#if realTimeErrors.userId}
			<p class="mt-1 text-sm text-red-500">{realTimeErrors.userId}</p>
		{/if}
		{#if validationErrors.userId}
			<div class="mt-1 text-sm text-red-500">
				{#each validationErrors.userId as error}
					<p>{error}</p>
				{/each}
			</div>
		{/if}
	</div>

	<div>
		<label for="teamId" class="block text-sm font-medium">Team ID</label>
		<Input
			id="teamId"
			bind:value={formData.teamId}
			on:input={(e) => debouncedValidation('teamId', e.target.value)}
			placeholder="Enter team ID"
			class={realTimeErrors.teamId || validationErrors.teamId ? 'border-red-500' : ''}
			disabled={isLoading}
		/>
		{#if realTimeErrors.teamId}
			<p class="mt-1 text-sm text-red-500">{realTimeErrors.teamId}</p>
		{/if}
		{#if validationErrors.teamId}
			<div class="mt-1 text-sm text-red-500">
				{#each validationErrors.teamId as error}
					<p>{error}</p>
				{/each}
			</div>
		{/if}
	</div>

	<div>
		<label for="role" class="block text-sm font-medium">Role</label>
		<select
			id="role"
			bind:value={formData.role}
			on:change={(e) => validateField('role', e.target.value)}
			class="w-full rounded border p-2"
			disabled={isLoading}
		>
			<option value="member">Member</option>
			<option value="admin">Admin</option>
			<option value="viewer">Viewer</option>
		</select>
		{#if realTimeErrors.role}
			<p class="mt-1 text-sm text-red-500">{realTimeErrors.role}</p>
		{/if}
		{#if validationErrors.role}
			<div class="mt-1 text-sm text-red-500">
				{#each validationErrors.role as error}
					<p>{error}</p>
				{/each}
			</div>
		{/if}
	</div>

	<Button
		type="submit"
		disabled={isLoading || Object.keys(realTimeErrors).length > 0}
		class="w-full"
	>
		{#if isLoading}
			Adding User...
		{:else}
			Add User to Team
		{/if}
	</Button>
</form>
```

## 6. Multiple Operations Component

```svelte
<script lang="ts">
	// Handle multiple operations with different loading states
	let operations = {
		adding: false,
		removing: false,
		loading: false
	}

	async function handleRemoveUser(userId: string, teamId: string) {
		const result = await TeamMemberController.removeUserFromTeam(
			{ userId, teamId },
			{
				onLoading: (loading) => {
					operations.removing = loading
					operations.loading = loading
				},
				onSuccess: (result) => {
					toast.success(result.message)

					// Remove from local state
					teamMembers = teamMembers.filter(
						(member) => !(member.userId === userId && member.teamId === teamId)
					)
				},
				onError: (error) => {
					switch (error.code) {
						case 'MEMBER_NOT_FOUND':
							toast.error('User is not a member of this team')
							break
						case 'PERMISSION_DENIED':
							toast.error("You don't have permission to remove this user")
							break
						default:
							toast.error(`Failed to remove user: ${error.message}`)
					}
				}
			}
		)
	}

	// Bulk operations
	async function handleBulkAddUsers(users: { userId: string; role: string }[]) {
		operations.adding = true
		operations.loading = true

		try {
			const results = await Promise.allSettled(
				users.map((user) =>
					TeamMemberController.addUserToTeam(
						{ ...user, teamId: formData.teamId },
						{
							onSuccess: (result) => {
								console.log(`Added ${user.userId} successfully`)
							},
							onError: (error) => {
								console.error(`Failed to add ${user.userId}:`, error.message)
							}
						}
					)
				)
			)

			const successful = results.filter((r) => r.status === 'fulfilled').length
			const failed = results.length - successful

			if (successful > 0) {
				toast.success(`Successfully added ${successful} users`)
			}
			if (failed > 0) {
				toast.error(`Failed to add ${failed} users`)
			}

			// Refresh the team members list
			await loadTeamMembers()
		} finally {
			operations.adding = false
			operations.loading = false
		}
	}
</script>

<!-- Team Members List -->
<div class="space-y-2">
	<h3 class="text-lg font-semibold">Team Members</h3>

	{#each teamMembers as member (member.id)}
		<div class="flex items-center justify-between rounded border p-3">
			<div>
				<p class="font-medium">{member.userId}</p>
				<p class="text-sm text-gray-600">{member.role} in {member.teamName}</p>
			</div>

			<Button
				variant="destructive"
				size="sm"
				on:click={() => handleRemoveUser(member.userId, member.teamId)}
				disabled={operations.removing}
			>
				{#if operations.removing}
					Removing...
				{:else}
					Remove
				{/if}
			</Button>
		</div>
	{/each}

	{#if teamMembers.length === 0}
		<p class="py-4 text-center text-gray-500">No team members found</p>
	{/if}
</div>
```

## 7. Error Boundary Component

```svelte
<script lang="ts">
	import { createEventDispatcher } from 'svelte'

	const dispatch = createEventDispatcher()

	// Global error handler for all team operations
	function createGlobalErrorHandler() {
		return {
			onError: (error: any) => {
				// Log error for debugging
				console.error('Team operation error:', error)

				// Dispatch to parent component
				dispatch('error', { error })

				// Global error handling
				if (error.code === 'PERMISSION_DENIED') {
					// Redirect to login or show permission modal
					toast.error('Session expired. Please log in again.')
				} else if (error.code === 'VALIDATION_ERROR') {
					// Handle validation errors globally
					toast.error('Please check your input and try again')
				} else {
					// Generic error handling
					toast.error('An unexpected error occurred')
				}
			}
		}
	}

	// Use global error handler
	const globalErrorHandler = createGlobalErrorHandler()
</script>
```

This comprehensive example shows how to use the MethodImplementation pattern in Svelte components with:

- **Type Safety**: Full TypeScript support with extracted types
- **Validation Handling**: Real-time and server-side validation
- **Error Management**: Specific error handling for different scenarios
- **Loading States**: Proper loading state management
- **Reactive Updates**: Svelte stores and reactive statements
- **User Experience**: Toast notifications and form feedback
- **Bulk Operations**: Handling multiple operations efficiently
