// src/lib/types/callback-methods.ts

/**
 * Generic callback functions interface
 */
export interface MethodCallbacks<TSuccess, TError = DefaultError> {
      onSuccess?: (result: TSuccess) => void | Promise<void>
      onError?: (error: TError) => void | Promise<void>
      onLoading?: (loading: boolean) => void | Promise<void>
      onProgress?: (progress: { current: number; total: number; message?: string }) => void | Promise<void>
}

/**
 * Default error structure
 */
export interface DefaultError {
      message: string
      code?: string
      details?: any
      timestamp?: Date
}

/**
 * Success response wrapper
 */
export interface SuccessResponse<TData> {
      data: TData
      message?: string
      metadata?: Record<string, any>
}

/**
 * Error response wrapper
 */
export interface ErrorResponse<TError = DefaultError> {
      error: TError
}

/**
 * Combined response type
 */
export type MethodResponse<TSuccess, TError = DefaultError> =
      | SuccessResponse<TSuccess>
      | ErrorResponse<TError>

/**
 * Generic callback-enabled method signature
 */
export type CallbackEnabledMethod<
      TInput,
      TSuccess,
      TError = DefaultError
> = (
      data: TInput,
      callbacks?: MethodCallbacks<TSuccess, TError>
) => Promise<MethodResponse<TSuccess, TError>>

/**
 * Async callback-enabled method signature (for methods that need async callbacks)
 */
export type AsyncCallbackEnabledMethod<
      TInput,
      TSuccess,
      TError = DefaultError
> = (
      data: TInput,
      callbacks?: {
            onSuccess?: (result: TSuccess) => Promise<void>
            onError?: (error: TError) => Promise<void>
            onLoading?: (loading: boolean) => Promise<void>
            onProgress?: (progress: { current: number; total: number; message?: string }) => Promise<void>
      }
) => Promise<MethodResponse<TSuccess, TError>>

/**
 * Batch operation method signature
 */
export type BatchCallbackEnabledMethod<
      TInput,
      TSuccess,
      TError = DefaultError
> = (
      data: TInput[],
      callbacks?: MethodCallbacks<TSuccess[], TError> & {
            onItemSuccess?: (result: TSuccess, index: number) => void | Promise<void>
            onItemError?: (error: TError, index: number) => void | Promise<void>
      }
) => Promise<MethodResponse<TSuccess[], TError>>

/**
 * Paginated method signature
 */
export interface PaginationInput {
      page?: number
      limit?: number
      sortBy?: string
      sortOrder?: 'asc' | 'desc'
      filters?: Record<string, any>
}

export interface PaginatedResult<TData> {
      items: TData[]
      total: number
      page: number
      limit: number
      totalPages: number
      hasNext: boolean
      hasPrev: boolean
}

export type PaginatedCallbackEnabledMethod<
      TInput extends PaginationInput,
      TSuccess,
      TError = DefaultError
> = (
      data: TInput,
      callbacks?: MethodCallbacks<PaginatedResult<TSuccess>, TError>
) => Promise<MethodResponse<PaginatedResult<TSuccess>, TError>>

/**
 * File upload method signature
 */
export interface FileUploadProgress {
      loaded: number
      total: number
      percentage: number
      speed?: number
      timeRemaining?: number
}

export type FileUploadCallbackEnabledMethod<
      TInput,
      TSuccess,
      TError = DefaultError
> = (
      data: TInput,
      callbacks?: MethodCallbacks<TSuccess, TError> & {
            onUploadProgress?: (progress: FileUploadProgress) => void | Promise<void>
      }
) => Promise<MethodResponse<TSuccess, TError>>

/**
 * Validation-enabled method signature
 */
export interface ValidationError extends DefaultError {
      code: 'VALIDATION_ERROR'
      details: {
            field: string
            message: string
            value?: any
      }[]
}

export type ValidatedCallbackEnabledMethod<
      TInput,
      TSuccess,
      TError = ValidationError
> = (
      data: TInput,
      callbacks?: MethodCallbacks<TSuccess, TError> & {
            onValidationError?: (errors: ValidationError['details']) => void | Promise<void>
      }
) => Promise<MethodResponse<TSuccess, TError>>

/**
 * Helper type for extracting input type from method
 */
export type ExtractInput<T> = T extends CallbackEnabledMethod<infer I, any, any> ? I : never

/**
 * Helper type for extracting success type from method
 */
export type ExtractSuccess<T> = T extends CallbackEnabledMethod<any, infer S, any> ? S : never

/**
 * Helper type for extracting error type from method
 */
export type ExtractError<T> = T extends CallbackEnabledMethod<any, any, infer E> ? E : never

/**
 * Utility type for creating method implementations
 */
export type MethodImplementation<
      TInput,
      TSuccess,
      TError = DefaultError
> = {
      execute: (data: TInput) => Promise<TSuccess>
      validate?: (data: TInput) => Promise<void> | void
      transform?: (result: TSuccess) => TSuccess
      handleError?: (error: any) => TError
}


