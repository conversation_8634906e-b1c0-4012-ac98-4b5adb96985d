import { lettersOnly, passwordConstraint } from './common.schema'
import {
	object,
	string,
	minLength,
	regex,
	email,
	check,
	type InferInput,
	boolean,
	optional,
	forward,
	pipe,
	any,
	picklist
} from 'valibot'
import stringVal from './stringVal'
import { UserAccountTypes } from '../generic'


const lettersOnlyRegex: RegExp = new RegExp(lettersOnly)
const passwordRegEx = new RegExp(passwordConstraint)

export const signUpCodeFormSchema = object({
	code: string(stringVal('Code')),
	token: optional(string()),
	principal: optional(string()),
	email: optional(string(stringVal('Email'))),
	returnUrl: optional(string(stringVal('Return Url')))
})

export type SignUpCodeFormType = InferInput<typeof signUpCodeFormSchema>

export const signUpFormSchema = object({
	acceptTerms: boolean(),
	authType: picklist(['passkey', 'email']),
	givenName: pipe(
		string(stringVal('First Name')),
		minLength(2, 'First Name must have a length of at least 2 characters'),
		regex(lettersOnlyRegex, 'Only alphabet characters allowed')
	),
	familyName: pipe(
		string(stringVal('Last Name')),
		minLength(2, 'Last Name must have a length of at least 2 characters'),
		regex(lettersOnlyRegex, 'Only alphabet characters allowed')
	),
	phoneNumber: optional(
		pipe(
			string(stringVal('Phone Number')),
			regex(/^[0-9]{10}$/, 'Phone number must be 10 digits')
		)
	),
	email: pipe(string(stringVal('Email')), email('Entry is not a valid email')),
	accountType: optional(string(), UserAccountTypes.Staff),
	businessName: pipe(
		string(stringVal('Business Name')),
		minLength(2, 'Business Name must have a length of at least 2 characters')
	),
	businessEmail: pipe(string(stringVal('Business Email')), email('Entry is not a valid email')),
	businessAddress: pipe(
		string(stringVal('Business Address')),
		minLength(2, 'Business Address must have a length of at least 2 characters')
	),
	password: optional(string())
})
export type SignUpFormType = InferInput<typeof signUpFormSchema>

/** reset Password schema*/
export const resetPasswordFormSchema = pipe(
	object({
		password: pipe(
			string(stringVal('Password')),
			minLength(6, 'Password must be at least 6 characters long'),
			regex(
				passwordRegEx,
				'Password must contain at least one number, one uppercase letter, and one symbol'
			)
		),
		confirmPassword: string(stringVal('Confirm Password')),
		userId: string(stringVal('AppUser Id'))
	}),
	forward(
		check((data) => data.password === data.confirmPassword, `Passwords don't match`),
		['confirmPassword']
	)
)

export type ResetPasswordFormType = InferInput<typeof resetPasswordFormSchema>

export const requestPasswordResetFormSchema = object({
	email: pipe(string(stringVal('Email')), email('Entry is not a valid email'))
})

export type RequestPasswordResetFormType = InferInput<typeof requestPasswordResetFormSchema>

export const loginFormSchema = object({
	email: pipe(string(stringVal('Email')), email('Entry is not a valid email')),
	token: optional(any()),
	authType: picklist(['passkey', 'email', 'apple', 'google']),
	returnUrl: optional(string(stringVal('Return Url')), '/'),
	isEmailVerified: optional(boolean(), false)
})

export type LoginFormType = InferInput<typeof loginFormSchema>

export const logOutFormSchema = object({
	userId: optional(string())
})
export type LogOutFormSchemaType = InferInput<typeof logOutFormSchema>

export const setPasswordFormSchema = pipe(
	object({
		password: pipe(
			string(stringVal('Password')),
			minLength(6, 'Password must be at least 6 characters long'),
			regex(
				passwordRegEx,
				'Password must contain at least one number, one uppercase letter, and one symbol'
			)
		),
		confirmPassword: string(stringVal('Confirm Password'))
	}),
	forward(
		check((data) => data.password === data.confirmPassword, `Passwords don't match`),
		['confirmPassword']
	)
)

export type SetPasswordFormType = InferInput<typeof setPasswordFormSchema>
