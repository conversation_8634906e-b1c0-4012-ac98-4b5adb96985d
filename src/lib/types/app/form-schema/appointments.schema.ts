import { appointmentStatuses, appointmentRepeatTypes } from '../generic'
import {
	object,
	string,
	minLength,
	pipe,
	optional,
	regex,
	email,
	picklist,
	date,
	type InferInput,
	any,
	minValue,
	number,
	boolean
} from 'valibot'
import stringVal from './stringVal'

export const newAppointmentFormSchema = object({
	appointmentTypeId: pipe(string(stringVal('Title')), minLength(1, 'Title is required')),
	appointmentNotes: optional(string(stringVal('Description'))),
	startTime: pipe(
		string(stringVal('Start Time')),
		regex(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}$/, 'Invalid date format')
	),
	endTime: pipe(
		string(stringVal('End Time')),
		regex(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}$/, 'Invalid date format')
	),
	customerEmail: pipe(string(), email('Invalid email address')),
	clientId: pipe(string(), minLength(1, 'Client name is required')),
	serviceId: optional(string()),
	customerName: pipe(string(stringVal('Full Name')), minLength(1, 'Client name is required')),
	customerPhoneNumber: optional(string(stringVal('Phone Number'))),
	locationId: string(stringVal('Location')),
	appointmentDate: date()
})

export type NewAppointmentFormSchemaType = InferInput<typeof newAppointmentFormSchema>

export const confirmAppointmentFormSchema = object({
	id: string(),
	status: picklist(['pending', 'confirmed', 'cancelled']),
	title: pipe(string(), minLength(1)),
	description: optional(string()),
	startTime: date(),
	endTime: date()
})

export type ConfirmAppointmentFormSchemaType = InferInput<typeof confirmAppointmentFormSchema>

export const rescheduleAppointmentFormSchema = object({
	appointmentId: string(),
	appointmentDate: any(),
	appointmentStartTime: any(),
	appointmentStatus: picklist([
		appointmentStatuses.Scheduled,
		appointmentStatuses.Cancelled,
		appointmentStatuses.Confirmed,
		appointmentStatuses.Done,
		appointmentStatuses.Missed
	])
})
export type RescheduleAppointmentFormType = InferInput<typeof rescheduleAppointmentFormSchema>


export const createAppointmentTypeFormSchema = object({
	id: optional(string()),
	name: pipe(string(), minLength(1, 'Name is required')),
	durationInMinutes: pipe(number(), minValue(15, 'Duration must be at least 1 minute')),
	isActive: boolean(),
	isRepeatEnabled: boolean(),
	requiresUpfrontPayment: boolean(),
	timeZoneId: string(),
	upfrontPaymentAmount: optional(number(), 0),
	repeatInterval: picklist([
		appointmentRepeatTypes.Daily,
		appointmentRepeatTypes.Weekly,
		appointmentRepeatTypes.EveryTwoWeeks,
		appointmentRepeatTypes.EveryFourWeeks,
		appointmentRepeatTypes.Monthly
	])
	// teamId: string(),
	// createdById: string() //pulled in DB
})
export type CreateAppointmentTypeFormSchemaType = InferInput<typeof createAppointmentTypeFormSchema>

export const deleteAppointmentTypeFormSchema = object({
	id: string()
})
export type DeleteAppointmentTypeFormSchemaType = InferInput<typeof deleteAppointmentTypeFormSchema>

