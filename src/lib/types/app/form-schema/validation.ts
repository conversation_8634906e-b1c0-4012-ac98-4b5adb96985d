
import {
	object,
	string,
	email,
	date,
	optional,
	boolean,
	picklist,
	pipe,
	type InferInput,
	any
} from 'valibot'
import { GenderTypes, TeamMemberStatuses, TeamRoles } from '../generic'

// Base User Profile Schema
export const userProfileSchema = object({
	givenName: string('First name is required'),
	familyName: string('Last name is required'),
	name: string(),
	accountType: string('Account type is required'),
	profilePhotoUrl: optional(string())
})

// User Root Schema that extends userProfileSchema

// Team Location Schema
export const teamSchema = object({
	id: string(),
	locationName: string('Location name is required'),
	locationAddress: optional(string()),
	locationEmail: pipe(string(), email('Invalid email format')),
	locationPhoneNumber: optional(string()),
	isActive: boolean(),
	isDefault: optional(boolean())
})

// Member Invite Schema
export const memberInviteSchema = object({
	inviteeEmail: pipe(string(), email('Invalid email format')),
	role: picklist(Object.values(TeamRoles), 'Invalid role selection'),
	teamLocation: teamSchema,
	invitedBy: any(),
	invitedAt: date(),
	status: picklist(Object.values(TeamMemberStatuses), 'Invalid status'),
	expiresAt: date()
})

// Team Schema
export const orgSchema = object({
	teamName: string('Team name is required'),
	businessEmail: pipe(string(), email('Invalid email format')),
	businessRegNo: optional(string()),
	phoneNumber: optional(string()),
	createdAt: date()
})

export const userRootSchema = object({
	...userProfileSchema.entries,
	email: pipe(string(), email('Invalid email format')),
	phoneNumber: string('Phone number is required'),
	nationalId: string('National ID is required'),
	dateOfBirth: date('Date of birth is required'),
	gender: picklist(Object.values(GenderTypes), 'Invalid gender selection'),
	address: optional(string()),
	city: optional(string()),
	country: optional(string()),
	organization: object({
		...orgSchema.entries
	})
})

// Team Member Schema
export const memberSchema = object({
	userAccount: userRootSchema,
	role: picklist(Object.values(TeamRoles), 'Invalid role selection'),
	location: teamSchema,
	joinedAt: date(),
	lastActive: optional(date()),
	memberStatus: picklist(Object.values(TeamMemberStatuses), 'Invalid status')
})

// Export the type
export type UserRootType = InferInput<typeof userRootSchema>
// Export types for use in forms and API
export type UserProfileType = InferInput<typeof userProfileSchema>
export type TeamLocationType = InferInput<typeof teamSchema>
export type TeamMemberType = InferInput<typeof memberSchema>
export type MemberInviteType = InferInput<typeof memberInviteSchema>
export type TeamType = InferInput<typeof orgSchema>
