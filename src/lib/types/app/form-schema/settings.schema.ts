import stringVal from '../stringVal'
import { type InferInput, object, string, optional, boolean, email, pipe, regex, date } from 'valibot'

export const settingsFormSchema = object({
	id: optional(string()),
	name: string(),
	businessEmail: string(),
	phoneNumber: optional(
		pipe(
			string(stringVal('Phone Number')),
			regex(/^[0-9]{10}$/, 'Phone number must be 10 digits')
		)
	),
	businessRegNo: string(),
	registrationDate: string()
})

export type SettingsFormSchemaType = InferInput<typeof settingsFormSchema>

export const staffInviteFormSchema = object({
	staffName: string(),
	staffEmail: string(),
	assignedLocation: string(),
	assignedRole: string(),
	gender: string(),
	phoneNumber: optional(
		pipe(
			string(stringVal('Phone Number')),
			regex(/^[0-9]{10}$/, 'Phone number must be 10 digits')
		)
	),
	idDocumentNumber: string(),
	dateOfBirth: string(),
	address: string(),
	city: string(),
	postalCode: string(),
	country: string()
})

export type StaffInviteFormSchemaType = InferInput<typeof staffInviteFormSchema>

export const locationFormSchema = object({
	id: optional(string()),
	locationAddress: string(),
	locationEmail: pipe(string(stringVal('Email')), email('Entry is not a valid email')),
	locationPhoneNumber: optional(
		pipe(
			string(stringVal('Phone Number')),
			regex(/^[0-9]{10}$/, 'Phone number must be 10 digits')
		)
	),
	locationName: string(),
	isActive: boolean(),
	isDefault: optional(boolean(), false),
	locationAdmin: optional(string())
})

export type LocationFormSchemaType = InferInput<typeof locationFormSchema>
