
import { object, optional, string, type InferInput, any } from 'valibot'
import { TeamMemberStatuses } from './app.types'


export const objectValuesToArray = <T extends Record<string, string | number>>(
	constObject: T
): T[keyof T][] => {
	return Object.values(constObject) as T[keyof T][]
}


export const enumToObject = <T extends Record<string, string | number>>(enumObject: T) => {
	return Object.keys(enumObject).reduce((acc, key) => {
		acc[key as keyof T] = enumObject[key as keyof T]
		return acc
	}, {} as T)
}


export const RolesType = {
	User: 'user',
	Admin: 'admin',
	OrgAdmin: 'organisation-admin',
	TeamMember: 'member',
	TeamLocationAdmin: 'team-location-admin'
} as const
export const rolesTypes = objectValuesToArray(RolesType)
export type RoleType = (typeof RolesType)[keyof typeof RolesType]
// Enum as the source of truth
export const GenderTypes = {
	Male: 'Male',
	Female: 'Female',
	Other: 'Other',
	Unknown: 'Unknown'
} as const
export const genderTypes = objectValuesToArray(GenderTypes)
export type GenderType = (typeof GenderTypes)[keyof typeof GenderTypes]

export const PermissionTypes = {
	ManageTeam: 'Manage Team',
	ReadTeam: 'Read Team',
	ReadMembers: 'Read Members',
	ManageMembers: 'Manage Members',
	Contribute: 'Contribute',
	ReadAppointments: 'Read Appointments',
	ManageAppointments: 'Manage Appointments',
	ManageAppointmentTypes: 'Manage Appointment Types',
	ReadAppointmentTypes: 'Read Appointment Types'
} as const

export const permissionTypes = objectValuesToArray(PermissionTypes)
export type PermissionType = (typeof PermissionTypes)[keyof typeof PermissionTypes]

export const AuthAppDataSchema = object({
	teamId: optional(string()),
	defaultRole: optional(string(), RolesType.TeamMember),
	teamLocationId: optional(string()),
	allowedRoles: optional(any(), [RolesType.TeamMember])
})

export type IAuthAppData = InferInput<typeof AuthAppDataSchema>

export const AppUserMetadataSchema = object({
	givenName: string(),
	familyName: string(),
	email: string(),
	teamId: string(),
	teamLocationId: string(),
	businessName: optional(string()),
	phoneNumber: string(),
	userStatus: optional(string(), TeamMemberStatuses.Active),
	idDocumentNumber: optional(string()),
	gender: optional(string(), GenderTypes.Male),
	dateOfBirth: optional(string()),
	avatarUrl: string()
})

export type IAppUserMetadata = InferInput<typeof AppUserMetadataSchema>

export const UserMetadataSchema = object({
	businessName: string(),
	businessEmail: string(),
	organizationId: string(),
	businessAddress: string()
})

export type IUserMetadata = InferInput<typeof UserMetadataSchema>

export const TeamPlans = {
	Free: 'Free',
	Pro: 'Pro',
	Enterprise: 'Enterprise'
} as const
export const teamPlans = objectValuesToArray(TeamPlans)
export type TeamPlan = (typeof TeamPlans)[keyof typeof TeamPlans]

export const TeamLocationStatuses = {
	Active: 'Active',
	InActive: 'InActive'
} as const
export const teamLocationStatuses = objectValuesToArray(TeamLocationStatuses)
export type TeamLocationStatus = (typeof TeamLocationStatuses)[keyof typeof TeamLocationStatuses]



export const TeamRoles = {
	TeamAdmin: 'team-admin',
	TeamLocationAdmin: 'team-location-admin',
	TeamMember: 'team-member'
} as const

export const WorkDays = {
	Sunday: 'Sunday',
	Monday: 'Monday',
	Tuesday: 'Tuesday',
	Wednesday: 'Wednesday',
	Thursday: 'Thursday',
	Friday: 'Friday',
	Saturday: 'Saturday'
} as const

export const workDays = objectValuesToArray(WorkDays)
export type WorkDay = (typeof WorkDays)[keyof typeof WorkDays]

export const UserAccountTypes = {
	Staff: 'Staff',
	Customer: 'Customer'
} as const
export const userAccountTypes = objectValuesToArray(UserAccountTypes)
export type UserAccountType = (typeof UserAccountTypes)[keyof typeof UserAccountTypes]


export const WeekDayNumber = {
	Sunday: 0,
	Monday: 1,
	Tuesday: 2,
	Wednesday: 3,
	Thursday: 4,
	Friday: 5,
	Saturday: 6
}
export const weekDayNumbers = objectValuesToArray(WeekDayNumber)
export type WeekDayNumber = (typeof WeekDayNumber)[keyof typeof WeekDayNumber]

export const DaysOfWeek = {
	Sunday: 'Sunday',
	Monday: 'Monday',
	Tuesday: 'Tuesday',
	Wednesday: 'Wednesday',
	Thursday: 'Thursday',
	Friday: 'Friday',
	Saturday: 'Saturday'
} as const
export const daysOfTheWeek = objectValuesToArray(DaysOfWeek)
export type DayOfWeek = (typeof DaysOfWeek)[keyof typeof DaysOfWeek]

export const BankAccountTypes = {
	Check: 'Check', Savings: 'Savings', Investment: 'Investment'
}
export const bankAccountTypes = objectValuesToArray(BankAccountTypes)
export type BankAccountType = (typeof BankAccountTypes)[keyof typeof BankAccountTypes]
