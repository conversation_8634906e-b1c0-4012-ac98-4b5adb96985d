import { boolean, type InferInput, object, optional, picklist, string, unknown } from 'valibot'

export const dynamicModalSchema = object({
      title: string('Required'),
      description: string('Required'),
      componentName: string('Required'),
      componentProps: optional(unknown()),
      canClose: optional(boolean('Required'), true),
      color: picklist([
            'primary',
            'gray',
            'red',
            'yellow',
            'green',
            'indigo',
            'purple',
            'pink',
            'blue',
            'light',
            'dark',
            'default',
            'dropdown',
            'navbar',
            'navbarUl',
            'form',
            'none'
      ]),
      size: picklist(['xl', 'lg', 'md'])
})

export type IDynamicModal = InferInput<typeof dynamicModalSchema>
export const ToastTypeSchema = picklist(['success', 'error', 'info', 'warning'])

export type ToastType = InferInput<typeof ToastTypeSchema>

export const dynamicSheetSchema = object({
      ...dynamicModalSchema.entries,
      position: picklist(['left', 'right', 'top', 'bottom']),
      side: picklist(['right', 'top', 'bottom', 'left'])
})

export type IDynamicSheet = InferInput<typeof dynamicSheetSchema>

export const SheetTypes = {
      Marketing: 'Marketing',
      SignUpSuccess: 'SignUpSuccess',
      Login: 'Login',
      SignUp: 'SignUp',
      SignUpCode: 'SignUpCode',
      ForgotPass: 'ForgotPass',
      ResetPass: 'ResetPass',
      ResetPassCode: 'ResetPassCode',
      LoginCode: 'LoginCode'
} as const

export type SheetType = (typeof SheetTypes)[keyof typeof SheetTypes]

export type IComponentCache = {
      componentName: string
      component: any
      dialogClass: string
}

export const CalendarViewTypes = {
      Day: 'Day',
      Week: 'Week',
      Month: 'Month'
} as const

export type CalendarViewType = (typeof CalendarViewTypes)[keyof typeof CalendarViewTypes]

export const InboxMessageTypes = {
      VerifyEmailDomain: 'VerifyEmailDomain'
}

export type InboxMessageType = (typeof InboxMessageTypes)[keyof typeof InboxMessageTypes]

export const TeamMemberStatuses = {
      Active: 'Active',
      InActive: 'Inactive',
      OnLeave: 'On Leave',
      Resigned: 'Resigned'
} as const
export type TeamMemberStatus = (typeof TeamMemberStatuses)[keyof typeof TeamMemberStatuses]



export const ActorEvents = {
      ItemCreated: 'item.created',
      ItemUpdated: 'item.updated',
      ItemDeleted: 'item.deleted'
} as const
export type ActorEvent = (typeof ActorEvents)[keyof typeof ActorEvents]

export const PostgresEvents = {
      ItemCreated: 'postgres.item.created',
      ItemUpdated: 'postgres.item.updated',
      ItemDeleted: 'postgres.item.deleted',
      CollectionUpdated: 'postgres.collection.updated'
} as const
export type PostgresEvent = (typeof PostgresEvents)[keyof typeof PostgresEvents]
