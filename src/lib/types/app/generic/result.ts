import { stringify } from 'superjson'

export type Result<T> =
	| {
		data: T
		error: undefined
		isOk: true
	}
	| {
		data: null
		error: string
		isOk: false
	}

export const Result = {
	success: <T>(data: T): Result<T> => {
		return { data, error: undefined, isOk: true }
	},

	failure: <T>(error: Error): Result<T> => {
		console.log('error log: ', stringify(error)) //to use with logging tools
		return { data: null, error: error?.message, isOk: false }
	}
}

export type Prettify<T> = {
	[K in keyof T]: T[K]
} & {}