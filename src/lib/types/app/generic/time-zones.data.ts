// This is a constant array of timezones that should be used throughout the application
// It is exported once and should be imported from this file only

export const TIMEZONES = [
	{
		id: 'UTC',
		name: 'UTC'
	},
	{
		id: 'America/New_York',
		name: 'America/New_York'
	},
	{
		id: 'America/Chicago',
		name: 'America/Chicago'
	},
	{
		id: 'America/Denver',
		name: 'America/Denver'
	},
	{
		id: 'America/Los_Angeles',
		name: 'America/Los_Angeles'
	},
	{
		id: 'Europe/London',
		name: 'Europe/London'
	},
	{
		id: 'Europe/Paris',
		name: 'Europe/Paris'
	},
	{
		id: 'Europe/Berlin',
		name: 'Europe/Berlin'
	},
	{
		id: 'Europe/Moscow',
		name: 'Europe/Moscow'
	},
	{
		id: 'Asia/Dubai',
		name: 'Asia/Dubai'
	},
	{
		id: 'Asia/Singapore',
		name: 'Asia/Singapore'
	},
	{
		id: 'Asia/Tokyo',
		name: 'Asia/Tokyo'
	},
	{
		id: 'Australia/Sydney',
		name: 'Australia/Sydney'
	},
	{
		id: 'Pacific/Auckland',
		name: 'Pacific/Auckland'
	},
	{
		id: 'Africa/Lagos',
		name: 'Africa/Lagos (Abuja)'
	},
	{
		id: 'Africa/Johannesburg',
		name: 'Africa/Johannesburg (Pretoria)'
	},
	{
		id: 'Africa/Nairobi',
		name: 'Africa/Nairobi'
	}
] as const

export type TimeZoneType = (typeof TIMEZONES)[number]
