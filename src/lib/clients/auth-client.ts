import { createAuthClient, type Error<PERSON>ontext } from "better-auth/svelte"
import { passkeyClient, emailOTPClient, inferAdditionalFields, organizationClient, adminClient, inferOrgAdditionalFields } from "better-auth/client/plugins"
import { ac, roles } from '$/lib'
import { checkWebAuthnSupport } from '../authn.check'
import { toast } from 'svelte-sonner'
import type { BetterAuthType } from '$/server'
import { uuidv7 } from 'uuidv7'


export const authClient = createAuthClient({
  plugins: [
    passkeyClient(), // Passkey authentication
    emailOTPClient(), // Email OTP authentication
    //jwtClient(), // JWT token support
    organizationClient({
      $inferAuth: {} as BetterAuthType,
      teams: {
        enabled: true
      },
      ac: ac as any,
      roles,
      schema: inferOrgAdditionalFields({
        organization: {
          additionalFields: {
            businessEmail: {
              type: "string",
              required: true,
              input: true
            },
            businessAddress: {
              type: "string",
              required: false,
              input: true
            },
            id: {
              type: "string",
              required: false,
              input: true
            }
          }
        }
      })
    }), // Organization management with teams and custom roles
    adminClient({
      ac: ac as any,
      roles
    }), // Admin plugin for user management
    //sessionDataClientPlugin(), // Session data plugin
    inferAdditionalFields({
      user: {
        roles: { type: "string[]" },
        // Core user info
        givenName: { type: "string" },
        familyName: { type: "string" },
        displayName: { type: "string" },
        // Contact info
        phoneNumber: { type: "string" },
        phoneNumberVerified: { type: "boolean" },
        // Profile info
        image: { type: "string" },
        avatarUrl: { type: "string" },
        // Status
        userStatus: { type: "string" },
        userType: { type: "string" },
        acceptTerms: { type: "boolean" },
        authType: { type: "string" },
        metadata: { type: "string" },
        gender: { type: "string" }

      },
      session: {
        activeOrganizationId: { type: "string" },
        impersonatedBy: { type: "string" }
      }
    })
  ]
})

export const {
  signIn,
  signUp,
  signOut,
  useSession,
  getSession
} = authClient



export const setupPasskey = async () => {
  const webAuthNSupport = await checkWebAuthnSupport()
  if (webAuthNSupport.isAvailable) {
    const passkeyList = await authClient.passkey.listUserPasskeys()
    if (passkeyList?.data && passkeyList.data.length > 0) return true
    await authClient.passkey.addPasskey({
      authenticatorAttachment: 'cross-platform',
      name: `Spendeed-${uuidv7()}`
    }, {
      onSuccess: () => {
        toast.success('Passkey registered successfully!')
      },
      onError: (error: ErrorContext) => {
        console.warn('Passkey registration failed:', error?.error.message)
        toast.warning(
          'Passkey registration failed. You can set it up later in settings.'
        )
      }
    })
  } else {
    console.log('Passkey not supported on this device.')
    return true
  }
}
