<script lang="ts">
	import * as Form from '$/components/ui/form'
	import * as Dialog from '$/components/ui/dialog'
	import { Button } from '$/components/ui/button'
	import { Input } from '$/components/ui/input'
	import { Label } from '$/components/ui/label'
	import { Switch } from '$/components/ui/switch'
	import * as Select from '$/components/ui/select'
	import {
		createAppointmentTypeFormSchema,
		type AppointmentRepeatType,
		appointmentRepeatTypes,
		AppointmentTypeController,
		AppointmentType
	} from '$/lib'
	import { superForm, defaults } from 'sveltekit-superforms'
	import { valibotClient } from 'sveltekit-superforms/adapters'
	import { FormAlert } from '$/components/common'
	import { getFlashModule } from '$/lib/helpers'
	import { TIMEZONES } from '$/lib'
	import 'linq-extensions'

	let { selectedType, showEditDialog }: { selectedType: any; showEditDialog: boolean } = $props()
	const form = superForm(defaults(selectedType, valibotClient(createAppointmentTypeFormSchema)), {
		validators: valibotClient(createAppointmentTypeFormSchema),
		SPA: true,
		clearOnSubmit: 'errors-and-message',
		multipleSubmits: 'prevent',
		async onUpdate({ form }) {
			try {
				if (!form.valid) {
					form.message = {
						status: 'error',
						text: 'validation failed'
					}
					return
				}

				const selectedTimeZone = TIMEZONES.find(
					(k) => k.id === (form.data.timeZoneId! as string)
				)
				const appointmentTypeData: Partial<AppointmentType> = {
					appointmentDetail: form.data.name,
					durationInMinutes: form.data.durationInMinutes,
					isActive: form.data.isActive,
					isRepeatable: form.data.isRepeatEnabled,
					repeatType: form.data.repeatInterval as AppointmentRepeatType,
					requiresUpfrontPayment: form.data.requiresUpfrontPayment,
					upfrontPaymentAmount: form.data.requiresUpfrontPayment
						? form.data.upfrontPaymentAmount
						: 0,
					timezoneId: selectedTimeZone?.id
				}
				const updateResp = await AppointmentTypeController.update(
					selectedType.id,
					appointmentTypeData
				)
				if (!updateResp.id) throw new Error('Failed to update appointment type')

				form.message = {
					status: 'success',
					text: 'Appointment type updated successfully'
				}
			} catch (error) {
				console.error(error)
				form.message = {
					status: 'error',
					text: 'Failed to update appointment type'
				}
			}
		},
		...getFlashModule()
	})
	const { form: formData, enhance, message } = form

	$inspect(selectedType)
</script>

<Dialog.Root bind:open={showEditDialog}>
	<Dialog.Content class="sm:max-w-[425px]" showCloseButton={false} interactOutsideBehavior="close">
		<Dialog.Header>
			<Dialog.Title>Edit Event Type</Dialog.Title>
			<Dialog.Description>
				Make changes to the event type. Click save when you're done.
			</Dialog.Description>
		</Dialog.Header>
		<form method="POST" use:enhance>
			<FormAlert {message} />
			<Form.Field {form} name="name">
				<Form.Control>
					{#snippet children({ props })}
						<Label for="name">Name</Label>
						<Input {...props} bind:value={$formData.name} />
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>
			<Form.Field {form} name="durationInMinutes">
				<Form.Control>
					{#snippet children({ props })}
						<Label for="duration">Duration (minutes)</Label>
						<Input type="number" {...props} bind:value={$formData.durationInMinutes} />
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>

			<Form.Field {form} name="isActive">
				<Form.Control>
					{#snippet children({ props })}
						<div class="flex items-center gap-2">
							<Label for="active">Active</Label>
							<Switch {...props} bind:checked={$formData.isActive} />
						</div>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>

			<Form.Field {form} name="isRepeatEnabled">
				<Form.Control>
					{#snippet children({ props })}
						<div class="flex items-center gap-2">
							<Label for="repeat">Enable Repeat</Label>
							<Switch {...props} bind:checked={$formData.isRepeatEnabled} />
						</div>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>

			{#if $formData.isRepeatEnabled}
				<Form.Field {form} name="repeatInterval">
					<Form.Control>
						{#snippet children({ props })}
							<Label for="repeatInterval">Repeat Interval</Label>
							<Select.Root type="single" bind:value={$formData.repeatInterval}>
								<Select.Trigger class="w-full" {...props}>
									{$formData.repeatInterval
										? $formData.repeatInterval
										: 'Select a repeat interval'}
								</Select.Trigger>
								<Select.Content>
									{#each Object.values(appointmentRepeatTypes) as type (type)}
										<Select.Item value={type as string}>{type}</Select.Item>
									{/each}
								</Select.Content>
							</Select.Root>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>
			{/if}

			<Dialog.Footer>
				<Button variant="outline" onclick={() => (showEditDialog = false)}>Cancel</Button>
				<Button type="submit">Save changes</Button>
			</Dialog.Footer>
		</form>
	</Dialog.Content>
</Dialog.Root>
