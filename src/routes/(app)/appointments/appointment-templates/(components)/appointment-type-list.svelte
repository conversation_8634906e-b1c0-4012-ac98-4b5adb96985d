<script lang="ts">
	import { Input } from '$/components/ui/input'
	import { Button, buttonVariants } from '$/components/ui/button'
	import { Switch } from '$/components/ui/switch'
	import { MoreHorizontal, Link2, Calendar, Delete } from '@lucide/svelte'
	import * as DropdownMenu from '$/components/ui/dropdown-menu'
	import { Card } from '$/components/ui/card'
	import * as Form from '$/components/ui/form'
	import { getFlashModule } from '$/lib/helpers'
	import { superForm } from 'sveltekit-superforms'
	import { valibotClient } from 'sveltekit-superforms/adapters'
	import { AppointmentTypeController, deleteAppointmentTypeFormSchema } from '$/lib'
	import { FormAlert, HiddenInput } from '$/components/common'
	import { default as CreateNewAppointmentType } from './create-appointment-type.svelte'
	import { default as EditAppointmentType } from './edit-appointment-type.svelte'
	import { AppointmentType } from '$/lib'
	import { remult } from 'remult'

	let { deleteAppointmentTypeForm, createAppointmentTypeForm } = $props()
	let showCreateDialog = $state(false)
	let showEditDialog = $state(false)
	let selectedType = $state(null)
	let searchQuery = $state('')

	// Entity data using liveQuery
	let appointmentTemplates = $state<AppointmentType[]>([])

	// Subscribe to appointment types
	$effect(() => {
		const unsubscribe = remult
			.repo(AppointmentType)
			.liveQuery({
				where: { organizationId: remult.user?.organizationId },
				orderBy: { appointmentDetail: 'asc' }
			})
			.subscribe((info) => {
				appointmentTemplates = info.items
			})

		return () => {
			unsubscribe()
		}
	})
	// Mock data for appointment types

	const deleteForm = superForm(deleteAppointmentTypeForm, {
		validators: valibotClient(deleteAppointmentTypeFormSchema),
		SPA: true,
		clearOnSubmit: 'errors-and-message',
		multipleSubmits: 'prevent',
		async onUpdate({ form }) {
			try {
				if (!form.valid) return
				const { data: formData } = form
				const appointmentType = appointmentTemplates?.find((at) => at.id === formData.id)
				if (!appointmentType) throw new Error('Appointment type not found')
				await AppointmentTypeController.deleteById(formData.id)
				form.message = {
					status: 'success',
					message: 'Appointment type deleted successfully'
				}
			} catch (error) {
				console.log(error)
				form.message = {
					status: 'error',
					message: `Failed to delete appointment type: ${(error as Error).message}`
				}
			}
		},
		...getFlashModule()
	})
	const { enhance, message, submitting } = deleteForm
	const filteredAppointmentTypes = $derived.by(() => {
		return (
			appointmentTemplates?.filter((type) => {
				const query = searchQuery.toLowerCase()
				return type.appointmentDetail?.toLowerCase().includes(query)
			}) ?? []
		)
	})

	const handleTypeClick = (type) => {
		selectedType = { ...type }
		showEditDialog = true
	}
</script>

<div class="container mx-auto space-y-6 p-4">
	<div class="flex items-center justify-between">
		<h2 class="text-2xl font-semibold">Event Types</h2>
		<div class="flex items-center gap-4">
			<Input
				type="search"
				placeholder="Search..."
				class="w-[150px] lg:w-[250px]"
				bind:value={searchQuery}
			/>
			<Button onclick={() => (showCreateDialog = true)}>Create Appointment Type</Button>
		</div>
	</div>

	{#if filteredAppointmentTypes.length > 0}
		<FormAlert {message} />
		<div class="grid gap-4">
			{#each filteredAppointmentTypes as type (type.id)}
				<Card
					class="hover:bg-muted/50 cursor-pointer p-4"
					onclick={() => handleTypeClick(type)}
				>
					<div class="flex items-center justify-between">
						<div class="flex items-center gap-4">
							<div>
								<h3 class="font-medium">{type.appointmentDetail}</h3>
								<div class="text-muted-foreground flex items-center gap-1 text-sm">
									{type.durationInMinutes}
									<span class="px-1">•</span>
									{type.isRepeatable ? 'Repeats' : 'Does not repeat'}
								</div>
								<div class="text-muted-foreground flex items-center gap-1 text-sm">
									{type.isActive ? 'Active' : 'Inactive'}
								</div>
							</div>
						</div>
						<div class="flex items-center gap-2">
							<Switch checked={type.isActive ?? false} disabled={true} />

							<DropdownMenu.Root>
								<DropdownMenu.Trigger class={buttonVariants({ variant: 'outline' })}>
									<MoreHorizontal class="size-4" />
								</DropdownMenu.Trigger>
								<DropdownMenu.Content>
									<DropdownMenu.Group>
										<DropdownMenu.Item>
											<Link2 class="mr-2 size-4" />
											Copy Link
										</DropdownMenu.Item>
										<DropdownMenu.Item>
											<Calendar class="mr-2 size-4" />
											View Bookings
										</DropdownMenu.Item>
									</DropdownMenu.Group>
									<DropdownMenu.Separator />
									<DropdownMenu.Item
										class="text-destructive focus:bg-destructive focus:text-destructive-foreground"
									>
										<form method="POST" use:enhance>
											<Form.Field form={deleteForm} name="id">
												<Form.Control>
													{#snippet children({ props })}
														<HiddenInput {props} value={type.id} />
													{/snippet}
												</Form.Control>
											</Form.Field>
											<Button
												variant="destructive"
												size="icon"
												type="submit"
												disabled={$submitting || !type.id}
											>
												<Delete class="size-4 pr-1" /> Delete
											</Button>
										</form>
									</DropdownMenu.Item>
								</DropdownMenu.Content>
							</DropdownMenu.Root>
						</div>
					</div>
				</Card>
			{/each}
		</div>
	{:else}
		<Card class="flex flex-col items-center justify-center p-8">
			<div class="flex flex-col items-center justify-center gap-2">
				<h3 class="text-lg font-medium">No event types found</h3>
				<p class="text-muted-foreground text-sm">
					{searchQuery
						? 'Try adjusting your search terms or clear the search'
						: 'Create your first event type to start accepting bookings'}
				</p>
				<Button onclick={() => (showCreateDialog = true)}>Create Appointment Type</Button>
			</div>
		</Card>
	{/if}
</div>

<CreateNewAppointmentType {createAppointmentTypeForm} showDialog={showCreateDialog} />
<EditAppointmentType {selectedType} {showEditDialog} />
