<script lang="ts">
	import { Button } from '$/components/ui/button'
	import { Calendar } from '$/components/ui/calendar'
	import { Label } from '$/components/ui/label'
	import { getAuthStore, getUtilsStore } from '$/stores'
	import {
		AppointmentController,
		appointmentStatuses,
		rescheduleAppointmentFormSchema,
		type RescheduleAppointmentFormType
	} from '$/lib'
	import * as Dialog from '$/components/ui/dialog'
	import * as Select from '$/components/ui/select'
	import { DateFormatter, getLocalTimeZone, today } from '@internationalized/date'

	import { superForm } from 'sveltekit-superforms'
	import { valibotClient } from 'sveltekit-superforms/adapters'
	import { FormAlert, HiddenInput } from '$/components/common'
	import * as Form from '$/components/ui/form'
	import * as Popover from '$/components/ui/popover'
	import { CalendarIcon } from '@lucide/svelte'
	import { cn } from '$/lib/shadcn.utils'
	import type { Appointment } from '$/lib'

	let {
		selectedAppointment,
		isDialogOpen,
		rescheduleAppointmentForm
	}: {
		selectedAppointment: Appointment | null
		isDialogOpen: boolean
		rescheduleAppointmentForm: RescheduleAppointmentFormType
	} = $props()
	const authStore = getAuthStore()
	const form = superForm(rescheduleAppointmentForm, {
		validators: valibotClient(rescheduleAppointmentFormSchema),
		SPA: true,
		clearOnSubmit: 'errors-and-message',
		multipleSubmits: 'prevent',
		async onUpdate({ form }) {
			try {
				if (!form.valid) return
				const { data: formData } = form

				if (selectedAppointment) {
					const updateResp = await AppointmentController.update(selectedAppointment.id, {
						id: selectedAppointment?.id,
						appointmentDate: formData.appointmentDate,
						startTime: formData.appointmentStartTime,
						updatedBy: authStore.user?.id,
						updatedAt: new Date()
					})
					if (!updateResp.id) throw new Error('Failed to update appointment')
				}
				form.message = {
					status: 'success',
					message: 'Appointment rescheduled successfully'
				}
			} catch (error) {
				console.error(error)
				form.message = {
					status: 'error',
					message: 'Failed to reschedule appointment'
				}
			}
		}
	})
	const { form: formData, enhance, submitting, message } = form
	const df = new DateFormatter('en-US', {
		dateStyle: 'long'
	})

	const utilsStore = getUtilsStore()
	let isRescheduling = $state(false)
	let isConfirming = $state(false)

	const valueString = $derived(
		$formData.appointmentDate
			? df.format($formData.appointmentDate.toDate(getLocalTimeZone()))
			: ''
	)

	const items = [
		{ value: 0, label: 'Today' },
		{ value: 1, label: 'Tomorrow' },
		{ value: 3, label: 'In 3 days' },
		{ value: 7, label: 'In a week' }
	]
	const startRescheduling = () => {
		isRescheduling = true
	}

	const generateTimeOptions = () => {
		const options: string[] = []
		for (let hours = 0; hours < 24; hours++) {
			for (let minutes = 0; minutes < 60; minutes += 30) {
				const time = new Date(2000, 0, 1, hours, minutes)
				options.push(time.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }))
			}
		}
		return options
	}

	let timeOptions = generateTimeOptions()
	const showConfirmationButton = $derived(
		selectedAppointment?.appointmentStatus === appointmentStatuses.Confirmed.toString() &&
			!isRescheduling
	)
	const confirmAppointment = async () => {
		isConfirming = true
		const confirmResp = await AppointmentController.updateStatus(
			selectedAppointment?.id!,
			appointmentStatuses.Confirmed
		)
		if (!confirmResp.id) {
			utilsStore.toastError('Failed to confirm appointment, please try again')
		} else {
			isConfirming = false
			//TODO: signal workflow to update appointment status
			utilsStore.toastSuccess('Appointment confirmed')
		}
	}
</script>

<Dialog.Root bind:open={isDialogOpen}>
	<Dialog.Content showCloseButton={false}>
		<Dialog.Header>
			<Dialog.Title>Appointment Details</Dialog.Title>
		</Dialog.Header>
		{#if selectedAppointment}
			<div class="grid gap-4 py-4">
				<div class="grid grid-cols-4 items-center gap-4">
					<Label class="text-right">Customer</Label>
					<div class="col-span-3">{selectedAppointment.customerName}</div>
				</div>
				<div class="grid grid-cols-4 items-center gap-4">
					<Label class="text-right">Date</Label>
					<div class="col-span-3">{selectedAppointment.appointmentDate}</div>
				</div>
				<div class="grid grid-cols-4 items-center gap-4">
					<Label class="text-right">Date</Label>
					<div class="col-span-3">{selectedAppointment.startTime}</div>
				</div>
				<div class="grid grid-cols-4 items-center gap-4">
					<Label class="text-right">Time</Label>
					<div class="col-span-3">{selectedAppointment.endTime}</div>
				</div>
				<div class="grid grid-cols-4 items-center gap-4">
					<Label class="text-right">Status</Label>
					<div class="col-span-3">{selectedAppointment.appointmentStatus}</div>
				</div>
				<div class="grid grid-cols-4 items-center gap-4">
					<Label class="text-right">Email</Label>
					<div class="col-span-3">{selectedAppointment.customerEmail}</div>
				</div>
				<div class="grid grid-cols-4 items-center gap-4">
					<Label class="text-right">Phone</Label>
					<div class="col-span-3">{selectedAppointment.customerPhoneNumber}</div>
				</div>
			</div>
			{#if isRescheduling}
				<form method="post" use:enhance>
					<FormAlert {message} />
					<Form.Field {form} name="appointmentId">
						<Form.Control>
							{#snippet children({ props }: any)}
								<HiddenInput {props} value={selectedAppointment?.id} />
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
					<fieldSet disabled={$submitting} class="grid gap-4 py-4">
						<Form.Field
							{form}
							name="appointmentDate"
							class="grid grid-cols-4 items-center gap-4"
						>
							<Form.Control>
								{#snippet children({ props }: any)}
									<Form.Label class="text-right">New Date</Form.Label>
									<Popover.Root>
										<Popover.Trigger>
											{#snippet child({ props })}
												<Button
													variant="outline"
													class={cn(
														'w-[240px] justify-start text-left font-normal',
														!$formData.appointmentDate && 'text-muted-foreground'
													)}
													{...props}
												>
													<CalendarIcon />
													{$formData.appointmentDate
														? df.format(
																$formData.appointmentDate.toDate(getLocalTimeZone())
															)
														: 'Pick a date'}
												</Button>
											{/snippet}
										</Popover.Trigger>
										<Popover.Content class="w-auto p-0" align="start">
											<Select.Root
												type="single"
												value={valueString}
												onValueChange={(v) => {
													if (!v) return
													$formData.appointmentDate = today(getLocalTimeZone()).add({
														days: Number.parseInt(v)
													})
												}}
											>
												<Select.Trigger>
													{valueString}
												</Select.Trigger>
												<Select.Content>
													{#each items as item (item.value)}
														<Select.Item value={`${item.value}`}
															>{item.label}</Select.Item
														>
													{/each}
												</Select.Content>
											</Select.Root>
											<div class="rounded-md border">
												<Calendar
													type="single"
													bind:value={$formData.appointmentDate}
												/>
											</div>
										</Popover.Content>
									</Popover.Root>
									Copy Installation
								{/snippet}
							</Form.Control>

							<div class="col-span-3">
								<Calendar
									type="single"
									bind:value={$formData.appointmentDate}
									class="border shadow"
								/>
							</div>
						</Form.Field>
						<Form.Field
							{form}
							name="appointmentStartTime"
							class="grid grid-cols-4 items-center gap-4"
						>
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label class="text-right">New Time</Form.Label>
									<Select.Root
										type="single"
										bind:value={$formData.appointmentStartTime}
										name={props.name}
									>
										<Select.Trigger {...props}>
											{$formData.appointmentStartTime
												? $formData.appointmentStartTime
												: 'Select a time slot'}
										</Select.Trigger>

										<Select.Content>
											{#each timeOptions as time (time)}
												<Select.Item value={time}>{time}</Select.Item>
											{/each}
										</Select.Content>
									</Select.Root>
								{/snippet}
							</Form.Control>
						</Form.Field>
					</fieldSet>
				</form>
			{/if}
			<Dialog.Footer>
				{#if showConfirmationButton}
					<Button disabled={isConfirming} onclick={confirmAppointment}>Confirm</Button>
				{/if}
				{#if !isRescheduling}
					<Button onclick={startRescheduling}>Reschedule</Button>
				{:else}
					<Button disabled={$submitting}>Confirm Reschedule</Button>
				{/if}
				<Button variant="outline" onclick={() => (isDialogOpen = false)}>Close</Button>
			</Dialog.Footer>
		{/if}
	</Dialog.Content>
</Dialog.Root>
