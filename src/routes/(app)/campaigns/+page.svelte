<script lang="ts">
	import { CardShell } from '$/components/layout'
	import { But<PERSON> } from '$/components/ui/button'
	import { Plus } from '@lucide/svelte'

	// Action handlers
	const handleCreateCampaign = () => {
		console.log('Create new campaign')
	}
</script>

<CardShell
	heading="Marketing Campaigns"
	subHeading="Create and manage marketing campaigns to drive customer engagement"
>
	{#snippet rightSlot()}
		<Button onclick={handleCreateCampaign} class="gap-2">
			<Plus class="h-4 w-4" />
			Create Campaign
		</Button>
	{/snippet}

	<div class="space-y-6">
		<!-- Placeholder for campaign components -->
		<div class="text-muted-foreground py-12 text-center">
			<p>Create, manage, and track the performance of your marketing campaigns.</p>
		</div>
	</div>
</CardShell>
