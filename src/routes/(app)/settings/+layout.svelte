<script lang="ts">
	import type { LayoutData } from './$types'
	import { DynamicModal } from '$/components/common'
	import type { IComponentCache } from '$/lib'
	import { AddTeamMember } from './staff/(components)'
	import { AddBankDetails, AddPaymentProvider } from './payment-engine/(components)'

	let { children }: { data: LayoutData; children: any } = $props()

	const componentsCollection = $state<IComponentCache[]>([
		{
			componentName: 'AddTeamMember',
			component: AddTeamMember,
			dialogClass: 'md:min-w-[920px]'
		},
		{
			componentName: 'AddBankDetails',
			component: AddBankDetails,
			dialogClass: 'md:min-w-[620px]'
		},
		{
			componentName: 'AddPaymentProvider',
			component: AddPaymentProvider,
			dialogClass: 'md:min-w-[620px]'
		}
	])
</script>

<div class="space-y-6">
	<div class="flex items-center justify-between">
		<h2 class="text-3xl font-bold tracking-tight">Settings</h2>
	</div>
	<div class="flex-1">
		{@render children?.()}
	</div>
</div>
<DynamicModal {componentsCollection} />
