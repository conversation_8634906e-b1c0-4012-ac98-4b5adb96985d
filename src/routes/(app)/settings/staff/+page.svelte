<script lang="ts">
	import { AppShell, AppShellHeader } from '$/components/layout'

	import * as Card from '$/components/ui/card'
	import { defaults } from 'sveltekit-superforms/client'
	import { valibotClient } from 'sveltekit-superforms/adapters'
	import { addMemberFormSchema } from '$/lib'
	import { StaffList } from './(components)'

	const heading = $state('Staff Manager')
	const subHeading = $state(
		'Manage all Team members/Employee Data. Change State, Add new , Edit and Disable Team members'
	)

	const addTeamMemberForm = defaults({} as any, valibotClient(addMemberFormSchema))
</script>

<AppShell>
	<AppShellHeader {heading} {subHeading} />
	<div class="grid gap-10">
		<Card.Root class="rounded-none">
			<Card.Header class="px-7">
				<Card.Title>Team Members</Card.Title>
				<Card.Description>Setup and Manage Team Members</Card.Description>
			</Card.Header>
			<Card.Content>
				<StaffList {addTeamMemberForm} />
			</Card.Content>
		</Card.Root>
	</div>
</AppShell>
