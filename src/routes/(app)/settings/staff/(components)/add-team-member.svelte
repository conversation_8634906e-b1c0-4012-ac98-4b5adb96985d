<script lang="ts">
	import { defaults, superForm } from 'sveltekit-superforms/client'
	import {
		addMemberFormSchema,
		type AddMemberFormSchemaType,
		authClient,
		type FormMessage,
		GenderTypes,
		RolesType
	} from '$/lib'
	import * as Form from '$/components/ui/form'
	import * as Card from '$/components/ui/card'
	import * as Select from '$/components/ui/select'
	import { Button } from '$/components/ui/button'
	import { Input } from '$/components/ui/input'
	import { getAuthStore, getUtilsStore } from '$/stores'
	import { FormAlert } from '$/components/common'
	import { valibotClient } from 'sveltekit-superforms/adapters'
	import { getFlashModule } from '$/lib/helpers'
	import { Loader2 } from '@lucide/svelte'
	import { onMount } from 'svelte'
	import type { MemberInvite, User } from '$/lib'
	import { Organization, Team } from '$/lib/models'
	import { remult } from 'remult'
	import 'linq-extensions'

	let { data = {} }: { data?: Partial<User> } = $props()
	const authStore = getAuthStore()

	let org = $state<Organization | undefined>(undefined)
	let teams = $state<Team[]>([])

	$effect(() => {
		const orgUnsubscribe = remult
			.repo(Organization)
			.liveQuery({
				where: { id: remult.user?.organizationId }
			})
			.subscribe((info) => {
				org = info.items.firstOrNull() ?? undefined
			})

		const teamsUnsubscribe = remult
			.repo(Team)
			.liveQuery({
				where: { organizationId: remult.user?.organizationId },
				orderBy: { name: 'asc' }
			})
			.subscribe((info) => {
				teams = info.items
			})

		return () => {
			orgUnsubscribe()
			teamsUnsubscribe()
		}
	})
	const form = superForm<AddMemberFormSchemaType, FormMessage>(
		defaults(data as any, valibotClient(addMemberFormSchema)),
		{
			SPA: true,
			resetForm: true,
			clearOnSubmit: 'errors-and-message',
			validators: valibotClient(addMemberFormSchema),
			async onUpdate({ form }) {
				try {
					const { data: formData } = form
					//member invite handle with betterauth

					const teamLocation = teams.firstOrNull((k: Team) => k.id === formData.location)
					const { data, error } = await authClient.organization.inviteMember({
						email: formData.email,
						role: formData.role as any,
						organizationId: org?.id,
						teamId: teamLocation?.id,
						resend: true
					})
					if (error) {
						throw error
					}
					console.log('member invite response data', data)
					form.message = {
						text: 'Team member invite sent successfully!',
						status: 'success'
					}
				} catch (error) {
					console.error(error)
					form.message = {
						text: 'Failed to send team member invite!',
						status: 'error'
					}
				}
			},
			...getFlashModule()
		}
	)

	const { form: formData, enhance, submitting, allErrors, message, validateForm } = form

	let utilsStore = getUtilsStore()
	const capitalizeWords = (word: string): string => {
		return word
			.split('-')
			.map((w) => w.charAt(0).toUpperCase() + w.slice(1))
			.join(' ')
	}

	onMount(() => {
		if (data?.id) validateForm?.({ update: true })
	})
</script>

<form method="POST" use:enhance>
	<FormAlert {message} />
	<Card.Root class="rounded-none">
		<Card.Content class=" mb-4 grid grid-cols-1 gap-3  md:grid-cols-3">
			<Form.Field {form} name="givenName">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>First Name</Form.Label>
						<Input {...props} placeholder="First Name" bind:value={$formData.givenName} />
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>
			<Form.Field {form} name="familyName">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>First Name</Form.Label>
						<Input {...props} placeholder="First Name" bind:value={$formData.familyName} />
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>
			<Form.Field {form} name="email">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>Email</Form.Label>
						<Input {...props} placeholder="Email" bind:value={$formData.email} />
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>
			<Form.Field {form} name="phoneNumber">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>Phone No.</Form.Label>
						<Input {...props} placeholder="Phone Number" bind:value={$formData.phoneNumber} />
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>
			<Form.Field {form} name="idDocumentNumber">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>ID Number</Form.Label>
						<Input
							{...props}
							placeholder="ID Number"
							bind:value={$formData.idDocumentNumber}
						/>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>
			<Form.Field {form} name="gender">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>Gender</Form.Label>
						<Select.Root type="single" bind:value={$formData.gender} name={props.name}>
							<Select.Trigger {...props}>
								{$formData.gender ? $formData.gender : 'Select a Gender'}
							</Select.Trigger>
							<Select.Content>
								<Select.Item value={GenderTypes.Male} label={GenderTypes.Male} />
								<Select.Item value={GenderTypes.Female} label={GenderTypes.Female} />
								<Select.Item value={GenderTypes.Other} label={GenderTypes.Other} />
							</Select.Content>
						</Select.Root>
						<input name={props.name} value={$formData.gender} hidden />
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>
			<Form.Field {form} name="role">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>Role</Form.Label>
						<Select.Root type="single" bind:value={$formData.role} name={props.name}>
							<Select.Trigger {...props}>
								{$formData.role ? $formData.role : 'Select a Role'}
							</Select.Trigger>
							<Select.Content>
								<Select.Item
									value={RolesType.TeamMember}
									label={capitalizeWords(RolesType.TeamMember)}
								/>
								<Select.Item
									value={RolesType.TeamLocationAdmin}
									label={capitalizeWords(RolesType.TeamLocationAdmin)}
								/>
								<Select.Item
									value={RolesType.OrgAdmin}
									label={capitalizeWords(RolesType.OrgAdmin)}
								/>
							</Select.Content>
						</Select.Root>
						<input name={props.name} value={$formData.gender} hidden />
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>
			<Form.Field {form} name="location">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>Assigned Location</Form.Label>
						<Select.Root type="single" bind:value={$formData.location} name={props.name}>
							<Select.Trigger>
								{$formData.location ? $formData.location : 'Select a Location'}
							</Select.Trigger>
							<Select.Content>
								{#each teams as teamLocation (teamLocation.id)}
									<Select.Item value={teamLocation.id!} label={teamLocation.name} />
								{/each}
							</Select.Content>
						</Select.Root>
						<input name={props.name} value={$formData.gender} hidden />
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>
		</Card.Content>
		<Card.Footer class="flex justify-between">
			<Button
				disabled={$submitting}
				variant="outline"
				class="mt-4 w-1/3"
				onclick={() => utilsStore.clearDynamicModal()}
				>Cancel
			</Button>
			<Form.Button disabled={$allErrors.length > 0 || $submitting} class="mt-4 w-4/10">
				{#if $submitting}
					<Loader2 class="mr-2 size-4 animate-spin" />
				{/if}
				{$submitting ? 'Please wait...' : 'Invite Team Member'}
			</Form.Button>
		</Card.Footer>
	</Card.Root>
</form>
