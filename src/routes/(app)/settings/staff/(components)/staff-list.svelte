<script lang="ts">
	import { NoData } from '$/components/common'
	import { Button } from '$/components/ui/button'
	import type { AddMemberFormSchemaType, IDynamicModal } from '$/lib'
	import { FileText } from '@lucide/svelte'
	import type { SuperValidated } from 'sveltekit-superforms/client'
	import { default as TeamListTable } from './member/team-list-table.svelte'
	import { getUtilsStore } from '$/stores'
	import { TeamMember, Team, User } from '$/lib/models'
	import { remult } from 'remult'

	let { addTeamMemberForm }: { addTeamMemberForm: SuperValidated<AddMemberFormSchemaType> } =
		$props()
	const uStore = getUtilsStore()

	let teamMembers = $state<TeamMember[]>([])
	let teams = $state<Team[]>([])
	let users = $state<User[]>([])

	let staffMembers = $derived.by(() => {
		return teamMembers
			.map((teamMember: TeamMember) => {
				// Find the team/location details
				const location = teams.firstOrNull((team: Team) => team.id === teamMember.teamId)

				// Find the user details
				const profile = users.firstOrNull((u: User) => u.id === teamMember.userId)

				return {
					profile,
					location,
					memberId: teamMember.id
				}
			})
			.filter((member) => member.memberId) // Filter out any members where user wasn't found
	})

	let hasStaffMembers = $derived(staffMembers?.length ?? 0 > 0)
	let searchTerm = $state('')
	let filteredMembers = $derived.by(() => {
		if (!staffMembers || !Array.isArray(staffMembers)) {
			return []
		}

		return staffMembers
			.where(
				(k) =>
					(k.profile?.name?.includes(searchTerm) ?? false) ||
					(k.profile?.email?.includes(searchTerm) ?? false) ||
					(k.profile?.familyName?.includes(searchTerm) ?? false) ||
					(k.location?.name?.includes(searchTerm) ?? false)
			)
			.toArray()
			.map((k) => {
				const { location, ...profile } = k
				return {
					profile,
					location
				}
			})
	})

	$effect(() => {
		const teamMembersUnsub = remult
			.repo(TeamMember)
			.liveQuery({
				where: { teamId: remult.user?.teamId },
				orderBy: { createdAt: 'desc' }
			})
			.subscribe((info) => {
				teamMembers = info.items
			})

		const teamsUnsub = remult
			.repo(Team)
			.liveQuery({
				where: { organizationId: remult.user?.organizationId },
				orderBy: { name: 'asc' }
			})
			.subscribe((info) => {
				teams = info.items
			})

		const usersUnsub = remult
			.repo(User)
			.liveQuery({
				where: { organizationId: remult.user?.organizationId },
				orderBy: { name: 'asc' }
			})
			.subscribe((info) => {
				users = info.items
			})

		return () => {
			teamMembersUnsub()
			teamsUnsub()
			usersUnsub()
		}
	})
	const startCreateTeamMember = () => {
		const newTeamMemberForm: IDynamicModal = {
			canClose: true,
			title: 'Invite new Team Member',
			componentName: 'AddTeamMember',
			size: 'lg',
			color: 'default',
			description: 'Send Team member invite to your team',
			componentProps: {
				addTeamMemberForm
			}
		}
		uStore.openDynamicModal(newTeamMemberForm)
	}
	$inspect('filtered memnbers', filteredMembers)
</script>

{#if hasStaffMembers}
	<div class="space-y-4">
		<TeamListTable data={filteredMembers as any} />
	</div>
{:else}
	{#snippet iconSnippet()}
		<FileText class="h-10 w-10" />
	{/snippet}
	<NoData
		noDataTitle="No Staff Data Found"
		noDataDescription="You do not have any staff/employee data captured yet. start creating employee Data"
		snippet={iconSnippet}
	>
		<div class="flex justify-between space-x-2">
			<Button variant="default" onclick={startCreateTeamMember}>New Invite</Button>
		</div>
	</NoData>
{/if}
