
import {
	BluetoothConnected,
	PlugZap,
	ScreenShareOff,
	Unplug,
	UserCheck,
	UserPlus,
	UserRoundPlus,
	Users
} from '@lucide/svelte'
import { default as StaffTableRowActions } from './staff-table-row-actions.svelte'
import type { ColumnDef } from '@tanstack/table-core'

import { renderComponent } from '$/components/ui/data-table'
import {
	DataTableCell,
	DataTableCheckbox,
	DataTableColumnHeader,
	DataTableStatusCell
} from '$/components/common/data-table'
import { getKeyByValue, RolesType, TeamMemberStatuses } from '$/lib'
import type { Team, User } from '$/lib'

export const columns: ColumnDef<{ profile: User, location: Team }>[] = [
	{
		id: 'select',
		header: ({ table }) =>
			renderComponent(DataTableCheckbox, {
				checked: table.getIsAllPageRowsSelected(),
				onCheckedChange: (value) => table.toggleAllPageRowsSelected(!!value),
				'aria-label': 'Select all',
				class: 'translate-y-[2px]'
			}),
		cell: ({ row }) =>
			renderComponent(DataTableCheckbox, {
				checked: row.getIsSelected(),
				onCheckedChange: (value) => row.toggleSelected(!!value),
				'aria-label': 'Select row',
				class: 'translate-y-[2px]'
			}),
		enableSorting: false,
		enableHiding: false
	},
	{
		accessorKey: 'fullName',
		header: ({ column }) =>
			renderComponent(DataTableColumnHeader, {
				column,
				title: 'Full Name'
			}),
		cell: ({ row }) => {
			return renderComponent(DataTableCell, {
				value: `${row.original.profile.givenName} ${row.original.profile.familyName}`
			})
		},
		enableSorting: true,
		enableColumnFilter: true
	},
	{
		accessorKey: 'memberStatus',
		header: ({ column }) =>
			renderComponent(DataTableColumnHeader, {
				column,
				title: 'User Status'
			}),
		cell: ({ row }) => {
			return renderComponent(DataTableStatusCell, {
				value: row.original.profile.userStatus || undefined,
				statuses: statuses
			})
		},
		enableSorting: true,
		filterFn: (row, id, value) => {
			return value.includes(row.getValue(id))
		}
	},
	{
		accessorKey: 'phoneNumber',
		header: ({ column }) =>
			renderComponent(DataTableColumnHeader, {
				column,
				title: 'Phone'
			}),
		cell: ({ row }) => {
			return renderComponent(DataTableCell, {
				value: row.original?.profile?.phoneNumber || ''
			})
		},
		enableSorting: true,
		enableColumnFilter: true
	},
	{
		accessorKey: 'email',
		header: ({ column }) =>
			renderComponent(DataTableColumnHeader, {
				column,
				title: 'Email'
			}),
		cell: ({ row }) => {
			return renderComponent(DataTableCell, {
				value: row.original.profile?.email || ''
			})
		},
		enableSorting: true,
		enableColumnFilter: true
	},
	{
		accessorKey: 'gender',
		header: ({ column }) =>
			renderComponent(DataTableColumnHeader, {
				column,
				title: 'Gender'
			}),
		cell: ({ row }) => {
			return renderComponent(DataTableCell, {
				value: row.original.profile?.gender?.toString()
			})
		},
		enableColumnFilter: false,
		enableSorting: true
	},
	{
		accessorKey: 'teamName',
		header: ({ column }) => {
			return renderComponent(DataTableColumnHeader, {
				title: 'Branch',
				column
			})
		},
		cell: ({ row }) => {
			return renderComponent(DataTableCell, {
				value: row.original.location.name
			})
		},
		enableColumnFilter: true,
		enableSorting: true,
		enableGrouping: true
	},
	{
		accessorKey: 'roles',
		header: ({ column }) => {
			return renderComponent(DataTableColumnHeader, {
				title: 'Roles',
				column
			})
		},
		cell: ({ row }) => {
			return renderComponent(DataTableCell, {
				value: Array.isArray(row.original.profile.roles) ? row.original.profile.roles.join(', ') : row.original.profile.roles
			})
		},
		enableColumnFilter: true,
		enableSorting: true,
		enableGrouping: true
	},
	{
		id: 'actions',
		cell: ({ row }) => renderComponent(StaffTableRowActions, { row: row as any })
	}
]

export const roles = [
	{
		label: 'Admin',
		value: RolesType.Admin,
		icon: UserCheck
	},
	{
		label: 'Org Admin',
		value: RolesType.OrgAdmin,
		icon: UserRoundPlus
	},
	{
		label: 'Location Admin',
		value: RolesType.TeamLocationAdmin,
		icon: UserPlus
	},
	{
		label: 'Team Member',
		value: RolesType.TeamMember,
		icon: Users
	}
]
export const statuses = [
	{
		value: TeamMemberStatuses.Active,
		label: 'Active',
		icon: BluetoothConnected
	},
	{
		value: TeamMemberStatuses.InActive,
		label: 'Inactive',
		icon: ScreenShareOff
	},
	{
		value: TeamMemberStatuses.OnLeave,
		label: 'On Leave',
		icon: Unplug
	},
	{
		value: TeamMemberStatuses.Resigned,
		label: 'Resigned',
		icon: PlugZap
	}
]
