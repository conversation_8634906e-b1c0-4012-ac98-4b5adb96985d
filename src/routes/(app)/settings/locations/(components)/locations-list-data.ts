
import { default as LocationTableRowActions } from './locations-row-actions.svelte'
import type { ColumnDef } from '@tanstack/table-core'

import { renderComponent } from '$/components/ui/data-table'
import {
	DataTableCell,
	DataTableCheckbox,
	DataTableColumnHeader
} from '$/components/common/data-table'
import type { Team, User } from '$/lib'

export const columns: ColumnDef<{ profile: User, location: Team }>[] = [
	{
		id: 'select',
		header: ({ table }) =>
			renderComponent(DataTableCheckbox, {
				checked: table.getIsAllPageRowsSelected(),
				onCheckedChange: (value) => table.toggleAllPageRowsSelected(!!value),
				'aria-label': 'Select all',
				class: 'translate-y-[2px]'
			}),
		cell: ({ row }) =>
			renderComponent(DataTableCheckbox, {
				checked: row.getIsSelected(),
				onCheckedChange: (value) => row.toggleSelected(!!value),
				'aria-label': 'Select row',
				class: 'translate-y-[2px]'
			}),
		enableSorting: false,
		enableHiding: false
	},
	{
		accessorKey: 'locationName',
		header: ({ column }) =>
			renderComponent(
				DataTableColumnHeader<{ profile: User, location: Team }, unknown>,
				{
					column,
					title: 'Location Name'
				}
			),
		cell: ({ row }) => {
			return renderComponent(DataTableCell, {
				value: row.original?.location?.name
			})
		},
		enableSorting: true,
		enableColumnFilter: true
	},
	{
		accessorKey: 'isActive',
		header: ({ column }) =>
			renderComponent(
				DataTableColumnHeader<{ profile: User, location: Team }, unknown>,
				{
					column,
					title: 'Active ?'
				}
			),
		cell: ({ row }) => {
			return renderComponent(DataTableCell, {
				value: row.original?.location?.isActive ? 'Yes' : 'No'
			})
		},
		enableSorting: true,
		filterFn: (row, id, value) => {
			return value.includes(row.getValue(id))
		}
	},
	{
		accessorKey: 'locationAddress',
		header: ({ column }) =>
			renderComponent(
				DataTableColumnHeader<{ profile: User, location: Team }, unknown>,
				{
					column,
					title: 'Address'
				}
			),
		cell: ({ row }) => {
			return renderComponent(DataTableCell, {
				value: row.original?.location?.locationAddress ?? ''
			})
		},
		enableSorting: true,
		enableColumnFilter: true
	},
	{
		accessorKey: 'locationEmail',
		header: ({ column }) =>
			renderComponent(
				DataTableColumnHeader<{ profile: User, location: Team }, unknown>,
				{
					column,
					title: 'Email'
				}
			),
		cell: ({ row }) => {
			return renderComponent(DataTableCell, {
				value: row.original?.location?.locationEmail ?? ''
			})
		},
		enableSorting: true,
		enableColumnFilter: true
	},
	{
		accessorKey: 'locationPhoneNumber',
		header: ({ column }) =>
			renderComponent(
				DataTableColumnHeader<{ profile: User, location: Team }, unknown>,
				{
					column,
					title: 'Phone'
				}
			),
		cell: ({ row }) => {
			return renderComponent(DataTableCell, {
				value: row.original?.location?.locationPhoneNumber ?? ''
			})
		},
		enableColumnFilter: false,
		enableSorting: true
	},
	{
		accessorKey: 'teamAdminName',
		header: ({ column }) => {
			return renderComponent(
				DataTableColumnHeader<{ profile: User, location: Team }, unknown>,
				{
					title: 'Team Manager',
					column
				}
			)
		},
		cell: ({ row }) => {
			return renderComponent(DataTableCell, {
				value: `${row.original?.profile?.givenName} ${row.original?.profile?.familyName}`
			})
		},
		enableColumnFilter: true,
		enableSorting: true,
		enableGrouping: true
	},
	{
		accessorKey: 'isDefault',
		header: ({ column }) => {
			return renderComponent(
				DataTableColumnHeader<{ profile: User, location: Team }, unknown>,
				{
					title: 'HQ ?',
					column
				}
			)
		},
		cell: ({ row }) => {
			return renderComponent(DataTableCell, {
				value: row.original?.location?.isDefault ? 'Yes' : 'No'
			})
		},
		enableColumnFilter: true,
		enableSorting: true,
		enableGrouping: true
	},
	{
		id: 'actions',
		cell: ({ row }) => renderComponent(LocationTableRowActions, { row: row as any })
	}
]
