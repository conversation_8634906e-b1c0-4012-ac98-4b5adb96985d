<script lang="ts">
	import { TeamController, type IDynamicModal } from '$/lib'
	import Ellipsis from '@lucide/svelte/icons/ellipsis'
	import type { Row } from '@tanstack/table-core'
	import * as DropdownMenu from '$/components/ui/dropdown-menu'
	import { Button } from '$/components/ui/button'
	import { getUtilsStore } from '$/stores'
	import type { Team } from '$/lib'

	let { row }: { row: Row<Team> } = $props()
	const uStore = getUtilsStore()
	let toggeStatusText = $state<string>(
		row.original?.isActive === true ? 'Deactivate Location' : 'Activate Location'
	)
	const toggleTeamStatus = async () => {
		const status = !row.original?.isActive
		await updateTeamStatus(status)
	}
	const editTeamLocation = () => {
		const newTeamLocationForm: IDynamicModal = {
			canClose: false,
			title: 'Edit Team/Branch Location',
			componentName: 'AddTeamLocation',
			size: 'lg',
			color: 'default',
			description: 'Edit Team/Branch details',
			componentProps: {
				data: row.original
			}
		}
		uStore.openDynamicModal(newTeamLocationForm)
	}
	const setInactiveLocation = async () => {
		await updateTeamStatus(false)
	}
	const updateTeamStatus = async (isActive: boolean) => {
		try {
			const updateResp = await TeamController.update(row.original.id, { isActive })
			if (updateResp.id) return uStore.toastSuccess('Team location status updated successfully')
			else {
				uStore.toastError('Failed to update team location status')
			}
		} catch (err) {
			uStore.toastError(`Failed to update status: ${(err as Error).message}`)
		}
	}
</script>

<DropdownMenu.Root>
	<DropdownMenu.Trigger>
		{#snippet child({ props })}
			<Button {...props} variant="ghost" class="data-[state=open]:bg-muted flex h-8 w-8 p-0">
				<Ellipsis />
				<span class="sr-only">Open Menu</span>
			</Button>
		{/snippet}
	</DropdownMenu.Trigger>
	<DropdownMenu.Content class="w-[160px]" align="end">
		<DropdownMenu.Item onclick={editTeamLocation}>Edit</DropdownMenu.Item>
		<DropdownMenu.Item onclick={setInactiveLocation}>Set InActive</DropdownMenu.Item>
		<DropdownMenu.Separator />
		<DropdownMenu.Item onclick={() => toggleTeamStatus()}>
			{toggeStatusText}
			<DropdownMenu.Shortcut>⌘⌫</DropdownMenu.Shortcut>
		</DropdownMenu.Item>
	</DropdownMenu.Content>
</DropdownMenu.Root>
