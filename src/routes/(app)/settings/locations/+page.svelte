<script lang="ts">
	import { NoData } from '$/components/common'
	import { CardShell } from '$/components/layout'
	import { But<PERSON> } from '$/components/ui/button'
	import * as Card from '$/components/ui/card'
	import { getUtilsStore } from '$/stores'
	import { RolesType, type IDynamicModal } from '$/lib'
	import { Team, TeamMember, User as UserEntity } from '$/lib/models'
	import { List, User } from '@lucide/svelte'
	import LocationsTable from './(components)/locations-table.svelte'
	import {
		subscribeToTeams,
		subscribeToTeamMembers,
		subscribeToUsers
	} from '$/lib/utils/live-query-helpers'

	const uStore = getUtilsStore()

	let teams = $state<Team[]>([])
	let teamMembers = $state<TeamMember[]>([])
	let users = $state<UserEntity[]>([])

	let staffMembers = $derived.by(() => {
		return teamMembers
			.map((teamMember: TeamMember) => {
				// Find the team/location details
				const location = teams.firstOrNull((team: Team) => team.id === teamMember.teamId)

				// Find the user details
				const profile = users.firstOrNull((u: UserEntity) => u.id === teamMember.userId)

				return {
					profile,
					location,
					memberId: teamMember.id
				}
			})
			.filter((member) => member.memberId)
	})

	let hasLocations = $derived(teams.length > 0)
	const heading = $state('Locations Manager')
	const subHeading = $state(
		'Manage team (business) locations. Change State, Add new , Edit and Disable Locations'
	)

	const teamData = $derived.by(() => {
		return teams?.map((tl) => {
			const teamAdmin = staffMembers?.firstOrNull(
				(sm) =>
					sm.profile?.teamId === tl.id &&
					sm.profile?.roles?.includes(RolesType.TeamLocationAdmin)
			)
			return {
				location: tl,
				profile: teamAdmin?.profile!
			}
		})
	})

	$effect(() => {
		const unsubTeams = subscribeToTeams((data) => (teams = data))
		const unsubTeamMembers = subscribeToTeamMembers((data) => (teamMembers = data))
		const unsubUsers = subscribeToUsers((data) => (users = data))

		return () => {
			unsubTeams()
			unsubTeamMembers()
			unsubUsers()
		}
	})
	const startCreateTeamLocation = () => {
		const newBranchLocation: IDynamicModal = {
			canClose: false,
			title: 'New team Location',
			componentName: 'AddTeamLocation',
			size: 'lg',
			color: 'default',
			description: 'Create new Team/Branch Location',
			componentProps: {}
		}
		uStore.openDynamicModal(newBranchLocation)
	}
</script>

<CardShell {heading} {subHeading}>
	<Card.Root class="rounded-none">
		<Card.Content>
			{#if hasLocations}
				<LocationsTable data={teamData} />
			{:else}
				{#snippet listSnippet()}
					<List class="h-10 w-10" />
				{/snippet}
				<NoData
					noDataTitle="No Team Location"
					noDataDescription="You do not have any location created"
					snippet={listSnippet}
				>
					<div class="flex justify-between space-x-2">
						<Button variant="default" onclick={startCreateTeamLocation}
							>Create Location</Button
						>
					</div>
				</NoData>
			{/if}
		</Card.Content>
	</Card.Root>
</CardShell>
