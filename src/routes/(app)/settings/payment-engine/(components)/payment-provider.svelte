<script lang="ts">
	import { NoData } from '$/components/common'
	import { Button } from '$/components/ui/button'
	import * as Card from '$/components/ui/card'
	import { Label } from '$/components/ui/label'
	import { getUtilsStore } from '$/stores'
	import type { IDynamicModal } from '$/lib'
	import { CreditCard, Pencil } from '@lucide/svelte'
	import { OrgPaymentProvider } from '$/lib/models'
	import { remult } from 'remult'
	import 'linq-extensions'

	const uStore = getUtilsStore()

	let orgPaymentProvider = $state<OrgPaymentProvider | undefined>(undefined)
	let hasPaymentProvider = $derived(!!orgPaymentProvider)

	$effect(() => {
		const unsubscribe = remult
			.repo(OrgPaymentProvider)
			.liveQuery({
				where: { organizationId: remult.user?.organizationId }
			})
			.subscribe((info) => {
				orgPaymentProvider = info.items.firstOrNull() ?? undefined
			})

		return () => {
			unsubscribe()
		}
	})

	const startConfigurePaymentEngine = () => {
		const paymentEngineForm: IDynamicModal = {
			canClose: false,
			title: 'Configure Payment Engine',
			componentName: 'AddPaymentProvider',
			size: 'lg',
			color: 'default',
			description: 'Set up your payment engine configuration',
			componentProps: {}
		}
		uStore.openDynamicModal(paymentEngineForm)
	}
</script>

{#if hasPaymentProvider}
	<Card.Root>
		<Card.Header>
			<Card.Title class="flex items-center justify-between"
				>Payment Provider
				<Button variant="ghost" size="icon" onclick={startConfigurePaymentEngine}>
					<Pencil class="size-4" />
				</Button>
			</Card.Title>
			<Card.Description
				>Manage your Payment Provider for receiving payments. Please note that this information
				is encrypted and stored. Only the Team admin's credential can decrypt and view the
				information
			</Card.Description>
		</Card.Header>
		<Card.Content>
			<div class="grid grid-cols-1 gap-4 space-y-4 md:grid-cols-3">
				<div class="space-y-1">
					<Label>Provider Name</Label>
					<p class="text-muted-foreground text-sm">
						{orgPaymentProvider?.providerName}
					</p>
				</div>
				<div class="space-y-1">
					<Label>Api URL</Label>
					<p class="text-muted-foreground text-sm">
						{orgPaymentProvider?.providerApiUrl}
					</p>
				</div>
				<div class="space-y-1">
					<Label>Api Key</Label>
					<p class="text-muted-foreground text-sm">
						{orgPaymentProvider?.providerApiKey}
					</p>
				</div>
				<div class="space-y-1">
					<Label>Api Secret</Label>
					<p class="text-muted-foreground text-sm">
						{orgPaymentProvider?.providerApiSecret}
					</p>
				</div>
				<div class="space-y-1">
					<Label>Date Created</Label>
					<p class="text-muted-foreground text-sm">
						{orgPaymentProvider?.createdAt}
					</p>
				</div>
				<div class="space-y-1">
					<Label>Date Last Updated</Label>
					<p class="text-muted-foreground text-sm">
						{orgPaymentProvider?.updatedAt}
					</p>
				</div>
			</div>
		</Card.Content>
	</Card.Root>
{:else}
	{#snippet paymentIconSnippet()}
		<CreditCard class="h-10 w-10" />
	{/snippet}
	<NoData
		noDataTitle="No Payment Engine Configured"
		noDataDescription="You haven't set up your payment engine yet. Configure your payment settings to start accepting payments."
		snippet={paymentIconSnippet}
	>
		<div class="flex justify-between space-x-2">
			<Button variant="default" onclick={startConfigurePaymentEngine}
				>Configure Payment Engine</Button
			>
		</div>
	</NoData>
{/if}
