<script lang="ts">
	import { getUtilsStore } from '$/stores'
	import { Button } from '$/components/ui/button'
	import * as Card from '$/components/ui/card'
	import { NoData } from '$/components/common'
	import { Landmark, Pencil } from '@lucide/svelte'
	import type { IDynamicModal } from '$/lib'
	import { Label } from '$/components/ui/label'
	import { OrgBankDetail } from '$/lib/models'
	import { remult } from 'remult'

	const uStore = getUtilsStore()

	let orgBankDetail = $state<OrgBankDetail | undefined>(undefined)
	let hasBankDetails = $derived(!!orgBankDetail)

	$effect(() => {
		const unsubscribe = remult
			.repo(OrgBankDetail)
			.liveQuery({
				where: { organizationId: remult.user?.organizationId },
				orderBy: { bankName: 'asc' }
			})
			.subscribe((info) => {
				orgBankDetail = info.items.firstOrNull() ?? undefined
			})

		return () => {
			unsubscribe()
		}
	})

	let noDataText = $derived(
		hasBankDetails
			? "You haven't added any bank account details yet. Add your bank details to start managing payments."
			: 'You may not have the permissions required to view this data. Please contact your Team administrator.'
	)
	const startAddBankDetails = () => {
		const bankDetailsForm: IDynamicModal = {
			canClose: false,
			title: 'Add Bank Details',
			componentName: 'AddBankDetails',
			size: 'lg',
			color: 'default',
			description: 'Add your bank account details for payment processing',
			componentProps: {
				bankDetailsForm: {} // Add your form schema here
			}
		}
		uStore.openDynamicModal(bankDetailsForm)
	}
</script>

{#if hasBankDetails}
	<div class="grid auto-cols-auto grid-flow-col gap-2 max-md:grid-rows-2">
		<Card.Root class="w-full">
			<Card.Header>
				<Card.Title class="flex items-center justify-between">
					Bank Account Details
					<Button variant="ghost" size="icon" onclick={startAddBankDetails}>
						<Pencil class="size-4" />
					</Button>
				</Card.Title>
			</Card.Header>
			<Card.Content>
				<div class="grid grid-cols-1 gap-4 space-y-4 md:grid-cols-3">
					<div class="space-y-1">
						<Label>Account Name/alias</Label>
						<p class="text-muted-foreground text-sm">
							{orgBankDetail?.accountName}
						</p>
					</div>
					<div class="space-y-1">
						<Label>Bank Name</Label>
						<p class="text-muted-foreground text-sm">
							{orgBankDetail?.bankName}
						</p>
					</div>
					<div class="space-y-1">
						<Label>Account Number</Label>
						<p class="text-muted-foreground text-sm">
							{orgBankDetail?.accountNumber}
						</p>
					</div>
					<div class="space-y-1">
						<Label>Branch Code</Label>
						<p class="text-muted-foreground text-sm">
							{orgBankDetail?.branchCode}
						</p>
					</div>
					<div class="space-y-1">
						<Label>Swift Code</Label>
						<p class="text-muted-foreground text-sm">
							{orgBankDetail?.swiftCode}
						</p>
					</div>
					<div class="space-y-1">
						<Label>Created At</Label>
						<p class="text-muted-foreground text-sm">
							{orgBankDetail?.createdAt}
						</p>
					</div>
					<div class="space-y-1">
						<Label>Last Update:</Label>
						<p class="text-muted-foreground text-sm">
							{orgBankDetail?.updatedAt}
						</p>
					</div>
				</div>
			</Card.Content>
		</Card.Root>
	</div>
{:else}
	{#snippet bankIconSnippet()}
		<Landmark class="h-10 w-10" />
	{/snippet}
	<NoData
		noDataTitle="No Bank Details Found"
		noDataDescription={noDataText}
		snippet={bankIconSnippet}
	>
		<div class="flex justify-between space-x-2">
			<Button variant="default" onclick={startAddBankDetails}>Add Bank Details</Button>
		</div>
	</NoData>
{/if}
