<script lang="ts">
	import * as Card from '$/components/ui/card'
	import * as Tabs from '$/components/ui/tabs'
	import { BankDetails, PaymentProvider } from './(components)'
</script>

<Card.Root>
	<Card.Header>
		<Card.Title class="flex items-center justify-between">Financial Info Settings</Card.Title>
	</Card.Header>
	<Card.Content>
		<Tabs.Root value="bank-details" class="w-full">
			<Tabs.List>
				<Tabs.Trigger value="bank-details">Bank Details</Tabs.Trigger>
				<Tabs.Trigger value="payment-engine">Payment Engine</Tabs.Trigger>
			</Tabs.List>
			<Tabs.Content value="bank-details">
				<BankDetails />
			</Tabs.Content>

			<Tabs.Content value="payment-engine">
				<PaymentProvider />
			</Tabs.Content>
		</Tabs.Root>
	</Card.Content>
</Card.Root>
