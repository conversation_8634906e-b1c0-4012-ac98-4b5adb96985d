<script lang="ts">
	import { CardShell } from '$/components/layout'
	import { MainModuleDashboard } from './(components)'
	import { Organization } from '$/lib/models'
	import { remult } from 'remult'
	import 'linq-extensions'

	let org = $state<Organization | undefined>(undefined)
	const heading = $derived(`${org?.name ?? 'SpenDeed'} Dashboard`)
	const subHeading = $state('Birds Eye View of Your Business')

	$effect(() => {
		const unsubscribe = remult
			.repo(Organization)
			.liveQuery({
				where: { id: remult.user?.organizationId }
			})
			.subscribe((info) => {
				org = info.items.firstOrNull() ?? undefined
			})

		return () => {
			unsubscribe()
		}
	})
</script>

<div class="space-y-6">
	<div class="flex items-center justify-between">
		<h2 class="text-3xl font-bold tracking-tight">Dashboard</h2>
	</div>
	<div class="flex-1">
		<CardShell {heading} {subHeading}>
			<MainModuleDashboard />
		</CardShell>
	</div>
</div>
