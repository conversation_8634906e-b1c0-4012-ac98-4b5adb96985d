<script lang="ts">
	import { CardShell } from '$/components/layout'
	import { Button } from '$/components/ui/button'
	import { Plus } from '@lucide/svelte'

	// Import components
	import SubscriptionStats from './(components)/subscription-stats.svelte'
	import RevenueAnalyticsChart from './(components)/revenue-analytics-chart.svelte'
	import ExpiringSubscriptions from './(components)/expiring-subscriptions.svelte'

	// TODO: Replace with actual data from Remult controllers
	const subscriptionStats = $state({
		totalSubscriptions: 3456,
		activeSubscriptions: 3234,
		monthlyRevenue: 89450,
		churnRate: 2.8,
		growthRate: 15.2
	})

	const revenueData = $state([
		{ date: new Date('2024-07-01'), revenue: 67890, subscriptions: 1654 },
		{ date: new Date('2024-08-01'), revenue: 72340, subscriptions: 1723 },
		{ date: new Date('2024-09-01'), revenue: 78920, subscriptions: 1789 },
		{ date: new Date('2024-10-01'), revenue: 84560, subscriptions: 1834 },
		{ date: new Date('2024-11-01'), revenue: 87230, subscriptions: 1847 },
		{ date: new Date('2024-12-01'), revenue: 89450, subscriptions: 1847 }
	])

	const expiringSubscriptionsData = $state([
		{
			id: 1,
			customerName: 'Small Business LLC',
			plan: 'Basic Plan',
			expiryDate: '2024-01-20',
			daysLeft: 5,
			amount: 9.99
		},
		{
			id: 2,
			customerName: 'Design Studio',
			plan: 'Pro Plan',
			expiryDate: '2024-01-22',
			daysLeft: 7,
			amount: 29.99
		},
		{
			id: 3,
			customerName: 'Marketing Agency',
			plan: 'Enterprise Plan',
			expiryDate: '2024-01-25',
			daysLeft: 10,
			amount: 99.99
		}
	])

	// Action handlers
	const handleCreatePlan = () => {
		console.log('Create new subscription plan')
	}

	const handleRenewSubscription = (id: number) => {
		console.log('Renew subscription:', id)
	}

	const handleContactCustomer = (id: number) => {
		console.log('Contact customer:', id)
	}
</script>

<CardShell
	heading="Subscriptions Dashboard"
	subHeading="Manage subscription plans and track recurring revenue metrics"
>
	{#snippet rightSlot()}
		<Button onclick={handleCreatePlan} class="gap-2">
			<Plus class="h-4 w-4" />
			Create Plan
		</Button>
	{/snippet}

	<div class="space-y-6">
		<!-- Key Metrics -->
		<SubscriptionStats stats={subscriptionStats} />

		<div class="grid gap-6 lg:grid-cols-2">
			<!-- Revenue Analytics Chart -->
			<RevenueAnalyticsChart data={revenueData} />

			<!-- Expiring Subscriptions -->
			<ExpiringSubscriptions
				subscriptions={expiringSubscriptionsData}
				onRenew={handleRenewSubscription}
				onContact={handleContactCustomer}
			/>
		</div>
	</div>
</CardShell>
