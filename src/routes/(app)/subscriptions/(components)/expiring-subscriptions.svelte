<script lang="ts">
	import * as Card from '$/components/ui/card'
	import { Badge } from '$/components/ui/badge'
	import { Button } from '$/components/ui/button'
	import { Clock, Calendar, CreditCard } from '@lucide/svelte'

	interface ExpiringSubscription {
		id: number
		customerName: string
		plan: string
		expiryDate: string
		daysLeft: number
		amount: number
	}

	interface Props {
		subscriptions: ExpiringSubscription[]
		onRenew?: (id: number) => void
		onContact?: (id: number) => void
	}

	let { subscriptions, onRenew, onContact }: Props = $props()

	const getDaysLeftVariant = (daysLeft: number) => {
		if (daysLeft <= 3) return 'destructive'
		if (daysLeft <= 7) return 'secondary'
		return 'outline'
	}
</script>

<Card.Root>
	<Card.Header>
		<Card.Title class="flex items-center gap-2">
			<Clock class="h-5 w-5" />
			Expiring Subscriptions
		</Card.Title>
		<Card.Description>Subscriptions expiring in the next 30 days</Card.Description>
	</Card.Header>
	<Card.Content>
		<div class="space-y-4">
			{#each subscriptions as subscription}
				<div class="flex items-center justify-between space-x-4 rounded-lg border p-4">
					<div class="space-y-1">
						<p class="text-sm font-medium leading-none">{subscription.customerName}</p>
						<p class="text-sm text-muted-foreground">{subscription.plan}</p>
						<div class="flex items-center gap-2 text-sm text-muted-foreground">
							<Calendar class="h-4 w-4" />
							Expires: {new Date(subscription.expiryDate).toLocaleDateString()}
						</div>
					</div>
					<div class="flex items-center gap-2">
						<Badge variant={getDaysLeftVariant(subscription.daysLeft)}>
							{subscription.daysLeft} days left
						</Badge>
						<div class="text-right">
							<p class="text-sm font-medium">${subscription.amount}</p>
							<div class="flex gap-1">
								<Button
									size="sm"
									variant="outline"
									onclick={() => onContact?.(subscription.id)}
									class="h-7 px-2"
								>
									Contact
								</Button>
								<Button
									size="sm"
									onclick={() => onRenew?.(subscription.id)}
									class="h-7 px-2"
								>
									<CreditCard class="h-3 w-3 mr-1" />
									Renew
								</Button>
							</div>
						</div>
					</div>
				</div>
			{/each}
		</div>
	</Card.Content>
</Card.Root>
