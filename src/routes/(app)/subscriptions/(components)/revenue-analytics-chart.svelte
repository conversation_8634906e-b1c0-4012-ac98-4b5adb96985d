<script lang="ts">
	import * as Card from '$/components/ui/card'
	import * as Chart from '$/components/ui/chart'
	import { LineChart } from 'layerchart'
	import { scaleUtc } from 'd3-scale'
	import { curveNatural } from 'd3-shape'
	import { TrendingUp } from '@lucide/svelte'

	interface RevenueData {
		date: Date
		revenue: number
		subscriptions: number
	}

	interface Props {
		data: RevenueData[]
	}

	let { data }: Props = $props()

	// Chart configuration
	const chartConfig = {
		revenue: {
			label: 'Revenue',
			color: 'var(--chart-1)'
		},
		subscriptions: {
			label: 'Subscriptions',
			color: 'var(--chart-2)'
		}
	} satisfies Chart.ChartConfig
</script>

<Card.Root>
	<Card.Header>
		<Card.Title class="flex items-center gap-2">
			<TrendingUp class="h-5 w-5" />
			Revenue Analytics
		</Card.Title>
		<Card.Description>Subscription revenue trends over the last 6 months</Card.Description>
	</Card.Header>
	<Card.Content>
		<Chart.Container config={chartConfig} class="h-[300px]">
			<LineChart
				{data}
				x="date"
				xScale={scaleUtc()}
				y="revenue"
				yPadding={[0, 25]}
				props={{
					spline: {
						curve: curveNatural,
						stroke: 'var(--color-revenue)',
						'stroke-width': 2
					},
					xAxis: {
						format: (v: Date) => v.toLocaleDateString('en-US', { month: 'short' })
					},
					yAxis: {
						format: (v: number) => `$${(v / 1000).toFixed(0)}k`
					}
				}}
				points={{ r: 4, fill: 'var(--color-revenue)' }}
			>
				{#snippet tooltip()}
					<Chart.Tooltip
						indicator="dot"
						labelFormatter={(v: Date) => {
							return v.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })
						}}
					/>
				{/snippet}
			</LineChart>
		</Chart.Container>
	</Card.Content>
</Card.Root>
