<script lang="ts">
	import * as Card from '$/components/ui/card'
	import { RefreshCw, TrendingUp, DollarSign, AlertTriangle } from '@lucide/svelte'

	interface SubscriptionStats {
		totalSubscriptions: number
		activeSubscriptions: number
		monthlyRevenue: number
		churnRate: number
		growthRate: number
	}

	interface Props {
		stats: SubscriptionStats
	}

	let { stats }: Props = $props()
</script>

<div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
	<Card.Root>
		<Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
			<Card.Title class="text-sm font-medium">Total Subscriptions</Card.Title>
			<RefreshCw class="h-4 w-4 text-muted-foreground" />
		</Card.Header>
		<Card.Content>
			<div class="text-2xl font-bold">{stats.totalSubscriptions.toLocaleString()}</div>
			<p class="text-xs text-muted-foreground">
				+{stats.growthRate}% from last month
			</p>
		</Card.Content>
	</Card.Root>

	<Card.Root>
		<Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
			<Card.Title class="text-sm font-medium">Active Subscriptions</Card.Title>
			<TrendingUp class="h-4 w-4 text-muted-foreground" />
		</Card.Header>
		<Card.Content>
			<div class="text-2xl font-bold">{stats.activeSubscriptions.toLocaleString()}</div>
			<p class="text-xs text-muted-foreground">
				{((stats.activeSubscriptions / stats.totalSubscriptions) * 100).toFixed(1)}% active rate
			</p>
		</Card.Content>
	</Card.Root>

	<Card.Root>
		<Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
			<Card.Title class="text-sm font-medium">Monthly Revenue</Card.Title>
			<DollarSign class="h-4 w-4 text-muted-foreground" />
		</Card.Header>
		<Card.Content>
			<div class="text-2xl font-bold">${stats.monthlyRevenue.toLocaleString()}</div>
			<p class="text-xs text-muted-foreground">
				+{stats.growthRate}% from last month
			</p>
		</Card.Content>
	</Card.Root>

	<Card.Root>
		<Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
			<Card.Title class="text-sm font-medium">Churn Rate</Card.Title>
			<AlertTriangle class="h-4 w-4 text-muted-foreground" />
		</Card.Header>
		<Card.Content>
			<div class="text-2xl font-bold">{stats.churnRate}%</div>
			<p class="text-xs text-muted-foreground">
				Monthly churn rate
			</p>
		</Card.Content>
	</Card.Root>
</div>
