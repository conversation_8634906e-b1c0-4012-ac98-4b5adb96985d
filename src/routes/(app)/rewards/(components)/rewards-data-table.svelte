<script lang="ts">
	import * as Card from '$/components/ui/card'
	import * as Table from '$/components/ui/table'
	import { Button } from '$/components/ui/button'
	import { Input } from '$/components/ui/input'
	import * as DropdownMenu from '$/components/ui/dropdown-menu'
	import { Gift } from '@lucide/svelte'
	import {
		type ColumnDef,
		getCoreRowModel,
		getPaginationRowModel,
		getSortedRowModel,
		getFilteredRowModel,
		type PaginationState,
		type SortingState,
		type ColumnFiltersState,
		type VisibilityState
	} from '@tanstack/table-core'
	import { createSvelteTable, FlexRender } from '$/components/ui/data-table'

	interface RewardTemplate {
		id: number
		name: string
		description: string
		type: string
		value: number
		category: string
		usageCount: number
		status: string
	}

	interface Props {
		rewards: RewardTemplate[]
		onView?: (id: number) => void
		onEdit?: (id: number) => void
		onDelete?: (id: number) => void
	}

	let { rewards, onView, onEdit, onDelete }: Props = $props()

	// Helper functions for badge variants
	const getTypeBadgeVariant = (type: string) => {
		switch (type) {
			case 'Points':
				return 'default'
			case 'Percentage':
				return 'secondary'
			case 'Discount':
				return 'outline'
			default:
				return 'outline'
		}
	}

	const getStatusBadgeVariant = (status: string) => {
		switch (status) {
			case 'Active':
				return 'default'
			case 'Inactive':
				return 'secondary'
			default:
				return 'outline'
		}
	}

	// Data table configuration
	const columns: ColumnDef<RewardTemplate>[] = [
		{
			accessorKey: 'name',
			header: 'Name',
			cell: ({ row }) => {
				const reward = row.original
				return `
					<div class="space-y-1">
						<div class="font-medium">${reward.name}</div>
						<div class="text-sm text-muted-foreground">${reward.description}</div>
					</div>
				`
			}
		},
		{
			accessorKey: 'type',
			header: 'Type',
			cell: ({ row }) => {
				const type = row.getValue('type') as string
				const variant = getTypeBadgeVariant(type)
				return `<span class="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium bg-${variant === 'default' ? 'primary' : variant === 'secondary' ? 'secondary' : 'muted'} text-${variant === 'default' ? 'primary-foreground' : variant === 'secondary' ? 'secondary-foreground' : 'muted-foreground'}">${type}</span>`
			}
		},
		{
			accessorKey: 'value',
			header: 'Value',
			cell: ({ row }) => {
				const reward = row.original
				let valueDisplay = ''
				if (reward.type === 'Points') {
					valueDisplay = `${reward.value} pts`
				} else if (reward.type === 'Percentage') {
					valueDisplay = `${reward.value}%`
				} else if (reward.type === 'Discount') {
					valueDisplay = `${reward.value}% off`
				} else {
					valueDisplay = 'Gift'
				}
				return `<span class="font-medium">${valueDisplay}</span>`
			}
		},
		{
			accessorKey: 'category',
			header: 'Category',
			cell: ({ row }) => {
				const category = row.getValue('category') as string
				return `<span class="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium bg-muted text-muted-foreground">${category}</span>`
			}
		},
		{
			accessorKey: 'usageCount',
			header: 'Usage',
			cell: ({ row }) => {
				const count = row.getValue('usageCount') as number
				return `<span class="font-medium">${count.toLocaleString()}</span>`
			}
		},
		{
			accessorKey: 'status',
			header: 'Status',
			cell: ({ row }) => {
				const status = row.getValue('status') as string
				const variant = getStatusBadgeVariant(status)
				return `<span class="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium bg-${variant === 'default' ? 'primary' : variant === 'secondary' ? 'secondary' : 'muted'} text-${variant === 'default' ? 'primary-foreground' : variant === 'secondary' ? 'secondary-foreground' : 'muted-foreground'}">${status}</span>`
			}
		},
		{
			id: 'actions',
			header: 'Actions',
			cell: ({ row }) => {
				const reward = row.original
				return `
					<div class="flex items-center justify-end gap-2">
						<button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-8 w-8 p-0" onclick="handleViewReward(${reward.id})">
							<svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path></svg>
						</button>
						<button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-8 w-8 p-0" onclick="handleEditReward(${reward.id})">
							<svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path></svg>
						</button>
						<button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-8 w-8 p-0 text-destructive hover:text-destructive" onclick="handleDeleteReward(${reward.id})">
							<svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path></svg>
						</button>
					</div>
				`
			}
		}
	]

	// Data table state
	let pagination = $state<PaginationState>({ pageIndex: 0, pageSize: 10 })
	let sorting = $state<SortingState>([])
	let columnFilters = $state<ColumnFiltersState>([])
	let columnVisibility = $state<VisibilityState>({})

	const table = createSvelteTable({
		get data() {
			return rewards
		},
		columns,
		getCoreRowModel: getCoreRowModel(),
		getPaginationRowModel: getPaginationRowModel(),
		getSortedRowModel: getSortedRowModel(),
		getFilteredRowModel: getFilteredRowModel(),
		onPaginationChange: (updater: any) => {
			if (typeof updater === 'function') {
				pagination = updater(pagination)
			} else {
				pagination = updater
			}
		},
		onSortingChange: (updater: any) => {
			if (typeof updater === 'function') {
				sorting = updater(sorting)
			} else {
				sorting = updater
			}
		},
		onColumnFiltersChange: (updater: any) => {
			if (typeof updater === 'function') {
				columnFilters = updater(columnFilters)
			} else {
				columnFilters = updater
			}
		},
		onColumnVisibilityChange: (updater: any) => {
			if (typeof updater === 'function') {
				columnVisibility = updater(columnVisibility)
			} else {
				columnVisibility = updater
			}
		},
		state: {
			get pagination() {
				return pagination
			},
			get sorting() {
				return sorting
			},
			get columnFilters() {
				return columnFilters
			},
			get columnVisibility() {
				return columnVisibility
			}
		}
	})

	// Action handlers
	const handleViewReward = (id: number) => {
		onView?.(id)
	}

	const handleEditReward = (id: number) => {
		onEdit?.(id)
	}

	const handleDeleteReward = (id: number) => {
		onDelete?.(id)
	}
</script>

<Card.Root>
	<Card.Header>
		<Card.Title class="flex items-center gap-2">
			<Gift class="h-5 w-5" />
			Reward Templates
		</Card.Title>
		<Card.Description>Manage and configure reward templates</Card.Description>
	</Card.Header>
	<Card.Content>
		<div class="space-y-4">
			<!-- Table Controls -->
			<div class="flex items-center justify-between">
				<Input
					placeholder="Filter rewards..."
					value={table.getColumn('name')?.getFilterValue() as string}
					onchange={(e) => table.getColumn('name')?.setFilterValue(e.currentTarget.value)}
					oninput={(e) => table.getColumn('name')?.setFilterValue(e.currentTarget.value)}
					class="max-w-sm"
				/>
				<DropdownMenu.Root>
					<DropdownMenu.Trigger>
						{#snippet child({ props })}
							<Button {...props} variant="outline" class="ml-auto">Columns</Button>
						{/snippet}
					</DropdownMenu.Trigger>
					<DropdownMenu.Content align="end">
						{#each table.getAllColumns().filter((col) => col.getCanHide()) as column (column.id)}
							<DropdownMenu.CheckboxItem
								class="capitalize"
								checked={column.getIsVisible()}
								onCheckedChange={(checked) => column.toggleVisibility(!!checked)}
							>
								{column.id}
							</DropdownMenu.CheckboxItem>
						{/each}
					</DropdownMenu.Content>
				</DropdownMenu.Root>
			</div>

			<!-- Data Table -->
			<div class="rounded-md border">
				<Table.Root>
					<Table.Header>
						{#each table.getHeaderGroups() as headerGroup (headerGroup.id)}
							<Table.Row>
								{#each headerGroup.headers as header (header.id)}
									<Table.Head colspan={header.colSpan}>
										{#if !header.isPlaceholder}
											<FlexRender
												content={header.column.columnDef.header}
												context={header.getContext()}
											/>
										{/if}
									</Table.Head>
								{/each}
							</Table.Row>
						{/each}
					</Table.Header>
					<Table.Body>
						{#each table.getRowModel().rows as row (row.id)}
							<Table.Row data-state={row.getIsSelected() && 'selected'}>
								{#each row.getVisibleCells() as cell (cell.id)}
									<Table.Cell>
										<FlexRender
											content={cell.column.columnDef.cell}
											context={cell.getContext()}
										/>
									</Table.Cell>
								{/each}
							</Table.Row>
						{:else}
							<Table.Row>
								<Table.Cell colspan={columns.length} class="h-24 text-center">
									No results.
								</Table.Cell>
							</Table.Row>
						{/each}
					</Table.Body>
				</Table.Root>
			</div>

			<!-- Pagination -->
			<div class="flex items-center justify-end space-x-2 py-4">
				<Button
					variant="outline"
					size="sm"
					onclick={() => table.previousPage()}
					disabled={!table.getCanPreviousPage()}
				>
					Previous
				</Button>
				<Button
					variant="outline"
					size="sm"
					onclick={() => table.nextPage()}
					disabled={!table.getCanNextPage()}
				>
					Next
				</Button>
			</div>
		</div>
	</Card.Content>
</Card.Root>

<svelte:head>
	<script>
		window.handleViewReward = (id) => {
			const event = new CustomEvent('viewReward', { detail: { id } })
			document.dispatchEvent(event)
		}
		window.handleEditReward = (id) => {
			const event = new CustomEvent('editReward', { detail: { id } })
			document.dispatchEvent(event)
		}
		window.handleDeleteReward = (id) => {
			const event = new CustomEvent('deleteReward', { detail: { id } })
			document.dispatchEvent(event)
		}
	</script>
</svelte:head>
