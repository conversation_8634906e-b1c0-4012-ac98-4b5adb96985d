<script lang="ts">
	import * as Card from '$/components/ui/card'
	import { Progress } from '$/components/ui/progress'
	import { BarChart3 } from '@lucide/svelte'

	interface ProgramData {
		program: string
		participants: number
		redemptions: number
		rate: number
	}

	interface Props {
		data: ProgramData[]
	}

	let { data }: Props = $props()
</script>

<Card.Root>
	<Card.Header>
		<Card.Title class="flex items-center gap-2">
			<BarChart3 class="h-5 w-5" />
			Program Performance
		</Card.Title>
		<Card.Description>Redemption rates across different reward programs</Card.Description>
	</Card.Header>
	<Card.Content>
		<div class="space-y-4">
			{#each data as program}
				<div class="space-y-2">
					<div class="flex items-center justify-between">
						<div class="space-y-1">
							<p class="text-sm font-medium leading-none">{program.program}</p>
							<p class="text-sm text-muted-foreground">
								{program.participants.toLocaleString()} participants
							</p>
						</div>
						<div class="text-right">
							<p class="text-sm font-medium">{program.rate}%</p>
							<p class="text-sm text-muted-foreground">
								{program.redemptions.toLocaleString()} redeemed
							</p>
						</div>
					</div>
					<Progress value={program.rate} class="h-2" />
				</div>
			{/each}
		</div>
	</Card.Content>
</Card.Root>
