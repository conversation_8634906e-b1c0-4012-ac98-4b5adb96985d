<script lang="ts">
	import * as Card from '$/components/ui/card'
	import * as Chart from '$/components/ui/chart'
	import { BarChart } from 'layerchart'
	import { TrendingUp } from '@lucide/svelte'

	interface RewardsAnalyticsData {
		month: string
		earned: number
		redeemed: number
	}

	interface Props {
		data: RewardsAnalyticsData[]
	}

	let { data }: Props = $props()

	// Chart configuration
	const chartConfig = {
		earned: {
			label: 'Earned',
			color: 'var(--chart-1)'
		},
		redeemed: {
			label: 'Redeemed',
			color: 'var(--chart-2)'
		}
	} satisfies Chart.ChartConfig
</script>

<Card.Root>
	<Card.Header>
		<Card.Title class="flex items-center gap-2">
			<TrendingUp class="h-5 w-5" />
			Rewards Analytics
		</Card.Title>
		<Card.Description>Rewards earned vs redeemed over the last 6 months</Card.Description>
	</Card.Header>
	<Card.Content>
		<Chart.Container config={chartConfig} class="h-[300px] w-full overflow-hidden">
			<BarChart
				{data}
				x="month"
				y={['earned', 'redeemed']}
				padding={{ left: 20, right: 20, top: 20, bottom: 40 }}
				yPadding={[0, 25]}
				props={{
					bars: {
						'fill-opacity': 0.8,
						motion: 'tween'
					}
				}}
			>
				{#snippet tooltip()}
					<Chart.Tooltip
						indicator="dashed"
						labelFormatter={(label: string) => `${label} 2024`}
					/>
				{/snippet}
			</BarChart>
		</Chart.Container>
	</Card.Content>
</Card.Root>
