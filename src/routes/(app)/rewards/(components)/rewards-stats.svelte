<script lang="ts">
	import * as Card from '$/components/ui/card'
	import { Gift, Star, Award, Zap } from '@lucide/svelte'

	interface RewardsStats {
		totalRewards: number
		activePrograms: number
		redemptionsToday: number
		totalValue: number
		growthRate: number
	}

	interface Props {
		stats: RewardsStats
	}

	let { stats }: Props = $props()
</script>

<div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
	<Card.Root>
		<Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
			<Card.Title class="text-sm font-medium">Total Rewards</Card.Title>
			<Gift class="h-4 w-4 text-muted-foreground" />
		</Card.Header>
		<Card.Content>
			<div class="text-2xl font-bold">{stats.totalRewards.toLocaleString()}</div>
			<p class="text-xs text-muted-foreground">
				+{stats.growthRate}% from last month
			</p>
		</Card.Content>
	</Card.Root>

	<Card.Root>
		<Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
			<Card.Title class="text-sm font-medium">Active Programs</Card.Title>
			<Star class="h-4 w-4 text-muted-foreground" />
		</Card.Header>
		<Card.Content>
			<div class="text-2xl font-bold">{stats.activePrograms}</div>
			<p class="text-xs text-muted-foreground">
				Across all categories
			</p>
		</Card.Content>
	</Card.Root>

	<Card.Root>
		<Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
			<Card.Title class="text-sm font-medium">Redemptions Today</Card.Title>
			<Zap class="h-4 w-4 text-muted-foreground" />
		</Card.Header>
		<Card.Content>
			<div class="text-2xl font-bold">{stats.redemptionsToday.toLocaleString()}</div>
			<p class="text-xs text-muted-foreground">
				+{stats.growthRate}% from yesterday
			</p>
		</Card.Content>
	</Card.Root>

	<Card.Root>
		<Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
			<Card.Title class="text-sm font-medium">Total Value</Card.Title>
			<Award class="h-4 w-4 text-muted-foreground" />
		</Card.Header>
		<Card.Content>
			<div class="text-2xl font-bold">${stats.totalValue.toLocaleString()}</div>
			<p class="text-xs text-muted-foreground">
				Rewards distributed
			</p>
		</Card.Content>
	</Card.Root>
</div>
