<script lang="ts">
	import { CardShell } from '$/components/layout'
	import { Button } from '$/components/ui/button'
	import { Plus } from '@lucide/svelte'

	// Import components
	import RewardsStats from './(components)/rewards-stats.svelte'
	import RewardsAnalyticsChart from './(components)/rewards-analytics-chart.svelte'
	import ProgramPerformance from './(components)/program-performance.svelte'

	// TODO: Replace with actual data from Remult controllers
	const rewardsStats = $state({
		totalRewards: 15420,
		activePrograms: 8,
		redemptionsToday: 234,
		totalValue: 125680,
		growthRate: 18.5
	})

	const rewardsAnalyticsData = $state([
		{ month: 'Jul', earned: 18500, redeemed: 12300 },
		{ month: 'Aug', earned: 21200, redeemed: 14800 },
		{ month: 'Sep', earned: 19800, redeemed: 13900 },
		{ month: 'Oct', earned: 23400, redeemed: 16200 },
		{ month: 'Nov', earned: 25100, redeemed: 17800 },
		{ month: 'Dec', earned: 27300, redeemed: 19400 }
	])

	const programPerformanceData = $state([
		{ program: 'Cashback', participants: 2456, redemptions: 1890, rate: 77 },
		{ program: 'Points', participants: 1890, redemptions: 1234, rate: 65 },
		{ program: 'Discounts', participants: 1567, redemptions: 1123, rate: 72 },
		{ program: 'Gifts', participants: 890, redemptions: 567, rate: 64 }
	])

	// Action handlers
	const handleCreateReward = () => {
		console.log('Create new reward')
	}
</script>

<CardShell
	heading="Rewards Dashboard"
	subHeading="Manage customer reward programs and track redemption analytics"
>
	{#snippet rightSlot()}
		<Button onclick={handleCreateReward} class="gap-2">
			<Plus class="h-4 w-4" />
			Create Reward
		</Button>
	{/snippet}

	<div class="space-y-6">
		<!-- Key Metrics -->
		<RewardsStats stats={rewardsStats} />

		<div class="grid gap-6 lg:grid-cols-2">
			<!-- Analytics Chart -->
			<RewardsAnalyticsChart data={rewardsAnalyticsData} />

			<!-- Program Performance -->
			<ProgramPerformance data={programPerformanceData} />
		</div>
	</div>
</CardShell>
