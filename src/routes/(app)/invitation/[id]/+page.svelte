<script lang="ts">
	import { onMount } from 'svelte'
	import { page } from '$app/stores'
	import { goto } from '$app/navigation'
	import { authClient, useSession } from '$/lib'

	const session = useSession()

	let invitation = $state<any>(null)
	let loading = $state(true)
	let error = $state('')
	let processing = $state(false)

	onMount(async () => {
		const invitationId = $page.params.id

		if (!invitationId) {
			error = 'Invalid invitation link'
			loading = false
			return
		}

		try {
			invitation = await getInvitation(invitationId)
			loading = false
		} catch (err: any) {
			error = err.message || 'Failed to load invitation'
			loading = false
		}
	})

	const handleAcceptInvitation = async () => {
		if (!invitation || processing) return

		processing = true

		try {
			await acceptInvitation(invitation.id)

			// Redirect to organization dashboard or success page
			goto('/dashboard?invitation=accepted')
		} catch (err: any) {
			error = err.message || 'Failed to accept invitation'
			processing = false
		}
	}

	const handleRejectInvitation = async () => {
		if (!invitation || processing) return

		processing = true

		try {
			await rejectInvitation(invitation.id)

			// Redirect to a rejection confirmation page
			goto('/?invitation=rejected')
		} catch (err: any) {
			error = err.message || 'Failed to reject invitation'
			processing = false
		}
	}

	const formatRole = (role: string | string[]): string => {
		if (Array.isArray(role)) {
			return role.join(', ')
		}
		return role
	}

	const getInvitation = async (invitationId: string) => {
		return await authClient.organization.getInvitation({
			query: {
				id: invitationId
			}
		})
	}
	const acceptInvitation = async (invitationId: string) => {
		return await authClient.organization.acceptInvitation({
			invitationId
		})
	}

	/**
	 * Reject an invitation (called by the invited user)
	 */
	const rejectInvitation = async (invitationId: string) => {
		return await authClient.organization.rejectInvitation({
			invitationId
		})
	}

	/**
	 * Cancel an invitation (called by the inviter or admin)
	 */
	const cancelInvitation = async (invitationId: string) => {
		return await authClient.organization.cancelInvitation({
			invitationId
		})
	}
</script>

<svelte:head>
	<title>Accept Invitation - SpenDeed</title>
</svelte:head>

<div class="invitation-page">
	<div class="container">
		{#if loading}
			<div class="loading">
				<div class="spinner"></div>
				<p>Loading invitation...</p>
			</div>
		{:else if error}
			<div class="error-state">
				<h1>Invalid Invitation</h1>
				<p class="error-message">{error}</p>
				<a href="/" class="btn btn-secondary">Go to Homepage</a>
			</div>
		{:else if invitation}
			<div class="invitation-card">
				{#if invitation.organization.logo}
					<div class="logo">
						<img
							src={invitation.organization.logo}
							alt="{invitation.organization.name} logo"
						/>
					</div>
				{/if}

				<div class="invitation-header">
					<h1>You're Invited!</h1>
					<p class="subtitle">
						Join <strong>{invitation.organization.name}</strong> as a
						<strong>{formatRole(invitation.role)}</strong>
					</p>
				</div>

				<div class="invitation-details">
					<div class="detail-item">
						<span class="label">Organization:</span>
						<span class="value">{invitation.organization.name}</span>
					</div>

					<div class="detail-item">
						<span class="label">Role:</span>
						<span class="value">{formatRole(invitation.role)}</span>
					</div>

					{#if invitation.team}
						<div class="detail-item">
							<span class="label">Team:</span>
							<span class="value">{invitation.team.name}</span>
						</div>
					{/if}

					<div class="detail-item">
						<span class="label">Invited by:</span>
						<span class="value"
							>{invitation.inviter.user.name || invitation.inviter.user.email}</span
						>
					</div>

					<div class="detail-item">
						<span class="label">Expires:</span>
						<span class="value">{new Date(invitation.expiresAt).toLocaleDateString()}</span>
					</div>
				</div>

				{#if !$session.data?.user}
					<div class="auth-required">
						<p>You need to be signed in to accept this invitation.</p>
						<a href="/sign-in" class="btn btn-primary">Sign In</a>
						<a href="/sign-up" class="btn btn-secondary">Create Account</a>
					</div>
				{:else}
					<div class="invitation-actions">
						<button
							class="btn btn-primary"
							onclick={handleAcceptInvitation}
							disabled={processing}
						>
							{processing ? 'Accepting...' : 'Accept Invitation'}
						</button>

						<button
							class="btn btn-secondary"
							onclick={handleRejectInvitation}
							disabled={processing}
						>
							{processing ? 'Rejecting...' : 'Decline'}
						</button>
					</div>
				{/if}

				{#if error}
					<div class="error-message">
						<p>{error}</p>
					</div>
				{/if}
			</div>
		{/if}
	</div>
</div>

<style>
	.invitation-page {
		min-height: 100vh;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 20px;
	}

	.container {
		width: 100%;
		max-width: 500px;
	}

	.loading {
		text-align: center;
		color: white;
	}

	.spinner {
		width: 40px;
		height: 40px;
		border: 4px solid rgba(255, 255, 255, 0.3);
		border-top: 4px solid white;
		border-radius: 50%;
		animation: spin 1s linear infinite;
		margin: 0 auto 20px;
	}

	@keyframes spin {
		0% {
			transform: rotate(0deg);
		}
		100% {
			transform: rotate(360deg);
		}
	}

	.invitation-card,
	.error-state {
		background: white;
		border-radius: 12px;
		padding: 40px;
		box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
		text-align: center;
	}

	.logo {
		margin-bottom: 30px;
	}

	.logo img {
		max-width: 120px;
		max-height: 60px;
		object-fit: contain;
	}

	.invitation-header h1 {
		font-size: 28px;
		font-weight: 700;
		color: #333;
		margin-bottom: 10px;
	}

	.subtitle {
		font-size: 16px;
		color: #666;
		margin-bottom: 30px;
		line-height: 1.5;
	}

	.invitation-details {
		text-align: left;
		margin-bottom: 30px;
	}

	.detail-item {
		display: flex;
		justify-content: space-between;
		padding: 12px 0;
		border-bottom: 1px solid #f0f0f0;
	}

	.detail-item:last-child {
		border-bottom: none;
	}

	.label {
		font-weight: 500;
		color: #666;
	}

	.value {
		font-weight: 600;
		color: #333;
	}

	.auth-required {
		background: #f8f9fa;
		padding: 20px;
		border-radius: 8px;
		margin-bottom: 20px;
	}

	.auth-required p {
		margin-bottom: 15px;
		color: #666;
	}

	.invitation-actions {
		display: flex;
		gap: 15px;
		justify-content: center;
	}

	.btn {
		padding: 12px 24px;
		border-radius: 6px;
		font-weight: 600;
		text-decoration: none;
		border: none;
		cursor: pointer;
		font-size: 14px;
		transition: all 0.2s;
		display: inline-block;
		text-align: center;
	}

	.btn:disabled {
		opacity: 0.6;
		cursor: not-allowed;
	}

	.btn-primary {
		background: #007ee6;
		color: white;
	}

	.btn-primary:hover:not(:disabled) {
		background: #0066cc;
	}

	.btn-secondary {
		background: #6c757d;
		color: white;
	}

	.btn-secondary:hover:not(:disabled) {
		background: #545b62;
	}

	.error-message {
		background: #f8d7da;
		color: #721c24;
		padding: 15px;
		border-radius: 6px;
		margin-top: 20px;
	}

	.error-state h1 {
		color: #721c24;
		margin-bottom: 15px;
	}

	@media (max-width: 600px) {
		.invitation-card,
		.error-state {
			padding: 30px 20px;
		}

		.invitation-actions {
			flex-direction: column;
		}

		.btn {
			width: 100%;
		}
	}
</style>
