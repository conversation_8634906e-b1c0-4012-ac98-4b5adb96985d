<script lang="ts">
	import * as Card from '$/components/ui/card'
	import { Wallet, TrendingUp, DollarSign, CreditCard } from '@lucide/svelte'

	interface WalletStats {
		totalWallets: number
		activeWallets: number
		totalBalance: number
		transactionsToday: number
		growthRate: number
	}

	interface Props {
		stats: WalletStats
	}

	let { stats }: Props = $props()
</script>

<div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
	<Card.Root>
		<Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
			<Card.Title class="text-sm font-medium">Total Wallets</Card.Title>
			<Wallet class="h-4 w-4 text-muted-foreground" />
		</Card.Header>
		<Card.Content>
			<div class="text-2xl font-bold">{stats.totalWallets.toLocaleString()}</div>
			<p class="text-xs text-muted-foreground">
				+{stats.growthRate}% from last month
			</p>
		</Card.Content>
	</Card.Root>

	<Card.Root>
		<Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
			<Card.Title class="text-sm font-medium">Active Wallets</Card.Title>
			<TrendingUp class="h-4 w-4 text-muted-foreground" />
		</Card.Header>
		<Card.Content>
			<div class="text-2xl font-bold">{stats.activeWallets.toLocaleString()}</div>
			<p class="text-xs text-muted-foreground">
				{((stats.activeWallets / stats.totalWallets) * 100).toFixed(1)}% active rate
			</p>
		</Card.Content>
	</Card.Root>

	<Card.Root>
		<Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
			<Card.Title class="text-sm font-medium">Total Balance</Card.Title>
			<DollarSign class="h-4 w-4 text-muted-foreground" />
		</Card.Header>
		<Card.Content>
			<div class="text-2xl font-bold">${stats.totalBalance.toLocaleString()}</div>
			<p class="text-xs text-muted-foreground">
				Across all wallets
			</p>
		</Card.Content>
	</Card.Root>

	<Card.Root>
		<Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
			<Card.Title class="text-sm font-medium">Transactions Today</Card.Title>
			<CreditCard class="h-4 w-4 text-muted-foreground" />
		</Card.Header>
		<Card.Content>
			<div class="text-2xl font-bold">{stats.transactionsToday.toLocaleString()}</div>
			<p class="text-xs text-muted-foreground">
				+{stats.growthRate}% from yesterday
			</p>
		</Card.Content>
	</Card.Root>
</div>
