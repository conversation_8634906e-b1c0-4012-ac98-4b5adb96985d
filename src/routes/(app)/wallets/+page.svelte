<script lang="ts">
	import { CardShell } from '$/components/layout'
	import { But<PERSON> } from '$/components/ui/button'
	import { Plus } from '@lucide/svelte'

	// Import components
	import WalletStats from './(components)/wallet-stats.svelte'

	// TODO: Replace with actual data from Remult controllers
	const walletStats = $state({
		totalWallets: 4567,
		activeWallets: 4234,
		totalBalance: 1250000,
		transactionsToday: 892,
		growthRate: 22.3
	})

	// Action handlers
	const handleCreateWallet = () => {
		console.log('Create new wallet')
	}
</script>

<CardShell
	heading="Wallets Dashboard"
	subHeading="Manage customer digital wallets and track transaction activity"
>
	{#snippet rightSlot()}
		<Button onclick={handleCreateWallet} class="gap-2">
			<Plus class="h-4 w-4" />
			Create Wallet
		</Button>
	{/snippet}

	<div class="space-y-6">
		<!-- Key Metrics -->
		<WalletStats stats={walletStats} />

		<!-- Placeholder for additional wallet components -->
		<div class="text-muted-foreground py-12 text-center">
			<p>Additional wallet management components will be added here</p>
		</div>
	</div>
</CardShell>
