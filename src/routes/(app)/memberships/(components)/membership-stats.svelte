<script lang="ts">
	import * as Card from '$/components/ui/card'
	import { Users, TrendingUp, Crown, DollarSign, Target } from '@lucide/svelte'

	interface MembershipStats {
		totalMembers: number
		activeMembers: number
		newThisMonth: number
		monthlyRevenue: number
		growthRate: number
	}

	interface Props {
		stats: MembershipStats
	}

	let { stats }: Props = $props()
</script>

<div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
	<Card.Root>
		<Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
			<Card.Title class="text-sm font-medium">Total Members</Card.Title>
			<Users class="text-muted-foreground h-4 w-4" />
		</Card.Header>
		<Card.Content>
			<div class="text-2xl font-bold">{stats.totalMembers.toLocaleString()}</div>
			<p class="text-muted-foreground text-xs">
				+{stats.growthRate}% from last month
			</p>
		</Card.Content>
	</Card.Root>

	<Card.Root>
		<Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
			<Card.Title class="text-sm font-medium">Active Members</Card.Title>
			<Target class="text-muted-foreground h-4 w-4" />
		</Card.Header>
		<Card.Content>
			<div class="text-2xl font-bold">{stats.activeMembers.toLocaleString()}</div>
			<p class="text-muted-foreground text-xs">
				{((stats.activeMembers / stats.totalMembers) * 100).toFixed(1)}% of total
			</p>
		</Card.Content>
	</Card.Root>

	<Card.Root>
		<Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
			<Card.Title class="text-sm font-medium">New This Month</Card.Title>
			<TrendingUp class="text-muted-foreground h-4 w-4" />
		</Card.Header>
		<Card.Content>
			<div class="text-2xl font-bold">{stats.newThisMonth.toLocaleString()}</div>
			<p class="text-muted-foreground text-xs">
				+{stats.growthRate}% from last month
			</p>
		</Card.Content>
	</Card.Root>

	<Card.Root>
		<Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
			<Card.Title class="text-sm font-medium">Monthly Revenue</Card.Title>
			<DollarSign class="text-muted-foreground h-4 w-4" />
		</Card.Header>
		<Card.Content>
			<div class="text-2xl font-bold">${stats.monthlyRevenue.toLocaleString()}</div>
			<p class="text-muted-foreground text-xs">
				+{stats.growthRate}% from last month
			</p>
		</Card.Content>
	</Card.Root>
</div>
