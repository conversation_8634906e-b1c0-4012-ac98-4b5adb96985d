<script lang="ts">
	import * as Card from '$/components/ui/card'
	import * as Chart from '$/components/ui/chart'
	import { AreaChart } from 'layerchart'
	import { scaleUtc } from 'd3-scale'
	import { curveNatural } from 'd3-shape'
	import { TrendingUp } from '@lucide/svelte'

	interface MembershipGrowthData {
		date: Date
		members: number
		newJoiners: number
	}

	interface Props {
		data: MembershipGrowthData[]
	}

	let { data }: Props = $props()

	// Chart configuration
	const chartConfig = {
		members: {
			label: 'Total Members',
			color: 'var(--chart-1)'
		},
		newJoiners: {
			label: 'New Joiners',
			color: 'var(--chart-2)'
		}
	} satisfies Chart.ChartConfig
</script>

<Card.Root>
	<Card.Header>
		<Card.Title class="flex items-center gap-2">
			<TrendingUp class="h-5 w-5" />
			Membership Growth
		</Card.Title>
		<Card.Description>Member acquisition trends over the last 6 months</Card.Description>
	</Card.Header>
	<Card.Content>
		<Chart.Container config={chartConfig} class="h-[300px] w-full overflow-hidden">
			<AreaChart
				{data}
				x="date"
				xScale={scaleUtc()}
				padding={{ left: 20, right: 20, top: 20, bottom: 40 }}
				yPadding={[0, 25]}
				series={[
					{ key: 'newJoiners', label: 'New Joiners', color: 'var(--color-newJoiners)' },
					{ key: 'members', label: 'Total Members', color: 'var(--color-members)' }
				]}
				seriesLayout="stack"
				props={{
					area: {
						curve: curveNatural,
						'fill-opacity': 0.4,
						line: { class: 'stroke-2' },
						motion: 'tween'
					},
					xAxis: {
						format: (v: Date) => v.toLocaleDateString('en-US', { month: 'short' })
					}
				}}
			>
				{#snippet tooltip()}
					<Chart.Tooltip
						indicator="dot"
						labelFormatter={(v: Date) => {
							return v.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })
						}}
					/>
				{/snippet}
			</AreaChart>
		</Chart.Container>
	</Card.Content>
</Card.Root>
