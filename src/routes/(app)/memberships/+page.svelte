<script lang="ts">
	import { CardShell } from '$/components/layout'
	import { But<PERSON> } from '$/components/ui/button'
	import { Plus } from '@lucide/svelte'

	// Import components
	import MembershipStats from './(components)/membership-stats.svelte'
	import MembershipGrowthChart from './(components)/membership-growth-chart.svelte'
	import MembersDataTable from './(components)/members-data-table.svelte'

	// TODO: Replace with actual data from Remult controllers
	const membershipStats = $state({
		totalMembers: 2847,
		activeMembers: 2654,
		newThisMonth: 156,
		monthlyRevenue: 45680,
		growthRate: 12.5
	})

	const membershipGrowthData = $state([
		{ date: new Date('2024-07-01'), members: 2156, newJoiners: 145 },
		{ date: new Date('2024-08-01'), members: 2298, newJoiners: 142 },
		{ date: new Date('2024-09-01'), members: 2445, newJoiners: 147 },
		{ date: new Date('2024-10-01'), members: 2612, newJoiners: 167 },
		{ date: new Date('2024-11-01'), members: 2734, newJoiners: 122 },
		{ date: new Date('2024-12-01'), members: 2847, newJoiners: 113 }
	])

	const recentMembers = $state([
		{
			id: 1,
			name: '<PERSON>',
			email: '<EMAIL>',
			tier: 'Gold',
			joinDate: '2024-01-15',
			status: 'Active',
			totalSpent: 1250.0
		},
		{
			id: 2,
			name: 'Michael Chen',
			email: '<EMAIL>',
			tier: 'Silver',
			joinDate: '2024-01-14',
			status: 'Active',
			totalSpent: 890.5
		},
		{
			id: 3,
			name: 'Emma Davis',
			email: '<EMAIL>',
			tier: 'Bronze',
			joinDate: '2024-01-13',
			status: 'Pending',
			totalSpent: 245.75
		},
		{
			id: 4,
			name: 'James Wilson',
			email: '<EMAIL>',
			tier: 'Gold',
			joinDate: '2024-01-12',
			status: 'Active',
			totalSpent: 2100.25
		}
	])

	// Action handlers
	const handleCreateMember = () => {
		console.log('Create new member')
	}

	const handleViewMember = (id: number) => {
		console.log('View member:', id)
	}

	const handleEditMember = (id: number) => {
		console.log('Edit member:', id)
	}

	const handleDeleteMember = (id: number) => {
		console.log('Delete member:', id)
	}
</script>

<CardShell
	heading="Memberships Dashboard"
	subHeading="Manage exclusive customer membership programs and track engagement"
>
	{#snippet rightSlot()}
		<Button onclick={handleCreateMember} class="gap-2">
			<Plus class="h-4 w-4" />
			Add Member
		</Button>
	{/snippet}

	<div class="space-y-6">
		<!-- Key Metrics -->
		<MembershipStats stats={membershipStats} />

		<!-- Growth Chart -->
		<MembershipGrowthChart data={membershipGrowthData} />

		<!-- Members Data Table -->
		<MembersDataTable
			members={recentMembers}
			onView={handleViewMember}
			onEdit={handleEditMember}
			onDelete={handleDeleteMember}
		/>
	</div>
</CardShell>
