<script lang="ts">
	import { Badge } from '$/components/ui/badge'

	interface Props {
		status: string
	}

	let { status }: Props = $props()

	// Helper function for badge variants
	const getStatusBadgeVariant = (status: string) => {
		switch (status) {
			case 'Active':
				return 'default'
			case 'Completed':
				return 'secondary'
			case 'Expired':
				return 'destructive'
			case 'Cancelled':
				return 'outline'
			default:
				return 'outline'
		}
	}

	const variant = $derived(getStatusBadgeVariant(status))
</script>

<Badge {variant}>{status}</Badge>
