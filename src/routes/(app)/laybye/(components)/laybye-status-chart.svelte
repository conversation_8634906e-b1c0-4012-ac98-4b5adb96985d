<script lang="ts">
	import * as Card from '$/components/ui/card'
	import * as Chart from '$/components/ui/chart'
	import { BarChart } from 'layerchart'
	import { ChartPie } from '@lucide/svelte'

	interface LaybyeStatusData {
		status: string
		count: number
		percentage: number
	}

	interface Props {
		data: LaybyeStatusData[]
	}

	let { data }: Props = $props()

	// Chart configuration with status-specific colors
	const chartConfig = {
		Active: {
			label: 'Active',
			color: 'var(--chart-1)'
		},
		Completed: {
			label: 'Completed',
			color: 'var(--chart-2)'
		},
		Expired: {
			label: 'Expired',
			color: 'var(--chart-3)'
		},
		Cancelled: {
			label: 'Cancelled',
			color: 'var(--chart-4)'
		}
	} satisfies Chart.ChartConfig

	// Status colors helper function for legend
	const getStatusColor = (status: string) => {
		switch (status) {
			case 'Active':
				return 'var(--chart-1)'
			case 'Completed':
				return 'var(--chart-2)'
			case 'Expired':
				return 'var(--chart-3)'
			case 'Cancelled':
				return 'var(--chart-4)'
			default:
				return 'var(--chart-5)'
		}
	}
</script>

<Card.Root>
	<Card.Header>
		<Card.Title class="flex items-center gap-2">
			<ChartPie class="h-5 w-5" />
			Status Distribution
		</Card.Title>
		<Card.Description>Current laybye status breakdown</Card.Description>
	</Card.Header>
	<Card.Content>
		<div class="space-y-4">
			<!-- Status Legend -->
			<div class="grid grid-cols-2 gap-4">
				{#each data as item}
					<div class="flex items-center justify-between">
						<div class="flex items-center gap-2">
							<div
								class="h-3 w-3 rounded-full"
								style="background-color: {getStatusColor(item.status)}"
							></div>
							<span class="text-sm font-medium">{item.status}</span>
						</div>
						<div class="text-right">
							<div class="text-sm font-bold">{item.count}</div>
							<div class="text-muted-foreground text-xs">{item.percentage}%</div>
						</div>
					</div>
				{/each}
			</div>

			<!-- Bar Chart -->
			<Chart.Container config={chartConfig} class="h-[200px]">
				<BarChart
					{data}
					x="status"
					y="count"
					yPadding={[0, 25]}
					props={{
						bars: {
							'fill-opacity': 0.8,
							motion: 'tween'
						}
					}}
				>
					{#snippet tooltip()}
						<Chart.Tooltip
							indicator="dashed"
							labelFormatter={(label: string) => `${label} Laybyes`}
						/>
					{/snippet}
				</BarChart>
			</Chart.Container>
		</div>
	</Card.Content>
</Card.Root>
