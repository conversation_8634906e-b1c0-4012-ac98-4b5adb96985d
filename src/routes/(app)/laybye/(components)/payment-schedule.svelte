<script lang="ts">
	import * as Card from '$/components/ui/card'
	import { Badge } from '$/components/ui/badge'
	import { Progress } from '$/components/ui/progress'
	import { Calendar, CheckCircle, Clock, AlertCircle } from '@lucide/svelte'

	interface PaymentScheduleItem {
		id: number
		customerName: string
		totalAmount: number
		payments: {
			installmentNumber: number
			amount: number
			dueDate: string
			paidDate?: string
			status: 'paid' | 'pending' | 'overdue'
		}[]
		overallProgress: number
	}

	interface Props {
		schedules: PaymentScheduleItem[]
	}

	let { schedules }: Props = $props()

	const getPaymentStatusIcon = (status: string) => {
		switch (status) {
			case 'paid':
				return CheckCircle
			case 'overdue':
				return AlertCircle
			default:
				return Clock
		}
	}

	const getPaymentStatusVariant = (status: string) => {
		switch (status) {
			case 'paid':
				return 'default'
			case 'overdue':
				return 'destructive'
			default:
				return 'secondary'
		}
	}

	const getPaymentStatusColor = (status: string) => {
		switch (status) {
			case 'paid':
				return 'text-green-600'
			case 'overdue':
				return 'text-red-600'
			default:
				return 'text-orange-600'
		}
	}
</script>

<Card.Root>
	<Card.Header>
		<Card.Title class="flex items-center gap-2">
			<Calendar class="h-5 w-5" />
			Payment Schedules
		</Card.Title>
		<Card.Description>Upcoming payment installments and progress tracking</Card.Description>
	</Card.Header>
	<Card.Content>
		<div class="space-y-6">
			{#each schedules as schedule}
				<div class="space-y-3 p-4 rounded-lg border">
					<div class="flex items-center justify-between">
						<div>
							<h4 class="font-medium">{schedule.customerName}</h4>
							<p class="text-sm text-muted-foreground">
								Total: ${schedule.totalAmount.toFixed(2)}
							</p>
						</div>
						<div class="text-right">
							<div class="text-sm font-medium">{schedule.overallProgress}% Complete</div>
							<Progress value={schedule.overallProgress} class="w-24 h-2 mt-1" />
						</div>
					</div>

					<div class="grid gap-2">
						{#each schedule.payments as payment}
							{@const StatusIcon = getPaymentStatusIcon(payment.status)}
							<div class="flex items-center justify-between p-3 rounded-md bg-muted/30">
								<div class="flex items-center gap-3">
									<StatusIcon class="h-4 w-4 {getPaymentStatusColor(payment.status)}" />
									<div>
										<div class="text-sm font-medium">
											Installment {payment.installmentNumber}
										</div>
										<div class="text-xs text-muted-foreground">
											Due: {new Date(payment.dueDate).toLocaleDateString()}
											{#if payment.paidDate}
												• Paid: {new Date(payment.paidDate).toLocaleDateString()}
											{/if}
										</div>
									</div>
								</div>
								<div class="flex items-center gap-2">
									<span class="text-sm font-medium">${payment.amount.toFixed(2)}</span>
									<Badge variant={getPaymentStatusVariant(payment.status)} class="text-xs">
										{payment.status.charAt(0).toUpperCase() + payment.status.slice(1)}
									</Badge>
								</div>
							</div>
						{/each}
					</div>
				</div>
			{/each}

			{#if schedules.length === 0}
				<div class="text-center py-8 text-muted-foreground">
					<Calendar class="mx-auto h-12 w-12 mb-4 opacity-50" />
					<p class="text-lg font-medium">No payment schedules</p>
					<p class="text-sm">Payment schedules will appear here when laybyes are created</p>
				</div>
			{/if}
		</div>
	</Card.Content>
</Card.Root>
