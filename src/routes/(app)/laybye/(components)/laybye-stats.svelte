<script lang="ts">
	import * as Card from '$/components/ui/card'
	import { ShoppingCart, DollarSign, Clock, TrendingUp, Target } from '@lucide/svelte'

	interface LaybyeStats {
		totalActiveLaybyes: number
		totalLaybyeValue: number
		laybyesExpiringThisMonth: number
		averageLaybyeAmount: number
		completionRate: number
		growthRate: number
	}

	interface Props {
		stats: LaybyeStats
	}

	let { stats }: Props = $props()
</script>

<div class="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
	<Card.Root>
		<Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
			<Card.Title class="text-sm font-medium">Active Laybyes</Card.Title>
			<ShoppingCart class="text-muted-foreground h-4 w-4" />
		</Card.Header>
		<Card.Content>
			<div class="text-2xl font-bold">{stats.totalActiveLaybyes.toLocaleString()}</div>
			<p class="text-muted-foreground text-xs">
				+{stats.growthRate}% from last month
			</p>
		</Card.Content>
	</Card.Root>

	<Card.Root>
		<Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
			<Card.Title class="text-sm font-medium">Total Value</Card.Title>
			<DollarSign class="text-muted-foreground h-4 w-4" />
		</Card.Header>
		<Card.Content>
			<div class="text-2xl font-bold">${stats.totalLaybyeValue.toLocaleString()}</div>
			<p class="text-muted-foreground text-xs">
				Across all active laybyes
			</p>
		</Card.Content>
	</Card.Root>

	<Card.Root>
		<Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
			<Card.Title class="text-sm font-medium">Expiring This Month</Card.Title>
			<Clock class="text-muted-foreground h-4 w-4" />
		</Card.Header>
		<Card.Content>
			<div class="text-2xl font-bold">{stats.laybyesExpiringThisMonth}</div>
			<p class="text-muted-foreground text-xs">
				Require immediate attention
			</p>
		</Card.Content>
	</Card.Root>

	<Card.Root>
		<Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
			<Card.Title class="text-sm font-medium">Average Amount</Card.Title>
			<TrendingUp class="text-muted-foreground h-4 w-4" />
		</Card.Header>
		<Card.Content>
			<div class="text-2xl font-bold">${stats.averageLaybyeAmount.toLocaleString()}</div>
			<p class="text-muted-foreground text-xs">
				Per laybye transaction
			</p>
		</Card.Content>
	</Card.Root>

	<Card.Root>
		<Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
			<Card.Title class="text-sm font-medium">Completion Rate</Card.Title>
			<Target class="text-muted-foreground h-4 w-4" />
		</Card.Header>
		<Card.Content>
			<div class="text-2xl font-bold">{stats.completionRate}%</div>
			<p class="text-muted-foreground text-xs">
				Successfully completed
			</p>
		</Card.Content>
	</Card.Root>
</div>
