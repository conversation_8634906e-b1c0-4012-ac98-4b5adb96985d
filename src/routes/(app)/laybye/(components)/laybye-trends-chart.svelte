<script lang="ts">
	import * as Card from '$/components/ui/card'
	import * as Chart from '$/components/ui/chart'
	import { AreaChart } from 'layerchart'
	import { scaleUtc } from 'd3-scale'
	import { curveNatural } from 'd3-shape'
	import { TrendingUp } from '@lucide/svelte'

	interface LaybyeTrendsData {
		date: Date
		laybyes: number
		value: number
	}

	interface Props {
		data: LaybyeTrendsData[]
	}

	let { data }: Props = $props()

	// Chart configuration
	const chartConfig = {
		laybyes: {
			label: 'Laybyes Created',
			color: 'var(--chart-1)'
		},
		value: {
			label: 'Total Value',
			color: 'var(--chart-2)'
		}
	} satisfies Chart.ChartConfig
</script>

<Card.Root>
	<Card.Header>
		<Card.Title class="flex items-center gap-2">
			<TrendingUp class="h-5 w-5" />
			Laybye Trends
		</Card.Title>
		<Card.Description
			>Monthly laybye creation and value trends over the last 6 months</Card.Description
		>
	</Card.Header>
	<Card.Content>
		<Chart.Container config={chartConfig} class="h-[300px] w-full overflow-hidden">
			<AreaChart
				{data}
				x="date"
				xScale={scaleUtc()}
				padding={{ left: 20, right: 20, top: 20, bottom: 40 }}
				yPadding={[0, 25]}
				series={[
					{ key: 'laybyes', label: 'Laybyes Created', color: 'var(--color-laybyes)' },
					{ key: 'value', label: 'Total Value ($)', color: 'var(--color-value)' }
				]}
				seriesLayout="stack"
				props={{
					area: {
						curve: curveNatural,
						'fill-opacity': 0.4,
						line: { class: 'stroke-2' },
						motion: 'tween'
					},
					xAxis: {
						format: (v: Date) => v.toLocaleDateString('en-US', { month: 'short' })
					}
				}}
			>
				{#snippet tooltip()}
					<Chart.Tooltip
						indicator="dot"
						labelFormatter={(v: Date) => {
							return v.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })
						}}
					/>
				{/snippet}
			</AreaChart>
		</Chart.Container>
	</Card.Content>
</Card.Root>
