<script lang="ts">
	import * as Card from '$/components/ui/card'
	import * as Table from '$/components/ui/table'
	import { Button } from '$/components/ui/button'
	import { Input } from '$/components/ui/input'
	import * as DropdownMenu from '$/components/ui/dropdown-menu'
	import { ShoppingCart } from '@lucide/svelte'
	import LaybyeActions from './laybye-actions.svelte'
	import LaybyeStatusBadge from './laybye-status-badge.svelte'
	import {
		type ColumnDef,
		getCoreRowModel,
		getPaginationRowModel,
		getSortedRowModel,
		getFilteredRowModel,
		type PaginationState,
		type SortingState,
		type ColumnFiltersState,
		type VisibilityState
	} from '@tanstack/table-core'
	import { createSvelteTable, FlexRender, renderComponent } from '$/components/ui/data-table'

	interface LaybyeItem {
		id: number
		customerName: string
		customerEmail: string
		items: string[]
		totalAmount: number
		paidAmount: number
		remainingAmount: number
		nextPaymentDate: string
		status: string
		createdDate: string
	}

	interface Props {
		laybyes: LaybyeItem[]
		onView?: (id: number) => void
		onEdit?: (id: number) => void
		onCancel?: (id: number) => void
		onRemind?: (id: number) => void
	}

	let { laybyes, onView, onEdit, onCancel, onRemind }: Props = $props()

	// Data table configuration
	const columns: ColumnDef<LaybyeItem>[] = [
		{
			accessorKey: 'customerName',
			header: 'Customer',
			cell: ({ row }) => {
				const laybye = row.original
				return `
					<div class="space-y-1">
						<div class="font-medium">${laybye.customerName}</div>
						<div class="text-sm text-muted-foreground">${laybye.customerEmail}</div>
					</div>
				`
			}
		},
		{
			accessorKey: 'items',
			header: 'Items',
			cell: ({ row }) => {
				const items = row.getValue('items') as string[]
				const displayItems = items.slice(0, 2).join(', ')
				const moreCount = items.length - 2
				return `
					<div class="space-y-1">
						<div class="font-medium">${displayItems}</div>
						${moreCount > 0 ? `<div class="text-xs text-muted-foreground">+${moreCount} more items</div>` : ''}
					</div>
				`
			}
		},
		{
			accessorKey: 'totalAmount',
			header: 'Total Amount',
			cell: ({ row }) => {
				const amount = row.getValue('totalAmount') as number
				return `<span class="font-medium">$${amount.toFixed(2)}</span>`
			}
		},
		{
			accessorKey: 'paidAmount',
			header: 'Paid / Remaining',
			cell: ({ row }) => {
				const laybye = row.original
				const paidPercentage = (laybye.paidAmount / laybye.totalAmount) * 100
				return `
					<div class="space-y-1">
						<div class="text-sm">
							<span class="font-medium text-green-600">$${laybye.paidAmount.toFixed(2)}</span>
							<span class="text-muted-foreground"> / </span>
							<span class="font-medium text-orange-600">$${laybye.remainingAmount.toFixed(2)}</span>
						</div>
						<div class="w-full bg-secondary rounded-full h-2">
							<div class="bg-primary h-2 rounded-full" style="width: ${paidPercentage}%"></div>
						</div>
					</div>
				`
			}
		},
		{
			accessorKey: 'nextPaymentDate',
			header: 'Next Payment',
			cell: ({ row }) => {
				const date = new Date(row.getValue('nextPaymentDate') as string)
				const isOverdue = date < new Date()
				return `
					<div class="text-sm ${isOverdue ? 'text-destructive font-medium' : ''}">
						${date.toLocaleDateString()}
						${isOverdue ? '<div class="text-xs">OVERDUE</div>' : ''}
					</div>
				`
			}
		},
		{
			accessorKey: 'status',
			header: 'Status',
			cell: ({ row }) => {
				const status = row.getValue('status') as string
				return renderComponent(LaybyeStatusBadge, { status })
			}
		},
		{
			id: 'actions',
			header: 'Actions',
			cell: ({ row }) => {
				const laybye = row.original
				return renderComponent(LaybyeActions, {
					laybyeId: laybye.id,
					onView,
					onEdit,
					onRemind,
					onCancel
				})
			}
		}
	]

	// Data table state
	let pagination = $state<PaginationState>({ pageIndex: 0, pageSize: 10 })
	let sorting = $state<SortingState>([])
	let columnFilters = $state<ColumnFiltersState>([])
	let columnVisibility = $state<VisibilityState>({})

	const table = createSvelteTable({
		get data() {
			return laybyes
		},
		columns,
		getCoreRowModel: getCoreRowModel(),
		getPaginationRowModel: getPaginationRowModel(),
		getSortedRowModel: getSortedRowModel(),
		getFilteredRowModel: getFilteredRowModel(),
		onPaginationChange: (updater: any) => {
			if (typeof updater === 'function') {
				pagination = updater(pagination)
			} else {
				pagination = updater
			}
		},
		onSortingChange: (updater: any) => {
			if (typeof updater === 'function') {
				sorting = updater(sorting)
			} else {
				sorting = updater
			}
		},
		onColumnFiltersChange: (updater: any) => {
			if (typeof updater === 'function') {
				columnFilters = updater(columnFilters)
			} else {
				columnFilters = updater
			}
		},
		onColumnVisibilityChange: (updater: any) => {
			if (typeof updater === 'function') {
				columnVisibility = updater(columnVisibility)
			} else {
				columnVisibility = updater
			}
		},
		state: {
			get pagination() {
				return pagination
			},
			get sorting() {
				return sorting
			},
			get columnFilters() {
				return columnFilters
			},
			get columnVisibility() {
				return columnVisibility
			}
		}
	})
</script>

<Card.Root>
	<Card.Header>
		<Card.Title class="flex items-center gap-2">
			<ShoppingCart class="h-5 w-5" />
			Recent Laybyes
		</Card.Title>
		<Card.Description>Latest laybye transactions and payment status</Card.Description>
	</Card.Header>
	<Card.Content>
		<div class="space-y-4">
			<!-- Table Controls -->
			<div class="flex items-center justify-between">
				<Input
					placeholder="Filter by customer..."
					value={table.getColumn('customerName')?.getFilterValue() as string}
					onchange={(e) =>
						table.getColumn('customerName')?.setFilterValue(e.currentTarget.value)}
					oninput={(e) =>
						table.getColumn('customerName')?.setFilterValue(e.currentTarget.value)}
					class="max-w-sm"
				/>
				<DropdownMenu.Root>
					<DropdownMenu.Trigger>
						{#snippet child({ props })}
							<Button {...props} variant="outline" class="ml-auto">Columns</Button>
						{/snippet}
					</DropdownMenu.Trigger>
					<DropdownMenu.Content align="end">
						{#each table
							.getAllColumns()
							.filter((col) => col.getCanHide()) as column (column.id)}
							<DropdownMenu.CheckboxItem
								class="capitalize"
								checked={column.getIsVisible()}
								onCheckedChange={(checked) => column.toggleVisibility(!!checked)}
							>
								{column.id}
							</DropdownMenu.CheckboxItem>
						{/each}
					</DropdownMenu.Content>
				</DropdownMenu.Root>
			</div>

			<!-- Data Table -->
			<div class="rounded-md border">
				<Table.Root>
					<Table.Header>
						{#each table.getHeaderGroups() as headerGroup (headerGroup.id)}
							<Table.Row>
								{#each headerGroup.headers as header (header.id)}
									<Table.Head colspan={header.colSpan}>
										{#if !header.isPlaceholder}
											<FlexRender
												content={header.column.columnDef.header}
												context={header.getContext()}
											/>
										{/if}
									</Table.Head>
								{/each}
							</Table.Row>
						{/each}
					</Table.Header>
					<Table.Body>
						{#each table.getRowModel().rows as row (row.id)}
							<Table.Row data-state={row.getIsSelected() && 'selected'}>
								{#each row.getVisibleCells() as cell (cell.id)}
									<Table.Cell>
										<FlexRender
											content={cell.column.columnDef.cell}
											context={cell.getContext()}
										/>
									</Table.Cell>
								{/each}
							</Table.Row>
						{:else}
							<Table.Row>
								<Table.Cell colspan={columns.length} class="h-24 text-center">
									No results.
								</Table.Cell>
							</Table.Row>
						{/each}
					</Table.Body>
				</Table.Root>
			</div>

			<!-- Pagination -->
			<div class="flex items-center justify-end space-x-2 py-4">
				<Button
					variant="outline"
					size="sm"
					onclick={() => table.previousPage()}
					disabled={!table.getCanPreviousPage()}
				>
					Previous
				</Button>
				<Button
					variant="outline"
					size="sm"
					onclick={() => table.nextPage()}
					disabled={!table.getCanNextPage()}
				>
					Next
				</Button>
			</div>
		</div>
	</Card.Content>
</Card.Root>
