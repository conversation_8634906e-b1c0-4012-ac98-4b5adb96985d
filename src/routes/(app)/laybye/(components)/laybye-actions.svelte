<script lang="ts">
	import { Button } from '$/components/ui/button'
	import { Eye, Edit, Send, X } from '@lucide/svelte'

	interface Props {
		laybyeId: number
		onView?: (id: number) => void
		onEdit?: (id: number) => void
		onRemind?: (id: number) => void
		onCancel?: (id: number) => void
	}

	let { laybyeId, onView, onEdit, onRemind, onCancel }: Props = $props()
</script>

<div class="flex items-center justify-end gap-1">
	<Button
		variant="ghost"
		size="sm"
		class="h-8 w-8 p-0"
		onclick={() => onView?.(laybyeId)}
		title="View Details"
	>
		<Eye class="h-4 w-4" />
	</Button>
	<Button
		variant="ghost"
		size="sm"
		class="h-8 w-8 p-0"
		onclick={() => onEdit?.(laybyeId)}
		title="Edit"
	>
		<Edit class="h-4 w-4" />
	</Button>
	<Button
		variant="ghost"
		size="sm"
		class="h-8 w-8 p-0"
		onclick={() => onRemind?.(laybyeId)}
		title="Remind Customer"
	>
		<Send class="h-4 w-4" />
	</Button>
	<Button
		variant="ghost"
		size="sm"
		class="text-destructive hover:text-destructive h-8 w-8 p-0"
		onclick={() => onCancel?.(laybyeId)}
		title="Cancel"
	>
		<X class="h-4 w-4" />
	</Button>
</div>
