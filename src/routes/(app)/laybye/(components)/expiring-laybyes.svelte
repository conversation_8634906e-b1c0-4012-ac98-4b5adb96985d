<script lang="ts">
	import * as Card from '$/components/ui/card'
	import { Badge } from '$/components/ui/badge'
	import { Button } from '$/components/ui/button'
	import { AlertTriangle, Calendar, Clock, Mail, Plus } from '@lucide/svelte'

	interface ExpiringLaybye {
		id: number
		customerName: string
		customerEmail: string
		items: string[]
		totalAmount: number
		remainingAmount: number
		nextPaymentDate: string
		daysUntilExpiry: number
		status: string
	}

	interface Props {
		laybyes: ExpiringLaybye[]
		onRemind?: (id: number) => void
		onExtend?: (id: number) => void
		onCancel?: (id: number) => void
		onView?: (id: number) => void
	}

	let { laybyes, onRemind, onExtend, onCancel, onView }: Props = $props()

	const getUrgencyVariant = (daysLeft: number) => {
		if (daysLeft <= 3) return 'destructive'
		if (daysLeft <= 7) return 'secondary'
		return 'outline'
	}

	const getUrgencyText = (daysLeft: number) => {
		if (daysLeft < 0) return 'OVERDUE'
		if (daysLeft === 0) return 'Due Today'
		if (daysLeft === 1) return '1 day left'
		return `${daysLeft} days left`
	}
</script>

<Card.Root>
	<Card.Header>
		<Card.Title class="flex items-center gap-2">
			<AlertTriangle class="h-5 w-5 text-orange-500" />
			Expiring Laybyes
		</Card.Title>
		<Card.Description>Laybyes requiring immediate attention</Card.Description>
	</Card.Header>
	<Card.Content>
		{#if laybyes.length === 0}
			<div class="text-center py-8 text-muted-foreground">
				<Clock class="mx-auto h-12 w-12 mb-4 opacity-50" />
				<p class="text-lg font-medium">No expiring laybyes</p>
				<p class="text-sm">All laybyes are on track with their payment schedules</p>
			</div>
		{:else}
			<div class="space-y-4">
				{#each laybyes as laybye}
					<div class="flex items-center justify-between space-x-4 rounded-lg border p-4 {laybye.daysUntilExpiry <= 3 ? 'border-destructive/50 bg-destructive/5' : laybye.daysUntilExpiry <= 7 ? 'border-orange-200 bg-orange-50' : ''}">
						<div class="space-y-2 flex-1">
							<div class="flex items-center justify-between">
								<div>
									<p class="font-medium">{laybye.customerName}</p>
									<p class="text-sm text-muted-foreground">{laybye.customerEmail}</p>
								</div>
								<Badge variant={getUrgencyVariant(laybye.daysUntilExpiry)}>
									{getUrgencyText(laybye.daysUntilExpiry)}
								</Badge>
							</div>
							
							<div class="text-sm text-muted-foreground">
								<div class="flex items-center gap-4">
									<span>Items: {laybye.items.slice(0, 2).join(', ')}{laybye.items.length > 2 ? ` +${laybye.items.length - 2} more` : ''}</span>
								</div>
								<div class="flex items-center gap-4 mt-1">
									<span>Remaining: <span class="font-medium text-orange-600">${laybye.remainingAmount.toFixed(2)}</span></span>
									<span class="flex items-center gap-1">
										<Calendar class="h-3 w-3" />
										Next: {new Date(laybye.nextPaymentDate).toLocaleDateString()}
									</span>
								</div>
							</div>
						</div>

						<div class="flex items-center gap-2">
							<Button
								size="sm"
								variant="outline"
								onclick={() => onView?.(laybye.id)}
								class="h-8 px-3"
							>
								View
							</Button>
							<Button
								size="sm"
								variant="outline"
								onclick={() => onRemind?.(laybye.id)}
								class="h-8 px-3"
							>
								<Mail class="h-3 w-3 mr-1" />
								Remind
							</Button>
							<Button
								size="sm"
								variant="outline"
								onclick={() => onExtend?.(laybye.id)}
								class="h-8 px-3"
							>
								<Plus class="h-3 w-3 mr-1" />
								Extend
							</Button>
							<Button
								size="sm"
								variant="destructive"
								onclick={() => onCancel?.(laybye.id)}
								class="h-8 px-3"
							>
								Cancel
							</Button>
						</div>
					</div>
				{/each}
			</div>
		{/if}
	</Card.Content>
</Card.Root>
