<script lang="ts">
	import { CardShell } from '$/components/layout'
	import { Button } from '$/components/ui/button'
	import { Plus } from '@lucide/svelte'

	// Import components
	import LaybyeStats from './(components)/laybye-stats.svelte'
	import LaybyeTrendsChart from './(components)/laybye-trends-chart.svelte'
	import LaybyeStatusChart from './(components)/laybye-status-chart.svelte'
	import RecentLaybyesTable from './(components)/recent-laybyes-table.svelte'
	import ExpiringLaybyes from './(components)/expiring-laybyes.svelte'
	import PaymentSchedule from './(components)/payment-schedule.svelte'

	// TODO: Replace with actual data from Remult controllers
	const laybyeStats = $state({
		totalActiveLaybyes: 89,
		totalLaybyeValue: 45680,
		laybyesExpiringThisMonth: 12,
		averageLaybyeAmount: 513,
		completionRate: 78.5,
		growthRate: 15.2
	})

	const laybyeTrendsData = $state([
		{ date: new Date('2024-07-01'), laybyes: 23, value: 11500 },
		{ date: new Date('2024-08-01'), laybyes: 28, value: 14200 },
		{ date: new Date('2024-09-01'), laybyes: 31, value: 15800 },
		{ date: new Date('2024-10-01'), laybyes: 26, value: 13300 },
		{ date: new Date('2024-11-01'), laybyes: 34, value: 17400 },
		{ date: new Date('2024-12-01'), laybyes: 29, value: 14900 }
	])

	const laybyeStatusData = $state([
		{ status: 'Active', count: 89, percentage: 57.1 },
		{ status: 'Completed', count: 45, percentage: 28.8 },
		{ status: 'Expired', count: 12, percentage: 7.7 },
		{ status: 'Cancelled', count: 10, percentage: 6.4 }
	])

	const recentLaybyesData = $state([
		{
			id: 1,
			customerName: 'Sarah Johnson',
			customerEmail: '<EMAIL>',
			items: ['iPhone 15 Pro', 'AirPods Pro', 'MagSafe Charger'],
			totalAmount: 1299.99,
			paidAmount: 433.33,
			remainingAmount: 866.66,
			nextPaymentDate: '2024-02-15',
			status: 'Active',
			createdDate: '2024-01-15'
		},
		{
			id: 2,
			customerName: 'Michael Chen',
			customerEmail: '<EMAIL>',
			items: ['MacBook Air M3', 'Magic Mouse'],
			totalAmount: 1399.0,
			paidAmount: 466.33,
			remainingAmount: 932.67,
			nextPaymentDate: '2024-02-10',
			status: 'Active',
			createdDate: '2024-01-10'
		},
		{
			id: 3,
			customerName: 'Emma Davis',
			customerEmail: '<EMAIL>',
			items: ['iPad Pro 12.9"', 'Apple Pencil', 'Smart Keyboard'],
			totalAmount: 1549.0,
			paidAmount: 1549.0,
			remainingAmount: 0.0,
			nextPaymentDate: '2024-01-20',
			status: 'Completed',
			createdDate: '2023-12-20'
		},
		{
			id: 4,
			customerName: 'James Wilson',
			customerEmail: '<EMAIL>',
			items: ['Apple Watch Ultra 2', 'Sport Loop Band'],
			totalAmount: 849.0,
			paidAmount: 283.0,
			remainingAmount: 566.0,
			nextPaymentDate: '2024-01-25',
			status: 'Expired',
			createdDate: '2023-12-25'
		}
	])

	const expiringLaybyesData = $state([
		{
			id: 1,
			customerName: 'Sarah Johnson',
			customerEmail: '<EMAIL>',
			items: ['iPhone 15 Pro', 'AirPods Pro'],
			totalAmount: 1299.99,
			remainingAmount: 866.66,
			nextPaymentDate: '2024-02-15',
			daysUntilExpiry: 3,
			status: 'Active'
		},
		{
			id: 2,
			customerName: 'Michael Chen',
			customerEmail: '<EMAIL>',
			items: ['MacBook Air M3'],
			totalAmount: 1399.0,
			remainingAmount: 932.67,
			nextPaymentDate: '2024-02-10',
			daysUntilExpiry: -2,
			status: 'Overdue'
		},
		{
			id: 5,
			customerName: 'Lisa Brown',
			customerEmail: '<EMAIL>',
			items: ['AirPods Max', 'Lightning Cable'],
			totalAmount: 579.0,
			remainingAmount: 386.0,
			nextPaymentDate: '2024-02-20',
			daysUntilExpiry: 8,
			status: 'Active'
		}
	])

	const paymentScheduleData = $state([
		{
			id: 1,
			customerName: 'Sarah Johnson',
			totalAmount: 1299.99,
			overallProgress: 33,
			payments: [
				{
					installmentNumber: 1,
					amount: 433.33,
					dueDate: '2024-01-15',
					paidDate: '2024-01-15',
					status: 'paid' as const
				},
				{
					installmentNumber: 2,
					amount: 433.33,
					dueDate: '2024-02-15',
					status: 'pending' as const
				},
				{
					installmentNumber: 3,
					amount: 433.33,
					dueDate: '2024-03-15',
					status: 'pending' as const
				}
			]
		},
		{
			id: 2,
			customerName: 'Michael Chen',
			totalAmount: 1399.0,
			overallProgress: 33,
			payments: [
				{
					installmentNumber: 1,
					amount: 466.33,
					dueDate: '2024-01-10',
					paidDate: '2024-01-10',
					status: 'paid' as const
				},
				{
					installmentNumber: 2,
					amount: 466.33,
					dueDate: '2024-02-10',
					status: 'overdue' as const
				},
				{
					installmentNumber: 3,
					amount: 466.34,
					dueDate: '2024-03-10',
					status: 'pending' as const
				}
			]
		}
	])

	// Action handlers
	const handleCreateLaybye = () => {
		console.log('Create new laybye')
	}

	const handleViewLaybye = (id: number) => {
		console.log('View laybye:', id)
	}

	const handleEditLaybye = (id: number) => {
		console.log('Edit laybye:', id)
	}

	const handleCancelLaybye = (id: number) => {
		console.log('Cancel laybye:', id)
	}

	const handleRemindCustomer = (id: number) => {
		console.log('Remind customer:', id)
	}

	const handleExtendLaybye = (id: number) => {
		console.log('Extend laybye:', id)
	}
</script>

<CardShell
	heading="Laybye Dashboard"
	subHeading="Manage customer layaway purchases and payment schedules"
>
	{#snippet rightSlot()}
		<Button onclick={handleCreateLaybye} class="gap-2">
			<Plus class="h-4 w-4" />
			Create Laybye
		</Button>
	{/snippet}

	<div class="space-y-6">
		<!-- Key Statistics -->
		<LaybyeStats stats={laybyeStats} />

		<!-- Charts Row -->
		<div class="grid gap-6 lg:grid-cols-2">
			<LaybyeTrendsChart data={laybyeTrendsData} />
			<LaybyeStatusChart data={laybyeStatusData} />
		</div>

		<!-- Expiring Laybyes Alert -->
		<ExpiringLaybyes
			laybyes={expiringLaybyesData}
			onRemind={handleRemindCustomer}
			onExtend={handleExtendLaybye}
			onCancel={handleCancelLaybye}
			onView={handleViewLaybye}
		/>

		<!-- Data Tables Row -->
		<div class="grid gap-6 lg:grid-cols-2">
			<RecentLaybyesTable
				laybyes={recentLaybyesData}
				onView={handleViewLaybye}
				onEdit={handleEditLaybye}
				onCancel={handleCancelLaybye}
				onRemind={handleRemindCustomer}
			/>
			<PaymentSchedule schedules={paymentScheduleData} />
		</div>
	</div>
</CardShell>
