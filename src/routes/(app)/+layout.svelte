<script lang="ts">
	import { LayoutWrapper } from '$/components/layout'
	import { remult } from 'remult'
	import { HomeSheetModal, LayoutMain } from './(components)'
	import { setupPasskey, SheetTypes } from '$/lib'
	import { getAuthStore, getUtilsStore } from '$/stores'

	let { children }: { children: any } = $props()
	const authStore = getAuthStore()
	const utilsStore = getUtilsStore()

	// Set homesheet to Marketing on app refresh/reload
	$effect.root(() => {
		utilsStore.setHomeSheet(SheetTypes.Marketing)
	})

	$effect.root(() => {
		if (remult.authenticated())
			setupPasskey().then((k) => {
				if (!k) console.log('Passkey setup failed')
			})
	})

	$inspect('is authstore authenticated', authStore.isAuthenticated)
</script>

<LayoutWrapper>
	<LayoutMain>
		{@render children?.()}
	</LayoutMain>
	<HomeSheetModal />
</LayoutWrapper>
