<script lang="ts">
	import * as Sidebar from '$/components/ui/sidebar'
	import { default as TeamSwitcher } from './team_switcher.svelte'
	import { default as NavMain } from './nav-main.svelte'
	import { default as NavUser } from './nav-user.svelte'
	import type { ComponentProps } from 'svelte'
	import { Logo } from '$/components/common'
	import { menuData } from '$/lib'

	let {
		ref = $bindable(null),
		collapsible = 'icon',
		...restProps
	}: ComponentProps<typeof Sidebar.Root> = $props()
</script>

<Sidebar.Root bind:ref {collapsible} {...restProps} class="text-lg">
	<Sidebar.Header>
		<Logo clsName="px-2 flex items-center justify-start" />
		<TeamSwitcher />
	</Sidebar.Header>
	<Sidebar.Content>
		<NavMain items={menuData.navMain} />
		<!--		<NavProjects projects={data.projects} />-->
	</Sidebar.Content>
	<Sidebar.Footer>
		<NavUser />
	</Sidebar.Footer>
	<Sidebar.Rail />
</Sidebar.Root>
