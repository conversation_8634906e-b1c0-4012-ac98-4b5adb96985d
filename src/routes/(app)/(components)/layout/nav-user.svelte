<script lang="ts">
	import { FormAlert } from '$/components/common'
	import * as Avatar from '$/components/ui/avatar'
	import { Button } from '$/components/ui/button'
	import * as DropdownMenu from '$/components/ui/dropdown-menu'
	import * as Sidebar from '$/components/ui/sidebar'
	import { useSidebar } from '$/components/ui/sidebar'
	import { PAGES } from '$/lib/routes'
	import { logOutFormSchema, TeamPlans, type FormMessage, type LogOutFormSchemaType } from '$/lib'
	import BadgeCheck from '@lucide/svelte/icons/badge-check'
	import Bell from '@lucide/svelte/icons/bell'
	import ChevronsUpDown from '@lucide/svelte/icons/chevrons-up-down'
	import CreditCard from '@lucide/svelte/icons/credit-card'
	import LogOut from '@lucide/svelte/icons/log-out'
	import Sparkles from '@lucide/svelte/icons/sparkles'
	import { valibot } from 'sveltekit-superforms/adapters'
	import { defaults, superForm } from 'sveltekit-superforms/client'
	import { getFlashModule } from '$/lib'
	import { goto } from '$app/navigation'
	import { getAuthStore } from '$/stores'
	import { authClient } from '$/lib/clients'
	import { Organization } from '$/lib/models'
	import { remult } from 'remult'

	const sidebar = useSidebar()
	const authStore = getAuthStore()

	let org = $state<Organization | undefined>(undefined)
	let orgNameInitials = $derived(
		org?.name
			?.split(' ')
			.map((wrd: string) => wrd.charAt(0).toUpperCase())
			.join('') ?? 'NA'
	)

	$effect(() => {
		const unsubscribe = remult
			.repo(Organization)
			.liveQuery({
				where: { id: remult.user?.organizationId }
			})
			.subscribe((info) => {
				org = info.items.firstOrNull() ?? undefined
			})

		return () => {
			unsubscribe()
		}
	})
	const form = superForm<LogOutFormSchemaType, FormMessage>(defaults(valibot(logOutFormSchema)), {
		SPA: true,
		validators: valibot(logOutFormSchema),
		multipleSubmits: 'prevent',
		async onUpdate({ form }) {
			if (form.valid) {
				// Call auth service directly for logout
				try {
					await authClient.signOut()

					authStore.clearStore()
					await goto(PAGES._ROOT)
				} catch (error) {
					console.log('logout error', error)
				}
			}
		},
		...getFlashModule()
	})
	const { enhance, message, submitting } = form
</script>

<Sidebar.Menu>
	<Sidebar.MenuItem>
		<DropdownMenu.Root>
			<DropdownMenu.Trigger>
				{#snippet child({ props })}
					<Sidebar.MenuButton
						size="lg"
						class="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
						{...props}
					>
						<Avatar.Root class="h-8 w-8 rounded-lg">
							<Avatar.Image src={authStore.user?.avatarUrl} alt={authStore.fullName} />
							<Avatar.Fallback class="rounded-lg">{orgNameInitials}</Avatar.Fallback>
						</Avatar.Root>
						<div class="grid flex-1 text-left text-sm leading-tight">
							<span class="truncate font-semibold">{authStore.fullName}</span>
							<span class="truncate text-xs">{authStore.user?.email ?? ''}</span>
						</div>
						<ChevronsUpDown class="ml-auto size-4" />
					</Sidebar.MenuButton>
				{/snippet}
			</DropdownMenu.Trigger>
			<DropdownMenu.Content
				class="w-[--bits-dropdown-menu-anchor-width] min-w-56 rounded-lg"
				side={sidebar.isMobile ? 'bottom' : 'right'}
				align="end"
				sideOffset={4}
			>
				<DropdownMenu.Label class="p-0 font-normal">
					<div class="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
						<Avatar.Root class="h-8 w-8 rounded-lg">
							<Avatar.Image src={authStore.user?.avatarUrl} alt={authStore.fullName} />
							<Avatar.Fallback class="rounded-lg">{orgNameInitials}</Avatar.Fallback>
						</Avatar.Root>
						<div class="grid flex-1 text-left text-sm leading-tight">
							<span class="truncate font-semibold">{authStore.fullName}</span>
							<span class="truncate text-xs">{authStore.user?.email}</span>
						</div>
					</div>
				</DropdownMenu.Label>
				<DropdownMenu.Separator />
				{#if org?.currentPlan === TeamPlans.Free}
					<DropdownMenu.Group>
						<DropdownMenu.Item>
							<Sparkles />
							Upgrade to Pro
						</DropdownMenu.Item>
					</DropdownMenu.Group>
				{/if}
				<DropdownMenu.Separator />
				<DropdownMenu.Group>
					<DropdownMenu.Item>
						<BadgeCheck />
						Account
					</DropdownMenu.Item>
					<DropdownMenu.Item>
						<CreditCard />
						Billing
					</DropdownMenu.Item>
					<DropdownMenu.Item>
						<Bell />
						Notifications
					</DropdownMenu.Item>
				</DropdownMenu.Group>
				<DropdownMenu.Separator />
				<DropdownMenu.Item>
					<form method="POST" use:enhance>
						<FormAlert {message} />
						<Button type="submit" variant="ghost" disabled={$submitting}>
							<LogOut />
							Log out
						</Button>
					</form>
				</DropdownMenu.Item>
			</DropdownMenu.Content>
		</DropdownMenu.Root>
	</Sidebar.MenuItem>
</Sidebar.Menu>
