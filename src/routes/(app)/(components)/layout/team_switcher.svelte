<script lang="ts">
	import * as Sidebar from '$/components/ui/sidebar'
	import { default as OrgName } from './app-org-name.svelte'
	import { Organization } from '$/lib/models'
	import { remult } from 'remult'
	import 'linq-extensions'

	let org = $state<Organization | undefined>(undefined)

	$effect(() => {
		const unsubscribe = remult
			.repo(Organization)
			.liveQuery({
				where: { id: remult.user?.organizationId }
			})
			.subscribe((info) => {
				org = info.items.firstOrNull() ?? undefined
			})

		return () => {
			unsubscribe()
		}
	})
</script>

<Sidebar.Menu>
	<Sidebar.MenuItem>
		<Sidebar.MenuButton
			size="lg"
			class="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
		>
			<div class="flex aspect-square size-8 items-center justify-center rounded-none">
				<OrgName />
			</div>
			<div class="grid flex-1 text-left text-sm leading-tight">
				<span class="truncate font-semibold">
					{org?.name}
				</span>
				<span class="truncate text-xs">{org?.currentPlan}</span>
			</div>
			<!--						<ChevronsUpDown class="ml-auto" />-->
		</Sidebar.MenuButton>
	</Sidebar.MenuItem>
</Sidebar.Menu>
