<script lang="ts">
	import * as Avatar from '$/components/ui/avatar'
	import { getInitials } from '$/lib'
	import { Organization } from '$/lib/models'
	import { remult } from 'remult'

	let org = $state<Organization | undefined>(undefined)
	const teamInitials = $derived(org?.name ? getInitials(org.name, '') : '')

	$effect(() => {
		const unsubscribe = remult
			.repo(Organization)
			.liveQuery({
				where: { id: remult.user?.organizationId }
			})
			.subscribe((info) => {
				org = info.items.firstOrNull() ?? undefined
			})

		return () => {
			unsubscribe()
		}
	})

	$inspect('team Name', teamInitials)
</script>

<span class="font-semibold">{teamInitials}</span>

<!-- <Avatar.Root class="h-8 w-8 rounded-lg">
	<Avatar.Image src="" alt={teamName} />
	<Avatar.Fallback class="rounded-lg">{teamInitials}</Avatar.Fallback>
</Avatar.Root> -->
