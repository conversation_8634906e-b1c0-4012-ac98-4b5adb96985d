<script lang="ts">
	import { getUtilsStore } from '$/stores'
	import { SheetTypes } from '$/lib'
	import { But<PERSON> } from '$/components/ui/button'
	import { ArrowRight } from '@lucide/svelte'

	const utilsStore = getUtilsStore()
	const openSignupModel = () => {
		utilsStore.setHomeSheet(SheetTypes.SignUp)
	}
</script>

<section class="bg-muted/40 w-full py-8 md:py-20 lg:py-28">
	<div class="container px-4 md:px-6">
		<div class="flex flex-col items-center space-y-4 text-center">
			<div class="space-y-2">
				<p class="section-heading">Transform Your Business Today</p>
				<p
					class="text-muted-foreground mx-auto max-w-[600px] md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed"
				>
					Start using SpenDeed now and see the difference in your customer engagement and
					sales.
				</p>
			</div>
			<Button onclick={openSignupModel} size="lg">
				Sign Up Now
				<ArrowRight class="ml-2 size-4" />
			</Button>
		</div>
	</div>
</section>
