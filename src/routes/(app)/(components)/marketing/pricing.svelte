<script lang="ts">
	import { default as PricingCard } from './pricing_card.svelte'

	const pricingPlans = $state([
		{
			title: 'Starter',
			price: 'R529/mo',
			features: ['Up to 500 customers', 'Basic reward programs', 'AI chat support'],
			buttonText: 'Get Started'
		},
		{
			title: 'Pro',
			price: 'R1,439/mo',
			features: [
				'Up to 2,000 customers',
				'Advanced reward programs',
				'AI chat with product recommendations'
			],
			buttonText: 'Get Started'
		},
		{
			title: 'Enterprise',
			price: 'Custom',
			features: ['Unlimited customers', 'Custom reward programs', 'Advanced AI features'],
			buttonText: 'Contact Sales'
		}
	])
</script>

<section id="pricing" class="bg-background w-full py-4 md:py-12 lg:py-26 xl:py-20">
	<div class="container px-4 md:px-6">
		<p class="section-heading mb-8 text-center">Simple, Transparent Pricing</p>
		<div class="grid gap-6 lg:grid-cols-3">
			{#each pricingPlans as plan}
				<PricingCard
					title={plan.title}
					price={plan.price}
					features={plan.features}
					buttonText={plan.buttonText}
				/>
			{/each}
		</div>
	</div>
</section>
