<script lang="ts">
	import { LightSwitch, Logo } from '$/components/common'
	import { Button } from '$/components/ui/button'
	import { cn } from '$/lib/shadcn.utils'
	import { getUtilsStore } from '$/stores'
	import { SheetTypes } from '$/lib'
	import { ChevronLeft } from '@lucide/svelte'

	let {
		children,
		pageTitle,
		pageDescription,
		containerWidth = 'w-[350px]'
	}: {
		children: any
		pageTitle: string
		pageDescription: string
		containerWidth?: string
	} = $props()
	const utilsStore = getUtilsStore()
	const switchToHome = () => {
		utilsStore.setHomeSheet(SheetTypes.Marketing)
	}
</script>

<div
	class="container grid h-screen w-full flex-col items-center justify-center lg:max-w-none lg:px-0"
>
	<Button onclick={switchToHome} variant="ghost" class="absolute top-4 left-4 md:top-8 md:left-8">
		<ChevronLeft className="mr-2 size-4" />
		Home
	</Button>
	<div class="!fixed right-8 bottom-2 z-500">
		<LightSwitch />
	</div>
	<div class="lg:p-8">
		<div class={cn('mx-auto flex w-full flex-col justify-center space-y-6', containerWidth)}>
			<div class="flex flex-col space-y-2 text-center">
				<Logo onclick={switchToHome} />
				<h1 class="mt-3 text-lg font-semibold tracking-tight">{pageTitle}</h1>
				<p class="text-muted-foreground text-sm">{pageDescription}</p>
			</div>
			{@render children?.()}
		</div>
	</div>
</div>
