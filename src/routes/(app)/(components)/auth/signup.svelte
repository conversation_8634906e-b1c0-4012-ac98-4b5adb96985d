<script lang="ts">
	import { FormAlert } from '$/components/common'
	import { Button } from '$/components/ui/button'
	import * as Form from '$/components/ui/form'
	import { Input } from '$/components/ui/input'
	import { getUtilsStore } from '$/stores'
	import { defaults, setError, superForm } from 'sveltekit-superforms'
	import { valibotClient } from 'sveltekit-superforms/adapters'
	import { default as AuthWrapper } from './auth_wrapper.svelte'
	import { PasskeyIcon } from '$/components/icons'
	import { getFlashModule } from '$/lib'
	import { generateRandomPassword } from '$/lib/helpers'
	import { toast } from 'svelte-sonner'
	import { Textarea } from '$/components/ui/textarea'
	import { image } from 'gravatar-gen'
	import {
		saveEmailLocally,
		SheetTypes,
		signUpFormSchema,
		type FormMessage,
		type SignUpFormType,
		UserAccountTypes,
		RolesType,
		type IUserMetadata
	} from '$/lib'
	import { authClient } from '$/lib/clients/auth-client'
	import { uuidv7 } from 'uuidv7'
	import type { ErrorContext } from 'better-auth/svelte'

	const utilsStore = getUtilsStore()
	const form = superForm<SignUpFormType, FormMessage>(
		defaults({} as any, valibotClient(signUpFormSchema)),
		{
			id: 'signup-form',
			validators: valibotClient(signUpFormSchema),
			multipleSubmits: 'prevent',
			dataType: 'json',
			SPA: true,
			onUpdate: async ({ form, cancel }) => {
				form.data.authType = 'email'
				form.data.acceptTerms = true
				if (!form.valid) {
					setError(form, '', 'Please fill in all fields')
					cancel()
				}

				const formData = form.data
				utilsStore.setCurrentAuthEmail(formData.email)

				try {
					const avatarUrl = await image(formData.email!, {
						size: 200,
						defaultImage: 'blank',
						includeExtention: true,
						rating: 'g'
					})
					const organizationId = uuidv7()
					const metadata: IUserMetadata = {
						businessName: formData.businessName,
						businessEmail: formData.businessEmail,
						organizationId: organizationId,
						businessAddress: formData.businessAddress
					}

					const signUpPayload = {
						email: formData.email,
						password: generateRandomPassword(9),
						name: `${formData.givenName} ${formData.familyName}`,
						image: avatarUrl,
						callbackURL: window.location.origin + '/signup-code',
						// Additional user fields
						givenName: formData.givenName,
						familyName: formData.familyName,
						displayName: `${formData.givenName} ${formData.familyName}`,
						phoneNumber: formData.phoneNumber || '',
						gender: 'Other', // Default gender value
						userStatus: 'Active',
						roles: [RolesType.User, RolesType.OrgAdmin],
						phoneNumberVerified: false,
						acceptTerms: formData.acceptTerms,
						userType: UserAccountTypes.Staff,
						authType: 'passkey',
						avatarUrl: avatarUrl,
						locale: 'en',
						metadata: JSON.stringify(metadata)
					}

					await authClient.signUp.email(signUpPayload, {
						onSuccess: (signUpData: any) => {
							if (signUpData) {
								utilsStore.st.homeSheet = {
									authingUserId: signUpData?.user?.id,
									authingUserEmail: formData.email,
									passkeyEnabled: false
								}
								saveEmailLocally(formData.email)
								utilsStore.setHomeSheet(SheetTypes.SignUpCode)
								toast.success(
									'Signup successful! Please check your email for verification code.'
								)
							} else {
								toast.error('Signup failed: No signup data received')
								cancel()
							}
						},
						onError: (signUpError: ErrorContext) => {
							console.error(signUpError.error.message)
							toast.error(`Signup failed: ${signUpError.error.message}`)
							cancel()
						}
					})
				} catch (err) {
					console.error('Signup error:', err)
					toast.error(`Signup failed: ${(err as Error)?.message}`)
					cancel()
				}
			},
			...getFlashModule()
		}
	)
	const { form: formData, enhance, submitting, message, allErrors } = form

	const switchToLogin = () => {
		utilsStore.setHomeSheet(SheetTypes.Login)
	}

	let signupButtonText = $state('Sign Up')
</script>

<AuthWrapper pageTitle="Sign Up" pageDescription="Create your account">
	<div class="grid gap-6">
		<form method="POST" class="space-y-6" use:enhance>
			<FormAlert {message} />
			<fieldset disabled={$submitting} class="grid gap-2">
				<Form.Field {form} name="givenName">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>First Name</Form.Label>
							<Input {...props} placeholder="First Name" bind:value={$formData.givenName} />
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>
				<Form.Field {form} name="familyName">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Last Name</Form.Label>
							<Input
								{...props}
								placeholder="Last Name"
								autocomplete="family-name"
								bind:value={$formData.familyName}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>
				<Form.Field {form} name="email">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Email</Form.Label>
							<Input
								type="email"
								{...props}
								placeholder="Email"
								autocomplete="email"
								bind:value={$formData.email}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>
				<Form.Field {form} name="businessName">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Business Name</Form.Label>
							<Input
								{...props}
								placeholder="Business Name"
								bind:value={$formData.businessName}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>
				<Form.Field {form} name="businessEmail">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Business Email</Form.Label>
							<Input
								type="email"
								{...props}
								placeholder="Email"
								autocomplete="email"
								bind:value={$formData.businessEmail}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>
				<Form.Field {form} name="phoneNumber">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Phone Number</Form.Label>
							<Input
								{...props}
								placeholder="Phone Number"
								bind:value={$formData.phoneNumber}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>
				<Form.Field {form} name="businessAddress">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Business Address</Form.Label>
							<Textarea
								{...props}
								placeholder="Business Address"
								bind:value={$formData.businessAddress}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>
			</fieldset>
			<Form.Button disabled={$submitting || $allErrors?.length > 0} class="mt-4 w-full">
				{#if $submitting}
					<PasskeyIcon class="animate-caret-blink size-5 fill-current" />
				{/if}
				{$submitting ? 'Please wait...' : signupButtonText}
			</Form.Button>
		</form>
		<div class="relative">
			<p class="text-muted-foreground px-8 text-center text-sm">
				<Button variant="link" onclick={switchToLogin} class="hover:text-brand no-underline">
					Already have an account? Sign In
				</Button>
			</p>
		</div>
	</div>
</AuthWrapper>
