<script lang="ts">
	import * as Form from '$/components/ui/form'
	import { Button } from '$/components/ui/button'
	import { Input } from '$/components/ui/input'
	import * as Tabs from '$/components/ui/tabs'
	import { getAuthStore, getUtilsStore } from '$/stores'
	import { toast } from 'svelte-sonner'
	import { SheetTypes, type FormMessage, loginFormSchema, type LoginFormType } from '$/lib'
	import { Loader2, KeyRound, Mail } from '@lucide/svelte'
	import { onMount } from 'svelte'
	import { defaults, superForm } from 'sveltekit-superforms'
	import { valibot } from 'sveltekit-superforms/adapters'
	import { FormAlert, HiddenInput } from '$/components/common'
	import { default as AuthWrapper } from './auth_wrapper.svelte'
	import { page } from '$app/state'
	import { getFlashModule } from '$/lib/helpers'
	import { checkWebAuthnSupport } from '$/lib'
	import { getLocalEmail, getISessionData } from '$/lib'
	import { authClient } from '$/lib/clients/auth-client'
	import type { ErrorContext, SuccessContext } from 'better-auth/svelte'

	let passKeyEnabled = $state(false)
	let currentRoute = $derived(page.url.pathname ?? '/')
	const utilsStore = getUtilsStore()
	const authStore = getAuthStore()
	const form = superForm<LoginFormType, FormMessage>(defaults(valibot(loginFormSchema)), {
		id: 'login-form',
		validators: valibot(loginFormSchema),
		multipleSubmits: 'prevent',
		SPA: true,
		onUpdate: async ({ form, cancel }) => {
			if (!form.valid) return
			try {
				const formData = form.data

				if (authStrategy === 'passkey' && passKeyEnabled) {
					await authClient.signIn.passkey(
						{
							autoFill: true,
							email: formData.email
						},
						{
							onSuccess: async (signInData: SuccessContext<any>) => {
								console.log('signInData', signInData?.data)
								if (signInData?.data) {
									await authStore.checkAuth()
									toast.success('Login successful!')
								} else {
									toast.error('Login failed: No sign-in data received')
									cancel()
								}
							},
							onError: (signInError: ErrorContext) => {
								console.error('passkey error:', signInError)
								toast.error(`Sign-in failed: ${signInError.error.message}`)
								cancel()
							}
						}
					)
				} else {
					await authClient.emailOtp.sendVerificationOtp(
						{
							email: formData.email,
							type: 'sign-in'
						},
						{
							onSuccess(context) {
								console.log(context.data)
								utilsStore.setCurrentAuthEmail(formData.email)
								utilsStore.st.homeSheet = {
									authingUserEmail: formData.email,
									authingUserId: undefined,
									passkeyEnabled: passKeyEnabled // Store passkey preference for potential registration
								}
								utilsStore.setHomeSheet(SheetTypes.LoginCode)
								toast.success('Please check your email for your sign-in code.')
							},
							onError(context) {
								console.error(context.error.message)
								toast.error(`Failed to send OTP: ${context.error.message}`)
								cancel()
							}
						}
					)

					// Store email for passkey prompt after successful login
				}
			} catch (err) {
				console.error('Login error:', err)
				toast.error(`Login failed: ${(err as Error).message}`)
				cancel()
			}
		},
		...getFlashModule()
	})
	const { form: formData, enhance, submitting, message, allErrors } = form

	const switchToSignUp = () => {
		utilsStore.setHomeSheet(SheetTypes.SignUp)
	}

	onMount(async () => {
		const webAuthNSupport = await checkWebAuthnSupport()
		passKeyEnabled = webAuthNSupport.isAvailable
		utilsStore.st.homeSheet.passkeyEnabled = true

		// Preload passkeys for conditional UI (browser autofill)
		if (passKeyEnabled && webAuthNSupport.isConditionalMediationAvailable) {
			try {
				// This will enable conditional UI - browser will show passkey autofill if available
				void authClient.signIn.passkey({
					autoFill: true
				})
				console.log('Conditional UI enabled - passkeys will autofill if available')
			} catch (error) {
				console.warn('Failed to enable conditional UI:', error)
			}
		}

		const email = getLocalEmail()
		if (email) $formData.email = email
	})

	let loginButtonText = $state('Log In')
	let authStrategy = $state<'passkey' | 'email'>('email')
	let submittingPasskey = $derived.by(() => $submitting && authStrategy === 'passkey')
	let readonlyEmail = $derived.by(() => submittingPasskey || undefined)

	// Update button text based on strategy and passkey availability
	$effect(() => {
		if (authStrategy === 'passkey' && passKeyEnabled) {
			loginButtonText = 'Login with Passkey'
		} else {
			loginButtonText = 'Login with Email'
		}
	})
</script>

<AuthWrapper pageTitle="Welcome back" pageDescription="Sign in to your account">
	<div class="grid gap-6">
		<!-- Authentication Strategy Selection -->
		<Tabs.Root bind:value={authStrategy} class="w-full">
			<Tabs.List class="grid w-full grid-cols-2">
				<Tabs.Trigger value="email" class="flex items-center gap-2">
					<Mail class="h-4 w-4" />
					Email OTP
				</Tabs.Trigger>
				<Tabs.Trigger
					value="passkey"
					disabled={!passKeyEnabled}
					class="flex items-center gap-2 {!passKeyEnabled ? 'opacity-50' : ''}"
				>
					<KeyRound class="h-4 w-4" />
					Passkey
				</Tabs.Trigger>
			</Tabs.List>
		</Tabs.Root>

		<form method="POST" class="space-y-6" use:enhance>
			<FormAlert {message} />
			<fieldset disabled={$submitting} class="grid gap-2">
				<Form.Field {form} name="email">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Email</Form.Label>
							<Input
								{...props}
								bind:value={$formData.email}
								autocomplete="email webauthn"
								readonly={readonlyEmail}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>
				<Form.Field {form} name="returnUrl">
					<Form.Control>
						{#snippet children({ props })}
							<HiddenInput {props} value={currentRoute} />
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<Form.Button disabled={submittingPasskey || $allErrors?.length > 0}>
					{#if submittingPasskey}
						<Loader2 class="mr-2 size-4 animate-spin" />
					{/if}
					{submittingPasskey ? 'Please wait...' : loginButtonText}
				</Form.Button>
			</fieldset>
		</form>
		<div class="relative">
			<p class="text-muted-foreground px-8 text-center text-sm">
				<Button variant="link" onclick={switchToSignUp} class="hover:text-brand no-underline">
					Don't have an account? Sign Up
				</Button>
			</p>
		</div>
	</div>
</AuthWrapper>
