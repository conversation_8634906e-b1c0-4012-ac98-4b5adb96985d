<script lang="ts">
	import * as Form from '$/components/ui/form'
	import { getUtilsStore } from '$/stores'
	import {
		SheetTypes,
		signUpCodeFormSchema,
		type FormMessage,
		type SignUpCodeFormType
	} from '$/lib'
	import { defaults, superForm } from 'sveltekit-superforms'
	import { valibot } from 'sveltekit-superforms/adapters'
	import { default as AuthWrapper } from './auth_wrapper.svelte'
	import { FormAlert } from '$/components/common'
	import { Loader2 } from '@lucide/svelte'
	import { onMount } from 'svelte'
	import { getFlashModule } from '$/lib/helpers'
	import { toast } from 'svelte-sonner'
	import { authClient } from '$/lib/clients/auth-client'
	import * as InputOTP from '$/components/ui/input-otp'
	import { Button } from '$/components/ui/button'
	import type { ErrorContext } from 'better-auth/svelte'

	const utilsStore = getUtilsStore()
	const form = superForm<SignUpCodeFormType, FormMessage>(
		defaults(valibot(signUpCodeFormSchema)),
		{
			id: 'signup-code-form',
			delayMs: 0,
			invalidateAll: true,
			multipleSubmits: 'prevent',
			SPA: true,
			validators: valibot(signUpCodeFormSchema),
			onUpdate: async ({ form, cancel }) => {
				if (!form.valid) return

				const formData = form.data
				const email = utilsStore.st.currentAuthEmail || formData.email

				await authClient.emailOtp.verifyEmail(
					{
						email: email || '',
						otp: formData.code
					},
					{
						onSuccess: (verifyEmailData: any) => {
							if (verifyEmailData) {
								utilsStore.setHomeSheet(SheetTypes.Login)
								toast.success('SignUp successful, please log in!')
							} else {
								toast.error('Signup failed: No signup data received')
								cancel()
							}
						},
						onError: (verifyEmailError: ErrorContext) => {
							console.error(verifyEmailError.error.message)
							toast.error(`Signup failed: ${verifyEmailError.error.message}`)
							cancel()
						}
					}
				)
			},
			...getFlashModule()
		}
	)
	const { form: formData, enhance, submitting, message, allErrors } = form

	let resendDisabled = $state(false)
	const resend = async () => {
		const email = utilsStore.st.currentAuthEmail
		if (email) {
			console.log('resending OTP to:', email)
			resendDisabled = true

			try {
				// Resend OTP using EmailOTP plugin
				const { error } = await authClient.emailOtp.sendVerificationOtp({
					email: email,
					type: 'email-verification' // Default to sign-in type for resend
				})

				if (error) {
					toast.error(`Failed to resend code: ${error.message}`)
				} else {
					toast.success('Verification code sent!')
				}
			} catch (err) {
				console.error('Resend error:', err)
				toast.error('Failed to resend code')
			}

			setTimeout(() => {
				resendDisabled = false
			}, 3000)
		} else {
			console.log('no email to resend to')
			toast.error('No email found to resend to.')
			utilsStore.setHomeSheet(SheetTypes.SignUp)
		}
	}
	const signIn = () => {
		utilsStore.setHomeSheet(SheetTypes.Login)
	}

	onMount(() => {
		const email = utilsStore.st.currentAuthEmail
		if (email) $formData.email = email
	})
</script>

<AuthWrapper pageTitle="Sign Up" pageDescription="Verify your Email">
	<div class="grid gap-6">
		<form method="POST" use:enhance>
			<FormAlert {message} />
			<Form.Field {form} name="code" class="mx-auto justify-items-center">
				<Form.Control>
					{#snippet children({ props })}
						<InputOTP.Root maxlength={6} {...props} bind:value={$formData.code}>
							{#snippet children({ cells })}
								<InputOTP.Group>
									{#each cells.slice(0, 2) as cell (cell)}
										<InputOTP.Slot {cell} />
									{/each}
								</InputOTP.Group>
								<InputOTP.Separator />
								<InputOTP.Group>
									{#each cells.slice(2, 4) as cell (cell)}
										<InputOTP.Slot {cell} />
									{/each}
								</InputOTP.Group>
								<InputOTP.Separator />
								<InputOTP.Group>
									{#each cells.slice(4, 6) as cell (cell)}
										<InputOTP.Slot {cell} />
									{/each}
								</InputOTP.Group>
							{/snippet}
						</InputOTP.Root>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>

			<Form.Button disabled={$submitting || $allErrors?.length > 0} class="mt-4 w-full">
				{#if $submitting}
					<Loader2 class="mr-2 size-4 animate-spin" />
				{/if}
				{$submitting ? 'Please wait...' : 'Verify Code'}
			</Form.Button>
		</form>
		<div class="relative">
			<p class="text-muted-foreground px-8 text-center text-sm">
				{#if resendDisabled}
					Email sent
				{:else}
					Still waiting?
					<button onclick={() => resend()} type="button" class="hover:underline"
						>Resend code</button
					>

					<Button variant="link" onclick={signIn} class="hover:text-brand no-underline">
						Already have an account? Sign In
					</Button>
				{/if}
			</p>
		</div>
	</div>
</AuthWrapper>
