<script lang="ts">
	import * as Form from '$/components/ui/form'
	import { getAuthStore, getUtilsStore } from '$/stores'
	import {
		isUserInRole,
		RolesType,
		SheetTypes,
		signUpCodeFormSchema,
		UserAccountTypes,
		type FormMessage,
		type SignUpCodeFormType
	} from '$/lib'
	import { defaults, superForm } from 'sveltekit-superforms'
	import { valibot } from 'sveltekit-superforms/adapters'
	import { default as AuthWrapper } from './auth_wrapper.svelte'
	import { FormAlert } from '$/components/common'
	import { Loader2 } from '@lucide/svelte'
	import { onMount } from 'svelte'
	import { getFlashModule } from '$/lib/helpers'
	import { toast } from 'svelte-sonner'
	import { authClient, setupPasskey } from '$/lib/clients/auth-client'
	import * as InputOTP from '$/components/ui/input-otp'
	import { Button } from '$/components/ui/button'
	import { image } from 'gravatar-gen'

	const utilsStore = getUtilsStore()
	const authStore = getAuthStore()
	const form = superForm<SignUpCodeFormType, FormMessage>(
		defaults(valibot(signUpCodeFormSchema)),
		{
			id: 'signup-code-form',
			delayMs: 0,
			invalidateAll: true,
			multipleSubmits: 'prevent',
			SPA: true,
			validators: valibot(signUpCodeFormSchema),
			onUpdate: async ({ form, cancel }) => {
				if (!form.valid) return
				try {
					const formData = form.data
					const email = utilsStore.st.currentAuthEmail || formData.email

					// Try EmailOTP sign-in first (for login flow) using plugin-enhanced auth client
					const { data: _signInResult, error: signInError } = await authClient.signIn.emailOtp(
						{
							email: email || '',
							otp: formData.code
						}
					)

					if (signInError) {
						console.error(signInError.message)
						toast.error(`Sign-in failed: ${signInError.message}`)
						cancel()
						return
					}
					await setUpOrganization()
					await setupPasskey()

					// Check if passkey registration was requested during signup or login
					authStore.checkAuth()
					toast.success('Sign-in successful!')
				} catch (err) {
					console.error('Verification error:', err)
					toast.error(`Verification failed: ${(err as Error).message}`)
					cancel()
				}
			},
			...getFlashModule()
		}
	)
	const { form: formData, enhance, submitting, message, allErrors } = form

	let resendDisabled = $state(false)
	const resend = async () => {
		const email = utilsStore.st.currentAuthEmail
		if (email) {
			console.log('resending OTP to:', email)
			resendDisabled = true

			try {
				// Resend OTP using EmailOTP plugin
				const { error } = await authClient.emailOtp.sendVerificationOtp({
					email: email,
					type: 'sign-in' // Default to sign-in type for resend
				})

				if (error) {
					toast.error(`Failed to resend code: ${error.message}`)
				} else {
					toast.success('Verification code sent!')
				}
			} catch (err) {
				console.error('Resend error:', err)
				toast.error('Failed to resend code')
			}

			setTimeout(() => {
				resendDisabled = false
			}, 3000)
		} else {
			console.log('no email to resend to')
			toast.error('No email found to resend to.')
			utilsStore.setHomeSheet(SheetTypes.SignUp)
		}
	}
	const signUp = () => {
		utilsStore.setHomeSheet(SheetTypes.SignUp)
	}

	const setUpOrganization = async () => {
		console.log('checking org')
		const { data: sessionData } = await authClient.getSession()
		if (!sessionData) return
		const { user, session } = sessionData
		console.log('setup Org user', JSON.stringify(user))
		if (
			user &&
			user.userType === UserAccountTypes.Staff &&
			isUserInRole(user as any, RolesType.OrgAdmin)
		) {
			console.log('Creating organization for Staff user:', user.email)
			const { data, error: _ } = await authClient.organization.list()
			console.log('Orgs:', JSON.stringify(data))
			if ((data ?? []).length > 0) {
				console.log('Organization already exists for user:', user.email)
				return
			}

			// Parse metadata to get business information
			let businessEmail = user.email
			let businessAddress = ''
			let businessName = ''
			try {
				if ((user as any).metadata) {
					// Check if metadata is already an object or needs parsing
					let metadata = (user as any).metadata
					if (typeof metadata === 'string') {
						metadata = JSON.parse(metadata)
					}
					businessEmail = metadata.businessEmail || user.email
					businessAddress = metadata.businessAddress || ''
					businessName = metadata.businessName || user.name!
				}
			} catch (error) {
				console.warn('Failed to parse user metadata:', error)
			}

			// Create organization using the organization plugin API
			const { data: organizationResponse, error: orgError } =
				await authClient.organization.create({
					id: user['organizationId'],
					name: businessName,
					slug: businessName!
						.toLowerCase()
						.replace(/[^a-z0-9]/g, '-')
						.replace(/-+/g, '-'),
					logo: await image(businessEmail, {
						size: 200,
						defaultImage: 'blank',
						includeExtention: true,
						rating: 'g'
					}),
					businessEmail: businessEmail,
					businessAddress: businessAddress
				})
			console.log('Organization response:', organizationResponse)
		}
	}
	onMount(() => {
		const email = utilsStore.st.currentAuthEmail
		if (email) $formData.email = email
	})
</script>

<AuthWrapper pageTitle="Login" pageDescription="Verify OTP">
	<div class="grid gap-6">
		<form method="POST" use:enhance>
			<FormAlert {message} />
			<Form.Field {form} name="code" class="mx-auto justify-items-center">
				<Form.Control>
					{#snippet children({ props })}
						<InputOTP.Root maxlength={6} {...props} bind:value={$formData.code}>
							{#snippet children({ cells })}
								<InputOTP.Group>
									{#each cells.slice(0, 2) as cell (cell)}
										<InputOTP.Slot {cell} />
									{/each}
								</InputOTP.Group>
								<InputOTP.Separator />
								<InputOTP.Group>
									{#each cells.slice(2, 4) as cell (cell)}
										<InputOTP.Slot {cell} />
									{/each}
								</InputOTP.Group>
								<InputOTP.Separator />
								<InputOTP.Group>
									{#each cells.slice(4, 6) as cell (cell)}
										<InputOTP.Slot {cell} />
									{/each}
								</InputOTP.Group>
							{/snippet}
						</InputOTP.Root>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>

			<Form.Button disabled={$submitting || $allErrors?.length > 0} class="mt-4 w-full">
				{#if $submitting}
					<Loader2 class="mr-2 size-4 animate-spin" />
				{/if}
				{$submitting ? 'Please wait...' : 'Verify Code'}
			</Form.Button>
		</form>
		<div class="relative">
			<p class="text-muted-foreground px-8 text-center text-sm">
				{#if resendDisabled}
					Email sent
				{:else}
					Still waiting?
					<button onclick={() => resend()} type="button" class="hover:underline"
						>Resend code</button
					>

					<Button variant="link" onclick={signUp} class="hover:text-brand no-underline">
						Dont have an account? Sign Up
					</Button>
				{/if}
			</p>
		</div>
	</div>
</AuthWrapper>
