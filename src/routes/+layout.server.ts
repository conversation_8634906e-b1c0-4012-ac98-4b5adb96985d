import { remult } from 'remult'
import type { MetaTagsProps } from 'svelte-meta-tags'
export const trailingSlash: string = 'always'

export const load = async ({ url }) => {



      const baseMetaTags = Object.freeze({
            title: 'Default',
            titleTemplate: '%s | SpenDeed',
            description:
                  'Ai Driven small business platform for running everything from AI assisted sales to rewards and appointments',
            canonical: new URL(url.pathname, url.origin).href,
            openGraph: {
                  type: 'website',
                  url: new URL(url.pathname, url.origin).href,
                  locale: 'en_GB',
                  title: 'Spendeed',
                  description:
                        'Ai Driven small business platform for running everything from AI assisted sales to rewards and appointments',
                  siteName: 'spendeed.com',
                  images: [
                        {
                              url: 'https://www.example.ie/og-image.jpg',
                              alt: 'Og Image Alt',
                              width: 800,
                              height: 600,
                              secureUrl: 'https://www.example.ie/og-image.jpg',
                              type: 'image/jpeg'
                        }
                  ]
            }
      }) as MetaTagsProps
      return {
            baseMetaTags,
            user: remult.user
      }
}
