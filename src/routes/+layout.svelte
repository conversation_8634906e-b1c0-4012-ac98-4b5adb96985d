<script lang="ts">
	import 'linq-extensions'
	import '../app.css'
	import { TailwindIndicator } from '$/components/common'
	import { dev } from '$app/environment'
	import { onNavigate } from '$app/navigation'
	import { ModeWatcher } from 'mode-watcher'
	import { initSuper<PERSON>son } from '$/lib'
	import { setAppointmentStoreContext, setAuthStoreContext, setUtilsStoreContext } from '$/stores'
	import { Remult, remult } from 'remult'
	import type { LayoutData } from './$types'
	import { createSubscriber } from 'svelte/reactivity'
	import { MetaTags } from 'svelte-meta-tags'

	let { children, data }: { children: any; data: LayoutData } = $props()
	let metaTags = $derived(data.baseMetaTags)

	//init
	initSuperJson()
	setUtilsStoreContext()
	setAuthStoreContext()
	setAppointmentStoreContext()

	//functions

	//lifecycle
	onNavigate((navigation) => {
		if (!document.startViewTransition) return

		return new Promise((resolve) => {
			document.startViewTransition(async () => {
				resolve()
				await navigation.complete
			})
		})
	})

	const initRemultSvelteReactivity = () => {
		// Auth reactivity (remult.user, remult.authenticated(), ...)
		{
			let update = () => {
				console.log('remult changed')
			}

			let s = createSubscriber((u) => {
				console.log('u is ', u)
				update = u
			})

			remult.subscribeAuth({
				reportObserved: () => s(),
				reportChanged: () => update()
			})
		}

		// Entities reactivity
		{
			Remult.entityRefInit = (x) => {
				let update = () => {}

				let s = createSubscriber((u) => {
					update = u
				})

				x.subscribe({
					reportObserved: () => s(),
					reportChanged: () => update()
				})
			}
		}
	}

	initRemultSvelteReactivity()
	remult.user = data.user

	$inspect('remult user', data.user)
</script>

<MetaTags {...metaTags} />
<ModeWatcher />
{@render children?.()}

{#if dev}
	<TailwindIndicator />
{/if}
