import type { ISessionData } from '$/lib'
import { addDays } from 'date-fns'
import cloneDeep from 'lodash-es/cloneDeep'
import superjson from 'superjson'
import { setStoreContext, useStore } from './use-shared-store'
import { User } from '$/lib'
import { remult } from 'remult'
import { PersistedState } from 'runed'

const defaultAuthState: ISessionData<User> = {
	...new User(),
	isAdmin: false,
	teamConfigIsSetup: false,
	isTeamLocationAdmin: false,
	isOrgAdmin: false,
	expires: addDays(new Date(), 7),
	organizationId: '',
	teamId: ''
}

class AuthStore extends PersistedState<ISessionData<User>> {
	constructor() {
		super('auth', cloneDeep(defaultAuthState), {
			storage: "session",
			syncTabs: false,
			serializer: {
				serialize: superjson.stringify,
				deserialize: superjson.parse
			}
		})
		$effect.root(() => {
			this.checkAuth()
		})
	}

	updateStore(sessionData: ISessionData) {
		if (!sessionData) {
			this.clearStore()
			return
		}
		this.current = { ...sessionData }
	}
	async clearStore() {
		await remult.initUser()
		this.current = cloneDeep(defaultAuthState)
	}

	async checkAuth() {
		await remult.initUser()
		this.current = remult.authenticated() ? remult.user as any : cloneDeep(defaultAuthState)
	}

	get fullName() {
		return `${this.current.givenName} ${this.current.familyName}`
	}

	get user() {
		return this.current
	}

	get isAdmin() {
		return this.current.isAdmin
	}
	get isOrgAdmin() {
		return this.current.isOrgAdmin
	}
	get isTeamLocationAdmin() {
		return this.current.isTeamLocationAdmin
	}

	get teamConfigIsSetup() {
		return this.current.teamConfigIsSetup
	}

	get showHomeModal() {
		return !this.current.id
	}
	get isAuthenticated() {
		return !!this.current?.id
	}
}
export type IAuthStore = AuthStore
export const authStoreKey: string = 'authState.store'
const authStore = new AuthStore()
export const setAuthStoreContext = () => {
	setStoreContext<IAuthStore>(authStoreKey, authStore)
}
export const getAuthStore = () => {
	return useStore<IAuthStore>(authStoreKey)
}
