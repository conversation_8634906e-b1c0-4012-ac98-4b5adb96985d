// import { MemberInvite, Organization, OrgBankDetail, OrgPaymentProvider, Team, TeamMember } from '$/lib'
// import { User } from '$/lib'
// import { remult } from 'remult'
// import { cloneDeep } from 'lodash-es'
// import { setStoreContext, useStore } from './use-shared-store'
// import { untrack } from 'svelte'
// import { PersistedState } from 'runed'
// import superjson from 'superjson'


// type OrgStoreState = {
// 	org: Organization | undefined
// 	teams: Team[]
// 	users: User[]
// 	staffMemberInvites: MemberInvite[]
// 	orgBankDetail: OrgBankDetail | undefined
// 	orgPaymentProvider: OrgPaymentProvider | undefined
// 	teamMembers: TeamMember[]
// }
// const defaultOrgStoreState: OrgStoreState = {
// 	org: undefined,
// 	teams: [],
// 	users: [],
// 	staffMemberInvites: [],
// 	teamMembers: [],
// 	orgBankDetail: undefined,
// 	orgPaymentProvider: undefined
// }

// class OrgStore extends PersistedState<OrgStoreState> {
// 	orgUnsub?: () => void
// 	teamsUnsub?: () => void
// 	usersUnsub?: () => void
// 	staffMemberInviteUnsub?: () => void
// 	orgBankDetailUnsub?: () => void
// 	orgPaymentProviderUnsub?: () => void
// 	teamMembersUnSub?: () => void

// 	constructor() {
// 		super('org', cloneDeep(defaultOrgStoreState), {
// 			storage: "session",
// 			syncTabs: false,
// 			serializer: {
// 				serialize: superjson.stringify,
// 				deserialize: superjson.parse
// 			}
// 		})
// 		$effect.root(() => {
// 			this.setupLiveQuery()
// 			return () => {
// 				this.cleanup()
// 			}
// 		})
// 	}

// 	private setupLiveQuery() {
// 		this.orgUnsub = remult.repo(Organization)
// 			.liveQuery({
// 				where: { id: remult.user?.organizationId }
// 			})
// 			.subscribe((info) => {
// 				console.log('Org details from subs', info.items)
// 				untrack(() => {
// 					this.current.org = info.items.firstOrNull() ?? undefined
// 				})
// 			})
// 		this.teamsUnsub = remult.repo(Team)
// 			.liveQuery({
// 				where: { organizationId: remult.user?.organizationId },
// 				orderBy: { name: 'asc' }
// 			})
// 			.subscribe((info) => {
// 				console.log('teams from subs', JSON.stringify(info.items))
// 				untrack(() => {
// 					this.current.teams = info.items
// 				})
// 			})
// 		this.usersUnsub = remult.repo(User)
// 			.liveQuery({
// 				where: { organizationId: remult.user?.organizationId },
// 				orderBy: { name: 'asc' }
// 			})
// 			.subscribe((info) => {
// 				untrack(() => {
// 					this.current.users = info.items
// 				})
// 			})
// 		this.staffMemberInviteUnsub = remult.repo(MemberInvite)
// 			.liveQuery({
// 				where: { organizationId: remult.user?.organizationId },
// 				orderBy: { createdAt: 'desc' }
// 			})
// 			.subscribe((info) => {
// 				untrack(() => {
// 					this.current.staffMemberInvites = info.items
// 				})
// 			})
// 		this.orgBankDetailUnsub = remult.repo(OrgBankDetail)
// 			.liveQuery({
// 				where: { organizationId: remult.user?.organizationId },
// 				orderBy: { bankName: 'asc' }
// 			})
// 			.subscribe((info) => {
// 				untrack(() => {
// 					this.current.orgBankDetail = info.items.firstOrNull() ?? undefined
// 				})
// 			})
// 		this.orgPaymentProviderUnsub = remult.repo(OrgPaymentProvider)
// 			.liveQuery({
// 				where: { organizationId: remult.user?.organizationId }
// 			})
// 			.subscribe((info) => {
// 				untrack(() => {
// 					this.current.orgPaymentProvider = info.items.firstOrNull() ?? undefined
// 				})
// 			})

// 		this.teamMembersUnSub = remult.repo(TeamMember)
// 			.liveQuery({
// 				where: { teamId: remult.user?.teamId },
// 				orderBy: { createdAt: 'desc' }
// 			})
// 			.subscribe((info) => {
// 				untrack(() => {
// 					this.current.teamMembers = info.items
// 				})
// 			})

// 	}
// 	private cleanup() {
// 		this.orgUnsub?.()
// 		this.teamsUnsub?.()
// 		this.usersUnsub?.()
// 		this.staffMemberInviteUnsub?.()
// 		this.orgBankDetailUnsub?.()
// 		this.orgPaymentProviderUnsub?.()
// 		this.teamMembersUnSub?.()
// 	}

// 	get org() {
// 		//return team only properties
// 		return this.current.org
// 	}
// 	get orgNameInitials() {
// 		return (
// 			this.current.org?.name
// 				?.split(' ')
// 				.map((wrd: string) => wrd.charAt(0).toUpperCase())
// 				.join('') ?? 'NA'
// 		)
// 	}
// 	get teams() {
// 		return this.current.teams ?? []
// 	}
// 	get staffMembers() {
// 		return this.current.teamMembers.map((teamMember: TeamMember) => {
// 			// Find the team/location details
// 			const location = this.current.teams.firstOrNull(
// 				(team: Team) => team.id === teamMember.teamId
// 			)

// 			// Find the user details
// 			const profile = this.current.users.firstOrNull(
// 				(u: User) => u.id === teamMember.userId
// 			)


// 			return {
// 				profile,
// 				location, memberId: teamMember.id
// 			}
// 		}).filter(member => member.memberId) // Filter out any members where user wasn't found
// 	}
// 	get staffMemberInvites() {
// 		return this.current.staffMemberInvites
// 	}
// 	get orgBankDetail() {
// 		return this.current.orgBankDetail
// 	}
// 	get orgPaymentProvider() {
// 		return this.current.orgPaymentProvider
// 	}
// 	get orgIsSetup() {
// 		return !!this.current.org?.name
// 	}
// }

// export type IOrgStore = OrgStore
// const orgStore = new OrgStore()

// export const orgStoreKey = 'org.store'
// export const setOrgStoreContext = () => {
// 	setStoreContext<IOrgStore>(orgStoreKey, orgStore)
// }
// export const getOrgStore = () => {
// 	return useStore<IOrgStore>(orgStoreKey)
// }


