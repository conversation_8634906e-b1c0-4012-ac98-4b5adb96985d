import { getRandomColorName, timeGreeting } from '$/lib'
import {
	SheetTypes,
	type SheetType,
	type IDynamicModal,
	type IDynamicSheet
} from '$/lib'
import cloneDeep from 'lodash-es/cloneDeep'
import { toast } from 'svelte-sonner'
import { setStoreContext, useStore } from './use-shared-store'
import superjson from 'superjson'
import { PersistedState } from 'runed'

const defaultUtilsState = {
	appLogoName: 'SpenDeed',
	appName: 'SpenDeed',
	isFirstTime: false,
	isNativePlatform: false,
	leftPanelOpen: false,
	showMobileMenu: false,
	activeNavBar: 1,
	homeSheet: {
		authingUserEmail: undefined as string | undefined,
		authingUserId: undefined as string | undefined,
		passkeyEnabled: undefined as boolean | undefined
	},
	currentHomeSheet: SheetTypes.Marketing as SheetType,
	currentAuthEmail: '',
	appLayout: {
		currentPageTitle: 'Home',
		sidebarVisible: false
	},
	dynamicModalItem: undefined as IDynamicModal | undefined,
	dynamicSheetItem: undefined as IDynamicSheet | undefined,
	isAuthRoute: false,
	areWeLive: false,
	appLogoColor: 'bg-green-900',
	signupPayload: undefined as any
}

export type IUtilsState = typeof defaultUtilsState
export type IToastType = 'success' | 'error' | 'warning' | 'info'

export class UtilsStore extends PersistedState<IUtilsState> {
	constructor() {
		super('utils', cloneDeep(defaultUtilsState), {
			storage: "session",
			syncTabs: false,
			serializer: {
				serialize: superjson.stringify,
				deserialize: superjson.parse
			}
		})
	}

	get st() {
		return this.current
	}
	get currentHomeSheet() {
		return this.current.currentHomeSheet
	}
	get signupPayload() {
		return this.current.signupPayload
	}
	setPageTitle(pageTitle: string) {
		this.current.appLayout.currentPageTitle = pageTitle
	}
	clearDynamicModal() {
		this.current.dynamicModalItem = undefined
	}
	openDynamicModal(dynamicFormObj: IDynamicModal) {
		this.current.dynamicModalItem = dynamicFormObj
	}
	closeDynamicSheet() {
		this.current.dynamicSheetItem = undefined
	}
	openDynamicSheet(dynamicFormObj: IDynamicSheet) {
		this.current.dynamicSheetItem = dynamicFormObj
	}

	greetings(displayName: string) {
		return `${timeGreeting()}  ${displayName ? displayName : 'Anonymous'}!`
	}

	toggleAppSideBar() {
		this.current.appLayout.sidebarVisible = !this.current.appLayout.sidebarVisible
	}
	toggleMobileMenu() {
		this.current.showMobileMenu = !this.current.showMobileMenu
	}

	toastError(message: string) {
		toast.error(message)
	}
	toastSuccess(message: string) {
		toast.success(message)
	}
	toastInfo(message: string) {
		toast.info(message)
	}
	toastWarning(message: string) {
		toast.warning(message)
	}
	setAppColor() {
		this.current.appLogoColor = getRandomColorName()
	}
	setHomeSheet(sheetType: SheetType) {
		this.current.currentHomeSheet = sheetType
	}
	setCurrentAuthEmail(email: string) {
		this.current.currentAuthEmail = email
	}
	holdSignupPayload(payload: any) {
		this.current.signupPayload = payload
	}
}

export type IUtilsStore = UtilsStore
const utilsStore = new UtilsStore()

export const utilsStoreKey: string = 'utilsState.store'
export const setUtilsStoreContext = () => {
	setStoreContext<IUtilsStore>(utilsStoreKey, utilsStore)
}

export const getUtilsStore = () => {
	return useStore<IUtilsStore>(utilsStoreKey)
}

// export const getUtilsStore = () => {
// 	return utilsStore
// }
