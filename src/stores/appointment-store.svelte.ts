import { setStoreContext, useStore } from './use-shared-store'

import {
	Appointment,
	AppointmentType,
	CalendarBlock,
	WorkdaySetting,
	type Team
} from '$/lib'
import { CalendarViewTypes, type CalendarViewType } from '$/lib'
import superjson from 'superjson'
import { cloneDeep } from 'lodash-es'
import { remult } from 'remult'
import { PersistedState } from 'runed'


type IAppointmentStoreState = {
	currentView?: CalendarViewType
	selectedAppointment?: Appointment
	selectedAppointmentLocation?: Team
}
const defaultStoreState: IAppointmentStoreState = {
	currentView: CalendarViewTypes.Day,
	selectedAppointment: undefined,
	selectedAppointmentLocation: undefined,
}
class AppointmentStore extends PersistedState<IAppointmentStoreState> {


	constructor() {
		super('appointments', cloneDeep(defaultStoreState), {
			storage: "session",
			syncTabs: false,
			serializer: {
				serialize: superjson.stringify,
				deserialize: superjson.parse
			}
		})
	}

	setSelectedAppointment(data: Appointment | undefined) {
		this.current.selectedAppointment = data
	}

	setCurrentView(view: CalendarViewType) {
		this.current.currentView = view
	}

	get selectedAppointment() {
		return this.current.selectedAppointment
	}
	get currentView() {
		return this.current.currentView
	}

	get selectedAppointmentLocation() {
		return this.current.selectedAppointmentLocation
	}

}

export type IAppointmentStore = AppointmentStore
const appointmentStore = new AppointmentStore()
export const appointmentStoreKey = 'appointments.store'

export const setAppointmentStoreContext = () => {
	setStoreContext<IAppointmentStore>(appointmentStoreKey, appointmentStore)
}
export const getAppointmentStore = () => {
	return useStore<IAppointmentStore>(appointmentStoreKey)
}

