<script lang="ts">
	import { default as CalendarHeaderDate } from './calendar-header-date.svelte'
	import { default as CalendarHeaderMenu } from './calendar-header-menu.svelte'

	import { CalendarViewTypes } from '$/lib'

	let {
		showAddAppointment,
		currentView = $bindable(CalendarViewTypes.Day)
	}: {
		showAddAppointment: boolean
		currentView?: any
	} = $props()
</script>

<div class="flex flex-col gap-4 border-b p-4 md:flex-row md:items-center md:justify-between">
	<CalendarHeaderDate />
	<CalendarHeaderMenu {showAddAppointment} bind:currentView />
</div>
