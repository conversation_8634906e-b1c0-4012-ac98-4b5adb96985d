<script lang="ts">
	import { getAppointmentStore } from '$/stores'
	import { CalendarViewTypes } from '$/lib'
	import { default as CalendarDayBody } from './day/calendar-day-body.svelte'
	import { default as CalendarWeekBody } from './week/calendar-week-body.svelte'
	import { default as CalendarMonthBody } from './month/calendar-month-body.svelte'

	let aStore = getAppointmentStore()
</script>

<div>
	{#if aStore.currentView === CalendarViewTypes.Month}
		<CalendarMonthBody />
	{:else if aStore.currentView === CalendarViewTypes.Week}
		<CalendarWeekBody />
	{:else}
		<CalendarDayBody />
	{/if}
</div>
