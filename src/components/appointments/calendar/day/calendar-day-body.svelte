<script lang="ts">
	import { onMount } from 'svelte'
	import { ScrollArea } from '$/components/ui/scroll-area'
	import { getAppointmentStore } from '$/stores'
	import { default as PickCalendar } from './pick-calendar.svelte'
	import { appointmentStatuses, Appointment, AppointmentType, Team, WorkdaySetting } from '$/lib'
	import { formatTimeWithAmPm } from '$/lib'
	import { remult } from 'remult'
	import 'linq-extensions'

	const appointmentColors = {
		Scheduled:
			'bg-indigo-50   rounded-d border px-2 py-1.5 text-xs focus-visible:outline-offset-2 border-indigo-200  text-indigo-700 dark:border-indigo-800 dark:bg-indigo-950 dark:text-indigo-300',
		Confirmed:
			'bg-emerald-50   rounded-d border px-2 py-1.5 text-xs focus-visible:outline-offset-2 border-emerald-200  text-emerald-700 dark:border-emerald-800 dark:bg-emerald-950 dark:text-emerald-300',
		Missed:
			'bg-amber-50   rounded-d border px-2 py-1.5 text-xs focus-visible:outline-offset-2 border-amber-200  text-amber-700 dark:border-amber-800 dark:bg-amber-950 dark:text-amber-300',
		Done: 'bg-rose-50   rounded-d border px-2 py-1.5 text-xs focus-visible:outline-offset-2 border-rose-200  text-rose-700 dark:border-rose-800 dark:bg-rose-950 dark:text-rose-300',
		Cancelled:
			'bg-red-50   rounded-d border px-2 py-1.5 text-xs focus-visible:outline-offset-2 border-red-200  text-red-700 dark:border-red-800 dark:bg-red-950 dark:text-red-300'
	} as const

	let aStore = getAppointmentStore()
	let currentDayOfWeek = $derived.by(() =>
		new Date().toLocaleString('default', { weekday: 'short' })
	)

	// Entity data using liveQuery
	let appointments = $state<Appointment[]>([])
	let appointmentTemplates = $state<AppointmentType[]>([])
	let teams = $state<Team[]>([])
	let workdaySetting = $state<WorkdaySetting | undefined>(undefined)

	// Subscribe to appointments
	$effect(() => {
		const unsubscribe = remult
			.repo(Appointment)
			.liveQuery({
				where: { organizationId: remult.user?.organizationId },
				orderBy: { createdAt: 'desc' }
			})
			.subscribe((info) => {
				appointments = info.items
			})

		return () => {
			unsubscribe()
		}
	})

	// Subscribe to appointment templates
	$effect(() => {
		const unsubscribe = remult
			.repo(AppointmentType)
			.liveQuery({
				where: { organizationId: remult.user?.organizationId },
				orderBy: { appointmentDetail: 'asc' }
			})
			.subscribe((info) => {
				appointmentTemplates = info.items
			})

		return () => {
			unsubscribe()
		}
	})

	// Subscribe to teams
	$effect(() => {
		const unsubscribe = remult
			.repo(Team)
			.liveQuery({
				where: { organizationId: remult.user?.organizationId },
				orderBy: { name: 'asc' }
			})
			.subscribe((info) => {
				teams = info.items
			})

		return () => {
			unsubscribe()
		}
	})

	// Subscribe to workday settings
	$effect(() => {
		const unsubscribe = remult
			.repo(WorkdaySetting)
			.liveQuery({
				where: { organizationId: remult.user?.organizationId }
			})
			.subscribe((info) => {
				workdaySetting = info.items.firstOrNull() ?? undefined
			})

		return () => {
			unsubscribe()
		}
	})

	// Current day appointments
	let currentDayAppointments = $derived.by(() => {
		return (
			appointments
				?.where((k) => new Date(k.appointmentDate).getDate() == new Date().getDate())
				.toArray() ?? []
		)
	})

	let appointmentPositions = $derived.by(() => {
		const startTime = workdaySetting?.startTime ?? '09:00'
		const [dayStartHour] = startTime.toString().split(':').map(Number)

		return (
			currentDayAppointments?.map((appointment) => {
				// Calculate position and height
				const [startHour, startMinute] = appointment.startTime.toString().split(':').map(Number)
				const [endHour, endMinute] = appointment.endTime.toString().split(':').map(Number)

				const startOffset = (startHour - dayStartHour) * 96 + (startMinute / 60) * 96
				const duration = (endHour - startHour) * 96 + ((endMinute - startMinute) / 60) * 96

				const formattedStartTime = formatTimeWithAmPm(appointment.startTime.toString())
				const formattedEndTime = formatTimeWithAmPm(appointment.endTime.toString())

				// Determine appointment color based on status
				const colorClass = appointmentColors[appointment.appointmentStatus]
				const template = appointmentTemplates.firstOrNull(
					(k) => k.id === appointment.appointmentTypeId
				)
				const location = teams.firstOrNull(({ id }) => id === appointment.teamId)

				return {
					...appointment,
					appointmentTemplate: template,
					location,
					startTime: formattedStartTime,
					endTime: formattedEndTime,
					style: `top: ${startOffset}px; width: 99%; left:0;`,
					innerStyle: `height: ${duration - 6}px`,
					colorClass
				}
			}) ?? []
		)
	})

	let currentDayOfMonth = $state(new Date().getDate().toString().padStart(2, '0'))

	let currentTimeFormatted = $state(
		new Date()
			.toLocaleString('en-US', {
				hour: 'numeric',
				minute: '2-digit',
				hour12: true
			})
			.toLowerCase()
	)
	let currentTimePosition = $derived.by(() => {
		const now = new Date()
		const startTime = workdaySetting?.startTime ?? '09:00'
		const [startHour] = startTime.toString().split(':').map(Number)

		const currentHour = now.getHours()
		const currentMinute = now.getMinutes()

		// Calculate position based on hour slot height (96px = 6rem = h-24)
		const hoursFromStart = currentHour - startHour
		const minutePercentage = currentMinute / 60
		const pixelsFromTop = (hoursFromStart + minutePercentage) * 96

		return `top: ${pixelsFromTop}px`
	})
	let formattedTimeIntervals = $derived.by(() => {
		const startTime = workdaySetting?.startTime ?? '09:00'
		const endTime = workdaySetting?.endTime ?? '17:00'

		// Convert start time to nearest hour
		const [startHour, startMinute] = startTime.toString().split(':').map(Number)
		const roundedStartHour = startMinute >= 30 ? startHour + 1 : startHour

		// Convert end time to hour
		const [endHour] = endTime.toString().split(':').map(Number)

		// Generate time slots
		const timeSlots: string[] = []
		for (let hour = roundedStartHour; hour <= endHour; hour++) {
			const date = new Date()
			date.setHours(hour, 0, 0)
			timeSlots.push(
				date
					.toLocaleString('en-US', {
						hour: 'numeric',
						minute: '2-digit',
						hour12: true
					})
					.toLowerCase()
			)
		}

		return timeSlots
	})

	let showCurrentTime = $derived.by(() => {
		const now = new Date()
		const currentHour = now.getHours()

		const startTime = workdaySetting?.startTime ?? '09:00'
		const endTime = workdaySetting?.endTime ?? '17:00'

		const [startHour] = startTime.toString().split(':').map(Number)
		const [endHour] = endTime.toString().split(':').map(Number)

		return currentHour >= startHour && currentHour <= endHour
	})

	onMount(() => {
		const interval = setInterval(() => {
			// Force a reactive update
			const now = new Date()
			currentTimeFormatted = now
				.toLocaleString('en-US', {
					hour: 'numeric',
					minute: '2-digit',
					hour12: true
				})
				.toLowerCase()
			currentDayOfMonth = now.getDate().toString().padStart(2, '0')
		}, 60000)

		return () => clearInterval(interval)
	})
</script>

<div class="flex border-b lg:border-b-0">
	<div class="flex flex-1 flex-col">
		<div>
			<div class="shadow-calendar dark:shadow-calendar-dark relative z-20 flex border-b">
				<div class="w-18"></div>
				<div class="flex-1 border-l py-2 text-center">
					<span class="text-muted-foreground text-xs font-medium">
						{currentDayOfWeek}
						<span class="text-t-secondary font-semibold">{currentDayOfMonth}</span>
					</span>
				</div>
			</div>
		</div>
		<ScrollArea class="!scrollbar-hide relative h-[608px]" orientation="vertical">
			<div class="flex">
				<div class="relative w-38">
					<!-- <div class="relative h-24 border-t">
						<div class="-top-3 right-2 flex h-6 items-center px-10"></div>
					</div> -->
					{#each formattedTimeIntervals as timeSlot (timeSlot)}
						<div class="relative h-24 border-t">
							<div class="-top-3 right-2 flex h-6 items-center px-10">
								<span class="text-muted-foreground text-xs">{timeSlot}</span>
							</div>
						</div>
					{/each}
				</div>

				<div class="relative flex-1 border-l">
					{#if showCurrentTime}
						<div
							class="border-primary-600 dark:border-primary-700 pointer-events-none absolute inset-x-0 z-50 border-t"
							style={currentTimePosition}
						>
							<div
								class="absolute -top-1.5 -left-1.5 size-3 rounded-full bg-indigo-600 dark:bg-indigo-700"
							></div>
							<div
								class="bg-bg-primary text-primary-600 dark:text-primary-700 absolute -left-18 flex w-16 -translate-y-1/2 justify-end pr-1 text-xs font-medium"
							>
								{currentTimeFormatted}
							</div>
						</div>
					{/if}
					<div class="relative">
						<!-- <div class="relative" style="height:96px;">
							<div class="absolute inset-x-0 top-0 border-b"></div>
							<div
								class="border-b-tertiary absolute inset-x-0 top-1/2 border-b border-dashed"
							></div>
						</div> -->
						{#each formattedTimeIntervals as timeSlot (timeSlot)}
							<div class="relative" style="height:96px;">
								<div class="absolute inset-x-0 top-0 border-b"></div>
								<div
									class="border-b-tertiary absolute inset-x-0 top-1/2 border-b border-dashed"
								></div>
							</div>
						{/each}
						{#each appointmentPositions as appointment (appointment.id)}
							<div class="absolute p-1" style={appointment.style}>
								<!-- svelte-ignore a11y_click_events_have_key_events -->
								<div
									role="button"
									tabindex="0"
									class="flex flex-col gap-0.5 truncate whitespace-nowrap select-none {appointment.colorClass}"
									style={appointment.innerStyle}
									onclick={() => aStore.setSelectedAppointment(appointment as any)}
								>
									<div class="flex items-center justify-between">
										<span class="truncate font-semibold"
											>{appointment.appointmentTemplate?.appointmentDetail}</span
										>
										{#if appointment.appointmentStatus === appointmentStatuses.Confirmed}
											<span class="bg-primary-200/50 rounded-d px-2 py-0.5 text-xs"
												>Confirmed</span
											>
										{/if}
									</div>
									<div class="flex items-center justify-between">
										<span class="text-xs opacity-75">
											{appointment.startTime} - {appointment.endTime}
										</span>
										<span class="text-xs opacity-75">{appointment.customerName}</span>
									</div>
									<span class="text-xs opacity-75">{appointment.location?.name}</span>
								</div>
							</div>
						{/each}
					</div>
				</div>
			</div>
		</ScrollArea>
	</div>
	<div class="hidden w-72 divide-y border-l lg:block">
		<PickCalendar />
	</div>
</div>
