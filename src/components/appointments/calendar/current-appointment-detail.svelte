<script lang="ts">
	import Button from '$/components/ui/button/button.svelte'
	import * as Card from '$/components/ui/card'
	import { Label } from '$/components/ui/label'
	import { getAppointmentStore, getUtilsStore } from '$/stores'
	import type { IDynamicModal } from '$/lib'

	let aStore = getAppointmentStore()
	let uStore = getUtilsStore()
	const closeDynamicModal = () => {
		aStore.setSelectedAppointment(undefined)
		uStore.clearDynamicModal()
	}
	const startEditAppointment = () => {
		const newAppointmentModal: IDynamicModal = {
			canClose: false,
			title: 'Edit Appointment',
			componentName: 'EditAppointment',
			size: 'lg',
			color: 'default',
			description: 'Modify appointment detail',
			componentProps: {}
		}
		uStore.openDynamicModal(newAppointmentModal)
	}
</script>

<Card.Root class="rounded-none">
	<Card.Content>
		<div class="grid grid-cols-1 gap-4 space-y-4 md:grid-cols-3">
			<div class="space-y-1">
				<Label>Location</Label>
				<p class="text-muted-foreground text-sm">
					{aStore.selectedAppointmentLocation?.name}
				</p>
			</div>
			<div class="space-y-1">
				<Label>Customer Name</Label>
				<p class="text-muted-foreground text-sm">
					{aStore.selectedAppointment?.customerName}
				</p>
			</div>
			<div class="space-y-1">
				<Label>Customer Email</Label>
				<p class="text-muted-foreground text-sm">
					{aStore.selectedAppointment?.customerEmail}
				</p>
			</div>
			<div class="space-y-1">
				<Label>Appointment Day</Label>
				<p class="text-muted-foreground text-sm">
					{aStore.selectedAppointment?.appointmentDate}
				</p>
			</div>

			<div class="space-y-1">
				<Label>Appointment Status</Label>
				<p class="text-muted-foreground text-sm">
					{aStore.selectedAppointment?.appointmentStatus}
				</p>
			</div>
			<div class="space-y-1">
				<Label>Start Time</Label>
				<p class="text-muted-foreground text-sm">
					{aStore.selectedAppointment?.startTime}
				</p>
			</div>
			<div class="space-y-1">
				<Label>End Time</Label>
				<p class="text-muted-foreground text-sm">
					{aStore.selectedAppointment?.endTime}
				</p>
			</div>
			<div class="space-y-1">
				<Label>Notes</Label>
				<p class="text-muted-foreground text-sm">
					{aStore.selectedAppointment?.appointmentNotes}
				</p>
			</div>
		</div>
	</Card.Content>
	<Card.Footer>
		<div class="flex justify-between">
			<Button variant="outline" size="icon" onclick={startEditAppointment}>Edit</Button>
			<Button variant="outline" size="icon" onclick={closeDynamicModal}>Close</Button>
		</div>
	</Card.Footer>
</Card.Root>
