<script lang="ts">
	import * as Card from '$/components/ui/card'
	import { getAppointmentStore, getAuthStore, getUtilsStore } from '$/stores'
	import {
		Appointment,
		AppointmentController,
		AppointmentType,
		Team,
		newAppointmentFormSchema,
		type FormMessage,
		type NewAppointmentFormSchemaType
	} from '$/lib'
	import { valibotClient } from 'sveltekit-superforms/adapters'
	import { defaults, superForm } from 'sveltekit-superforms/client'
	import { getFlashModule } from '$/lib/helpers'
	import { onMount, tick } from 'svelte'
	import { FormAlert } from '$/components/common'
	import * as Form from '$/components/ui/form'
	import { Input } from '$/components/ui/input'
	import * as Popover from '$/components/ui/popover'
	import * as Command from '$/components/ui/command'
	import { Button, buttonVariants } from '$/components/ui/button'
	import { Check, ChevronsUpDown, Loader2 } from '@lucide/svelte'
	import { cn } from '$/lib/shadcn.utils'
	import { useId } from 'bits-ui'
	import { remult } from 'remult'
	import 'linq-extensions'

	let aStore = getAppointmentStore()
	let utilsStore = getUtilsStore()
	let authStore = getAuthStore()

	// Entity data using liveQuery
	let appointmentTemplates = $state<AppointmentType[]>([])
	let teams = $state<Team[]>([])

	// Subscribe to appointment types
	$effect(() => {
		const unsubscribe = remult
			.repo(AppointmentType)
			.liveQuery({
				where: { organizationId: remult.user?.organizationId },
				orderBy: { appointmentDetail: 'asc' }
			})
			.subscribe((info) => {
				appointmentTemplates = info.items
			})

		return () => {
			unsubscribe()
		}
	})

	// Subscribe to teams
	$effect(() => {
		const unsubscribe = remult
			.repo(Team)
			.liveQuery({
				where: { organizationId: remult.user?.organizationId },
				orderBy: { name: 'asc' }
			})
			.subscribe((info) => {
				teams = info.items
			})

		return () => {
			unsubscribe()
		}
	})

	const form = superForm<NewAppointmentFormSchemaType, FormMessage>(
		defaults(aStore.selectedAppointment as any, valibotClient(newAppointmentFormSchema)),
		{
			dataType: 'json',
			SPA: true,
			clearOnSubmit: 'errors-and-message',
			validators: valibotClient(newAppointmentFormSchema),
			async onUpdate({ form }) {
				try {
					if (!form.valid) return
					const { data: formData } = form

					const appointmentType = appointmentTemplates.firstOrNull(
						(k) => k.id === formData.appointmentTypeId
					)
					const appointmentLocation = teams?.firstOrNull((k) => k.id === formData.locationId)
					const params: Partial<Appointment> = {
						id: aStore.selectedAppointment?.id,
						customerName: formData.customerName,
						customerEmail: formData.customerEmail,
						customerPhoneNumber: formData.customerPhoneNumber,
						appointmentTypeId: appointmentType?.id,
						appointmentDate: formData.appointmentDate,
						startTime: formData.startTime,
						endTime: formData.endTime,
						organizationId: remult.user?.organizationId,
						teamId: appointmentLocation?.id,
						updatedBy: authStore.user?.id,
						updatedAt: new Date()
					}
					const updateResp = await AppointmentController.update(
						aStore.selectedAppointment?.id!,
						params
					)
					if (!updateResp.id) throw new Error('Failed to update appointment')
					form.message = {
						status: 'success',
						text: 'Appointment updated successfully!'
					}
				} catch (err) {
					console.error(err)
					form.message = {
						status: 'error',
						text: 'Appointment update failed!'
					}
				}
			},
			onResult: ({ result }) => {
				if (result.type === 'success') {
					utilsStore.toastSuccess('Bank details updated successfully!')
					utilsStore.clearDynamicModal()
				}
			},
			...getFlashModule()
		}
	)

	const { form: formData, enhance, submitting, allErrors, message, validateForm } = form
	let open = $state(false)
	let locationOpen = $state(false)
	const triggerId = $state(useId())
	const locationTriggerId = $state(useId())
	const closeAndFocusTrigger = (triggerId: string) => {
		open = false
		tick().then(() => {
			document.getElementById(triggerId)?.focus()
		})
	}

	onMount(() => {
		validateForm()
	})
</script>

<form method="POST" use:enhance>
	<FormAlert {message} />
	<Card.Root class="rounded-none">
		<Card.Content>
			<div class="grid grid-cols-1 gap-4 space-y-4 md:grid-cols-3">
				<Form.Field {form} name="customerName">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>full Name</Form.Label>
							<Input
								{...props}
								placeholder="Customer Name"
								bind:value={$formData.customerName}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>
				<Form.Field {form} name="customerEmail">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Email</Form.Label>
							<Input
								{...props}
								placeholder="Customer Email"
								bind:value={$formData.customerEmail}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<Form.Field {form} name="customerPhoneNumber">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Phone Number</Form.Label>
							<Input
								{...props}
								placeholder="Phone Number"
								bind:value={$formData.customerPhoneNumber}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>
				<Form.Field {form} name="appointmentTypeId" class="flex flex-col">
					<Popover.Root bind:open>
						<Form.Control id={triggerId}>
							{#snippet children({ props })}
								<Form.Label>Appointment Type</Form.Label>
								<Popover.Trigger
									class={cn(
										buttonVariants({ variant: 'outline' }),
										'w-[200px] justify-between',
										!$formData.appointmentTypeId && 'text-muted-foreground'
									)}
									role="combobox"
									{...props}
								>
									{appointmentTemplates.find((f) => f.id === $formData.appointmentTypeId)
										?.appointmentDetail ?? 'Select Appointment Type'}
									<ChevronsUpDown class="opacity-50" />
								</Popover.Trigger>
								<input hidden value={$formData.appointmentTypeId} name={props.name} />
							{/snippet}
						</Form.Control>
						<Popover.Content class="w-[200px] p-0">
							<Command.Root>
								<Command.Input
									autofocus
									placeholder="Select appointment Type..."
									class="h-9"
								/>
								<Command.Empty>No Appointment type found.</Command.Empty>
								<Command.Group>
									{#each appointmentTemplates ?? [] as appointmentType (appointmentType.id)}
										<Command.Item
											value={appointmentType.id!}
											onSelect={() => {
												$formData.appointmentTypeId = appointmentType.id!
												closeAndFocusTrigger(triggerId)
											}}
										>
											{appointmentType.appointmentDetail}
											<Check
												class={cn(
													'ml-auto',
													appointmentType.id !== $formData.appointmentTypeId &&
														'text-transparent'
												)}
											/>
										</Command.Item>
									{/each}
								</Command.Group>
							</Command.Root>
						</Popover.Content>
					</Popover.Root>
					<Form.Description>This is the service the appointment is for.</Form.Description>
					<Form.FieldErrors />
				</Form.Field>
				<Form.Field {form} name="appointmentDate">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Appointment Date</Form.Label>
							<Input
								type="datetime-local"
								{...props}
								placeholder="Appointment Date"
								bind:value={$formData.appointmentDate}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>
				<Form.Field {form} name="startTime">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Start Time</Form.Label>
							<Input {...props} placeholder="Start Time" bind:value={$formData.startTime} />
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>
				<Form.Field {form} name="endTime">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>End Time</Form.Label>
							<Input {...props} placeholder="End Time" bind:value={$formData.endTime} />
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>
				<Form.Field {form} name="locationId">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Business Location</Form.Label>
							<Input
								{...props}
								placeholder="Branch Location"
								bind:value={$formData.locationId}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<Form.Field {form} name="locationId" class="flex flex-col">
					<Popover.Root bind:open={locationOpen}>
						<Form.Control id={triggerId}>
							{#snippet children({ props })}
								<Form.Label>Branch/Location</Form.Label>
								<Popover.Trigger
									class={cn(
										buttonVariants({ variant: 'outline' }),
										'w-[200px] justify-between',
										!$formData.locationId && 'text-muted-foreground'
									)}
									role="combobox"
									{...props}
								>
									{teams?.firstOrNull((f) => f.id === $formData.locationId)?.name ??
										'Select branch Location'}
									<ChevronsUpDown class="opacity-50" />
								</Popover.Trigger>
								<input hidden value={$formData.locationId} name={props.name} />
							{/snippet}
						</Form.Control>
						<Popover.Content class="w-[200px] p-0">
							<Command.Root>
								<Command.Input autofocus placeholder="Search locations..." class="h-9" />
								<Command.Empty>No location data found.</Command.Empty>
								<Command.Group>
									{#each teams ?? [] as location (location.id)}
										<Command.Item
											value={location.id!}
											onSelect={() => {
												$formData.locationId = location.id!
												closeAndFocusTrigger(locationTriggerId)
											}}
										>
											{location.name}
											<Check
												class={cn(
													'ml-auto',
													location.id !== $formData.locationId && 'text-transparent'
												)}
											/>
										</Command.Item>
									{/each}
								</Command.Group>
							</Command.Root>
						</Popover.Content>
					</Popover.Root>
					<Form.FieldErrors />
				</Form.Field>
			</div>
		</Card.Content>
		<Card.Footer class="flex justify-between">
			<Button
				disabled={$submitting}
				variant="outline"
				class="mt-4 w-1/3"
				onclick={() => utilsStore.clearDynamicModal()}
				>Cancel
			</Button>
			<Form.Button disabled={$allErrors.length > 0 || $submitting} class="mt-4 w-4/10">
				{#if $submitting}
					<Loader2 class="mr-2 size-4 animate-spin" />
				{/if}
				{$submitting ? 'Please wait...' : 'Update Appointment'}
			</Form.Button>
		</Card.Footer>
	</Card.Root>
</form>
