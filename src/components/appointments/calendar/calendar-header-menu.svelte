<script lang="ts">
	import { Button } from '$/components/ui/button'
	import { Plus, List, Columns2, Grid3X3 } from '@lucide/svelte'
	import { ButtonGroup } from '$/components/common'
	import { CalendarViewTypes, type CalendarViewType } from '$/lib'

	let {
		showAddAppointment = true,
		currentView = $bindable(CalendarViewTypes.Day),
		onViewChange
	}: {
		showAddAppointment: boolean
		currentView: CalendarViewType
		onViewChange?: (view: CalendarViewType) => void
	} = $props()

	function setCurrentView(view: CalendarViewType) {
		currentView = view
		onViewChange?.(view)
	}
</script>

<div class="flex items-center justify-between gap-3">
	<div class="inline-flex">
		<ButtonGroup>
			<Button
				variant="outline"
				class="rounded-r-none"
				onclick={() => setCurrentView(CalendarViewTypes.Day)}
			>
				<List class="mr-2" />
				<span class="hidden xl:block">Day</span>
			</Button>
			<Button
				variant="outline"
				class="hidden rounded-l-none rounded-r-none border-l-0 lg:inline-flex"
				onclick={() => setCurrentView(CalendarViewTypes.Week)}
			>
				<Columns2 class="xl:mr-2" />
				<span class="hidden xl:block">Week</span>
			</Button>
			<Button
				variant="outline"
				class="rounded-l-none border-l-0"
				onclick={() => setCurrentView(CalendarViewTypes.Month)}
			>
				<Grid3X3 class="mr-2" />
				<span class="hidden xl:block">Month</span>
			</Button>
		</ButtonGroup>
	</div>
	{#if showAddAppointment}
		<Button class="transition-colors">
			<Plus class="mr-2 size-4" />
			<span>Create Appointment</span>
		</Button>
	{/if}
</div>
