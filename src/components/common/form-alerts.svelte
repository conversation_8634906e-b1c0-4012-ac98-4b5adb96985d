<script lang="ts">
	import { getUtilsStore } from '$/stores'
	import type { FormMessage } from '$/lib'
	import type { Writable } from 'svelte/store'

	interface Props {
		message: Writable<FormMessage | undefined>
	}
	let { message }: Props = $props()

	const utilsStore = getUtilsStore()

	$effect(() => {
		message.subscribe((msg) => {
			if (msg) {
				console.log(msg)
				switch (msg.status) {
					case 'error':
						utilsStore.toastError(msg.text)
						break
					case 'success':
						utilsStore.toastSuccess(msg.text)
						break
					case 'warning':
						utilsStore.toastWarning(msg.text)
						break
					default:
						utilsStore.toastInfo(msg.text ?? msg)
						break
				}
			}
		})
	})
</script>
