<script lang="ts">
	import { getUtilsStore } from '$/stores'
	import type { IComponentCache, IDynamicSheet } from '$/lib'
	import { cn } from '$/lib/shadcn.utils'
	import * as Sheet from '$/components/ui/sheet'

	let { componentCollection }: { componentCollection: IComponentCache[] } = $props()
	const utilsStore = getUtilsStore()

	// for custom sheet 'bg-secondary/75 backdrop-blur-none'
	const defaultSheetClass = $state('px-2 py-auto min-w-full md:min-w-[40%]')

	//for custom sheet 'px-2 py-auto min-w-full md:min-w-[40%] bg-secondary/70'
	let CurrentComponent: any = $state(null)
	let canClose: boolean = $state(true)
	let sheetClass: string = $state('')
	let componentProps: any = $state({})
	let openSheet = $state(false)
	let sheetSide: 'right' | 'top' | 'bottom' | 'left' | undefined = $state('right')

	// const componentsCollection: IComponentCache[] = [
	// 	{
	// 		componentName: 'RequestTermsAndConditions',
	// 		component: RequestTermsAndConditions,
	// 		dialogClass: ''
	// 	},
	// 	{
	// 		componentName: 'EmployeeRequestView',
	// 		component: EmployeeRequestView,
	// 		dialogClass: 'md:min-w-[30%]'
	// 	},
	// 	{
	// 		componentName: 'EmployerRequestView',
	// 		component: EmployerRequestView,
	// 		dialogClass: 'md:min-w-[30%]'
	// 	},
	// 	{
	// 		componentName: 'LiquidityRequestView',
	// 		component: LiquidityRequestView,
	// 		dialogClass: 'md:min-w-[30%]'
	// 	}
	// ]

	const showHidePopup = (dynamicSheetItem: IDynamicSheet | undefined) => {
		console.log('dynamic sheet', dynamicSheetItem)

		if (dynamicSheetItem?.componentName) {
			const currentItem = componentCollection.firstOrNull(
				(x) => x.componentName === utilsStore.st?.dynamicSheetItem?.componentName
			)
			if (currentItem) {
				CurrentComponent = currentItem.component
				canClose = dynamicSheetItem.canClose ?? false
				componentProps = dynamicSheetItem.componentProps
				sheetClass = cn(defaultSheetClass, currentItem.dialogClass)
				sheetSide = dynamicSheetItem.side ?? 'right'
			}
		}
	}

	$effect(() => {
		if (utilsStore.st?.dynamicSheetItem) {
			openSheet = true
			showHidePopup(utilsStore.st?.dynamicSheetItem)
		} else {
			openSheet = false
		}
	})
	const openStateChanged = (isOpen: any) => {
		console.log('open state changed', isOpen)
		if (!openSheet) {
			utilsStore.closeDynamicSheet()
		}
	}
</script>

<Sheet.Root bind:open={openSheet} onOpenChange={openStateChanged}>
	<Sheet.Trigger />
	<Sheet.Content
		class={sheetClass}
		side={sheetSide}
		showCloseButton={true}
		interactOutsideBehavior={canClose ? 'close' : 'ignore'}
	>
		<CurrentComponent {...componentProps} />
	</Sheet.Content>
</Sheet.Root>
