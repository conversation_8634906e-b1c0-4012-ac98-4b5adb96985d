<script lang="ts" module>
	type TData = unknown
</script>

<script lang="ts" generics="TData">
	import X from '@lucide/svelte/icons/x'
	import type { Table } from '@tanstack/table-core'
	import { default as DataTableViewOptions } from './data_table_view_options.svelte'
	import { Input } from '$/components/ui/input'

	let {
		table,
		children,
		filterColumn = 'title',
		filterPlaceholder = 'Filter tasks...'
	}: {
		table: Table<TData>
		children?: any
		filterColumn?: string
		filterPlaceholder?: string
	} = $props()
	// TODO: use in calling parent
	// const isFiltered = $derived(table.getState().columnFilters.length > 0)
	// const statusCol = $derived(table.getColumn('status'))
	// const priorityCol = $derived(table.getColumn('priority'))
</script>

<div class="flex items-center justify-between">
	<div class="flex flex-1 items-center space-x-2">
		<Input
			placeholder={filterPlaceholder}
			value={(table.getColumn(filterColumn)?.getFilterValue() as string) ?? ''}
			oninput={(e) => {
				table.getColumn(filterColumn)?.setFilterValue(e.currentTarget.value)
			}}
			onchange={(e) => {
				table.getColumn(filterColumn)?.setFilterValue(e.currentTarget.value)
			}}
			class="h-8 w-[150px] lg:w-[250px]"
		/>
		{@render children?.()}
		<!-- {#if statusCol}
			<DataTableFacetedFilter column={statusCol} title="Status" options={statuses} />
		{/if}
		{#if priorityCol}
			<DataTableFacetedFilter column={priorityCol} title="Priority" options={priorities} />
		{/if}

		{#if isFiltered}
			<Button
				variant="ghost"
				onclick={() => table.resetColumnFilters()}
				class="h-8 px-2 lg:px-3"
			>
				Reset
				<X />
			</Button>
		{/if} -->
	</div>
	<DataTableViewOptions {table} />
</div>
