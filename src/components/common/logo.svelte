<script lang="ts">
	import { ReceiptText } from '@lucide/svelte'
	import { cn } from '$/lib/shadcn.utils'

	interface Props {
		clsName?: string
		onclick?: () => void
	}

	let { clsName = 'flex items-center justify-center', onclick }: Props = $props()

	const handleClick = (event: MouseEvent) => {
		if (onclick) {
			console.log('onclick has event')
			event.preventDefault()
			onclick()
		}
	}
</script>

<button
	class={cn('cursor-pointer focus:outline-none', clsName)}
	onclick={handleClick}
	type="button"
>
	<ReceiptText class="text-primary size-6" />
	<span class="ml-0 truncate text-xl font-bold">
		<span class="text-primary">Spen</span>
		<span class="text-muted-foreground -ml-1">Deed</span>
	</span>
</button>
