<script lang="ts">
	import { page } from '$app/state'
	import { Toaster } from '$/components/ui/sonner'
	import { toast } from 'svelte-sonner'
	import 'linq-extensions'
	import { SvelteKitTopLoader } from 'sveltekit-top-loader'

	let { children }: { children: any } = $props()
	let pageTitle = $derived(`SpenDeed | ${page.data.title}`)

	// Use page.data.flash directly instead of getFlash to avoid lifecycle issues
	$effect(() => {
		const flashData = page.data.flash
		if (flashData) {
			switch (flashData.type) {
				case 'success':
					toast.success(flashData.message)
					break
				case 'error':
					toast.error(flashData.message)
					break
			}
		}
	})
</script>

<svelte:head>
	<title>{pageTitle}</title>
</svelte:head>
<SvelteKitTopLoader showSpinner={false} />
{@render children?.()}
<Toaster richColors expand={true} position="top-right" />
