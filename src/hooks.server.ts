import { handleAuth, api as handleRemult } from '$/server'
import { sequence } from '@sveltejs/kit/hooks'
import type { Handle } from '@sveltejs/kit'

const handleDomain: Handle = async ({ event, resolve }) => {
      const { request, locals } = event
      const host = request.headers.get('host') // e.g. alice.yourdomain.com
      let subdomain: string | null = null

      if (host) {
            const parts = host.split('.')
            // Adjust for your domain structure (e.g. alice.yourdomain.com)
            if (parts.length > 2) {
                  subdomain = parts[0] // 'alice'
            }
      }

      // Add subdomain to locals
      locals.subdomain = subdomain
      return resolve(event)
}


export const handle = sequence(handleDomain, handleRemult, handleAuth)