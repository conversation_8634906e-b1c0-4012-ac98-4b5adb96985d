import {
	Contain<PERSON>,
	Head,
	Heading,
	Html,
	render,
	Section,
	Tailwind,
	Text,
	Button,
	Hr,
	Img
} from '@react-email/components'
import { SpendeedLogo } from './components/logo'
import { getCopyRight } from '$/lib'

interface InvitationEmailProps {
	inviterName: string
	organizationName: string
	teamName?: string
	role: string
	inviteLink: string
	logoUrl?: string
}

function InvitationEmail({
	inviterName,
	organizationName,
	teamName,
	role,
	inviteLink,
	logoUrl
}: InvitationEmailProps) {
	const teamText = teamName ? ` to the ${teamName} team` : ''
	const roleText = role ? ` as ${role}` : ''

	return (
		<Html>
			<Tailwind>
				<Head />
				<Container className="mx-auto max-w-2xl bg-white font-sans">
					{/* Header with Logo */}
					<Section className="bg-gradient-to-r from-blue-600 to-purple-600 px-8 py-6 text-center">
						<SpendeedLogo size="lg" className="text-white" href="https://spendeed.ai" />
					</Section>

					{/* Main Content */}
					<Container className="px-8 py-8">
						<Heading className="mb-6 text-center text-2xl font-bold text-gray-900">
							You're invited to join {organizationName}
						</Heading>

						{/* Organization Logo if provided */}
						{logoUrl && (
							<Section className="mb-6 text-center">
								<Img
									src={logoUrl}
									alt={`${organizationName} logo`}
									className="mx-auto h-16 w-16 rounded-lg border border-gray-200"
								/>
							</Section>
						)}

						{/* Invitation Details */}
						<Section className="my-8 rounded-lg border border-gray-200 bg-blue-50 p-6">
							<Text className="mb-4 text-center text-lg font-semibold text-gray-900">
								{inviterName} has invited you to join {organizationName}
								{teamText}
								{roleText}
							</Text>

							<Text className="mb-6 text-center text-gray-600">
								Accept this invitation to start collaborating with your team on SpenDeed.
							</Text>

							{/* Accept Invitation Button */}
							<Section className="text-center">
								<Button
									href={inviteLink}
									className="text-decoration-none inline-block rounded-lg bg-blue-600 px-8 py-3 text-center font-semibold text-white hover:bg-blue-700"
								>
									Accept Invitation
								</Button>
							</Section>
						</Section>

						{/* Invitation Details Card */}
						<Section className="my-6 rounded-lg border border-gray-200 bg-gray-50 p-4">
							<Text className="mb-2 text-sm font-semibold text-gray-700">
								Invitation Details:
							</Text>
							<Text className="mb-1 text-sm text-gray-600">
								<span className="font-medium">Organization:</span> {organizationName}
							</Text>
							{teamName && (
								<Text className="mb-1 text-sm text-gray-600">
									<span className="font-medium">Team:</span> {teamName}
								</Text>
							)}
							<Text className="mb-1 text-sm text-gray-600">
								<span className="font-medium">Role:</span> {role}
							</Text>
							<Text className="text-sm text-gray-600">
								<span className="font-medium">Invited by:</span> {inviterName}
							</Text>
						</Section>

						{/* Alternative Link */}
						<Section className="my-6">
							<Text className="mb-2 text-sm text-gray-600">
								If the button above doesn't work, copy and paste this link into your
								browser:
							</Text>
							<Text className="text-sm break-all text-blue-600">{inviteLink}</Text>
						</Section>

						{/* Security Notice */}
						<Section className="my-6 rounded-lg border border-amber-200 bg-amber-50 p-4">
							<Text className="text-sm text-amber-800">
								<span className="font-semibold">Security Notice:</span> This invitation will
								expire in 48 hours. If you didn't expect this invitation or don't know{' '}
								{inviterName}, you can safely ignore this email.
							</Text>
						</Section>

						<Hr className="my-6 border-gray-200" />

						{/* Footer */}
						<Section className="text-center">
							<Text className="mb-2 text-xs text-gray-400">
								This invitation was sent by {inviterName} from {organizationName}
							</Text>
							<Text className="text-xs text-gray-400">{getCopyRight()}</Text>
						</Section>
					</Container>
				</Container>
			</Tailwind>
		</Html>
	)
}

export const getInvitationEmail = async (props: InvitationEmailProps) => {
	return await render(<InvitationEmail {...props} />)
}
