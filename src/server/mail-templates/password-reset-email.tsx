import {
	Container,
	Head,
	Heading,
	Html,
	render,
	Section,
	Tailwind,
	Text,
	Hr
} from '@react-email/components'
import { SpendeedLogo } from './components/logo'
import { getCopyRight } from '$/lib'

function Password_reset_email({ code, expires }: { code: string; expires: Date }) {
	const hoursValid = Math.floor((+expires - Date.now()) / (60 * 60 * 1000))

	return (
		<Html>
			<Tailwind>
				<Head />
				<Container className="mx-auto max-w-2xl bg-white font-sans">
					{/* Header with Logo */}
					<Section className="bg-gradient-to-r from-blue-600 to-purple-600 px-8 py-6 text-center">
						<SpendeedLogo size="lg" className="text-white" href="https://spendeed.ai" />
					</Section>

					{/* Main Content */}
					<Container className="px-8 py-8">
						<Heading className="mb-6 text-center text-2xl font-bold text-gray-900">
							Reset your password
						</Heading>

						<Text className="mb-6 text-center text-gray-600">
							Please enter the following code on the password reset page to create a new
							password.
						</Text>

						{/* Reset Code Section */}
						<Section className="my-8 rounded-lg border border-gray-200 bg-gray-50 p-6 text-center">
							<Text className="mb-2 text-sm font-semibold tracking-wide text-gray-700 uppercase">
								Password Reset Code
							</Text>
							<Text className="mb-4 text-4xl font-bold tracking-widest text-blue-600">
								{code}
							</Text>
							<Text className="text-sm text-gray-500">
								This code is valid for {hoursValid} hours
							</Text>
						</Section>

						<Text className="mb-4 text-center text-sm text-gray-600">
							If you didn't request a password reset, you can safely ignore this email.
						</Text>

						<Hr className="my-6 border-gray-200" />

						{/* Footer */}
						<Section className="text-center">
							<Text className="text-xs text-gray-400">{getCopyRight()}</Text>
						</Section>
					</Container>
				</Container>
			</Tailwind>
		</Html>
	)
}

export const getPasswordResetEmail = async (code: string, expires: Date) => {
	return await render(<Password_reset_email code={code} expires={expires} />)
}
