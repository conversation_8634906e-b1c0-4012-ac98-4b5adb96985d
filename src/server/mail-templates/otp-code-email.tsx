import {
	Con<PERSON><PERSON>,
	Head,
	Heading,
	Html,
	render,
	Section,
	Tailwind,
	Text,
	Button,
	Hr
} from '@react-email/components'
import { SpendeedLogo } from './components/logo'
import { getCopyRight } from '$/lib'

interface OTPCodeEmailProps {
	otp: string
	type: 'sign-in' | 'email-verification'
	email: string
}

const emailTitle = {
	'sign-in': 'Sign in to SpenDeed',
	'email-verification': 'SignUp: Verify your email address'
}
const descriptionText = {
	'sign-in': 'Use this code to complete your sign-in to SpenDeed.',
	'email-verification':
		'Use this code to verify your email address and complete your account setup.'
}
function OTPCodeEmail({ otp, type, email }: OTPCodeEmailProps) {
	console.log('email type is:', type)
	const isSignIn = type === 'sign-in'
	const title = emailTitle[type]
	const description = descriptionText[type]

	return (
		<Html>
			<Tailwind>
				<Head />
				<Container className="mx-auto max-w-2xl bg-white font-sans">
					{/* Header with Logo */}
					<Section className="bg-gradient-to-r from-blue-600 to-purple-600 px-8 py-6 text-center">
						<SpendeedLogo size="lg" className="text-white" href="https://spendeed.ai" />
					</Section>

					{/* Main Content */}
					<Container className="px-8 py-8">
						<Heading className="mb-6 text-center text-2xl font-bold text-gray-900">
							{title}
						</Heading>

						<Text className="mb-6 text-center text-gray-600">{description}</Text>

						<Text className="mb-2 text-sm text-gray-500">Email: {email}</Text>

						{/* OTP Code Section */}
						<Section className="my-8 rounded-lg border border-gray-200 bg-gray-50 p-6 text-center">
							<Text className="mb-2 text-sm font-semibold tracking-wide text-gray-700 uppercase">
								Your verification code
							</Text>
							<Text className="mb-4 text-4xl font-bold tracking-widest text-blue-600">
								{otp}
							</Text>
							<Text className="text-sm text-gray-500">
								This code will expire in 10 minutes
							</Text>
						</Section>

						{/* Instructions */}
						<Section className="my-6">
							<Text className="mb-4 text-sm text-gray-600">
								{isSignIn
									? 'Enter this code on the sign-in page to access your account.'
									: 'Enter this code to verify your email address and activate your account.'}
							</Text>

							<Text className="text-sm text-gray-500">
								If you didn't request this code, you can safely ignore this email.
							</Text>
						</Section>

						<Hr className="my-6 border-gray-200" />

						{/* Footer */}
						<Section className="text-center">
							<Text className="mb-2 text-xs text-gray-400">
								This email was sent to {email}
							</Text>
							<Text className="text-xs text-gray-400">{getCopyRight()}</Text>
						</Section>
					</Container>
				</Container>
			</Tailwind>
		</Html>
	)
}

export const getOTPCodeEmail = async (
	otp: string,
	type: 'sign-in' | 'email-verification',
	email: string
) => {
	return await render(<OTPCodeEmail otp={otp} type={type} email={email} />)
}
