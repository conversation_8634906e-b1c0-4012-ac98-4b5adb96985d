import {
	Con<PERSON><PERSON>,
	Head,
	Heading,
	Html,
	render,
	Section,
	Tailwind,
	Text,
	Button,
	Hr
} from '@react-email/components'
import { SpendeedLogo } from './components/logo'
import { getCopyRight } from '$/lib'

function PasswordLinkEmail({ host, magicLink }: { host: string; magicLink: string }) {
	const escapedHost = host.replace(/\./g, '&#8203;.')

	return (
		<Html>
			<Tailwind>
				<Head />
				<Container className="mx-auto max-w-2xl bg-white font-sans">
					{/* Header with Logo */}
					<Section className="bg-gradient-to-r from-blue-600 to-purple-600 px-8 py-6 text-center">
						<SpendeedLogo size="lg" className="text-white" href="https://spendeed.ai" />
					</Section>

					{/* Main Content */}
					<Container className="px-8 py-8">
						<Heading className="mb-6 text-center text-2xl font-bold text-gray-900">
							Sign in to SpenDeed
						</Heading>

						<Text className="mb-6 text-center text-gray-600">
							Please click the link below to sign in to {escapedHost}.
						</Text>

						{/* Sign In Section */}
						<Section className="my-8 rounded-lg border border-gray-200 bg-blue-50 p-6 text-center">
							<Text className="mb-4 text-lg font-semibold text-gray-900">
								Click to sign in securely
							</Text>

							<Button
								href={magicLink}
								className="text-decoration-none inline-block rounded-lg bg-blue-600 px-8 py-3 text-center font-semibold text-white hover:bg-blue-700"
							>
								Sign In to SpenDeed
							</Button>
						</Section>

						<Text className="mb-4 text-center text-sm text-gray-600">
							If you did not request a sign in link, please ignore this email.
						</Text>

						<Hr className="my-6 border-gray-200" />

						{/* Footer */}
						<Section className="text-center">
							<Text className="text-xs text-gray-400">{getCopyRight()}</Text>
						</Section>
					</Container>
				</Container>
			</Tailwind>
		</Html>
	)
}

export const getPasswordLinkEmail = async (host: string, magicLink: string) => {
	return await render(<PasswordLinkEmail host={host} magicLink={magicLink} />)
}
