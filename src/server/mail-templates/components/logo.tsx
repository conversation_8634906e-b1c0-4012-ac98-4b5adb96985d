import React from 'react'

interface LogoProps {
	size?: 'sm' | 'md' | 'lg' | 'xl'
	className?: string
	showIcon?: boolean
	href?: string
}

// ReceiptText icon SVG (from Lucide) - matching your web component
const ReceiptTextIcon = ({ className = 'w-6 h-6' }: { className?: string }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		width="24"
		height="24"
		viewBox="0 0 24 24"
		fill="none"
		stroke="currentColor"
		strokeWidth="2"
		strokeLinecap="round"
		strokeLinejoin="round"
		className={className}
	>
		<path d="M4 2v20l2-1 2 1 2-1 2 1 2-1 2 1 2-1 2 1V2l-2 1-2-1-2 1-2-1-2 1-2-1-2 1Z" />
		<path d="M16 8h-6a2 2 0 1 0 0 4h4a2 2 0 1 1 0 4H8" />
		<path d="M12 18V6" />
	</svg>
)

export const SpendeedLogo = ({
	size = 'md',
	className = '',
	showIcon = true,
	href = 'https://spendeed.ai'
}: LogoProps) => {
	// Size configurations matching your design system
	const sizeConfig = {
		sm: {
			icon: 'w-4 h-4',
			text: 'text-sm',
			spacing: 'ml-1'
		},
		md: {
			icon: 'w-6 h-6',
			text: 'text-xl',
			spacing: 'ml-2'
		},
		lg: {
			icon: 'w-8 h-8',
			text: 'text-2xl',
			spacing: 'ml-3'
		},
		xl: {
			icon: 'w-12 h-12',
			text: 'text-4xl',
			spacing: 'ml-4'
		}
	}

	const config = sizeConfig[size]

	const LogoContent = () => (
		<div className={`flex items-center justify-center ${className}`}>
			{showIcon && <ReceiptTextIcon className={`text-primary ${config.icon}`} />}
			<span className={`${config.spacing} truncate ${config.text} font-bold`}>
				<span className="text-primary">Spen</span>
				<span className="text-muted-foreground -ml-1">Deed</span>
			</span>
		</div>
	)

	if (href) {
		return (
			<a
				href={href}
				className="transition-opacity hover:opacity-80 focus:outline-none"
				style={{ textDecoration: 'none' }}
			>
				<LogoContent />
			</a>
		)
	}

	return <LogoContent />
}

// Compact version for email headers
export const SpendeedLogoCompact = ({
	className = '',
	href = 'https://spendeed.ai'
}: {
	className?: string
	href?: string
}) => <SpendeedLogo size="sm" className={className} href={href} showIcon={true} />

// Large version for email heroes
export const SpendeedLogoHero = ({
	className = '',
	href = 'https://spendeed.ai'
}: {
	className?: string
	href?: string
}) => <SpendeedLogo size="xl" className={className} href={href} showIcon={true} />

// Text-only version for footers
export const SpendeedLogoText = ({
	size = 'md',
	className = '',
	href = 'https://spendeed.ai'
}: {
	size?: 'sm' | 'md' | 'lg' | 'xl'
	className?: string
	href?: string
}) => <SpendeedLogo size={size} className={className} href={href} showIcon={false} />

// Icon-only version for minimal spaces
export const SpendeedIcon = ({
	size = 'md',
	className = ''
}: {
	size?: 'sm' | 'md' | 'lg' | 'xl'
	className?: string
}) => {
	const sizeConfig = {
		sm: 'w-4 h-4',
		md: 'w-6 h-6',
		lg: 'w-8 h-8',
		xl: 'w-12 h-12'
	}

	return <ReceiptTextIcon className={`text-primary ${sizeConfig[size]} ${className}`} />
}

export default SpendeedLogo
