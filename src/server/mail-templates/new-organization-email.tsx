import {
	Con<PERSON><PERSON>,
	Head,
	Heading,
	Html,
	render,
	Section,
	Tailwind,
	Text,
	Button,
	Hr,
	Img
} from '@react-email/components'
import { SpendeedLogo } from './components/logo'

const getCopyRight = () => {
	const today = new Date()
	return `© ${today.getFullYear()} SpenDeed.AI. All rights reserved.`
}

interface NewOrganizationEmailProps {
	userName: string
	userEmail: string
	organizationName: string
	organizationId: string
	dashboardUrl: string
	logoUrl?: string
}

function NewOrganizationEmail({
	userName,
	userEmail,
	organizationName,
	organizationId,
	dashboardUrl,
	logoUrl
}: NewOrganizationEmailProps) {
	return (
		<Html>
			<Tailwind>
				<Head />
				<Container className="mx-auto max-w-2xl bg-white font-sans">
					{/* Header with Logo */}
					<Section className="bg-gradient-to-r from-blue-600 to-purple-600 px-8 py-6 text-center">
						<SpendeedLogo size="lg" className="text-white" href="https://spendeed.ai" />
					</Section>

					{/* Main Content */}
					<Container className="px-8 py-8">
						{/* Welcome Heading */}
						<Heading className="mb-6 text-center text-2xl font-bold text-gray-900">
							🎉 Welcome to SpenDeed!
						</Heading>

						<Text className="mb-6 text-center text-lg text-gray-600">
							Congratulations {userName}! Your organization has been successfully created.
						</Text>

						{/* Organization Logo if provided */}
						{logoUrl && (
							<Section className="mb-6 text-center">
								<Img
									src={logoUrl}
									alt={`${organizationName} logo`}
									className="mx-auto h-20 w-20 rounded-lg border border-gray-200 shadow-sm"
								/>
							</Section>
						)}

						{/* Organization Details Card */}
						<Section className="my-8 rounded-lg border border-green-200 bg-green-50 p-6">
							<Text className="mb-4 text-center text-xl font-semibold text-green-900">
								🏢 {organizationName}
							</Text>
							
							<Text className="mb-6 text-center text-gray-700">
								Your organization is now ready! You can start managing your expenses, 
								inviting team members, and tracking your financial goals.
							</Text>

							{/* Get Started Button */}
							<Section className="text-center">
								<Button
									href={dashboardUrl}
									className="text-decoration-none inline-block rounded-lg bg-green-600 px-8 py-3 text-center font-semibold text-white hover:bg-green-700"
								>
									Access Your Dashboard
								</Button>
							</Section>
						</Section>

						{/* Organization Details */}
						<Section className="my-6 rounded-lg border border-gray-200 bg-gray-50 p-4">
							<Text className="mb-2 text-sm font-semibold text-gray-700">
								Organization Details:
							</Text>
							<Text className="mb-1 text-sm text-gray-600">
								<span className="font-medium">Name:</span> {organizationName}
							</Text>
							<Text className="mb-1 text-sm text-gray-600">
								<span className="font-medium">Admin:</span> {userName} ({userEmail})
							</Text>
							<Text className="text-sm text-gray-600">
								<span className="font-medium">Organization ID:</span> {organizationId}
							</Text>
						</Section>

						{/* Next Steps */}
						<Section className="my-8 rounded-lg border border-blue-200 bg-blue-50 p-6">
							<Text className="mb-4 text-lg font-semibold text-blue-900">
								🚀 What's Next?
							</Text>
							
							<Text className="mb-3 text-sm text-gray-700">
								<span className="font-medium">1. Set up your profile:</span> Complete your organization profile and upload your logo
							</Text>
							<Text className="mb-3 text-sm text-gray-700">
								<span className="font-medium">2. Invite team members:</span> Add your team to start collaborating on expense management
							</Text>
							<Text className="mb-3 text-sm text-gray-700">
								<span className="font-medium">3. Configure settings:</span> Set up your expense categories, approval workflows, and integrations
							</Text>
							<Text className="text-sm text-gray-700">
								<span className="font-medium">4. Start tracking:</span> Begin recording and managing your organization's expenses
							</Text>
						</Section>

						{/* Alternative Link */}
						<Section className="my-6">
							<Text className="mb-2 text-sm text-gray-600">
								If the button above doesn't work, copy and paste this link into your browser:
							</Text>
							<Text className="break-all text-sm text-blue-600">{dashboardUrl}</Text>
						</Section>

						{/* Support Section */}
						<Section className="my-6 rounded-lg border border-purple-200 bg-purple-50 p-4">
							<Text className="mb-2 text-sm font-semibold text-purple-900">
								💬 Need Help?
							</Text>
							<Text className="text-sm text-purple-800">
								Our support team is here to help you get started. Visit our{' '}
								<a href="https://spendeed.ai/help" className="text-purple-600 underline">
									Help Center
								</a>{' '}
								or contact us at{' '}
								<a href="mailto:<EMAIL>" className="text-purple-600 underline">
									<EMAIL>
								</a>
							</Text>
						</Section>

						<Hr className="my-6 border-gray-200" />

						{/* Footer */}
						<Section className="text-center">
							<Text className="mb-2 text-xs text-gray-400">
								This email was sent because you created an organization on SpenDeed
							</Text>
							<Text className="text-xs text-gray-400">{getCopyRight()}</Text>
						</Section>
					</Container>
				</Container>
			</Tailwind>
		</Html>
	)
}

export const getNewOrganizationEmail = async (props: NewOrganizationEmailProps) => {
	return await render(<NewOrganizationEmail {...props} />)
}
