import {
	Con<PERSON><PERSON>,
	Head,
	Heading,
	Html,
	render,
	Section,
	Tailwind,
	Text,
	Button,
	Hr
} from '@react-email/components'
import { SpendeedLogo } from './components/logo'
import { getCopyRight } from '$/lib'

function GetInviteEmail({
	teamName,
	inviteLink,
	expires,
	inviterName
}: {
	teamName: string
	inviteLink: string
	expires: Date
	inviterName: string
}) {
	const hoursValid = Math.floor((+expires - Date.now()) / (60 * 60 * 1000))

	return (
		<Html>
			<Tailwind>
				<Head />
				<Container className="mx-auto max-w-2xl bg-white font-sans">
					{/* Header with Logo */}
					<Section className="bg-gradient-to-r from-blue-600 to-purple-600 px-8 py-6 text-center">
						<SpendeedLogo size="lg" className="text-white" href="https://spendeed.ai" />
					</Section>

					{/* Main Content */}
					<Container className="px-8 py-8">
						<Heading className="mb-6 text-center text-2xl font-bold text-gray-900">
							Invitation to join Team {teamName}
						</Heading>

						<Text className="mb-6 text-center text-gray-600">
							{inviterName} has invited you to join {teamName} on SpenDeed.
						</Text>

						{/* Invitation Details */}
						<Section className="my-8 rounded-lg border border-gray-200 bg-blue-50 p-6 text-center">
							<Text className="mb-4 text-lg font-semibold text-gray-900">
								Click the link below to accept your invitation
							</Text>

							<Button
								href={inviteLink}
								className="text-decoration-none inline-block rounded-lg bg-blue-600 px-8 py-3 text-center font-semibold text-white hover:bg-blue-700"
							>
								Accept Invitation
							</Button>

							<Text className="mt-4 text-sm text-gray-500">
								This invitation is valid for {hoursValid} hours
							</Text>
						</Section>

						<Text className="mb-4 text-center text-sm text-gray-600">
							If you didn't expect this invitation, you can safely ignore this email.
						</Text>

						<Hr className="my-6 border-gray-200" />

						{/* Footer */}
						<Section className="text-center">
							<Text className="text-xs text-gray-400">{getCopyRight()}</Text>
						</Section>
					</Container>
				</Container>
			</Tailwind>
		</Html>
	)
}

export const getInviteEmail = async (
	teamName: string,
	inviteLink: string,
	expires: Date,
	inviterName: string
) => {
	return await render(
		<GetInviteEmail
			teamName={teamName}
			inviteLink={inviteLink}
			expires={expires}
			inviterName={inviterName}
		/>
	)
}
