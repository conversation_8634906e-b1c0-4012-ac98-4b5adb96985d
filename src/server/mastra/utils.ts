import { Memory } from '@mastra/memory'
import { PgVector, PostgresStore } from '@mastra/pg'
import { PinoLogger } from '@mastra/loggers'
import { fastembed } from '@mastra/fastembed'
import { DATABASE_URL, OLLAMA_BASE_URL } from '$env/static/private'
import { createOllama } from 'ollama-ai-provider'

export const logger = new PinoLogger({
	name: 'mastra',
	level: 'debug'
})

export const storage = new PostgresStore({
	connectionString: DATABASE_URL,
	schemaName: 'mastra'
})
export const vector = new PgVector({
	connectionString: DATABASE_URL
})

export const memory = new Memory({
	storage,
	vector,
	embedder: fastembed,
	options: {
		semanticRecall: true,
		// Number of recent messages to include
		lastMessages: 20,

		// Working memory configuration
		workingMemory: {
			enabled: true
		},

		// Thread options
		threads: {
			generateTitle: true
		}
	}
})


export const ollama = createOllama({
	baseURL: OLLAMA_BASE_URL

})