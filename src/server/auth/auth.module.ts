
import { auth as authConfig } from "./auth.config.js"
import { remult } from "remult"
import { addRolesToUser } from "./auth.helpers.js"
import { entityGroups, getISessionData, RolesType } from '$/lib'
import { Module } from 'remult/server'
import { SUPER_ADMIN_EMAILS } from '$env/static/private'

export const authModule = () => new Module({
  key: "auth",
  entities: entityGroups.auth,

  initApi: async () => {
    // Add some roles to some users.
    const emails = (SUPER_ADMIN_EMAILS ?? "")
      .split(",")
      .map((c) => c.trim())
      .filter(Boolean)
    await addRolesToUser(emails, Object.values(RolesType))
  },

  initRequest: async () => {
    const s = await authConfig.api.getSession({
      headers: new Headers(remult.context.headers?.getAll()),
    })
    console.log('auth init request ', s)
    if (s) {
      const user = s.user
      const session = s.session
      const sessionData = getISessionData(user as any, session as any)
      if (!sessionData) return
      remult.user = {
        ...sessionData
      } as any

    } else {
      remult.user = undefined
    }
  }
}) 
