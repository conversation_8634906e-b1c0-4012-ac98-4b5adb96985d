
import { ac, roles, RolesType } from '$/lib'

export const adminConfig = {
      // Default role for new users
      defaultRole: RolesType.User,
      // Roles that are considered admin roles
      adminRoles: [RolesType.Admin, RolesType.OrgAdmin],
      // Custom access control and roles - using full access control with all resources
      ac: ac as any, // Type assertion to work around TypeScript compatibility
      roles
}