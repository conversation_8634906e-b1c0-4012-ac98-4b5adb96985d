import { entities } from '$/lib'
import { createPostgresDataProvider } from 'remult/postgres'
import { remultApi } from "remult/remult-sveltekit"
import { authModule } from './auth'
import { DATABASE_URL } from '$env/static/private'
import { TimeZone } from '$/lib/models/time-zone.entity'
import { Gender } from '$/lib/models/gender.entity'
import { Role } from '$/lib/models/role.entity'
import { genderTypes, rolesTypes } from '$/lib/types'

// Timezone data from seed.ts
const TIMEZONES_DATA = [
      // UTC
      'UTC',

      // North America - Eastern
      'America/New_York',
      'America/Toronto',

      // North America - Central
      'America/Chicago',
      'America/Mexico_City',

      // North America - Mountain
      'America/Denver',
      'America/Phoenix',

      // North America - Pacific
      'America/Los_Angeles',
      'America/Vancouver',

      // Europe - Western
      'Europe/London',
      'Europe/Dublin',

      // Europe - Central
      'Europe/Paris',
      'Europe/Berlin',

      // Europe - Eastern
      'Europe/Moscow',
      'Europe/Istanbul',

      // Asia - Middle East
      'Asia/Dubai',
      'Asia/Tehran',

      // Asia - South
      'Asia/Kolkata',
      'Asia/Karachi',

      // Asia - Southeast
      'Asia/Singapore',
      'Asia/Bangkok',

      // Asia - East
      'Asia/Tokyo',
      'Asia/Shanghai',

      // Australia/Oceania
      'Australia/Sydney',
      'Pacific/Auckland',

      // Africa
      'Africa/Lagos',
      'Africa/Cairo',

      // South America
      'America/Sao_Paulo',
      'America/Buenos_Aires'
]

export const api = remultApi({
      dataProvider: createPostgresDataProvider({
            connectionString: DATABASE_URL
      }),
      admin: true,
      entities: [...entities],
      modules: [authModule()],
      async initApi(remult) {
            //check to see if timezones,gender and roles data are loaded
            //if not,load them
            console.log('init api ', remult.user)

            try {
                  // Seed TimeZones
                  const timeZoneRepo = remult.repo(TimeZone)
                  const timeZoneCount = await timeZoneRepo.count()
                  if (timeZoneCount === 0) {
                        console.log('🔄 Seeding timezones...')
                        for (const timezone of TIMEZONES_DATA) {
                              await timeZoneRepo.insert({
                                    timeZoneName: timezone,
                                    offsetHours: 0 // Default offset, can be updated later
                              })
                        }
                        console.log(`✅ Successfully seeded ${TIMEZONES_DATA.length} timezones`)
                  }

                  // Seed Genders
                  const genderRepo = remult.repo(Gender)
                  const genderCount = await genderRepo.count()
                  if (genderCount === 0) {
                        console.log('🔄 Seeding genders...')
                        for (const genderType of genderTypes) {
                              await genderRepo.insert({ name: genderType })
                        }
                        console.log(`✅ Successfully seeded ${genderTypes.length} genders`)
                  }

                  // Seed Roles
                  const roleRepo = remult.repo(Role)
                  const roleCount = await roleRepo.count()
                  if (roleCount === 0) {
                        console.log('🔄 Seeding roles...')
                        for (const roleType of rolesTypes) {
                              await roleRepo.insert({ role: roleType })
                        }
                        console.log(`✅ Successfully seeded ${rolesTypes.length} roles`)
                  }

            } catch (error) {
                  console.error('❌ Error seeding initial data:', error)
            }
      },
})