import { stringify } from 'superjson'
import type { PublishParams, SendParams } from './types'
import { safeJsonParse } from '../../lib/utils'
import { PLUNK_API_URL, PLUNK_SECRET_KEY } from '$env/static/private'

const sendMail = async <T>({
	json,
	url,
	...options
}: RequestInit & {
	url: string
	json: any
	headers?: Record<string, string>
}) => {
	console.log('plunk API URL', PLUNK_API_URL)
	console.log('plunk url', url)
	const apiUrl = PLUNK_API_URL
	const apiKey = PLUNK_SECRET_KEY
	const res = await fetch(
		new URL(url, apiUrl).toString(),
		{
			...options,
			headers: {
				Authorization: `Bearer ${apiKey}`,
				...(json && { 'Content-Type': 'application/json' }),
				...options.headers
			},
			body: json ? JSON.stringify(json) : undefined
		}
	)

	const text = await res.text()

	const data: any = JSON.parse(text)

	if (res?.status === 401) {
		console.log(res.statusText)
		throw new Error(` 401 Error : ${res.statusText}: ${data?.message}`)
	}

	if (res?.status === 404) {
		throw new Error(`404 Error : ${res.statusText}: ${data?.message}`)
	}

	if (!res.ok) {
		throw new Error(`Unknown API Error : ${res.statusText}: ${data?.message}`)
	}
	return data as T
}

export const plunk = {
	events: {
		/**
		 * Publishes an event to Plunk
		 * @param {string} event.event - The event you want to publish
		 * @param {string} event.email - The email associated with this event
		 * @param {Object=} event.data - The user data associated with this event
		 * @param {boolean=true} event.subscribed - Whether the user is subscribed to marketing emails
		 */
		track: async (event: PublishParams) => {
			return await sendMail<{
				success: true
			}>({
				method: 'POST',
				url: 'track',
				json: { ...event }
			})
		}
	},
	emails: {
		/**
		 * Sends a transactional email with Plunk
		 *
		 * @param {string} body.to - The email you want to send to
		 * @param {string} body.subject - The subject of the email
		 * @param {string} body.body - The body of the email
		 * @param {string=} body.from - The email you want to send from
		 * @param {string=} body.name - The name you want to send as
		 * @param {string=html} body.type - The type of email you want to send
		 * @param {boolean=false} body.subscribed - Whether the user is subscribed to marketing emails
		 */
		send: async (body: SendParams) => {
			return await sendMail<{
				success: true
			}>({
				method: 'POST',
				url: 'send',
				json: {
					...body
				}
			})
		}
	}
}

