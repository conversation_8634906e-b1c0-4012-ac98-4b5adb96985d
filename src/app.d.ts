// See https://svelte.dev/docs/kit/types#app
import type { Session as AuthSession } from 'svelte-kit-cookie-session'
import type { ISessionData, User } from '$/lib'


// for information about these interfaces
declare global {
	namespace App {
		// interface Error {}
		interface Locals {
			session: AuthSession<ISessionData>
			showHomeModal?: boolean
			startTimer: number
			error: string
			errorId: string
			errorStackTrace: string
			message: unknown
			track: unknown
			// api: HonoClientType['api']
			honoApiKey: string
			// jazzWorker: any
			// jazzInbox: any
			subdomain: string | null
		}
		interface PageData {
			flash?: {
				type: 'success' | 'error'
				message: string
			}
			session: ISessionData
			title: string
			[key: string]: any
		}

		interface Error {
			code?: string
			errorId?: string
		}
		// interface PageState {}
		// interface Platform {}
		interface LayoutData {
			flash?: {
				type: 'success' | 'error'
				message: string
			}
			session: ISessionData
			title: string
			[key: string]: any
		}
		type Prettify<T> = {
			[K in keyof T]: T[K]
		} & {}

		namespace superforms {
			type FormMessage = {
				status: 'error' | 'success' | 'warning'
				text: string
				data?: any
			}
		}
	}
}

declare module 'tailwindcss-motion'

declare module "remult" {
	interface UserInfo extends User {
		isAdmin: boolean
		isOrgAdmin: boolean
		isTeamLocationAdmin: boolean
		teamConfigIsSetup: boolean
		expires: Date
		organizationId: string
		teamId: string
	}
	// interface FieldOptions<entityType, valueType> {
	//   placeholder?: string;
	// }
}
export { }
