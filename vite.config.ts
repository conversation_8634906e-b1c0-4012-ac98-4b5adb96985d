import { sveltekit } from '@sveltejs/kit/vite'
import { defineConfig } from 'vitest/config'
import svg from '@poppanator/sveltekit-svg'
import { kitRoutes } from 'vite-plugin-kit-routes'
import type { KIT_ROUTES } from './src/lib/routes'
import tailwindcss from '@tailwindcss/vite'
import devtoolsJson from 'vite-plugin-devtools-json'
import removeConsole from 'vite-plugin-remove-console'

export default defineConfig({
	plugins: [
		tailwindcss(),
		svg(),
		kitRoutes<KIT_ROUTES>({ format: 'object[symbol]', format_short: true }),
		sveltekit(),
		devtoolsJson(),
		removeConsole()
	],
	test: {
		include: ['src/**/*.{test,spec}.{js,ts}']
	},
	server: {
		open: true,
		host: 'localhost',
		port: 5173,
		fs: {
			strict: false // Disable strict file serving restrictions
		}
	},
	build: {
		rollupOptions: {
			external: [
				// Externalize native Node.js modules
				'@anush008/tokenizers',
				'onnxruntime-node',
				// Add other native modules if needed
				'@mastra/fastembed',
				'@mastra/core'
			]
		}
	},
	optimizeDeps: {
		exclude: [
			// Exclude native modules from dependency optimization
			'@anush008/tokenizers',
			'onnxruntime-node',
			'@mastra/fastembed',
			'@mastra/core'
		]
	}
})
