name: pgai
services:
   db:
      image: timescale/timescaledb-ha:pg17
      environment:
         POSTGRES_PASSWORD: postgres
         POSTGRES_DB: spendeed
      ports:
         - '5439:5432'
      volumes:
         - data:/home/<USER>/pgdata/data
         - pgdata:/var/lib/postgresql/data
         - ./seed-data:/docker-entrypoint-initdb.d
      command: |
         postgres 
         -c ai.ollama_host=http://ollama:11434
         -c wal_level=logical
         -c max_wal_senders=10 
         -c max_replication_slots=5 
         -c hot_standby=on 
         -c hot_standby_feedback=on
   vectorizer-worker:
      image: timescale/pgai-vectorizer-worker:latest
      environment:
         PGAI_VECTORIZER_WORKER_DB_URL: ************************************/spendeed
         OLLAMA_HOST: http://ollama:11434
      command: ['--poll-interval', '5s', '--log-level', 'DEBUG']
   ollama:
      image: ollama/ollama
   # zero:
   #    image: rocicorp/zero:latest
   restate:
      image: restatedev/restate:latest
      ports:
         - '8090:8050'
volumes:
   data:
      driver: local
   pgdata:
      driver: local
