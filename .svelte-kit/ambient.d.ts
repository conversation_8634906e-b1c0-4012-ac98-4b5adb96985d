
// this file is generated — do not edit it


/// <reference types="@sveltejs/kit" />

/**
 * Environment variables [loaded by Vite](https://vitejs.dev/guide/env-and-mode.html#env-files) from `.env` files and `process.env`. Like [`$env/dynamic/private`](https://svelte.dev/docs/kit/$env-dynamic-private), this module cannot be imported into client-side code. This module only includes variables that _do not_ begin with [`config.kit.env.publicPrefix`](https://svelte.dev/docs/kit/configuration#env) _and do_ start with [`config.kit.env.privatePrefix`](https://svelte.dev/docs/kit/configuration#env) (if configured).
 * 
 * _Unlike_ [`$env/dynamic/private`](https://svelte.dev/docs/kit/$env-dynamic-private), the values exported from this module are statically injected into your bundle at build time, enabling optimisations like dead code elimination.
 * 
 * ```ts
 * import { API_KEY } from '$env/static/private';
 * ```
 * 
 * Note that all environment variables referenced in your code should be declared (for example in an `.env` file), even if they don't have a value until the app is deployed:
 * 
 * ```
 * MY_FEATURE_FLAG=""
 * ```
 * 
 * You can override `.env` values from the command line like so:
 * 
 * ```sh
 * MY_FEATURE_FLAG="enabled" npm run dev
 * ```
 */
declare module '$env/static/private' {
	export const AUTH_TYPE: string;
	export const AUTH_SECRET: string;
	export const OLLAMA_BASE_URL: string;
	export const PLUNK_SECRET_KEY: string;
	export const DATABASE_URL: string;
	export const REDIS_URL: string;
	export const APP_AUTH_EMAIL: string;
	export const APP_EMAIL_ADDRESS: string;
	export const SESSION_EXPIRY_TIME: string;
	export const SESSION_EXPIRY_UNIT: string;
	export const API_SECRET: string;
	export const INVITE_PARAMS: string;
	export const SPENDEED_DOMAIN: string;
	export const SESSION_SECRET: string;
	export const OPEN_AI_API_KEY: string;
	export const BETTER_AUTH_TRUSTED_ORIGINS: string;
	export const AUTH_RP_ID: string;
	export const APP_NAME: string;
	export const SUPER_ADMIN_EMAILS: string;
	export const PLUNK_API_URL: string;
	export const NVM_INC: string;
	export const CPLUS_INCLUDE_PATH: string;
	export const STARSHIP_SHELL: string;
	export const TERM_PROGRAM: string;
	export const NODE: string;
	export const ANDROID_HOME: string;
	export const NVM_CD_FLAGS: string;
	export const INIT_CWD: string;
	export const TERM: string;
	export const SHELL: string;
	export const TMPDIR: string;
	export const LIBRARY_PATH: string;
	export const TERM_PROGRAM_VERSION: string;
	export const NODE_OPTIONS: string;
	export const ZDOTDIR: string;
	export const FIG_NEW_SESSION: string;
	export const ORIGINAL_XDG_CURRENT_DESKTOP: string;
	export const MallocNanoZone: string;
	export const npm_config_registry: string;
	export const PNPM_HOME: string;
	export const LC_ALL: string;
	export const NVM_DIR: string;
	export const USER: string;
	export const COMMAND_MODE: string;
	export const PNPM_SCRIPT_SRC_DIR: string;
	export const SSH_AUTH_SOCK: string;
	export const VSCODE_PROFILE_INITIALIZED: string;
	export const __CF_USER_TEXT_ENCODING: string;
	export const HASURA_GRAPHQL_ENABLE_CONSOLE: string;
	export const DENO_INSTALL: string;
	export const npm_execpath: string;
	export const npm_config_frozen_lockfile: string;
	export const npm_config_verify_deps_before_run: string;
	export const PATH: string;
	export const STARSHIP_CONFIG: string;
	export const npm_package_json: string;
	export const USER_ZDOTDIR: string;
	export const C_INCLUDE_PATH: string;
	export const __CFBundleIdentifier: string;
	export const PWD: string;
	export const npm_command: string;
	export const EDITOR: string;
	export const npm_config__jsr_registry: string;
	export const npm_lifecycle_event: string;
	export const LANG: string;
	export const npm_package_name: string;
	export const NODE_PATH: string;
	export const VSCODE_GIT_ASKPASS_EXTRA_ARGS: string;
	export const XPC_FLAGS: string;
	export const npm_config_node_gyp: string;
	export const XPC_SERVICE_NAME: string;
	export const npm_package_version: string;
	export const pnpm_config_verify_deps_before_run: string;
	export const VSCODE_INJECTION: string;
	export const SHLVL: string;
	export const HOME: string;
	export const VSCODE_GIT_ASKPASS_MAIN: string;
	export const STARSHIP_SESSION_KEY: string;
	export const LOGNAME: string;
	export const MODULAR_HOME: string;
	export const npm_lifecycle_script: string;
	export const VSCODE_GIT_IPC_HANDLE: string;
	export const BUN_INSTALL: string;
	export const NVM_BIN: string;
	export const npm_config_user_agent: string;
	export const VSCODE_GIT_ASKPASS_NODE: string;
	export const GIT_ASKPASS: string;
	export const FLYCTL_INSTALL: string;
	export const COLORTERM: string;
	export const npm_node_execpath: string;
	export const NODE_ENV: string;
}

/**
 * Similar to [`$env/static/private`](https://svelte.dev/docs/kit/$env-static-private), except that it only includes environment variables that begin with [`config.kit.env.publicPrefix`](https://svelte.dev/docs/kit/configuration#env) (which defaults to `PUBLIC_`), and can therefore safely be exposed to client-side code.
 * 
 * Values are replaced statically at build time.
 * 
 * ```ts
 * import { PUBLIC_BASE_URL } from '$env/static/public';
 * ```
 */
declare module '$env/static/public' {
	export const PUBLIC_APP_NAME: string;
	export const PUBLIC_FRONTEND_URL: string;
	export const PUBLIC_COOKIE_SECRET: string;
}

/**
 * This module provides access to runtime environment variables, as defined by the platform you're running on. For example if you're using [`adapter-node`](https://github.com/sveltejs/kit/tree/main/packages/adapter-node) (or running [`vite preview`](https://svelte.dev/docs/kit/cli)), this is equivalent to `process.env`. This module only includes variables that _do not_ begin with [`config.kit.env.publicPrefix`](https://svelte.dev/docs/kit/configuration#env) _and do_ start with [`config.kit.env.privatePrefix`](https://svelte.dev/docs/kit/configuration#env) (if configured).
 * 
 * This module cannot be imported into client-side code.
 * 
 * Dynamic environment variables cannot be used during prerendering.
 * 
 * ```ts
 * import { env } from '$env/dynamic/private';
 * console.log(env.DEPLOYMENT_SPECIFIC_VARIABLE);
 * ```
 * 
 * > In `dev`, `$env/dynamic` always includes environment variables from `.env`. In `prod`, this behavior will depend on your adapter.
 */
declare module '$env/dynamic/private' {
	export const env: {
		AUTH_TYPE: string;
		AUTH_SECRET: string;
		OLLAMA_BASE_URL: string;
		PLUNK_SECRET_KEY: string;
		DATABASE_URL: string;
		REDIS_URL: string;
		APP_AUTH_EMAIL: string;
		APP_EMAIL_ADDRESS: string;
		SESSION_EXPIRY_TIME: string;
		SESSION_EXPIRY_UNIT: string;
		API_SECRET: string;
		INVITE_PARAMS: string;
		SPENDEED_DOMAIN: string;
		SESSION_SECRET: string;
		OPEN_AI_API_KEY: string;
		BETTER_AUTH_TRUSTED_ORIGINS: string;
		AUTH_RP_ID: string;
		APP_NAME: string;
		SUPER_ADMIN_EMAILS: string;
		PLUNK_API_URL: string;
		NVM_INC: string;
		CPLUS_INCLUDE_PATH: string;
		STARSHIP_SHELL: string;
		TERM_PROGRAM: string;
		NODE: string;
		ANDROID_HOME: string;
		NVM_CD_FLAGS: string;
		INIT_CWD: string;
		TERM: string;
		SHELL: string;
		TMPDIR: string;
		LIBRARY_PATH: string;
		TERM_PROGRAM_VERSION: string;
		NODE_OPTIONS: string;
		ZDOTDIR: string;
		FIG_NEW_SESSION: string;
		ORIGINAL_XDG_CURRENT_DESKTOP: string;
		MallocNanoZone: string;
		npm_config_registry: string;
		PNPM_HOME: string;
		LC_ALL: string;
		NVM_DIR: string;
		USER: string;
		COMMAND_MODE: string;
		PNPM_SCRIPT_SRC_DIR: string;
		SSH_AUTH_SOCK: string;
		VSCODE_PROFILE_INITIALIZED: string;
		__CF_USER_TEXT_ENCODING: string;
		HASURA_GRAPHQL_ENABLE_CONSOLE: string;
		DENO_INSTALL: string;
		npm_execpath: string;
		npm_config_frozen_lockfile: string;
		npm_config_verify_deps_before_run: string;
		PATH: string;
		STARSHIP_CONFIG: string;
		npm_package_json: string;
		USER_ZDOTDIR: string;
		C_INCLUDE_PATH: string;
		__CFBundleIdentifier: string;
		PWD: string;
		npm_command: string;
		EDITOR: string;
		npm_config__jsr_registry: string;
		npm_lifecycle_event: string;
		LANG: string;
		npm_package_name: string;
		NODE_PATH: string;
		VSCODE_GIT_ASKPASS_EXTRA_ARGS: string;
		XPC_FLAGS: string;
		npm_config_node_gyp: string;
		XPC_SERVICE_NAME: string;
		npm_package_version: string;
		pnpm_config_verify_deps_before_run: string;
		VSCODE_INJECTION: string;
		SHLVL: string;
		HOME: string;
		VSCODE_GIT_ASKPASS_MAIN: string;
		STARSHIP_SESSION_KEY: string;
		LOGNAME: string;
		MODULAR_HOME: string;
		npm_lifecycle_script: string;
		VSCODE_GIT_IPC_HANDLE: string;
		BUN_INSTALL: string;
		NVM_BIN: string;
		npm_config_user_agent: string;
		VSCODE_GIT_ASKPASS_NODE: string;
		GIT_ASKPASS: string;
		FLYCTL_INSTALL: string;
		COLORTERM: string;
		npm_node_execpath: string;
		NODE_ENV: string;
		[key: `PUBLIC_${string}`]: undefined;
		[key: `${string}`]: string | undefined;
	}
}

/**
 * Similar to [`$env/dynamic/private`](https://svelte.dev/docs/kit/$env-dynamic-private), but only includes variables that begin with [`config.kit.env.publicPrefix`](https://svelte.dev/docs/kit/configuration#env) (which defaults to `PUBLIC_`), and can therefore safely be exposed to client-side code.
 * 
 * Note that public dynamic environment variables must all be sent from the server to the client, causing larger network requests — when possible, use `$env/static/public` instead.
 * 
 * Dynamic environment variables cannot be used during prerendering.
 * 
 * ```ts
 * import { env } from '$env/dynamic/public';
 * console.log(env.PUBLIC_DEPLOYMENT_SPECIFIC_VARIABLE);
 * ```
 */
declare module '$env/dynamic/public' {
	export const env: {
		PUBLIC_APP_NAME: string;
		PUBLIC_FRONTEND_URL: string;
		PUBLIC_COOKIE_SECRET: string;
		[key: `PUBLIC_${string}`]: string | undefined;
	}
}
