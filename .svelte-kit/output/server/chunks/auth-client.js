import { createAuthClient } from "better-auth/svelte";
import { passkeyClient, emailOTPClient, organizationClient, adminClient, inferAdditionalFields, inferOrgAdditionalFields } from "better-auth/client/plugins";
import "clsx";
import "@sveltejs/kit/internal";
import "./exports.js";
import "./state.svelte.js";
import { Fields, Entity, Validators, Allow, Relations, BackendMethod, remult } from "remult";
import { object, picklist, optional, string, boolean, unknown, any, date, pipe, minLength, email, regex, number, minValue, forward, check, maxLength } from "valibot";
import superjson from "superjson";
import { getYear, getHours } from "date-fns";
import "@oslojs/crypto/sha2";
import { Decimal } from "decimal.js";
import "linq-extensions";
import { createAccessControl } from "better-auth/plugins/access";
import { uuidv7 } from "uuidv7";
import { M as run, N as derived } from "./index2.js";
const checkWebAuthnSupport = async () => {
  const support = {
    isAvailable: false,
    isPlatformAuthenticatorAvailable: false,
    isConditionalMediationAvailable: false,
    isAutoFillAvailable: false
  };
  try {
    support.isAvailable = window && window.PublicKeyCredential !== void 0 && typeof window.PublicKeyCredential === "function";
    if (support.isAvailable) {
      if (window.PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable) {
        support.isPlatformAuthenticatorAvailable = await window.PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable();
      }
      if (window.PublicKeyCredential.isConditionalMediationAvailable) {
        support.isConditionalMediationAvailable = await window.PublicKeyCredential.isConditionalMediationAvailable();
      }
      support.isAutoFillAvailable = support.isConditionalMediationAvailable && support.isAvailable;
    }
    return support;
  } catch (error) {
    console.error("Error checking WebAuthn support:", error);
    return support;
  }
};
const dynamicModalSchema = object({
  title: string("Required"),
  description: string("Required"),
  componentName: string("Required"),
  componentProps: optional(unknown()),
  canClose: optional(boolean("Required"), true),
  color: picklist([
    "primary",
    "gray",
    "red",
    "yellow",
    "green",
    "indigo",
    "purple",
    "pink",
    "blue",
    "light",
    "dark",
    "default",
    "dropdown",
    "navbar",
    "navbarUl",
    "form",
    "none"
  ]),
  size: picklist(["xl", "lg", "md"])
});
picklist(["success", "error", "info", "warning"]);
object({
  ...dynamicModalSchema.entries,
  position: picklist(["left", "right", "top", "bottom"]),
  side: picklist(["right", "top", "bottom", "left"])
});
const SheetTypes = {
  Marketing: "Marketing",
  SignUpSuccess: "SignUpSuccess",
  Login: "Login",
  SignUp: "SignUp",
  SignUpCode: "SignUpCode",
  ForgotPass: "ForgotPass",
  ResetPass: "ResetPass",
  ResetPassCode: "ResetPassCode",
  LoginCode: "LoginCode"
};
const CalendarViewTypes = {
  Day: "Day",
  Week: "Week",
  Month: "Month"
};
const TeamMemberStatuses = {
  Active: "Active",
  InActive: "Inactive",
  OnLeave: "On Leave",
  Resigned: "Resigned"
};
const appointmentRepeatTypes = {
  Daily: "Daily",
  Weekly: "Weekly",
  EveryTwoWeeks: "EveryTwoWeeks",
  EveryFourWeeks: "EveryFourWeeks",
  Monthly: "Monthly"
};
const appointmentStatuses = {
  Scheduled: "Scheduled",
  Confirmed: "Confirmed",
  Cancelled: "Cancelled",
  Done: "Done",
  Missed: "Missed"
};
const objectValuesToArray = (constObject) => {
  return Object.values(constObject);
};
const RolesType = {
  User: "user",
  Admin: "admin",
  OrgAdmin: "organisation-admin",
  TeamMember: "member",
  TeamLocationAdmin: "team-location-admin"
};
const rolesTypes = objectValuesToArray(RolesType);
const GenderTypes = {
  Male: "Male",
  Female: "Female",
  Other: "Other",
  Unknown: "Unknown"
};
const genderTypes = objectValuesToArray(GenderTypes);
const PermissionTypes = {
  ManageTeam: "Manage Team",
  ReadTeam: "Read Team",
  ReadMembers: "Read Members",
  ManageMembers: "Manage Members",
  Contribute: "Contribute",
  ReadAppointments: "Read Appointments",
  ManageAppointments: "Manage Appointments",
  ManageAppointmentTypes: "Manage Appointment Types",
  ReadAppointmentTypes: "Read Appointment Types"
};
objectValuesToArray(PermissionTypes);
object({
  teamId: optional(string()),
  defaultRole: optional(string(), RolesType.TeamMember),
  teamLocationId: optional(string()),
  allowedRoles: optional(any(), [RolesType.TeamMember])
});
object({
  givenName: string(),
  familyName: string(),
  email: string(),
  teamId: string(),
  teamLocationId: string(),
  businessName: optional(string()),
  phoneNumber: string(),
  userStatus: optional(string(), TeamMemberStatuses.Active),
  idDocumentNumber: optional(string()),
  gender: optional(string(), GenderTypes.Male),
  dateOfBirth: optional(string()),
  avatarUrl: string()
});
object({
  businessName: string(),
  businessEmail: string(),
  organizationId: string(),
  businessAddress: string()
});
const TeamPlans = {
  Free: "Free",
  Pro: "Pro",
  Enterprise: "Enterprise"
};
objectValuesToArray(TeamPlans);
const TeamLocationStatuses = {
  Active: "Active",
  InActive: "InActive"
};
objectValuesToArray(TeamLocationStatuses);
const TeamRoles = {
  TeamAdmin: "team-admin",
  TeamLocationAdmin: "team-location-admin",
  TeamMember: "team-member"
};
const WorkDays = {
  Sunday: "Sunday",
  Monday: "Monday",
  Tuesday: "Tuesday",
  Wednesday: "Wednesday",
  Thursday: "Thursday",
  Friday: "Friday",
  Saturday: "Saturday"
};
objectValuesToArray(WorkDays);
const UserAccountTypes = {
  Staff: "Staff",
  Customer: "Customer"
};
objectValuesToArray(UserAccountTypes);
const WeekDayNumber = {
  Sunday: 0,
  Monday: 1,
  Tuesday: 2,
  Wednesday: 3,
  Thursday: 4,
  Friday: 5,
  Saturday: 6
};
objectValuesToArray(WeekDayNumber);
const DaysOfWeek = {
  Sunday: "Sunday",
  Monday: "Monday",
  Tuesday: "Tuesday",
  Wednesday: "Wednesday",
  Thursday: "Thursday",
  Friday: "Friday",
  Saturday: "Saturday"
};
objectValuesToArray(DaysOfWeek);
const BankAccountTypes = {
  Check: "Check",
  Savings: "Savings",
  Investment: "Investment"
};
objectValuesToArray(BankAccountTypes);
const stringVal$1 = (name) => {
  return `${name} must be a string`;
};
const newAppointmentFormSchema = object({
  appointmentTypeId: pipe(string(stringVal$1("Title")), minLength(1, "Title is required")),
  appointmentNotes: optional(string(stringVal$1("Description"))),
  startTime: pipe(
    string(stringVal$1("Start Time")),
    regex(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}$/, "Invalid date format")
  ),
  endTime: pipe(
    string(stringVal$1("End Time")),
    regex(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}$/, "Invalid date format")
  ),
  customerEmail: pipe(string(), email("Invalid email address")),
  clientId: pipe(string(), minLength(1, "Client name is required")),
  serviceId: optional(string()),
  customerName: pipe(string(stringVal$1("Full Name")), minLength(1, "Client name is required")),
  customerPhoneNumber: optional(string(stringVal$1("Phone Number"))),
  locationId: string(stringVal$1("Location")),
  appointmentDate: date()
});
object({
  id: string(),
  status: picklist(["pending", "confirmed", "cancelled"]),
  title: pipe(string(), minLength(1)),
  description: optional(string()),
  startTime: date(),
  endTime: date()
});
object({
  appointmentId: string(),
  appointmentDate: any(),
  appointmentStartTime: any(),
  appointmentStatus: picklist([
    appointmentStatuses.Scheduled,
    appointmentStatuses.Cancelled,
    appointmentStatuses.Confirmed,
    appointmentStatuses.Done,
    appointmentStatuses.Missed
  ])
});
const createAppointmentTypeFormSchema = object({
  id: optional(string()),
  name: pipe(string(), minLength(1, "Name is required")),
  durationInMinutes: pipe(number(), minValue(15, "Duration must be at least 1 minute")),
  isActive: boolean(),
  isRepeatEnabled: boolean(),
  requiresUpfrontPayment: boolean(),
  timeZoneId: string(),
  upfrontPaymentAmount: optional(number(), 0),
  repeatInterval: picklist([
    appointmentRepeatTypes.Daily,
    appointmentRepeatTypes.Weekly,
    appointmentRepeatTypes.EveryTwoWeeks,
    appointmentRepeatTypes.EveryFourWeeks,
    appointmentRepeatTypes.Monthly
  ])
  // teamId: string(),
  // createdById: string() //pulled in DB
});
const deleteAppointmentTypeFormSchema = object({
  id: string()
});
const lettersOnly = /^[A-Za-z]+$/;
const passwordConstraint = "^(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*])(?=.{6,})";
const lettersOnlyRegex = new RegExp(lettersOnly);
const passwordRegEx = new RegExp(passwordConstraint);
const signUpCodeFormSchema = object({
  code: string(stringVal$1("Code")),
  token: optional(string()),
  principal: optional(string()),
  email: optional(string(stringVal$1("Email"))),
  returnUrl: optional(string(stringVal$1("Return Url")))
});
const signUpFormSchema = object({
  acceptTerms: boolean(),
  authType: picklist(["passkey", "email"]),
  givenName: pipe(
    string(stringVal$1("First Name")),
    minLength(2, "First Name must have a length of at least 2 characters"),
    regex(lettersOnlyRegex, "Only alphabet characters allowed")
  ),
  familyName: pipe(
    string(stringVal$1("Last Name")),
    minLength(2, "Last Name must have a length of at least 2 characters"),
    regex(lettersOnlyRegex, "Only alphabet characters allowed")
  ),
  phoneNumber: optional(
    pipe(
      string(stringVal$1("Phone Number")),
      regex(/^[0-9]{10}$/, "Phone number must be 10 digits")
    )
  ),
  email: pipe(string(stringVal$1("Email")), email("Entry is not a valid email")),
  accountType: optional(string(), UserAccountTypes.Staff),
  businessName: pipe(
    string(stringVal$1("Business Name")),
    minLength(2, "Business Name must have a length of at least 2 characters")
  ),
  businessEmail: pipe(string(stringVal$1("Business Email")), email("Entry is not a valid email")),
  businessAddress: pipe(
    string(stringVal$1("Business Address")),
    minLength(2, "Business Address must have a length of at least 2 characters")
  ),
  password: optional(string())
});
pipe(
  object({
    password: pipe(
      string(stringVal$1("Password")),
      minLength(6, "Password must be at least 6 characters long"),
      regex(
        passwordRegEx,
        "Password must contain at least one number, one uppercase letter, and one symbol"
      )
    ),
    confirmPassword: string(stringVal$1("Confirm Password")),
    userId: string(stringVal$1("AppUser Id"))
  }),
  forward(
    check((data) => data.password === data.confirmPassword, `Passwords don't match`),
    ["confirmPassword"]
  )
);
object({
  email: pipe(string(stringVal$1("Email")), email("Entry is not a valid email"))
});
const loginFormSchema = object({
  email: pipe(string(stringVal$1("Email")), email("Entry is not a valid email")),
  token: optional(any()),
  authType: picklist(["passkey", "email", "apple", "google"]),
  returnUrl: optional(string(stringVal$1("Return Url")), "/"),
  isEmailVerified: optional(boolean(), false)
});
const logOutFormSchema = object({
  userId: optional(string())
});
pipe(
  object({
    password: pipe(
      string(stringVal$1("Password")),
      minLength(6, "Password must be at least 6 characters long"),
      regex(
        passwordRegEx,
        "Password must contain at least one number, one uppercase letter, and one symbol"
      )
    ),
    confirmPassword: string(stringVal$1("Confirm Password"))
  }),
  forward(
    check((data) => data.password === data.confirmPassword, `Passwords don't match`),
    ["confirmPassword"]
  )
);
const stringVal = (name) => {
  return { message: `${name} must be a string` };
};
const settingsFormSchema = object({
  id: optional(string()),
  name: string(),
  businessEmail: string(),
  phoneNumber: optional(
    pipe(
      string(stringVal("Phone Number")),
      regex(/^[0-9]{10}$/, "Phone number must be 10 digits")
    )
  ),
  businessRegNo: string(),
  registrationDate: string()
});
object({
  staffName: string(),
  staffEmail: string(),
  assignedLocation: string(),
  assignedRole: string(),
  gender: string(),
  phoneNumber: optional(
    pipe(
      string(stringVal("Phone Number")),
      regex(/^[0-9]{10}$/, "Phone number must be 10 digits")
    )
  ),
  idDocumentNumber: string(),
  dateOfBirth: string(),
  address: string(),
  city: string(),
  postalCode: string(),
  country: string()
});
object({
  id: optional(string()),
  locationAddress: string(),
  locationEmail: pipe(string(stringVal("Email")), email("Entry is not a valid email")),
  locationPhoneNumber: optional(
    pipe(
      string(stringVal("Phone Number")),
      regex(/^[0-9]{10}$/, "Phone number must be 10 digits")
    )
  ),
  locationName: string(),
  isActive: boolean(),
  isDefault: optional(boolean(), false),
  locationAdmin: optional(string())
});
const addMemberFormSchema = object({
  id: optional(string()),
  givenName: string(),
  familyName: string(),
  gender: any(),
  email: string(),
  role: string(),
  location: string(),
  idDocumentNumber: string(),
  phoneNumber: pipe(
    string(stringVal$1("Phone Number")),
    regex(/^[0-9]{10}$/, "Phone number must be 10 digits")
  )
});
const bankAccountFormSchema = object({
  accountName: pipe(
    string(stringVal$1("Account Name")),
    minLength(2, "Account name must be at least 2 characters long"),
    maxLength(100, "Account name must be less than 100 characters")
  ),
  accountNumber: pipe(
    string(stringVal$1("Account Number")),
    minLength(6, "Account number must be at least 6 characters long"),
    maxLength(20, "Account number must be less than 20 characters")
  ),
  bankName: pipe(
    string(stringVal$1("Bank Name")),
    minLength(2, "Bank name must be at least 2 characters long"),
    maxLength(100, "Bank name must be less than 100 characters")
  ),
  branchCode: pipe(
    string(stringVal$1("Branch Code")),
    minLength(4, "Branch code must be at least 4 characters long"),
    maxLength(10, "Branch code must be less than 10 characters")
  ),
  swiftCode: pipe(
    string(stringVal$1("Swift Code")),
    minLength(8, "SWIFT code must be at least 8 characters long"),
    maxLength(11, "SWIFT code must be less than 11 characters")
  )
});
const paymentProviderFormSchema = object({
  id: optional(string(stringVal$1("ID"))),
  providerName: pipe(
    string(stringVal$1("Provider Name")),
    minLength(2, "Provider name must be at least 2 characters long"),
    maxLength(100, "Provider name must be less than 100 characters")
  ),
  providerApiUrl: pipe(
    string(stringVal$1("API URL")),
    minLength(5, "API URL must be at least 5 characters long"),
    maxLength(255, "API URL must be less than 255 characters")
  ),
  providerApiKey: optional(
    pipe(
      string(stringVal$1("Client ID")),
      minLength(8, "Client ID must be at least 8 characters long"),
      maxLength(255, "Client ID must be less than 255 characters")
    )
  ),
  providerApiSecret: optional(
    pipe(
      string(stringVal$1("Client Secret")),
      minLength(16, "Client secret must be at least 16 characters long"),
      maxLength(255, "Client secret must be less than 255 characters")
    )
  ),
  providerClientId: optional(
    pipe(
      string(stringVal$1("Client Secret")),
      minLength(16, "Client secret must be at least 16 characters long"),
      maxLength(255, "Client secret must be less than 255 characters")
    )
  ),
  isActive: optional(boolean(), false),
  providerWebhookUrl: optional(
    pipe(
      string(stringVal$1("Webhook URL")),
      minLength(5, "Webhook URL must be at least 5 characters long"),
      maxLength(255, "Webhook URL must be less than 255 characters")
    )
  )
});
const userProfileSchema = object({
  givenName: string("First name is required"),
  familyName: string("Last name is required"),
  name: string(),
  accountType: string("Account type is required"),
  profilePhotoUrl: optional(string())
});
const teamSchema = object({
  id: string(),
  locationName: string("Location name is required"),
  locationAddress: optional(string()),
  locationEmail: pipe(string(), email("Invalid email format")),
  locationPhoneNumber: optional(string()),
  isActive: boolean(),
  isDefault: optional(boolean())
});
object({
  inviteeEmail: pipe(string(), email("Invalid email format")),
  role: picklist(Object.values(TeamRoles), "Invalid role selection"),
  teamLocation: teamSchema,
  invitedBy: any(),
  invitedAt: date(),
  status: picklist(Object.values(TeamMemberStatuses), "Invalid status"),
  expiresAt: date()
});
const orgSchema = object({
  teamName: string("Team name is required"),
  businessEmail: pipe(string(), email("Invalid email format")),
  businessRegNo: optional(string()),
  phoneNumber: optional(string()),
  createdAt: date()
});
const userRootSchema = object({
  ...userProfileSchema.entries,
  email: pipe(string(), email("Invalid email format")),
  phoneNumber: string("Phone number is required"),
  nationalId: string("National ID is required"),
  dateOfBirth: date("Date of birth is required"),
  gender: picklist(Object.values(GenderTypes), "Invalid gender selection"),
  address: optional(string()),
  city: optional(string()),
  country: optional(string()),
  organization: object({
    ...orgSchema.entries
  })
});
object({
  userAccount: userRootSchema,
  role: picklist(Object.values(TeamRoles), "Invalid role selection"),
  location: teamSchema,
  joinedAt: date(),
  lastActive: optional(date()),
  memberStatus: picklist(Object.values(TeamMemberStatuses), "Invalid status")
});
var __defProp$E = Object.defineProperty;
var __getOwnPropDesc$E = Object.getOwnPropertyDescriptor;
var __decorateClass$E = (decorators, target, key, kind) => {
  var result = kind > 1 ? void 0 : kind ? __getOwnPropDesc$E(target, key) : target;
  for (var i = decorators.length - 1, decorator; i >= 0; i--)
    if (decorator = decorators[i])
      result = (kind ? decorator(target, key, result) : decorator(result)) || result;
  if (kind && result) __defProp$E(target, key, result);
  return result;
};
let Gender = class {
  name = GenderTypes.Unknown;
};
__decorateClass$E([
  Fields.literal(() => genderTypes)
], Gender.prototype, "name", 2);
Gender = __decorateClass$E([
  Entity("genders", {
    allowApiCrud: [RolesType.Admin],
    allowApiRead: true
  })
], Gender);
var __defProp$D = Object.defineProperty;
var __getOwnPropDesc$D = Object.getOwnPropertyDescriptor;
var __decorateClass$D = (decorators, target, key, kind) => {
  var result = kind > 1 ? void 0 : kind ? __getOwnPropDesc$D(target, key) : target;
  for (var i = decorators.length - 1, decorator; i >= 0; i--)
    if (decorator = decorators[i])
      result = (kind ? decorator(target, key, result) : decorator(result)) || result;
  if (kind && result) __defProp$D(target, key, result);
  return result;
};
let Role = class {
  role = RolesType.User;
  createdAt;
  updatedAt;
  deletedAt;
};
__decorateClass$D([
  Fields.literal(() => rolesTypes)
], Role.prototype, "role", 2);
__decorateClass$D([
  Fields.createdAt({
    dbName: "created_at",
    required: true,
    defaultValue: () => /* @__PURE__ */ new Date(),
    allowApiUpdate: false
  })
], Role.prototype, "createdAt", 2);
__decorateClass$D([
  Fields.updatedAt({
    dbName: "updated_at",
    required: true,
    defaultValue: () => /* @__PURE__ */ new Date(),
    allowApiUpdate: false
  })
], Role.prototype, "updatedAt", 2);
__decorateClass$D([
  Fields.date({ dbName: "deleted_at" })
], Role.prototype, "deletedAt", 2);
Role = __decorateClass$D([
  Entity("roles", {
    allowApiCrud: RolesType.Admin,
    allowApiRead: true
  })
], Role);
var __defProp$C = Object.defineProperty;
var __getOwnPropDesc$C = Object.getOwnPropertyDescriptor;
var __decorateClass$C = (decorators, target, key, kind) => {
  var result = kind > 1 ? void 0 : kind ? __getOwnPropDesc$C(target, key) : target;
  for (var i = decorators.length - 1, decorator; i >= 0; i--)
    if (decorator = decorators[i])
      result = (kind ? decorator(target, key, result) : decorator(result)) || result;
  if (kind && result) __defProp$C(target, key, result);
  return result;
};
let Account = class {
  id = "";
  accountId = "";
  providerId = "";
  userId = "";
  accessToken;
  refreshToken;
  idToken;
  accessTokenExpiresAt;
  refreshTokenExpiresAt;
  scope;
  password;
  createdAt;
  updatedAt;
  deletedAt;
};
__decorateClass$C([
  Fields.uuid()
], Account.prototype, "id", 2);
__decorateClass$C([
  Fields.string({ dbName: "account_id" })
], Account.prototype, "accountId", 2);
__decorateClass$C([
  Fields.string({ dbName: "provider_id" })
], Account.prototype, "providerId", 2);
__decorateClass$C([
  Fields.string({ dbName: "user_id" })
], Account.prototype, "userId", 2);
__decorateClass$C([
  Fields.string({ includeInApi: false, dbName: "access_token" })
], Account.prototype, "accessToken", 2);
__decorateClass$C([
  Fields.string({ includeInApi: false, dbName: "refresh_token" })
], Account.prototype, "refreshToken", 2);
__decorateClass$C([
  Fields.string({ includeInApi: false, dbName: "id_token" })
], Account.prototype, "idToken", 2);
__decorateClass$C([
  Fields.date({ dbName: "access_token_expires_at" })
], Account.prototype, "accessTokenExpiresAt", 2);
__decorateClass$C([
  Fields.date({ dbName: "refresh_token_expires_at" })
], Account.prototype, "refreshTokenExpiresAt", 2);
__decorateClass$C([
  Fields.string()
], Account.prototype, "scope", 2);
__decorateClass$C([
  Fields.string({ includeInApi: false })
], Account.prototype, "password", 2);
__decorateClass$C([
  Fields.createdAt({
    dbName: "created_at",
    required: true,
    defaultValue: () => /* @__PURE__ */ new Date(),
    allowApiUpdate: false
  })
], Account.prototype, "createdAt", 2);
__decorateClass$C([
  Fields.updatedAt({
    dbName: "updated_at",
    required: true,
    defaultValue: () => /* @__PURE__ */ new Date(),
    allowApiUpdate: false
  })
], Account.prototype, "updatedAt", 2);
__decorateClass$C([
  Fields.date({ dbName: "deleted_at" })
], Account.prototype, "deletedAt", 2);
Account = __decorateClass$C([
  Entity("accounts", {
    dbName: 'auth."accounts"',
    allowApiCrud: false
    // Sensitive auth data
  })
], Account);
var __defProp$B = Object.defineProperty;
var __getOwnPropDesc$B = Object.getOwnPropertyDescriptor;
var __decorateClass$B = (decorators, target, key, kind) => {
  var result = kind > 1 ? void 0 : kind ? __getOwnPropDesc$B(target, key) : target;
  for (var i = decorators.length - 1, decorator; i >= 0; i--)
    if (decorator = decorators[i])
      result = (kind ? decorator(target, key, result) : decorator(result)) || result;
  if (kind && result) __defProp$B(target, key, result);
  return result;
};
let Session = class {
  id = "";
  expiresAt;
  ipAddress;
  userAgent;
  token = "";
  userId = "";
  activeOrganizationId;
  activeTeamId;
  impersonatedBy;
  createdAt;
  updatedAt;
  deletedAt;
};
__decorateClass$B([
  Fields.uuid()
], Session.prototype, "id", 2);
__decorateClass$B([
  Fields.date({ dbName: "expires_at" })
], Session.prototype, "expiresAt", 2);
__decorateClass$B([
  Fields.string({ dbName: "ip_address" })
], Session.prototype, "ipAddress", 2);
__decorateClass$B([
  Fields.string({ dbName: "user_agent" })
], Session.prototype, "userAgent", 2);
__decorateClass$B([
  Fields.string({ required: true, validate: Validators.unique() })
], Session.prototype, "token", 2);
__decorateClass$B([
  Fields.string({ dbName: "user_id" })
], Session.prototype, "userId", 2);
__decorateClass$B([
  Fields.string({ dbName: "active_organization_id" })
], Session.prototype, "activeOrganizationId", 2);
__decorateClass$B([
  Fields.string({ dbName: "active_team_id" })
], Session.prototype, "activeTeamId", 2);
__decorateClass$B([
  Fields.string({ dbName: "impersonated_by" })
], Session.prototype, "impersonatedBy", 2);
__decorateClass$B([
  Fields.createdAt({
    dbName: "created_at",
    required: true,
    defaultValue: () => /* @__PURE__ */ new Date(),
    allowApiUpdate: false
  })
], Session.prototype, "createdAt", 2);
__decorateClass$B([
  Fields.updatedAt({
    dbName: "updated_at",
    required: true,
    defaultValue: () => /* @__PURE__ */ new Date(),
    allowApiUpdate: false
  })
], Session.prototype, "updatedAt", 2);
__decorateClass$B([
  Fields.date({ dbName: "deleted_at" })
], Session.prototype, "deletedAt", 2);
Session = __decorateClass$B([
  Entity("sessions", {
    dbName: 'auth."sessions"',
    allowApiCrud: false
    // Sensitive auth data
  })
], Session);
var __defProp$A = Object.defineProperty;
var __getOwnPropDesc$A = Object.getOwnPropertyDescriptor;
var __decorateClass$A = (decorators, target, key, kind) => {
  var result = kind > 1 ? void 0 : kind ? __getOwnPropDesc$A(target, key) : target;
  for (var i = decorators.length - 1, decorator; i >= 0; i--)
    if (decorator = decorators[i])
      result = (kind ? decorator(target, key, result) : decorator(result)) || result;
  if (kind && result) __defProp$A(target, key, result);
  return result;
};
let Verification = class {
  id = "";
  identifier = "";
  value = "";
  expiresAt;
  createdAt;
  updatedAt;
  deletedAt;
};
__decorateClass$A([
  Fields.uuid()
], Verification.prototype, "id", 2);
__decorateClass$A([
  Fields.string()
], Verification.prototype, "identifier", 2);
__decorateClass$A([
  Fields.string()
], Verification.prototype, "value", 2);
__decorateClass$A([
  Fields.date({ dbName: "expires_at" })
], Verification.prototype, "expiresAt", 2);
__decorateClass$A([
  Fields.createdAt({
    dbName: "created_at",
    required: true,
    defaultValue: () => /* @__PURE__ */ new Date(),
    allowApiUpdate: false
  })
], Verification.prototype, "createdAt", 2);
__decorateClass$A([
  Fields.updatedAt({
    dbName: "updated_at",
    required: true,
    defaultValue: () => /* @__PURE__ */ new Date(),
    allowApiUpdate: false
  })
], Verification.prototype, "updatedAt", 2);
__decorateClass$A([
  Fields.date({ dbName: "deleted_at" })
], Verification.prototype, "deletedAt", 2);
Verification = __decorateClass$A([
  Entity("verifications", {
    dbName: 'auth."verifications"',
    allowApiCrud: false
    // Sensitive auth data
  })
], Verification);
var __defProp$z = Object.defineProperty;
var __getOwnPropDesc$z = Object.getOwnPropertyDescriptor;
var __decorateClass$z = (decorators, target, key, kind) => {
  var result = kind > 1 ? void 0 : kind ? __getOwnPropDesc$z(target, key) : target;
  for (var i = decorators.length - 1, decorator; i >= 0; i--)
    if (decorator = decorators[i])
      result = (kind ? decorator(target, key, result) : decorator(result)) || result;
  if (kind && result) __defProp$z(target, key, result);
  return result;
};
let Passkey = class {
  id = "";
  name;
  publicKey = "";
  userId = "";
  credentialID = "";
  counter = 0;
  deviceType = "";
  backedUp = false;
  transports;
  aaguid;
  createdAt;
  updatedAt;
  deletedAt;
};
__decorateClass$z([
  Fields.uuid()
], Passkey.prototype, "id", 2);
__decorateClass$z([
  Fields.string()
], Passkey.prototype, "name", 2);
__decorateClass$z([
  Fields.string({ dbName: "public_key" })
], Passkey.prototype, "publicKey", 2);
__decorateClass$z([
  Fields.string({ dbName: "user_id" })
], Passkey.prototype, "userId", 2);
__decorateClass$z([
  Fields.string({ dbName: "credential_id" })
], Passkey.prototype, "credentialID", 2);
__decorateClass$z([
  Fields.integer()
], Passkey.prototype, "counter", 2);
__decorateClass$z([
  Fields.string({ dbName: "device_type" })
], Passkey.prototype, "deviceType", 2);
__decorateClass$z([
  Fields.boolean({ dbName: "backed_up" })
], Passkey.prototype, "backedUp", 2);
__decorateClass$z([
  Fields.string()
], Passkey.prototype, "transports", 2);
__decorateClass$z([
  Fields.string()
], Passkey.prototype, "aaguid", 2);
__decorateClass$z([
  Fields.createdAt({
    dbName: "created_at",
    required: true,
    defaultValue: () => /* @__PURE__ */ new Date(),
    allowApiUpdate: false
  })
], Passkey.prototype, "createdAt", 2);
__decorateClass$z([
  Fields.updatedAt({
    dbName: "updated_at",
    required: true,
    defaultValue: () => /* @__PURE__ */ new Date(),
    allowApiUpdate: false
  })
], Passkey.prototype, "updatedAt", 2);
__decorateClass$z([
  Fields.date({ dbName: "deleted_at" })
], Passkey.prototype, "deletedAt", 2);
Passkey = __decorateClass$z([
  Entity("passkeys", {
    dbName: 'auth."passkeys"',
    allowApiCrud: Allow.authenticated
  })
], Passkey);
var __defProp$y = Object.defineProperty;
var __getOwnPropDesc$y = Object.getOwnPropertyDescriptor;
var __decorateClass$y = (decorators, target, key, kind) => {
  var result = kind > 1 ? void 0 : kind ? __getOwnPropDesc$y(target, key) : target;
  for (var i = decorators.length - 1, decorator; i >= 0; i--)
    if (decorator = decorators[i])
      result = (kind ? decorator(target, key, result) : decorator(result)) || result;
  if (kind && result) __defProp$y(target, key, result);
  return result;
};
let Jwks = class {
  id = "";
  publicKey = "";
  privateKey = "";
  createdAt;
  updatedAt;
  deletedAt;
};
__decorateClass$y([
  Fields.uuid()
], Jwks.prototype, "id", 2);
__decorateClass$y([
  Fields.string({ includeInApi: false, dbName: "public_key" })
], Jwks.prototype, "publicKey", 2);
__decorateClass$y([
  Fields.string({ includeInApi: false, dbName: "private_key" })
], Jwks.prototype, "privateKey", 2);
__decorateClass$y([
  Fields.createdAt({
    dbName: "created_at",
    required: true,
    defaultValue: () => /* @__PURE__ */ new Date(),
    allowApiUpdate: false
  })
], Jwks.prototype, "createdAt", 2);
__decorateClass$y([
  Fields.updatedAt({
    dbName: "updated_at",
    required: true,
    defaultValue: () => /* @__PURE__ */ new Date(),
    allowApiUpdate: false
  })
], Jwks.prototype, "updatedAt", 2);
__decorateClass$y([
  Fields.date({ dbName: "deleted_at" })
], Jwks.prototype, "deletedAt", 2);
Jwks = __decorateClass$y([
  Entity("jwks", {
    dbName: 'auth."jwks"',
    allowApiCrud: false
    // Sensitive auth data
  })
], Jwks);
var __defProp$x = Object.defineProperty;
var __getOwnPropDesc$x = Object.getOwnPropertyDescriptor;
var __decorateClass$x = (decorators, target, key, kind) => {
  var result = kind > 1 ? void 0 : kind ? __getOwnPropDesc$x(target, key) : target;
  for (var i = decorators.length - 1, decorator; i >= 0; i--)
    if (decorator = decorators[i])
      result = (kind ? decorator(target, key, result) : decorator(result)) || result;
  if (kind && result) __defProp$x(target, key, result);
  return result;
};
let AppointmentType$1 = class AppointmentType2 {
  id = "";
  name = "";
  description = "";
  duration = 0;
  price = 0;
  organizationId = "";
  teamId = "";
  createdBy = "";
  isActive = true;
  createdAt;
  updatedAt;
  deletedAt;
  organization;
  team;
  appointments;
};
__decorateClass$x([
  Fields.uuid()
], AppointmentType$1.prototype, "id", 2);
__decorateClass$x([
  Fields.string()
], AppointmentType$1.prototype, "name", 2);
__decorateClass$x([
  Fields.string()
], AppointmentType$1.prototype, "description", 2);
__decorateClass$x([
  Fields.number()
], AppointmentType$1.prototype, "duration", 2);
__decorateClass$x([
  Fields.number()
], AppointmentType$1.prototype, "price", 2);
__decorateClass$x([
  Fields.string({ dbName: "organization_id" })
], AppointmentType$1.prototype, "organizationId", 2);
__decorateClass$x([
  Fields.string({ dbName: "team_id" })
], AppointmentType$1.prototype, "teamId", 2);
__decorateClass$x([
  Fields.string({ dbName: "created_by" })
], AppointmentType$1.prototype, "createdBy", 2);
__decorateClass$x([
  Fields.boolean({ dbName: "is_active" })
], AppointmentType$1.prototype, "isActive", 2);
__decorateClass$x([
  Fields.createdAt({
    dbName: "created_at",
    required: true,
    defaultValue: () => /* @__PURE__ */ new Date(),
    allowApiUpdate: false
  })
], AppointmentType$1.prototype, "createdAt", 2);
__decorateClass$x([
  Fields.updatedAt({
    dbName: "updated_at",
    required: true,
    defaultValue: () => /* @__PURE__ */ new Date(),
    allowApiUpdate: false
  })
], AppointmentType$1.prototype, "updatedAt", 2);
__decorateClass$x([
  Fields.date({ dbName: "deleted_at" })
], AppointmentType$1.prototype, "deletedAt", 2);
__decorateClass$x([
  Relations.toOne(() => Organization, "organizationId")
], AppointmentType$1.prototype, "organization", 2);
__decorateClass$x([
  Relations.toOne(() => Team, "teamId")
], AppointmentType$1.prototype, "team", 2);
__decorateClass$x([
  Relations.toMany(() => Appointment, "appointmentTypeId")
], AppointmentType$1.prototype, "appointments", 2);
AppointmentType$1 = __decorateClass$x([
  Entity("appointment_types", {
    allowApiCrud: Allow.authenticated,
    allowApiInsert: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin],
    allowApiUpdate: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin],
    allowApiDelete: [RolesType.Admin, RolesType.OrgAdmin]
  })
], AppointmentType$1);
var __defProp$w = Object.defineProperty;
var __getOwnPropDesc$w = Object.getOwnPropertyDescriptor;
var __decorateClass$w = (decorators, target, key, kind) => {
  var result = kind > 1 ? void 0 : kind ? __getOwnPropDesc$w(target, key) : target;
  for (var i = decorators.length - 1, decorator; i >= 0; i--)
    if (decorator = decorators[i])
      result = (kind ? decorator(target, key, result) : decorator(result)) || result;
  if (kind && result) __defProp$w(target, key, result);
  return result;
};
let TimeZone = class {
  id = "";
  timeZoneName = "";
  offsetHours = 0;
  appointmentTypes;
  createdAt;
  updatedAt;
  deletedAt;
};
__decorateClass$w([
  Fields.uuid()
], TimeZone.prototype, "id", 2);
__decorateClass$w([
  Fields.string({ dbName: "time_zone_name" })
], TimeZone.prototype, "timeZoneName", 2);
__decorateClass$w([
  Fields.number({ dbName: "offset_hours" })
], TimeZone.prototype, "offsetHours", 2);
__decorateClass$w([
  Relations.toMany(() => AppointmentType$1)
], TimeZone.prototype, "appointmentTypes", 2);
__decorateClass$w([
  Fields.createdAt({
    dbName: "created_at",
    required: true,
    defaultValue: () => /* @__PURE__ */ new Date(),
    allowApiUpdate: false
  })
], TimeZone.prototype, "createdAt", 2);
__decorateClass$w([
  Fields.updatedAt({
    dbName: "updated_at",
    required: true,
    defaultValue: () => /* @__PURE__ */ new Date(),
    allowApiUpdate: false
  })
], TimeZone.prototype, "updatedAt", 2);
__decorateClass$w([
  Fields.date({ dbName: "deleted_at" })
], TimeZone.prototype, "deletedAt", 2);
TimeZone = __decorateClass$w([
  Entity("time_zones", {
    allowApiCrud: RolesType.Admin,
    allowApiRead: true
  })
], TimeZone);
var __defProp$v = Object.defineProperty;
var __getOwnPropDesc$v = Object.getOwnPropertyDescriptor;
var __decorateClass$v = (decorators, target, key, kind) => {
  var result = kind > 1 ? void 0 : kind ? __getOwnPropDesc$v(target, key) : target;
  for (var i = decorators.length - 1, decorator; i >= 0; i--)
    if (decorator = decorators[i])
      result = (kind ? decorator(target, key, result) : decorator(result)) || result;
  if (kind && result) __defProp$v(target, key, result);
  return result;
};
let StorageBucket = class {
  id = "";
  downloadExpiration = 30;
  minUploadFileSize = 1;
  maxUploadFileSize = 5e7;
  cacheControl = "max-age=3600";
  presignedUrlsEnabled = true;
  createdAt;
  updatedAt;
  deletedAt;
  files;
};
__decorateClass$v([
  Fields.uuid()
], StorageBucket.prototype, "id", 2);
__decorateClass$v([
  Fields.integer({ dbName: "download_expiration" })
], StorageBucket.prototype, "downloadExpiration", 2);
__decorateClass$v([
  Fields.integer({ dbName: "min_upload_file_size" })
], StorageBucket.prototype, "minUploadFileSize", 2);
__decorateClass$v([
  Fields.integer({ dbName: "max_upload_file_size" })
], StorageBucket.prototype, "maxUploadFileSize", 2);
__decorateClass$v([
  Fields.string({ dbName: "cache_control" })
], StorageBucket.prototype, "cacheControl", 2);
__decorateClass$v([
  Fields.boolean({ dbName: "presigned_urls_enabled" })
], StorageBucket.prototype, "presignedUrlsEnabled", 2);
__decorateClass$v([
  Fields.createdAt({
    dbName: "created_at",
    required: true,
    defaultValue: () => /* @__PURE__ */ new Date(),
    allowApiUpdate: false
  })
], StorageBucket.prototype, "createdAt", 2);
__decorateClass$v([
  Fields.updatedAt({
    dbName: "updated_at",
    required: true,
    defaultValue: () => /* @__PURE__ */ new Date(),
    allowApiUpdate: false
  })
], StorageBucket.prototype, "updatedAt", 2);
__decorateClass$v([
  Fields.date({ dbName: "deleted_at" })
], StorageBucket.prototype, "deletedAt", 2);
__decorateClass$v([
  Relations.toMany(() => StorageFile)
], StorageBucket.prototype, "files", 2);
StorageBucket = __decorateClass$v([
  Entity("storage_buckets", {
    allowApiCrud: RolesType.Admin
  })
], StorageBucket);
let StorageFile = class {
  id = "";
  bucketId = "";
  name;
  size;
  mimeType;
  etag;
  isUploaded = false;
  uploadedByUserId;
  metadata;
  createdAt;
  updatedAt;
  deletedAt;
  bucket;
};
__decorateClass$v([
  Fields.uuid()
], StorageFile.prototype, "id", 2);
__decorateClass$v([
  Fields.string({ dbName: "bucket_id" })
], StorageFile.prototype, "bucketId", 2);
__decorateClass$v([
  Fields.string()
], StorageFile.prototype, "name", 2);
__decorateClass$v([
  Fields.integer()
], StorageFile.prototype, "size", 2);
__decorateClass$v([
  Fields.string({ dbName: "mime_type" })
], StorageFile.prototype, "mimeType", 2);
__decorateClass$v([
  Fields.string()
], StorageFile.prototype, "etag", 2);
__decorateClass$v([
  Fields.boolean({ dbName: "is_uploaded" })
], StorageFile.prototype, "isUploaded", 2);
__decorateClass$v([
  Fields.string({ dbName: "uploaded_by_user_id" })
], StorageFile.prototype, "uploadedByUserId", 2);
__decorateClass$v([
  Fields.json()
], StorageFile.prototype, "metadata", 2);
__decorateClass$v([
  Fields.createdAt({
    dbName: "created_at",
    required: true,
    defaultValue: () => /* @__PURE__ */ new Date(),
    allowApiUpdate: false
  })
], StorageFile.prototype, "createdAt", 2);
__decorateClass$v([
  Fields.updatedAt({
    dbName: "updated_at",
    required: true,
    defaultValue: () => /* @__PURE__ */ new Date(),
    allowApiUpdate: false
  })
], StorageFile.prototype, "updatedAt", 2);
__decorateClass$v([
  Fields.date({ dbName: "deleted_at" })
], StorageFile.prototype, "deletedAt", 2);
__decorateClass$v([
  Relations.toOne(() => StorageBucket, "bucketId")
], StorageFile.prototype, "bucket", 2);
StorageFile = __decorateClass$v([
  Entity("storage_files", {
    allowApiCrud: Allow.authenticated
  })
], StorageFile);
var __defProp$u = Object.defineProperty;
var __getOwnPropDesc$u = Object.getOwnPropertyDescriptor;
var __decorateClass$u = (decorators, target, key, kind) => {
  var result = kind > 1 ? void 0 : kind ? __getOwnPropDesc$u(target, key) : target;
  for (var i = decorators.length - 1, decorator; i >= 0; i--)
    if (decorator = decorators[i])
      result = (kind ? decorator(target, key, result) : decorator(result)) || result;
  if (kind && result) __defProp$u(target, key, result);
  return result;
};
let Organization$1 = class Organization2 {
  id = "";
  name = "";
  slug = "";
  description = "";
  logo = "";
  website = "";
  phone = "";
  email = "";
  address = "";
  city = "";
  state = "";
  country = "";
  postalCode = "";
  timezone = "";
  currency = "";
  language = "";
  industry = "";
  size = "";
  plan = "";
  isActive = true;
  isVerified = false;
  createdBy = "";
  createdAt;
  updatedAt;
  deletedAt;
  teams;
  members;
  users;
  calendarBlocks;
  appointmentTypes;
  appointments;
  workdaySettings;
  bankDetails;
  paymentProviders;
};
__decorateClass$u([
  Fields.uuid()
], Organization$1.prototype, "id", 2);
__decorateClass$u([
  Fields.string()
], Organization$1.prototype, "name", 2);
__decorateClass$u([
  Fields.string()
], Organization$1.prototype, "slug", 2);
__decorateClass$u([
  Fields.string()
], Organization$1.prototype, "description", 2);
__decorateClass$u([
  Fields.string()
], Organization$1.prototype, "logo", 2);
__decorateClass$u([
  Fields.string()
], Organization$1.prototype, "website", 2);
__decorateClass$u([
  Fields.string()
], Organization$1.prototype, "phone", 2);
__decorateClass$u([
  Fields.string()
], Organization$1.prototype, "email", 2);
__decorateClass$u([
  Fields.string()
], Organization$1.prototype, "address", 2);
__decorateClass$u([
  Fields.string()
], Organization$1.prototype, "city", 2);
__decorateClass$u([
  Fields.string()
], Organization$1.prototype, "state", 2);
__decorateClass$u([
  Fields.string()
], Organization$1.prototype, "country", 2);
__decorateClass$u([
  Fields.string()
], Organization$1.prototype, "postalCode", 2);
__decorateClass$u([
  Fields.string()
], Organization$1.prototype, "timezone", 2);
__decorateClass$u([
  Fields.string()
], Organization$1.prototype, "currency", 2);
__decorateClass$u([
  Fields.string()
], Organization$1.prototype, "language", 2);
__decorateClass$u([
  Fields.string()
], Organization$1.prototype, "industry", 2);
__decorateClass$u([
  Fields.string()
], Organization$1.prototype, "size", 2);
__decorateClass$u([
  Fields.string()
], Organization$1.prototype, "plan", 2);
__decorateClass$u([
  Fields.boolean({ dbName: "is_active" })
], Organization$1.prototype, "isActive", 2);
__decorateClass$u([
  Fields.boolean({ dbName: "is_verified" })
], Organization$1.prototype, "isVerified", 2);
__decorateClass$u([
  Fields.string({ dbName: "created_by" })
], Organization$1.prototype, "createdBy", 2);
__decorateClass$u([
  Fields.createdAt({
    dbName: "created_at",
    required: true,
    defaultValue: () => /* @__PURE__ */ new Date(),
    allowApiUpdate: false
  })
], Organization$1.prototype, "createdAt", 2);
__decorateClass$u([
  Fields.updatedAt({
    dbName: "updated_at",
    required: true,
    defaultValue: () => /* @__PURE__ */ new Date(),
    allowApiUpdate: false
  })
], Organization$1.prototype, "updatedAt", 2);
__decorateClass$u([
  Fields.date({ dbName: "deleted_at" })
], Organization$1.prototype, "deletedAt", 2);
__decorateClass$u([
  Relations.toMany(() => Team, "organizationId")
], Organization$1.prototype, "teams", 2);
__decorateClass$u([
  Relations.toMany(() => Member, "organizationId")
], Organization$1.prototype, "members", 2);
__decorateClass$u([
  Relations.toMany(() => User, "organizationId")
], Organization$1.prototype, "users", 2);
__decorateClass$u([
  Relations.toMany(() => CalendarBlock, "organizationId")
], Organization$1.prototype, "calendarBlocks", 2);
__decorateClass$u([
  Relations.toMany(() => AppointmentType, "organizationId")
], Organization$1.prototype, "appointmentTypes", 2);
__decorateClass$u([
  Relations.toMany(() => Appointment, "organizationId")
], Organization$1.prototype, "appointments", 2);
__decorateClass$u([
  Relations.toMany(() => WorkdaySetting, "organizationId")
], Organization$1.prototype, "workdaySettings", 2);
__decorateClass$u([
  Relations.toMany(() => OrgBankDetail, "organizationId")
], Organization$1.prototype, "bankDetails", 2);
__decorateClass$u([
  Relations.toMany(() => OrgPaymentProvider, "organizationId")
], Organization$1.prototype, "paymentProviders", 2);
Organization$1 = __decorateClass$u([
  Entity("organizations", {
    allowApiCrud: Allow.authenticated,
    allowApiInsert: [RolesType.Admin],
    allowApiUpdate: [RolesType.Admin, RolesType.OrgAdmin],
    allowApiDelete: [RolesType.Admin]
  })
], Organization$1);
var __defProp$t = Object.defineProperty;
var __getOwnPropDesc$t = Object.getOwnPropertyDescriptor;
var __decorateClass$t = (decorators, target, key, kind) => {
  var result = kind > 1 ? void 0 : kind ? __getOwnPropDesc$t(target, key) : target;
  for (var i = decorators.length - 1, decorator; i >= 0; i--)
    if (decorator = decorators[i])
      result = (kind ? decorator(target, key, result) : decorator(result)) || result;
  if (kind && result) __defProp$t(target, key, result);
  return result;
};
let Team$1 = class Team2 {
  id = "";
  name = "";
  description = "";
  organizationId = "";
  createdBy = "";
  isActive = true;
  createdAt;
  updatedAt;
  deletedAt;
  organization;
  members;
  users;
  calendarBlocks;
  appointmentTypes;
  appointments;
};
__decorateClass$t([
  Fields.uuid()
], Team$1.prototype, "id", 2);
__decorateClass$t([
  Fields.string()
], Team$1.prototype, "name", 2);
__decorateClass$t([
  Fields.string()
], Team$1.prototype, "description", 2);
__decorateClass$t([
  Fields.string({ dbName: "organization_id" })
], Team$1.prototype, "organizationId", 2);
__decorateClass$t([
  Fields.string({ dbName: "created_by" })
], Team$1.prototype, "createdBy", 2);
__decorateClass$t([
  Fields.boolean({ dbName: "is_active" })
], Team$1.prototype, "isActive", 2);
__decorateClass$t([
  Fields.createdAt({
    dbName: "created_at",
    required: true,
    defaultValue: () => /* @__PURE__ */ new Date(),
    allowApiUpdate: false
  })
], Team$1.prototype, "createdAt", 2);
__decorateClass$t([
  Fields.updatedAt({
    dbName: "updated_at",
    required: true,
    defaultValue: () => /* @__PURE__ */ new Date(),
    allowApiUpdate: false
  })
], Team$1.prototype, "updatedAt", 2);
__decorateClass$t([
  Fields.date({ dbName: "deleted_at" })
], Team$1.prototype, "deletedAt", 2);
__decorateClass$t([
  Relations.toOne(() => Organization, "organizationId")
], Team$1.prototype, "organization", 2);
__decorateClass$t([
  Relations.toMany(() => Member, "teamId")
], Team$1.prototype, "members", 2);
__decorateClass$t([
  Relations.toMany(() => User, "teamId")
], Team$1.prototype, "users", 2);
__decorateClass$t([
  Relations.toMany(() => CalendarBlock, "teamId")
], Team$1.prototype, "calendarBlocks", 2);
__decorateClass$t([
  Relations.toMany(() => AppointmentType, "teamId")
], Team$1.prototype, "appointmentTypes", 2);
__decorateClass$t([
  Relations.toMany(() => Appointment, "teamId")
], Team$1.prototype, "appointments", 2);
Team$1 = __decorateClass$t([
  Entity("teams", {
    allowApiCrud: Allow.authenticated,
    allowApiInsert: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin],
    allowApiUpdate: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin],
    allowApiDelete: [RolesType.Admin, RolesType.OrgAdmin]
  })
], Team$1);
var __defProp$s = Object.defineProperty;
var __getOwnPropDesc$s = Object.getOwnPropertyDescriptor;
var __decorateClass$s = (decorators, target, key, kind) => {
  var result = kind > 1 ? void 0 : kind ? __getOwnPropDesc$s(target, key) : target;
  for (var i = decorators.length - 1, decorator; i >= 0; i--)
    if (decorator = decorators[i])
      result = (kind ? decorator(target, key, result) : decorator(result)) || result;
  if (kind && result) __defProp$s(target, key, result);
  return result;
};
let UserRole$1 = class UserRole {
  id = "";
  userId = "";
  role = "";
  createdAt;
  updatedAt;
  deletedAt;
  user;
};
__decorateClass$s([
  Fields.uuid()
], UserRole$1.prototype, "id", 2);
__decorateClass$s([
  Fields.string({ dbName: "user_id" })
], UserRole$1.prototype, "userId", 2);
__decorateClass$s([
  Fields.string()
], UserRole$1.prototype, "role", 2);
__decorateClass$s([
  Fields.createdAt({
    dbName: "created_at",
    required: true,
    defaultValue: () => /* @__PURE__ */ new Date(),
    allowApiUpdate: false
  })
], UserRole$1.prototype, "createdAt", 2);
__decorateClass$s([
  Fields.updatedAt({
    dbName: "updated_at",
    required: true,
    defaultValue: () => /* @__PURE__ */ new Date(),
    allowApiUpdate: false
  })
], UserRole$1.prototype, "updatedAt", 2);
__decorateClass$s([
  Fields.date({ dbName: "deleted_at" })
], UserRole$1.prototype, "deletedAt", 2);
__decorateClass$s([
  Relations.toOne(() => User, "userId")
], UserRole$1.prototype, "user", 2);
UserRole$1 = __decorateClass$s([
  Entity("user_roles", {
    allowApiCrud: RolesType.Admin
  })
], UserRole$1);
var __defProp$r = Object.defineProperty;
var __getOwnPropDesc$r = Object.getOwnPropertyDescriptor;
var __decorateClass$r = (decorators, target, key, kind) => {
  var result = kind > 1 ? void 0 : kind ? __getOwnPropDesc$r(target, key) : target;
  for (var i = decorators.length - 1, decorator; i >= 0; i--)
    if (decorator = decorators[i])
      result = (kind ? decorator(target, key, result) : decorator(result)) || result;
  if (kind && result) __defProp$r(target, key, result);
  return result;
};
let Member$1 = class Member2 {
  id = "";
  userId = "";
  organizationId = "";
  teamId = "";
  role = "member";
  position = "";
  department = "";
  location = "";
  phone = "";
  email = "";
  isActive = true;
  isVerified = false;
  createdBy = "";
  createdAt;
  updatedAt;
  deletedAt;
  user;
  organization;
  team;
  appointments;
};
__decorateClass$r([
  Fields.uuid()
], Member$1.prototype, "id", 2);
__decorateClass$r([
  Fields.string({ dbName: "user_id" })
], Member$1.prototype, "userId", 2);
__decorateClass$r([
  Fields.string({ dbName: "organization_id" })
], Member$1.prototype, "organizationId", 2);
__decorateClass$r([
  Fields.string({ dbName: "team_id" })
], Member$1.prototype, "teamId", 2);
__decorateClass$r([
  Fields.string()
], Member$1.prototype, "role", 2);
__decorateClass$r([
  Fields.string()
], Member$1.prototype, "position", 2);
__decorateClass$r([
  Fields.string()
], Member$1.prototype, "department", 2);
__decorateClass$r([
  Fields.string()
], Member$1.prototype, "location", 2);
__decorateClass$r([
  Fields.string()
], Member$1.prototype, "phone", 2);
__decorateClass$r([
  Fields.string()
], Member$1.prototype, "email", 2);
__decorateClass$r([
  Fields.boolean({ dbName: "is_active" })
], Member$1.prototype, "isActive", 2);
__decorateClass$r([
  Fields.boolean({ dbName: "is_verified" })
], Member$1.prototype, "isVerified", 2);
__decorateClass$r([
  Fields.string({ dbName: "created_by" })
], Member$1.prototype, "createdBy", 2);
__decorateClass$r([
  Fields.createdAt({
    dbName: "created_at",
    required: true,
    defaultValue: () => /* @__PURE__ */ new Date(),
    allowApiUpdate: false
  })
], Member$1.prototype, "createdAt", 2);
__decorateClass$r([
  Fields.updatedAt({
    dbName: "updated_at",
    required: true,
    defaultValue: () => /* @__PURE__ */ new Date(),
    allowApiUpdate: false
  })
], Member$1.prototype, "updatedAt", 2);
__decorateClass$r([
  Fields.date({ dbName: "deleted_at" })
], Member$1.prototype, "deletedAt", 2);
__decorateClass$r([
  Relations.toOne(() => User, "userId")
], Member$1.prototype, "user", 2);
__decorateClass$r([
  Relations.toOne(() => Organization, "organizationId")
], Member$1.prototype, "organization", 2);
__decorateClass$r([
  Relations.toOne(() => Team, "teamId")
], Member$1.prototype, "team", 2);
__decorateClass$r([
  Relations.toMany(() => Appointment, "memberId")
], Member$1.prototype, "appointments", 2);
Member$1 = __decorateClass$r([
  Entity("members", {
    allowApiCrud: Allow.authenticated,
    allowApiInsert: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin],
    allowApiUpdate: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin],
    allowApiDelete: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin]
  })
], Member$1);
var __defProp$q = Object.defineProperty;
var __getOwnPropDesc$q = Object.getOwnPropertyDescriptor;
var __decorateClass$q = (decorators, target, key, kind) => {
  var result = kind > 1 ? void 0 : kind ? __getOwnPropDesc$q(target, key) : target;
  for (var i = decorators.length - 1, decorator; i >= 0; i--)
    if (decorator = decorators[i])
      result = (kind ? decorator(target, key, result) : decorator(result)) || result;
  if (kind && result) __defProp$q(target, key, result);
  return result;
};
let CalendarBlock$1 = class CalendarBlock2 {
  id = "";
  startDate;
  endDate;
  startTime = "";
  endTime = "";
  organizationId = "";
  teamId = "";
  createdBy = "";
  isActive = true;
  createdAt;
  updatedAt;
  deletedAt;
  organization;
  team;
};
__decorateClass$q([
  Fields.uuid()
], CalendarBlock$1.prototype, "id", 2);
__decorateClass$q([
  Fields.dateOnly({ dbName: "start_date" })
], CalendarBlock$1.prototype, "startDate", 2);
__decorateClass$q([
  Fields.dateOnly({ dbName: "end_date" })
], CalendarBlock$1.prototype, "endDate", 2);
__decorateClass$q([
  Fields.string({ dbName: "start_time" })
], CalendarBlock$1.prototype, "startTime", 2);
__decorateClass$q([
  Fields.string({ dbName: "end_time" })
], CalendarBlock$1.prototype, "endTime", 2);
__decorateClass$q([
  Fields.string({ dbName: "organization_id" })
], CalendarBlock$1.prototype, "organizationId", 2);
__decorateClass$q([
  Fields.string({ dbName: "team_id" })
], CalendarBlock$1.prototype, "teamId", 2);
__decorateClass$q([
  Fields.string({ dbName: "created_by" })
], CalendarBlock$1.prototype, "createdBy", 2);
__decorateClass$q([
  Fields.boolean({ dbName: "is_active" })
], CalendarBlock$1.prototype, "isActive", 2);
__decorateClass$q([
  Fields.createdAt({
    dbName: "created_at",
    required: true,
    defaultValue: () => /* @__PURE__ */ new Date(),
    allowApiUpdate: false
  })
], CalendarBlock$1.prototype, "createdAt", 2);
__decorateClass$q([
  Fields.updatedAt({
    dbName: "updated_at",
    required: true,
    defaultValue: () => /* @__PURE__ */ new Date(),
    allowApiUpdate: false
  })
], CalendarBlock$1.prototype, "updatedAt", 2);
__decorateClass$q([
  Fields.date({ dbName: "deleted_at" })
], CalendarBlock$1.prototype, "deletedAt", 2);
__decorateClass$q([
  Relations.toOne(() => Organization, "organizationId")
], CalendarBlock$1.prototype, "organization", 2);
__decorateClass$q([
  Relations.toOne(() => Team, "teamId")
], CalendarBlock$1.prototype, "team", 2);
CalendarBlock$1 = __decorateClass$q([
  Entity("calendar_blocks", {
    allowApiCrud: Allow.authenticated,
    allowApiInsert: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin],
    allowApiUpdate: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin],
    allowApiDelete: [RolesType.Admin, RolesType.OrgAdmin]
  })
], CalendarBlock$1);
var __defProp$p = Object.defineProperty;
var __getOwnPropDesc$p = Object.getOwnPropertyDescriptor;
var __decorateClass$p = (decorators, target, key, kind) => {
  var result = kind > 1 ? void 0 : kind ? __getOwnPropDesc$p(target, key) : target;
  for (var i = decorators.length - 1, decorator; i >= 0; i--)
    if (decorator = decorators[i])
      result = (kind ? decorator(target, key, result) : decorator(result)) || result;
  if (kind && result) __defProp$p(target, key, result);
  return result;
};
let Appointment$1 = class Appointment2 {
  id = "";
  appointmentTypeId = "";
  userId = "";
  organizationId = "";
  teamId = "";
  memberId = "";
  appointmentDate;
  appointmentTime = "";
  status = "scheduled";
  notes = "";
  createdBy = "";
  isActive = true;
  createdAt;
  updatedAt;
  deletedAt;
  appointmentType;
  user;
  organization;
  team;
  member;
};
__decorateClass$p([
  Fields.uuid()
], Appointment$1.prototype, "id", 2);
__decorateClass$p([
  Fields.string({ dbName: "appointment_type_id" })
], Appointment$1.prototype, "appointmentTypeId", 2);
__decorateClass$p([
  Fields.string({ dbName: "user_id" })
], Appointment$1.prototype, "userId", 2);
__decorateClass$p([
  Fields.string({ dbName: "organization_id" })
], Appointment$1.prototype, "organizationId", 2);
__decorateClass$p([
  Fields.string({ dbName: "team_id" })
], Appointment$1.prototype, "teamId", 2);
__decorateClass$p([
  Fields.string({ dbName: "member_id" })
], Appointment$1.prototype, "memberId", 2);
__decorateClass$p([
  Fields.dateOnly({ dbName: "appointment_date" })
], Appointment$1.prototype, "appointmentDate", 2);
__decorateClass$p([
  Fields.string({ dbName: "appointment_time" })
], Appointment$1.prototype, "appointmentTime", 2);
__decorateClass$p([
  Fields.string()
], Appointment$1.prototype, "status", 2);
__decorateClass$p([
  Fields.string()
], Appointment$1.prototype, "notes", 2);
__decorateClass$p([
  Fields.string({ dbName: "created_by" })
], Appointment$1.prototype, "createdBy", 2);
__decorateClass$p([
  Fields.boolean({ dbName: "is_active" })
], Appointment$1.prototype, "isActive", 2);
__decorateClass$p([
  Fields.createdAt({
    dbName: "created_at",
    required: true,
    defaultValue: () => /* @__PURE__ */ new Date(),
    allowApiUpdate: false
  })
], Appointment$1.prototype, "createdAt", 2);
__decorateClass$p([
  Fields.updatedAt({
    dbName: "updated_at",
    required: true,
    defaultValue: () => /* @__PURE__ */ new Date(),
    allowApiUpdate: false
  })
], Appointment$1.prototype, "updatedAt", 2);
__decorateClass$p([
  Fields.date({ dbName: "deleted_at" })
], Appointment$1.prototype, "deletedAt", 2);
__decorateClass$p([
  Relations.toOne(() => AppointmentType, "appointmentTypeId")
], Appointment$1.prototype, "appointmentType", 2);
__decorateClass$p([
  Relations.toOne(() => User, "userId")
], Appointment$1.prototype, "user", 2);
__decorateClass$p([
  Relations.toOne(() => Organization, "organizationId")
], Appointment$1.prototype, "organization", 2);
__decorateClass$p([
  Relations.toOne(() => Team, "teamId")
], Appointment$1.prototype, "team", 2);
__decorateClass$p([
  Relations.toOne(() => Member, "memberId")
], Appointment$1.prototype, "member", 2);
Appointment$1 = __decorateClass$p([
  Entity("appointments", {
    allowApiCrud: Allow.authenticated,
    allowApiInsert: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin, RolesType.User],
    allowApiUpdate: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin, RolesType.User],
    allowApiDelete: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin]
  })
], Appointment$1);
var __defProp$o = Object.defineProperty;
var __getOwnPropDesc$o = Object.getOwnPropertyDescriptor;
var __decorateClass$o = (decorators, target, key, kind) => {
  var result = kind > 1 ? void 0 : kind ? __getOwnPropDesc$o(target, key) : target;
  for (var i = decorators.length - 1, decorator; i >= 0; i--)
    if (decorator = decorators[i])
      result = (kind ? decorator(target, key, result) : decorator(result)) || result;
  if (kind && result) __defProp$o(target, key, result);
  return result;
};
let User$1 = class User2 {
  id = "";
  email = "";
  firstName = "";
  lastName = "";
  phone = "";
  avatar = "";
  role = "user";
  isActive = true;
  emailVerified = false;
  organizationId = "";
  teamId = "";
  createdBy = "";
  createdAt;
  updatedAt;
  deletedAt;
  organization;
  team;
  userRoles;
  memberships;
  calendarBlocks;
  appointmentTypes;
  updatedAppointments;
  attendedAppointments;
};
__decorateClass$o([
  Fields.uuid()
], User$1.prototype, "id", 2);
__decorateClass$o([
  Fields.string()
], User$1.prototype, "email", 2);
__decorateClass$o([
  Fields.string()
], User$1.prototype, "firstName", 2);
__decorateClass$o([
  Fields.string()
], User$1.prototype, "lastName", 2);
__decorateClass$o([
  Fields.string()
], User$1.prototype, "phone", 2);
__decorateClass$o([
  Fields.string()
], User$1.prototype, "avatar", 2);
__decorateClass$o([
  Fields.string()
], User$1.prototype, "role", 2);
__decorateClass$o([
  Fields.boolean({ dbName: "is_active" })
], User$1.prototype, "isActive", 2);
__decorateClass$o([
  Fields.boolean({ dbName: "email_verified" })
], User$1.prototype, "emailVerified", 2);
__decorateClass$o([
  Fields.string({ dbName: "organization_id" })
], User$1.prototype, "organizationId", 2);
__decorateClass$o([
  Fields.string({ dbName: "team_id" })
], User$1.prototype, "teamId", 2);
__decorateClass$o([
  Fields.string({ dbName: "created_by" })
], User$1.prototype, "createdBy", 2);
__decorateClass$o([
  Fields.createdAt({
    dbName: "created_at",
    required: true,
    defaultValue: () => /* @__PURE__ */ new Date(),
    allowApiUpdate: false
  })
], User$1.prototype, "createdAt", 2);
__decorateClass$o([
  Fields.updatedAt({
    dbName: "updated_at",
    required: true,
    defaultValue: () => /* @__PURE__ */ new Date(),
    allowApiUpdate: false
  })
], User$1.prototype, "updatedAt", 2);
__decorateClass$o([
  Fields.date({ dbName: "deleted_at" })
], User$1.prototype, "deletedAt", 2);
__decorateClass$o([
  Relations.toOne(() => Organization$1, "organizationId")
], User$1.prototype, "organization", 2);
__decorateClass$o([
  Relations.toOne(() => Team$1, "teamId")
], User$1.prototype, "team", 2);
__decorateClass$o([
  Relations.toMany(() => UserRole$1, "userId")
], User$1.prototype, "userRoles", 2);
__decorateClass$o([
  Relations.toMany(() => Member$1, "userId")
], User$1.prototype, "memberships", 2);
__decorateClass$o([
  Relations.toMany(() => CalendarBlock$1, "createdBy")
], User$1.prototype, "calendarBlocks", 2);
__decorateClass$o([
  Relations.toMany(() => AppointmentType$1, "createdBy")
], User$1.prototype, "appointmentTypes", 2);
__decorateClass$o([
  Relations.toMany(() => Appointment$1, "updatedBy")
], User$1.prototype, "updatedAppointments", 2);
__decorateClass$o([
  Relations.toMany(() => Appointment$1, "attendedBy")
], User$1.prototype, "attendedAppointments", 2);
User$1 = __decorateClass$o([
  Entity("users", {
    allowApiCrud: Allow.authenticated,
    allowApiInsert: [RolesType.Admin, RolesType.OrgAdmin],
    allowApiUpdate: [RolesType.Admin, RolesType.OrgAdmin, RolesType.User],
    allowApiDelete: [RolesType.Admin, RolesType.OrgAdmin]
  })
], User$1);
var __defProp$n = Object.defineProperty;
var __getOwnPropDesc$n = Object.getOwnPropertyDescriptor;
var __decorateClass$n = (decorators, target, key, kind) => {
  var result = kind > 1 ? void 0 : kind ? __getOwnPropDesc$n(target, key) : target;
  for (var i = decorators.length - 1, decorator; i >= 0; i--)
    if (decorator = decorators[i])
      result = (kind ? decorator(target, key, result) : decorator(result)) || result;
  if (kind && result) __defProp$n(target, key, result);
  return result;
};
let TeamMember = class {
  id = "";
  teamId = "";
  userId = "";
  createdAt;
  updatedAt;
  deletedAt;
  // Relations removed to avoid circular dependencies
  // Relations can be added later using proper forward declarations
};
__decorateClass$n([
  Fields.uuid()
], TeamMember.prototype, "id", 2);
__decorateClass$n([
  Fields.string({ dbName: "team_id" })
], TeamMember.prototype, "teamId", 2);
__decorateClass$n([
  Fields.string({ dbName: "user_id" })
], TeamMember.prototype, "userId", 2);
__decorateClass$n([
  Fields.createdAt({
    dbName: "created_at",
    required: true,
    defaultValue: () => /* @__PURE__ */ new Date(),
    allowApiUpdate: false
  })
], TeamMember.prototype, "createdAt", 2);
__decorateClass$n([
  Fields.updatedAt({
    dbName: "updated_at",
    required: true,
    defaultValue: () => /* @__PURE__ */ new Date(),
    allowApiUpdate: false
  })
], TeamMember.prototype, "updatedAt", 2);
__decorateClass$n([
  Fields.date({ dbName: "deleted_at" })
], TeamMember.prototype, "deletedAt", 2);
TeamMember = __decorateClass$n([
  Entity("teamMembers", {
    dbName: 'auth."team_members"',
    allowApiCrud: Allow.authenticated,
    allowApiInsert: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin],
    allowApiUpdate: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin],
    allowApiDelete: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin]
  })
], TeamMember);
var __defProp$m = Object.defineProperty;
var __getOwnPropDesc$m = Object.getOwnPropertyDescriptor;
var __decorateClass$m = (decorators, target, key, kind) => {
  var result = kind > 1 ? void 0 : kind ? __getOwnPropDesc$m(target, key) : target;
  for (var i = decorators.length - 1, decorator; i >= 0; i--)
    if (decorator = decorators[i])
      result = (kind ? decorator(target, key, result) : decorator(result)) || result;
  if (kind && result) __defProp$m(target, key, result);
  return result;
};
let Invitation = class {
  id = "";
  email = "";
  inviterId = "";
  organizationId = "";
  role = "";
  status = "pending";
  expiresAt;
  teamId;
  createdAt;
  updatedAt;
  deletedAt;
  inviter;
  organization;
  team;
};
__decorateClass$m([
  Fields.uuid()
], Invitation.prototype, "id", 2);
__decorateClass$m([
  Fields.string({
    validate: (invitation) => {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(invitation.email)) {
        throw "Please enter a valid email address";
      }
    }
  })
], Invitation.prototype, "email", 2);
__decorateClass$m([
  Fields.string({ dbName: "inviter_id" })
], Invitation.prototype, "inviterId", 2);
__decorateClass$m([
  Fields.string({ dbName: "organization_id" })
], Invitation.prototype, "organizationId", 2);
__decorateClass$m([
  Fields.string()
], Invitation.prototype, "role", 2);
__decorateClass$m([
  Fields.string()
], Invitation.prototype, "status", 2);
__decorateClass$m([
  Fields.date({ dbName: "expires_at" })
], Invitation.prototype, "expiresAt", 2);
__decorateClass$m([
  Fields.string({ dbName: "team_id" })
], Invitation.prototype, "teamId", 2);
__decorateClass$m([
  Fields.createdAt({
    dbName: "created_at",
    required: true,
    defaultValue: () => /* @__PURE__ */ new Date(),
    allowApiUpdate: false
  })
], Invitation.prototype, "createdAt", 2);
__decorateClass$m([
  Fields.updatedAt({
    dbName: "updated_at",
    required: true,
    defaultValue: () => /* @__PURE__ */ new Date(),
    allowApiUpdate: false
  })
], Invitation.prototype, "updatedAt", 2);
__decorateClass$m([
  Fields.date({ dbName: "deleted_at" })
], Invitation.prototype, "deletedAt", 2);
__decorateClass$m([
  Relations.toOne(() => User$1, "inviterId")
], Invitation.prototype, "inviter", 2);
__decorateClass$m([
  Relations.toOne(() => Organization$1, "organizationId")
], Invitation.prototype, "organization", 2);
__decorateClass$m([
  Relations.toOne(() => Team$1, "teamId")
], Invitation.prototype, "team", 2);
Invitation = __decorateClass$m([
  Entity("invitations", {
    dbName: 'auth."invitations"',
    allowApiCrud: Allow.authenticated,
    allowApiInsert: [RolesType.Admin, RolesType.OrgAdmin],
    allowApiUpdate: [RolesType.Admin, RolesType.OrgAdmin],
    allowApiDelete: [RolesType.Admin, RolesType.OrgAdmin]
  })
], Invitation);
let MemberInvite = class {
  id = "";
  invitedBy = "";
  inviteeEmail = "";
  organizationId = "";
  teamId = "";
  isPending = true;
  givenName = "";
  familyName = "";
  gender;
  role = "";
  createdAt;
  updatedAt;
  deletedAt;
  organization;
  team;
  inviter;
  genderEntity;
  roleEntity;
};
__decorateClass$m([
  Fields.uuid()
], MemberInvite.prototype, "id", 2);
__decorateClass$m([
  Fields.string({ dbName: "invited_by" })
], MemberInvite.prototype, "invitedBy", 2);
__decorateClass$m([
  Fields.string({
    dbName: "invitee_email",
    validate: (invite) => {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(invite.inviteeEmail)) {
        throw "Please enter a valid email address";
      }
    }
  })
], MemberInvite.prototype, "inviteeEmail", 2);
__decorateClass$m([
  Fields.string({ dbName: "organization_id" })
], MemberInvite.prototype, "organizationId", 2);
__decorateClass$m([
  Fields.string({ dbName: "team_id" })
], MemberInvite.prototype, "teamId", 2);
__decorateClass$m([
  Fields.boolean({ dbName: "is_pending" })
], MemberInvite.prototype, "isPending", 2);
__decorateClass$m([
  Fields.string({ dbName: "given_name" })
], MemberInvite.prototype, "givenName", 2);
__decorateClass$m([
  Fields.string({ dbName: "family_name" })
], MemberInvite.prototype, "familyName", 2);
__decorateClass$m([
  Fields.string()
], MemberInvite.prototype, "gender", 2);
__decorateClass$m([
  Fields.string()
], MemberInvite.prototype, "role", 2);
__decorateClass$m([
  Fields.createdAt({
    dbName: "created_at",
    required: true,
    defaultValue: () => /* @__PURE__ */ new Date(),
    allowApiUpdate: false
  })
], MemberInvite.prototype, "createdAt", 2);
__decorateClass$m([
  Fields.updatedAt({
    dbName: "updated_at",
    required: true,
    defaultValue: () => /* @__PURE__ */ new Date(),
    allowApiUpdate: false
  })
], MemberInvite.prototype, "updatedAt", 2);
__decorateClass$m([
  Fields.date({ dbName: "deleted_at" })
], MemberInvite.prototype, "deletedAt", 2);
__decorateClass$m([
  Relations.toOne(() => Organization$1, "organizationId")
], MemberInvite.prototype, "organization", 2);
__decorateClass$m([
  Relations.toOne(() => Team$1, "teamId")
], MemberInvite.prototype, "team", 2);
__decorateClass$m([
  Relations.toOne(() => User$1, "invitedBy")
], MemberInvite.prototype, "inviter", 2);
__decorateClass$m([
  Relations.toOne(() => Gender, "gender")
], MemberInvite.prototype, "genderEntity", 2);
__decorateClass$m([
  Relations.toOne(() => Role, "role")
], MemberInvite.prototype, "roleEntity", 2);
MemberInvite = __decorateClass$m([
  Entity("memberInvites", {
    dbName: 'auth."member_invites"',
    allowApiCrud: Allow.authenticated,
    allowApiInsert: [RolesType.Admin, RolesType.OrgAdmin],
    allowApiUpdate: [RolesType.Admin, RolesType.OrgAdmin],
    allowApiDelete: [RolesType.Admin, RolesType.OrgAdmin]
  })
], MemberInvite);
var __defProp$l = Object.defineProperty;
var __getOwnPropDesc$l = Object.getOwnPropertyDescriptor;
var __decorateClass$l = (decorators, target, key, kind) => {
  var result = kind > 1 ? void 0 : kind ? __getOwnPropDesc$l(target, key) : target;
  for (var i = decorators.length - 1, decorator; i >= 0; i--)
    if (decorator = decorators[i])
      result = (kind ? decorator(target, key, result) : decorator(result)) || result;
  if (kind && result) __defProp$l(target, key, result);
  return result;
};
let OrgPaymentProvider$1 = class OrgPaymentProvider2 {
  id = "";
  organizationId = "";
  provider = "";
  apiKey = "";
  secretKey = "";
  webhookUrl = "";
  isActive = true;
  createdBy = "";
  createdAt;
  updatedAt;
  deletedAt;
  organization;
};
__decorateClass$l([
  Fields.uuid()
], OrgPaymentProvider$1.prototype, "id", 2);
__decorateClass$l([
  Fields.string({ dbName: "organization_id" })
], OrgPaymentProvider$1.prototype, "organizationId", 2);
__decorateClass$l([
  Fields.string()
], OrgPaymentProvider$1.prototype, "provider", 2);
__decorateClass$l([
  Fields.string()
], OrgPaymentProvider$1.prototype, "apiKey", 2);
__decorateClass$l([
  Fields.string()
], OrgPaymentProvider$1.prototype, "secretKey", 2);
__decorateClass$l([
  Fields.string()
], OrgPaymentProvider$1.prototype, "webhookUrl", 2);
__decorateClass$l([
  Fields.boolean({ dbName: "is_active" })
], OrgPaymentProvider$1.prototype, "isActive", 2);
__decorateClass$l([
  Fields.string({ dbName: "created_by" })
], OrgPaymentProvider$1.prototype, "createdBy", 2);
__decorateClass$l([
  Fields.createdAt({
    dbName: "created_at",
    required: true,
    defaultValue: () => /* @__PURE__ */ new Date(),
    allowApiUpdate: false
  })
], OrgPaymentProvider$1.prototype, "createdAt", 2);
__decorateClass$l([
  Fields.updatedAt({
    dbName: "updated_at",
    required: true,
    defaultValue: () => /* @__PURE__ */ new Date(),
    allowApiUpdate: false
  })
], OrgPaymentProvider$1.prototype, "updatedAt", 2);
__decorateClass$l([
  Fields.date({ dbName: "deleted_at" })
], OrgPaymentProvider$1.prototype, "deletedAt", 2);
__decorateClass$l([
  Relations.toOne(() => Organization, "organizationId")
], OrgPaymentProvider$1.prototype, "organization", 2);
OrgPaymentProvider$1 = __decorateClass$l([
  Entity("org_payment_providers", {
    allowApiCrud: Allow.authenticated,
    allowApiInsert: [RolesType.Admin, RolesType.OrgAdmin],
    allowApiUpdate: [RolesType.Admin, RolesType.OrgAdmin],
    allowApiDelete: [RolesType.Admin, RolesType.OrgAdmin]
  })
], OrgPaymentProvider$1);
var __defProp$k = Object.defineProperty;
var __getOwnPropDesc$k = Object.getOwnPropertyDescriptor;
var __decorateClass$k = (decorators, target, key, kind) => {
  var result = kind > 1 ? void 0 : kind ? __getOwnPropDesc$k(target, key) : target;
  for (var i = decorators.length - 1, decorator; i >= 0; i--)
    if (decorator = decorators[i])
      result = (kind ? decorator(target, key, result) : decorator(result)) || result;
  if (kind && result) __defProp$k(target, key, result);
  return result;
};
let OrgBankDetail$1 = class OrgBankDetail2 {
  id = "";
  organizationId = "";
  bankName = "";
  accountNumber = "";
  accountHolder = "";
  branchCode = "";
  accountType = "";
  isPrimary = false;
  isActive = true;
  createdBy = "";
  createdAt;
  updatedAt;
  deletedAt;
  organization;
};
__decorateClass$k([
  Fields.uuid()
], OrgBankDetail$1.prototype, "id", 2);
__decorateClass$k([
  Fields.string({ dbName: "organization_id" })
], OrgBankDetail$1.prototype, "organizationId", 2);
__decorateClass$k([
  Fields.string()
], OrgBankDetail$1.prototype, "bankName", 2);
__decorateClass$k([
  Fields.string()
], OrgBankDetail$1.prototype, "accountNumber", 2);
__decorateClass$k([
  Fields.string()
], OrgBankDetail$1.prototype, "accountHolder", 2);
__decorateClass$k([
  Fields.string()
], OrgBankDetail$1.prototype, "branchCode", 2);
__decorateClass$k([
  Fields.string()
], OrgBankDetail$1.prototype, "accountType", 2);
__decorateClass$k([
  Fields.boolean({ dbName: "is_primary" })
], OrgBankDetail$1.prototype, "isPrimary", 2);
__decorateClass$k([
  Fields.boolean({ dbName: "is_active" })
], OrgBankDetail$1.prototype, "isActive", 2);
__decorateClass$k([
  Fields.string({ dbName: "created_by" })
], OrgBankDetail$1.prototype, "createdBy", 2);
__decorateClass$k([
  Fields.createdAt({
    dbName: "created_at",
    required: true,
    defaultValue: () => /* @__PURE__ */ new Date(),
    allowApiUpdate: false
  })
], OrgBankDetail$1.prototype, "createdAt", 2);
__decorateClass$k([
  Fields.updatedAt({
    dbName: "updated_at",
    required: true,
    defaultValue: () => /* @__PURE__ */ new Date(),
    allowApiUpdate: false
  })
], OrgBankDetail$1.prototype, "updatedAt", 2);
__decorateClass$k([
  Fields.date({ dbName: "deleted_at" })
], OrgBankDetail$1.prototype, "deletedAt", 2);
__decorateClass$k([
  Relations.toOne(() => Organization, "organizationId")
], OrgBankDetail$1.prototype, "organization", 2);
OrgBankDetail$1 = __decorateClass$k([
  Entity("org_bank_details", {
    allowApiCrud: Allow.authenticated,
    allowApiInsert: [RolesType.Admin, RolesType.OrgAdmin],
    allowApiUpdate: [RolesType.Admin, RolesType.OrgAdmin],
    allowApiDelete: [RolesType.Admin, RolesType.OrgAdmin]
  })
], OrgBankDetail$1);
var __defProp$j = Object.defineProperty;
var __getOwnPropDesc$j = Object.getOwnPropertyDescriptor;
var __decorateClass$j = (decorators, target, key, kind) => {
  var result = kind > 1 ? void 0 : kind ? __getOwnPropDesc$j(target, key) : target;
  for (var i = decorators.length - 1, decorator; i >= 0; i--)
    if (decorator = decorators[i])
      result = (kind ? decorator(target, key, result) : decorator(result)) || result;
  if (kind && result) __defProp$j(target, key, result);
  return result;
};
let WorkdaySetting$1 = class WorkdaySetting2 {
  id = "";
  organizationId = "";
  dayOfWeek = "";
  startTime = "";
  endTime = "";
  isWorkday = true;
  isActive = true;
  createdBy = "";
  createdAt;
  updatedAt;
  deletedAt;
  organization;
};
__decorateClass$j([
  Fields.uuid()
], WorkdaySetting$1.prototype, "id", 2);
__decorateClass$j([
  Fields.string({ dbName: "organization_id" })
], WorkdaySetting$1.prototype, "organizationId", 2);
__decorateClass$j([
  Fields.string()
], WorkdaySetting$1.prototype, "dayOfWeek", 2);
__decorateClass$j([
  Fields.string({ dbName: "start_time" })
], WorkdaySetting$1.prototype, "startTime", 2);
__decorateClass$j([
  Fields.string({ dbName: "end_time" })
], WorkdaySetting$1.prototype, "endTime", 2);
__decorateClass$j([
  Fields.boolean({ dbName: "is_workday" })
], WorkdaySetting$1.prototype, "isWorkday", 2);
__decorateClass$j([
  Fields.boolean({ dbName: "is_active" })
], WorkdaySetting$1.prototype, "isActive", 2);
__decorateClass$j([
  Fields.string({ dbName: "created_by" })
], WorkdaySetting$1.prototype, "createdBy", 2);
__decorateClass$j([
  Fields.createdAt({
    dbName: "created_at",
    required: true,
    defaultValue: () => /* @__PURE__ */ new Date(),
    allowApiUpdate: false
  })
], WorkdaySetting$1.prototype, "createdAt", 2);
__decorateClass$j([
  Fields.updatedAt({
    dbName: "updated_at",
    required: true,
    defaultValue: () => /* @__PURE__ */ new Date(),
    allowApiUpdate: false
  })
], WorkdaySetting$1.prototype, "updatedAt", 2);
__decorateClass$j([
  Fields.date({ dbName: "deleted_at" })
], WorkdaySetting$1.prototype, "deletedAt", 2);
__decorateClass$j([
  Relations.toOne(() => Organization$1, "organizationId")
], WorkdaySetting$1.prototype, "organization", 2);
WorkdaySetting$1 = __decorateClass$j([
  Entity("workday_settings", {
    allowApiCrud: Allow.authenticated,
    allowApiInsert: [RolesType.Admin, RolesType.OrgAdmin],
    allowApiUpdate: [RolesType.Admin, RolesType.OrgAdmin],
    allowApiDelete: [RolesType.Admin, RolesType.OrgAdmin]
  })
], WorkdaySetting$1);
var __defProp$i = Object.defineProperty;
var __getOwnPropDesc$i = Object.getOwnPropertyDescriptor;
var __decorateClass$i = (decorators, target, key, kind) => {
  var result = __getOwnPropDesc$i(target, key);
  for (var i = decorators.length - 1, decorator; i >= 0; i--)
    if (decorator = decorators[i])
      result = decorator(target, key, result) || result;
  if (result) __defProp$i(target, key, result);
  return result;
};
class AppointmentTypeController {
  static async findAll() {
    const repo = remult.repo(AppointmentType$1);
    return await repo.find({
      include: {
        organization: true,
        timezone: true,
        creator: true
      },
      orderBy: { appointmentDetail: "asc" }
    });
  }
  static async findById(id) {
    const repo = remult.repo(AppointmentType$1);
    return await repo.findId(id, {
      include: {
        organization: true,
        timezone: true,
        creator: true,
        appointments: true
      }
    });
  }
  static async findByOrganization(organizationId) {
    const repo = remult.repo(AppointmentType$1);
    return await repo.find({
      where: { organizationId, isActive: true },
      include: {
        organization: true,
        timezone: true,
        creator: true
      },
      orderBy: { appointmentDetail: "asc" }
    });
  }
  static async findActive() {
    const repo = remult.repo(AppointmentType$1);
    return await repo.find({
      where: { isActive: true },
      include: {
        organization: true,
        timezone: true,
        creator: true
      },
      orderBy: { appointmentDetail: "asc" }
    });
  }
  static async findByDuration(durationInMinutes) {
    const repo = remult.repo(AppointmentType$1);
    return await repo.find({
      where: { durationInMinutes, isActive: true },
      include: {
        organization: true,
        timezone: true,
        creator: true
      },
      orderBy: { appointmentDetail: "asc" }
    });
  }
  static async findRequiringPayment() {
    const repo = remult.repo(AppointmentType$1);
    return await repo.find({
      where: { requiresUpfrontPayment: true, isActive: true },
      include: {
        organization: true,
        timezone: true,
        creator: true
      },
      orderBy: { appointmentDetail: "asc" }
    });
  }
  static async insert(appointmentType) {
    const repo = remult.repo(AppointmentType$1);
    if (!appointmentType.createdBy && remult.user) {
      appointmentType.createdBy = remult.user.id;
    }
    return await repo.insert(appointmentType);
  }
  static async update(id, appointmentType) {
    const repo = remult.repo(AppointmentType$1);
    return await repo.update(id, appointmentType);
  }
  static async delete(appointmentType) {
    const repo = remult.repo(AppointmentType$1);
    return await repo.delete(appointmentType);
  }
  static async deleteById(id) {
    const repo = remult.repo(AppointmentType$1);
    const item = await repo.findId(id);
    if (item) {
      return await repo.delete(item);
    }
    throw new Error("Appointment type not found");
  }
  static async softDelete(id) {
    const repo = remult.repo(AppointmentType$1);
    const item = await repo.findId(id);
    if (item) {
      return await repo.save({
        ...item,
        isActive: false,
        deletedAt: /* @__PURE__ */ new Date()
      });
    }
    throw new Error("Appointment type not found");
  }
  static async restore(id) {
    const repo = remult.repo(AppointmentType$1);
    const item = await repo.findId(id);
    if (item) {
      return await repo.save({
        ...item,
        isActive: true,
        deletedAt: void 0
      });
    }
    throw new Error("Appointment type not found");
  }
  static async toggleActive(id) {
    const repo = remult.repo(AppointmentType$1);
    const item = await repo.findId(id);
    if (item) {
      return await repo.save({
        ...item,
        isActive: !item.isActive
      });
    }
    throw new Error("Appointment type not found");
  }
  static async updatePaymentSettings(id, requiresUpfrontPayment, upfrontPaymentAmount) {
    const repo = remult.repo(AppointmentType$1);
    const item = await repo.findId(id);
    if (item) {
      return await repo.save({
        ...item,
        requiresUpfrontPayment,
        upfrontPaymentAmount: upfrontPaymentAmount || item.upfrontPaymentAmount
      });
    }
    throw new Error("Appointment type not found");
  }
}
__decorateClass$i([
  BackendMethod({ allowed: Allow.authenticated })
], AppointmentTypeController, "findAll");
__decorateClass$i([
  BackendMethod({ allowed: Allow.authenticated })
], AppointmentTypeController, "findById");
__decorateClass$i([
  BackendMethod({ allowed: Allow.authenticated })
], AppointmentTypeController, "findByOrganization");
__decorateClass$i([
  BackendMethod({ allowed: Allow.authenticated })
], AppointmentTypeController, "findActive");
__decorateClass$i([
  BackendMethod({ allowed: Allow.authenticated })
], AppointmentTypeController, "findByDuration");
__decorateClass$i([
  BackendMethod({ allowed: Allow.authenticated })
], AppointmentTypeController, "findRequiringPayment");
__decorateClass$i([
  BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin] })
], AppointmentTypeController, "insert");
__decorateClass$i([
  BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin] })
], AppointmentTypeController, "update");
__decorateClass$i([
  BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin] })
], AppointmentTypeController, "delete");
__decorateClass$i([
  BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin] })
], AppointmentTypeController, "deleteById");
__decorateClass$i([
  BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin] })
], AppointmentTypeController, "softDelete");
__decorateClass$i([
  BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin] })
], AppointmentTypeController, "restore");
__decorateClass$i([
  BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin] })
], AppointmentTypeController, "toggleActive");
__decorateClass$i([
  BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin] })
], AppointmentTypeController, "updatePaymentSettings");
var __defProp$h = Object.defineProperty;
var __getOwnPropDesc$h = Object.getOwnPropertyDescriptor;
var __decorateClass$h = (decorators, target, key, kind) => {
  var result = __getOwnPropDesc$h(target, key);
  for (var i = decorators.length - 1, decorator; i >= 0; i--)
    if (decorator = decorators[i])
      result = decorator(target, key, result) || result;
  if (result) __defProp$h(target, key, result);
  return result;
};
class AppointmentController {
  static async findAll() {
    const repo = remult.repo(Appointment$1);
    return await repo.find({
      include: {
        organization: true,
        team: true,
        appointmentType: true,
        updater: true,
        attendee: true
      },
      orderBy: { appointmentDate: "desc", startTime: "asc" }
    });
  }
  static async findById(id) {
    const repo = remult.repo(Appointment$1);
    return await repo.findId(id, {
      include: {
        organization: true,
        team: true,
        appointmentType: true,
        updater: true,
        attendee: true
      }
    });
  }
  static async findByOrganization(organizationId) {
    const repo = remult.repo(Appointment$1);
    return await repo.find({
      where: { organizationId },
      include: {
        organization: true,
        team: true,
        appointmentType: true,
        updater: true,
        attendee: true
      },
      orderBy: { appointmentDate: "desc", startTime: "asc" }
    });
  }
  static async findByTeam(teamId) {
    const repo = remult.repo(Appointment$1);
    return await repo.find({
      where: { teamId },
      include: {
        organization: true,
        team: true,
        appointmentType: true,
        updater: true,
        attendee: true
      },
      orderBy: { appointmentDate: "desc", startTime: "asc" }
    });
  }
  static async findByDateRange(startDate, endDate) {
    const repo = remult.repo(Appointment$1);
    return await repo.find({
      where: {
        appointmentDate: { $gte: startDate, $lte: endDate }
      },
      include: {
        organization: true,
        team: true,
        appointmentType: true,
        updater: true,
        attendee: true
      },
      orderBy: { appointmentDate: "asc", startTime: "asc" }
    });
  }
  static async findByStatus(appointmentStatus) {
    const repo = remult.repo(Appointment$1);
    return await repo.find({
      where: { appointmentStatus },
      include: {
        organization: true,
        team: true,
        appointmentType: true,
        updater: true,
        attendee: true
      },
      orderBy: { appointmentDate: "desc", startTime: "asc" }
    });
  }
  static async findUpcoming() {
    const repo = remult.repo(Appointment$1);
    const today = /* @__PURE__ */ new Date();
    return await repo.find({
      where: {
        appointmentDate: { $gte: today },
        appointmentStatus: { $ne: "cancelled" }
      },
      include: {
        organization: true,
        team: true,
        appointmentType: true,
        updater: true,
        attendee: true
      },
      orderBy: { appointmentDate: "asc", startTime: "asc" }
    });
  }
  static async findByCustomer(customerEmail) {
    const repo = remult.repo(Appointment$1);
    return await repo.find({
      where: { customerEmail },
      include: {
        organization: true,
        team: true,
        appointmentType: true,
        updater: true,
        attendee: true
      },
      orderBy: { appointmentDate: "desc", startTime: "asc" }
    });
  }
  static async insert(appointment) {
    const repo = remult.repo(Appointment$1);
    if (!appointment.updatedBy && remult.user) {
      appointment.updatedBy = remult.user.id;
    }
    return await repo.insert(appointment);
  }
  static async update(id, appointment) {
    const repo = remult.repo(Appointment$1);
    if (remult.user) {
      appointment.updatedBy = remult.user.id;
    }
    return await repo.update(id, appointment);
  }
  static async delete(appointment) {
    const repo = remult.repo(Appointment$1);
    return await repo.delete(appointment);
  }
  static async deleteById(id) {
    const repo = remult.repo(Appointment$1);
    const item = await repo.findId(id);
    if (item) {
      return await repo.delete(item);
    }
    throw new Error("Appointment not found");
  }
  static async updateStatus(id, appointmentStatus, appointmentNotes) {
    const repo = remult.repo(Appointment$1);
    const item = await repo.findId(id);
    if (item) {
      return await repo.save({
        ...item,
        appointmentStatus,
        appointmentNotes: appointmentNotes || item.appointmentNotes,
        updatedBy: remult.user?.id || item.updatedBy
      });
    }
    throw new Error("Appointment not found");
  }
  static async markAttended(id, attendedBy) {
    const repo = remult.repo(Appointment$1);
    const item = await repo.findId(id);
    if (item) {
      return await repo.save({
        ...item,
        appointmentStatus: "completed",
        attendedBy: attendedBy || remult.user?.id || item.attendedBy,
        updatedBy: remult.user?.id || item.updatedBy
      });
    }
    throw new Error("Appointment not found");
  }
  static async updateDepositStatus(id, depositPaid) {
    const repo = remult.repo(Appointment$1);
    const item = await repo.findId(id);
    if (item) {
      return await repo.save({
        ...item,
        depositPaid,
        updatedBy: remult.user?.id || item.updatedBy
      });
    }
    throw new Error("Appointment not found");
  }
}
__decorateClass$h([
  BackendMethod({ allowed: Allow.authenticated })
], AppointmentController, "findAll");
__decorateClass$h([
  BackendMethod({ allowed: Allow.authenticated })
], AppointmentController, "findById");
__decorateClass$h([
  BackendMethod({ allowed: Allow.authenticated })
], AppointmentController, "findByOrganization");
__decorateClass$h([
  BackendMethod({ allowed: Allow.authenticated })
], AppointmentController, "findByTeam");
__decorateClass$h([
  BackendMethod({ allowed: Allow.authenticated })
], AppointmentController, "findByDateRange");
__decorateClass$h([
  BackendMethod({ allowed: Allow.authenticated })
], AppointmentController, "findByStatus");
__decorateClass$h([
  BackendMethod({ allowed: Allow.authenticated })
], AppointmentController, "findUpcoming");
__decorateClass$h([
  BackendMethod({ allowed: Allow.authenticated })
], AppointmentController, "findByCustomer");
__decorateClass$h([
  BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin, RolesType.TeamMember] })
], AppointmentController, "insert");
__decorateClass$h([
  BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin, RolesType.TeamMember] })
], AppointmentController, "update");
__decorateClass$h([
  BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin] })
], AppointmentController, "delete");
__decorateClass$h([
  BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin] })
], AppointmentController, "deleteById");
__decorateClass$h([
  BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin, RolesType.TeamMember] })
], AppointmentController, "updateStatus");
__decorateClass$h([
  BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin, RolesType.TeamMember] })
], AppointmentController, "markAttended");
__decorateClass$h([
  BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin, RolesType.TeamMember] })
], AppointmentController, "updateDepositStatus");
var __defProp$g = Object.defineProperty;
var __getOwnPropDesc$g = Object.getOwnPropertyDescriptor;
var __decorateClass$g = (decorators, target, key, kind) => {
  var result = __getOwnPropDesc$g(target, key);
  for (var i = decorators.length - 1, decorator; i >= 0; i--)
    if (decorator = decorators[i])
      result = decorator(target, key, result) || result;
  if (result) __defProp$g(target, key, result);
  return result;
};
class CalendarBlockController {
  static async findAll() {
    const repo = remult.repo(CalendarBlock$1);
    return await repo.find({
      include: {
        creator: true,
        organization: true,
        team: true
      },
      orderBy: { startDate: "desc" }
    });
  }
  static async findById(id) {
    const repo = remult.repo(CalendarBlock$1);
    return await repo.findId(id, {
      include: {
        creator: true,
        organization: true,
        team: true
      }
    });
  }
  static async findByOrganization(organizationId) {
    const repo = remult.repo(CalendarBlock$1);
    return await repo.find({
      where: { organizationId },
      include: {
        creator: true,
        organization: true,
        team: true
      },
      orderBy: { startDate: "desc" }
    });
  }
  static async findByTeam(teamId) {
    const repo = remult.repo(CalendarBlock$1);
    return await repo.find({
      where: { teamId },
      include: {
        creator: true,
        organization: true,
        team: true
      },
      orderBy: { startDate: "desc" }
    });
  }
  static async findByDateRange(startDate, endDate) {
    const repo = remult.repo(CalendarBlock$1);
    return await repo.find({
      where: {
        startDate: { $gte: startDate },
        endDate: { $lte: endDate },
        isActive: true
      },
      include: {
        creator: true,
        organization: true,
        team: true
      },
      orderBy: { startDate: "asc" }
    });
  }
  static async insert(calendarBlock) {
    const repo = remult.repo(CalendarBlock$1);
    if (!calendarBlock.createdBy && remult.user) {
      calendarBlock.createdBy = remult.user.id;
    }
    return await repo.insert(calendarBlock);
  }
  static async update(id, calendarBlock) {
    const repo = remult.repo(CalendarBlock$1);
    return await repo.update(id, calendarBlock);
  }
  static async delete(calendarBlock) {
    const repo = remult.repo(CalendarBlock$1);
    return await repo.delete(calendarBlock);
  }
  static async deleteById(id) {
    const repo = remult.repo(CalendarBlock$1);
    const item = await repo.findId(id);
    if (item) {
      return await repo.delete(item);
    }
    throw new Error("Calendar block not found");
  }
  static async softDelete(id) {
    const repo = remult.repo(CalendarBlock$1);
    const item = await repo.findId(id);
    if (item) {
      return await repo.save({
        ...item,
        isActive: false,
        deletedAt: /* @__PURE__ */ new Date()
      });
    }
    throw new Error("Calendar block not found");
  }
  static async restore(id) {
    const repo = remult.repo(CalendarBlock$1);
    const item = await repo.findId(id);
    if (item) {
      return await repo.save({
        ...item,
        isActive: true,
        deletedAt: void 0
      });
    }
    throw new Error("Calendar block not found");
  }
  static async findActive() {
    const repo = remult.repo(CalendarBlock$1);
    return await repo.find({
      where: { isActive: true },
      include: {
        creator: true,
        organization: true,
        team: true
      },
      orderBy: { startDate: "desc" }
    });
  }
  static async findDeleted() {
    const repo = remult.repo(CalendarBlock$1);
    return await repo.find({
      where: { isActive: false },
      include: {
        creator: true,
        organization: true,
        team: true
      },
      orderBy: { deletedAt: "desc" }
    });
  }
}
__decorateClass$g([
  BackendMethod({ allowed: Allow.authenticated })
], CalendarBlockController, "findAll");
__decorateClass$g([
  BackendMethod({ allowed: Allow.authenticated })
], CalendarBlockController, "findById");
__decorateClass$g([
  BackendMethod({ allowed: Allow.authenticated })
], CalendarBlockController, "findByOrganization");
__decorateClass$g([
  BackendMethod({ allowed: Allow.authenticated })
], CalendarBlockController, "findByTeam");
__decorateClass$g([
  BackendMethod({ allowed: Allow.authenticated })
], CalendarBlockController, "findByDateRange");
__decorateClass$g([
  BackendMethod({ allowed: Allow.authenticated })
], CalendarBlockController, "insert");
__decorateClass$g([
  BackendMethod({ allowed: Allow.authenticated })
], CalendarBlockController, "update");
__decorateClass$g([
  BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin] })
], CalendarBlockController, "delete");
__decorateClass$g([
  BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin] })
], CalendarBlockController, "deleteById");
__decorateClass$g([
  BackendMethod({ allowed: Allow.authenticated })
], CalendarBlockController, "softDelete");
__decorateClass$g([
  BackendMethod({ allowed: Allow.authenticated })
], CalendarBlockController, "restore");
__decorateClass$g([
  BackendMethod({ allowed: Allow.authenticated })
], CalendarBlockController, "findActive");
__decorateClass$g([
  BackendMethod({ allowed: Allow.authenticated })
], CalendarBlockController, "findDeleted");
var __defProp$f = Object.defineProperty;
var __getOwnPropDesc$f = Object.getOwnPropertyDescriptor;
var __decorateClass$f = (decorators, target, key, kind) => {
  var result = __getOwnPropDesc$f(target, key);
  for (var i = decorators.length - 1, decorator; i >= 0; i--)
    if (decorator = decorators[i])
      result = decorator(target, key, result) || result;
  if (result) __defProp$f(target, key, result);
  return result;
};
class GenderController {
  static async findAll() {
    const repo = remult.repo(Gender);
    return await repo.find({
      orderBy: { name: "asc" }
    });
  }
  static async findByName(name) {
    const repo = remult.repo(Gender);
    return await repo.findFirst({ name });
  }
  static async insert(gender) {
    const repo = remult.repo(Gender);
    return await repo.insert(gender);
  }
  static async update(id, gender) {
    const repo = remult.repo(Gender);
    return await repo.update(id, gender);
  }
  static async delete(gender) {
    const repo = remult.repo(Gender);
    return await repo.delete(gender);
  }
  static async deleteByName(name) {
    const repo = remult.repo(Gender);
    const item = await repo.findFirst({ name });
    if (item) {
      return await repo.delete(item);
    }
    throw new Error("Gender not found");
  }
  static async seed() {
    const repo = remult.repo(Gender);
    const defaultGenders = [
      "Male",
      "Female",
      "Other"
    ];
    const results = [];
    for (const genderName of defaultGenders) {
      const existing = await repo.findFirst({ name: genderName });
      if (!existing) {
        const created = await repo.insert({ name: genderName });
        results.push(created);
      }
    }
    return results;
  }
}
__decorateClass$f([
  BackendMethod({ allowed: true })
], GenderController, "findAll");
__decorateClass$f([
  BackendMethod({ allowed: true })
], GenderController, "findByName");
__decorateClass$f([
  BackendMethod({ allowed: RolesType.Admin })
], GenderController, "insert");
__decorateClass$f([
  BackendMethod({ allowed: RolesType.Admin })
], GenderController, "update");
__decorateClass$f([
  BackendMethod({ allowed: RolesType.Admin })
], GenderController, "delete");
__decorateClass$f([
  BackendMethod({ allowed: RolesType.Admin })
], GenderController, "deleteByName");
__decorateClass$f([
  BackendMethod({ allowed: RolesType.Admin })
], GenderController, "seed");
var __defProp$e = Object.defineProperty;
var __getOwnPropDesc$e = Object.getOwnPropertyDescriptor;
var __decorateClass$e = (decorators, target, key, kind) => {
  var result = __getOwnPropDesc$e(target, key);
  for (var i = decorators.length - 1, decorator; i >= 0; i--)
    if (decorator = decorators[i])
      result = decorator(target, key, result) || result;
  if (result) __defProp$e(target, key, result);
  return result;
};
class InvitationController {
  static async findAll() {
    const repo = remult.repo(Invitation);
    return await repo.find({
      include: {
        inviter: true,
        organization: true,
        team: true
      },
      orderBy: { createdAt: "desc" }
    });
  }
  static async findById(id) {
    const repo = remult.repo(Invitation);
    return await repo.findId(id, {
      include: {
        inviter: true,
        organization: true,
        team: true
      }
    });
  }
  static async findByOrganization(organizationId) {
    const repo = remult.repo(Invitation);
    return await repo.find({
      where: { organizationId },
      include: {
        inviter: true,
        organization: true,
        team: true
      },
      orderBy: { createdAt: "desc" }
    });
  }
  static async findByTeam(teamId) {
    const repo = remult.repo(Invitation);
    return await repo.find({
      where: { teamId },
      include: {
        inviter: true,
        organization: true,
        team: true
      },
      orderBy: { createdAt: "desc" }
    });
  }
  static async findByEmail(email2) {
    const repo = remult.repo(Invitation);
    return await repo.find({
      where: { email: email2 },
      include: {
        inviter: true,
        organization: true,
        team: true
      },
      orderBy: { createdAt: "desc" }
    });
  }
  static async findByStatus(status) {
    const repo = remult.repo(Invitation);
    return await repo.find({
      where: { status },
      include: {
        inviter: true,
        organization: true,
        team: true
      },
      orderBy: { createdAt: "desc" }
    });
  }
  static async findPending() {
    const repo = remult.repo(Invitation);
    const now = /* @__PURE__ */ new Date();
    return await repo.find({
      where: {
        status: "pending",
        expiresAt: { $gt: now }
      },
      include: {
        inviter: true,
        organization: true,
        team: true
      },
      orderBy: { createdAt: "desc" }
    });
  }
  static async findExpired() {
    const repo = remult.repo(Invitation);
    const now = /* @__PURE__ */ new Date();
    return await repo.find({
      where: {
        status: "pending",
        expiresAt: { $lt: now }
      },
      include: {
        inviter: true,
        organization: true,
        team: true
      },
      orderBy: { expiresAt: "desc" }
    });
  }
  static async insert(invitation) {
    const repo = remult.repo(Invitation);
    if (!invitation.inviterId && remult.user) {
      invitation.inviterId = remult.user.id;
    }
    if (!invitation.expiresAt) {
      const expiresAt = /* @__PURE__ */ new Date();
      expiresAt.setDate(expiresAt.getDate() + 7);
      invitation.expiresAt = expiresAt;
    }
    if (!remult.user?.roles?.includes("admin")) {
      const userOrganizationId = remult.user?.organizationId;
      if (userOrganizationId !== invitation.organizationId) {
        throw new Error("You can only create invitations for your own organization");
      }
    }
    return await repo.insert(invitation);
  }
  static async update(id, invitation) {
    const repo = remult.repo(Invitation);
    if (!remult.user?.roles?.includes("admin")) {
      const existingInvitation = await repo.findId(id);
      if (!existingInvitation) {
        throw new Error("Invitation not found");
      }
      const userOrganizationId = remult.user?.organizationId;
      if (userOrganizationId !== existingInvitation.organizationId) {
        throw new Error("You can only update invitations for your own organization");
      }
    }
    return await repo.update(id, invitation);
  }
  static async delete(invitation) {
    const repo = remult.repo(Invitation);
    if (!remult.user?.roles?.includes("admin")) {
      const userOrganizationId = remult.user?.organizationId;
      if (userOrganizationId !== invitation.organizationId) {
        throw new Error("You can only delete invitations for your own organization");
      }
    }
    return await repo.delete(invitation);
  }
  static async deleteById(id) {
    const repo = remult.repo(Invitation);
    const item = await repo.findId(id);
    if (item) {
      if (!remult.user?.roles?.includes("admin")) {
        const userOrganizationId = remult.user?.organizationId;
        if (userOrganizationId !== item.organizationId) {
          throw new Error("You can only delete invitations for your own organization");
        }
      }
      return await repo.delete(item);
    }
    throw new Error("Invitation not found");
  }
  static async accept(id) {
    const repo = remult.repo(Invitation);
    const item = await repo.findId(id);
    if (item) {
      if (item.expiresAt < /* @__PURE__ */ new Date()) {
        throw new Error("Invitation has expired");
      }
      if (item.status !== "pending") {
        throw new Error("Invitation is no longer pending");
      }
      return await repo.save({
        ...item,
        status: "accepted"
      });
    }
    throw new Error("Invitation not found");
  }
  static async decline(id) {
    const repo = remult.repo(Invitation);
    const item = await repo.findId(id);
    if (item) {
      return await repo.save({
        ...item,
        status: "declined"
      });
    }
    throw new Error("Invitation not found");
  }
  static async cancel(id) {
    const repo = remult.repo(Invitation);
    const item = await repo.findId(id);
    if (item) {
      if (!remult.user?.roles?.includes("admin")) {
        const userOrganizationId = remult.user?.organizationId;
        if (userOrganizationId !== item.organizationId) {
          throw new Error("You can only cancel invitations for your own organization");
        }
      }
      return await repo.save({
        ...item,
        status: "cancelled"
      });
    }
    throw new Error("Invitation not found");
  }
  static async resend(id, newExpirationDays = 7) {
    const repo = remult.repo(Invitation);
    const item = await repo.findId(id);
    if (item) {
      if (!remult.user?.roles?.includes("admin")) {
        const userOrganizationId = remult.user?.organizationId;
        if (userOrganizationId !== item.organizationId) {
          throw new Error("You can only resend invitations for your own organization");
        }
      }
      const newExpiresAt = /* @__PURE__ */ new Date();
      newExpiresAt.setDate(newExpiresAt.getDate() + newExpirationDays);
      return await repo.save({
        ...item,
        status: "pending",
        expiresAt: newExpiresAt
      });
    }
    throw new Error("Invitation not found");
  }
  static async getStats(organizationId) {
    const repo = remult.repo(Invitation);
    const whereClause = organizationId ? { organizationId } : {};
    const invitations = await repo.find({
      where: whereClause
    });
    const now = /* @__PURE__ */ new Date();
    const statusStats = {};
    let expiredCount = 0;
    invitations.forEach((inv) => {
      statusStats[inv.status] = (statusStats[inv.status] || 0) + 1;
      if (inv.status === "pending" && inv.expiresAt < now) {
        expiredCount++;
      }
    });
    return {
      totalInvitations: invitations.length,
      statusDistribution: statusStats,
      expiredPendingInvitations: expiredCount
    };
  }
  static async cleanupExpired() {
    const repo = remult.repo(Invitation);
    const now = /* @__PURE__ */ new Date();
    const expiredInvitations = await repo.find({
      where: {
        status: "pending",
        expiresAt: { $lt: now }
      }
    });
    const results = [];
    for (const invitation of expiredInvitations) {
      const updated = await repo.save({
        ...invitation,
        status: "expired"
      });
      results.push(updated);
    }
    return results;
  }
  static async bulkCancel(invitationIds) {
    const repo = remult.repo(Invitation);
    const results = [];
    for (const id of invitationIds) {
      const item = await repo.findId(id);
      if (item) {
        if (!remult.user?.roles?.includes("admin")) {
          const userOrganizationId = remult.user?.organizationId;
          if (userOrganizationId !== item.organizationId) {
            continue;
          }
        }
        const updated = await repo.save({
          ...item,
          status: "cancelled"
        });
        results.push(updated);
      }
    }
    return results;
  }
}
__decorateClass$e([
  BackendMethod({ allowed: Allow.authenticated })
], InvitationController, "findAll");
__decorateClass$e([
  BackendMethod({ allowed: Allow.authenticated })
], InvitationController, "findById");
__decorateClass$e([
  BackendMethod({ allowed: Allow.authenticated })
], InvitationController, "findByOrganization");
__decorateClass$e([
  BackendMethod({ allowed: Allow.authenticated })
], InvitationController, "findByTeam");
__decorateClass$e([
  BackendMethod({ allowed: Allow.authenticated })
], InvitationController, "findByEmail");
__decorateClass$e([
  BackendMethod({ allowed: Allow.authenticated })
], InvitationController, "findByStatus");
__decorateClass$e([
  BackendMethod({ allowed: Allow.authenticated })
], InvitationController, "findPending");
__decorateClass$e([
  BackendMethod({ allowed: Allow.authenticated })
], InvitationController, "findExpired");
__decorateClass$e([
  BackendMethod({ allowed: ["admin", "organisation-admin"] })
], InvitationController, "insert");
__decorateClass$e([
  BackendMethod({ allowed: ["admin", "organisation-admin"] })
], InvitationController, "update");
__decorateClass$e([
  BackendMethod({ allowed: ["admin", "organisation-admin"] })
], InvitationController, "delete");
__decorateClass$e([
  BackendMethod({ allowed: ["admin", "organisation-admin"] })
], InvitationController, "deleteById");
__decorateClass$e([
  BackendMethod({ allowed: Allow.authenticated })
], InvitationController, "accept");
__decorateClass$e([
  BackendMethod({ allowed: Allow.authenticated })
], InvitationController, "decline");
__decorateClass$e([
  BackendMethod({ allowed: ["admin", "organisation-admin"] })
], InvitationController, "cancel");
__decorateClass$e([
  BackendMethod({ allowed: ["admin", "organisation-admin"] })
], InvitationController, "resend");
__decorateClass$e([
  BackendMethod({ allowed: Allow.authenticated })
], InvitationController, "getStats");
__decorateClass$e([
  BackendMethod({ allowed: "admin" })
], InvitationController, "cleanupExpired");
__decorateClass$e([
  BackendMethod({ allowed: ["admin", "organisation-admin"] })
], InvitationController, "bulkCancel");
var __defProp$d = Object.defineProperty;
var __getOwnPropDesc$d = Object.getOwnPropertyDescriptor;
var __decorateClass$d = (decorators, target, key, kind) => {
  var result = __getOwnPropDesc$d(target, key);
  for (var i = decorators.length - 1, decorator; i >= 0; i--)
    if (decorator = decorators[i])
      result = decorator(target, key, result) || result;
  if (result) __defProp$d(target, key, result);
  return result;
};
class MemberController {
  static async findAll() {
    const repo = remult.repo(Member$1);
    return await repo.find({
      include: {
        user: true,
        organization: true,
        team: true
      },
      orderBy: { createdAt: "desc" }
    });
  }
  static async findById(id) {
    const repo = remult.repo(Member$1);
    return await repo.findId(id, {
      include: {
        user: true,
        organization: true,
        team: true
      }
    });
  }
  static async findByOrganization(organizationId) {
    const repo = remult.repo(Member$1);
    return await repo.find({
      where: { organizationId },
      include: {
        user: true,
        organization: true,
        team: true
      },
      orderBy: { createdAt: "desc" }
    });
  }
  static async findByTeam(teamId) {
    const repo = remult.repo(Member$1);
    return await repo.find({
      where: { teamId },
      include: {
        user: true,
        organization: true,
        team: true
      },
      orderBy: { createdAt: "desc" }
    });
  }
  static async findByUser(userId) {
    const repo = remult.repo(Member$1);
    return await repo.find({
      where: { userId },
      include: {
        user: true,
        organization: true,
        team: true
      },
      orderBy: { createdAt: "desc" }
    });
  }
  static async findByRole(role) {
    const repo = remult.repo(Member$1);
    return await repo.find({
      where: { role },
      include: {
        user: true,
        organization: true,
        team: true
      },
      orderBy: { createdAt: "desc" }
    });
  }
  static async insert(member) {
    const repo = remult.repo(Member$1);
    if (!remult.user?.roles?.includes(RolesType.Admin)) {
      const userOrganizationId = remult.user?.organizationId;
      if (userOrganizationId !== member.organizationId) {
        throw new Error("You can only add members to your own organization");
      }
    }
    return await repo.insert(member);
  }
  static async update(id, member) {
    const repo = remult.repo(Member$1);
    if (!remult.user?.roles?.includes(RolesType.Admin)) {
      const existingMember = await repo.findId(id);
      if (!existingMember) {
        throw new Error("Member not found");
      }
      const userOrganizationId = remult.user?.organizationId;
      if (userOrganizationId !== existingMember.organizationId) {
        throw new Error("You can only update members in your own organization");
      }
    }
    return await repo.update(id, member);
  }
  static async delete(member) {
    const repo = remult.repo(Member$1);
    return await repo.delete(member);
  }
  static async deleteById(id) {
    const repo = remult.repo(Member$1);
    const item = await repo.findId(id);
    if (item) {
      return await repo.delete(item);
    }
    throw new Error("Member not found");
  }
  static async updateRole(id, role) {
    const repo = remult.repo(Member$1);
    const item = await repo.findId(id);
    if (item) {
      if (!remult.user?.roles?.includes(RolesType.Admin)) {
        const userOrganizationId = remult.user?.organizationId;
        if (userOrganizationId !== item.organizationId) {
          throw new Error("You can only update members in your own organization");
        }
      }
      return await repo.save({
        ...item,
        role
      });
    }
    throw new Error("Member not found");
  }
  static async assignToTeam(id, teamId) {
    const repo = remult.repo(Member$1);
    const item = await repo.findId(id);
    if (item) {
      if (!remult.user?.roles?.includes("admin")) {
        const userOrganizationId = remult.user?.organizationId;
        if (userOrganizationId !== item.organizationId) {
          throw new Error("You can only update members in your own organization");
        }
      }
      return await repo.save({
        ...item,
        teamId
      });
    }
    throw new Error("Member not found");
  }
  static async removeFromTeam(id) {
    const repo = remult.repo(Member$1);
    const item = await repo.findId(id);
    if (item) {
      if (!remult.user?.roles?.includes("admin")) {
        const userOrganizationId = remult.user?.organizationId;
        if (userOrganizationId !== item.organizationId) {
          throw new Error("You can only update members in your own organization");
        }
      }
      return await repo.save({
        ...item,
        teamId: void 0
      });
    }
    throw new Error("Member not found");
  }
  static async getOrganizationStats(organizationId) {
    const repo = remult.repo(Member$1);
    const members = await repo.find({
      where: { organizationId },
      include: {
        user: true,
        team: true
      }
    });
    const roleStats = {};
    members.forEach((m) => {
      roleStats[m.role] = (roleStats[m.role] || 0) + 1;
    });
    const teamStats = {};
    members.forEach((m) => {
      if (m.teamId) {
        const teamName = m.team?.name || "Unknown Team";
        teamStats[teamName] = (teamStats[teamName] || 0) + 1;
      }
    });
    return {
      totalMembers: members.length,
      roleDistribution: roleStats,
      teamDistribution: teamStats,
      membersWithoutTeam: members.filter((m) => !m.teamId).length
    };
  }
  static async getTeamStats(teamId) {
    const repo = remult.repo(Member$1);
    const members = await repo.find({
      where: { teamId },
      include: {
        user: true,
        organization: true
      }
    });
    const roleStats = {};
    members.forEach((m) => {
      roleStats[m.role] = (roleStats[m.role] || 0) + 1;
    });
    return {
      totalMembers: members.length,
      roleDistribution: roleStats
    };
  }
  static async bulkUpdateRole(memberIds, role) {
    const repo = remult.repo(Member$1);
    const results = [];
    for (const id of memberIds) {
      const item = await repo.findId(id);
      if (item) {
        if (!remult.user?.roles?.includes(RolesType.Admin)) {
          const userOrganizationId = remult.user?.organizationId;
          if (userOrganizationId !== item.organizationId) {
            continue;
          }
        }
        const updated = await repo.save({
          ...item,
          role
        });
        results.push(updated);
      }
    }
    return results;
  }
  static async bulkAssignToTeam(memberIds, teamId) {
    const repo = remult.repo(Member$1);
    const results = [];
    for (const id of memberIds) {
      const item = await repo.findId(id);
      if (item) {
        if (!remult.user?.roles?.includes("admin")) {
          const userOrganizationId = remult.user?.organizationId;
          if (userOrganizationId !== item.organizationId) {
            continue;
          }
        }
        const updated = await repo.save({
          ...item,
          teamId
        });
        results.push(updated);
      }
    }
    return results;
  }
}
__decorateClass$d([
  BackendMethod({ allowed: Allow.authenticated })
], MemberController, "findAll");
__decorateClass$d([
  BackendMethod({ allowed: Allow.authenticated })
], MemberController, "findById");
__decorateClass$d([
  BackendMethod({ allowed: Allow.authenticated })
], MemberController, "findByOrganization");
__decorateClass$d([
  BackendMethod({ allowed: Allow.authenticated })
], MemberController, "findByTeam");
__decorateClass$d([
  BackendMethod({ allowed: Allow.authenticated })
], MemberController, "findByUser");
__decorateClass$d([
  BackendMethod({ allowed: Allow.authenticated })
], MemberController, "findByRole");
__decorateClass$d([
  BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin] })
], MemberController, "insert");
__decorateClass$d([
  BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin] })
], MemberController, "update");
__decorateClass$d([
  BackendMethod({ allowed: RolesType.Admin })
], MemberController, "delete");
__decorateClass$d([
  BackendMethod({ allowed: RolesType.Admin })
], MemberController, "deleteById");
__decorateClass$d([
  BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin] })
], MemberController, "updateRole");
__decorateClass$d([
  BackendMethod({ allowed: ["admin", "organisation-admin"] })
], MemberController, "assignToTeam");
__decorateClass$d([
  BackendMethod({ allowed: ["admin", "organisation-admin"] })
], MemberController, "removeFromTeam");
__decorateClass$d([
  BackendMethod({ allowed: Allow.authenticated })
], MemberController, "getOrganizationStats");
__decorateClass$d([
  BackendMethod({ allowed: Allow.authenticated })
], MemberController, "getTeamStats");
__decorateClass$d([
  BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin] })
], MemberController, "bulkUpdateRole");
__decorateClass$d([
  BackendMethod({ allowed: ["admin", "organisation-admin"] })
], MemberController, "bulkAssignToTeam");
var __defProp$c = Object.defineProperty;
var __getOwnPropDesc$c = Object.getOwnPropertyDescriptor;
var __decorateClass$c = (decorators, target, key, kind) => {
  var result = __getOwnPropDesc$c(target, key);
  for (var i = decorators.length - 1, decorator; i >= 0; i--)
    if (decorator = decorators[i])
      result = decorator(target, key, result) || result;
  if (result) __defProp$c(target, key, result);
  return result;
};
class StorageBucketController {
  static async findAll() {
    const repo = remult.repo(StorageBucket);
    return await repo.find({
      include: {
        files: true
      },
      orderBy: { createdAt: "desc" }
    });
  }
  static async findById(id) {
    const repo = remult.repo(StorageBucket);
    return await repo.findId(id, {
      include: {
        files: true
      }
    });
  }
  static async findActive() {
    const repo = remult.repo(StorageBucket);
    return await repo.find({
      where: { deletedAt: void 0 },
      include: {
        files: true
      },
      orderBy: { createdAt: "desc" }
    });
  }
  static async insert(bucket) {
    const repo = remult.repo(StorageBucket);
    return await repo.insert(bucket);
  }
  static async update(id, bucket) {
    const repo = remult.repo(StorageBucket);
    return await repo.update(id, bucket);
  }
  static async delete(bucket) {
    const repo = remult.repo(StorageBucket);
    return await repo.delete(bucket);
  }
  static async deleteById(id) {
    const repo = remult.repo(StorageBucket);
    const item = await repo.findId(id);
    if (item) {
      return await repo.delete(item);
    }
    throw new Error("Storage bucket not found");
  }
  static async softDelete(id) {
    const repo = remult.repo(StorageBucket);
    const item = await repo.findId(id);
    if (item) {
      return await repo.save({
        ...item,
        deletedAt: /* @__PURE__ */ new Date()
      });
    }
    throw new Error("Storage bucket not found");
  }
  static async restore(id) {
    const repo = remult.repo(StorageBucket);
    const item = await repo.findId(id);
    if (item) {
      return await repo.save({
        ...item,
        deletedAt: void 0
      });
    }
    throw new Error("Storage bucket not found");
  }
  static async updateSettings(id, settings) {
    const repo = remult.repo(StorageBucket);
    const item = await repo.findId(id);
    if (item) {
      return await repo.save({
        ...item,
        ...settings
      });
    }
    throw new Error("Storage bucket not found");
  }
  static async getStats(id) {
    const repo = remult.repo(StorageBucket);
    const bucket = await repo.findId(id, {
      include: {
        files: true
      }
    });
    if (!bucket) {
      throw new Error("Storage bucket not found");
    }
    const files = bucket.files || [];
    const totalFiles = files.length;
    const uploadedFiles = files.filter((f) => f.isUploaded).length;
    const totalSize = files.reduce((sum, f) => sum + (f.size || 0), 0);
    return {
      id: bucket.id,
      totalFiles,
      uploadedFiles,
      pendingFiles: totalFiles - uploadedFiles,
      totalSize,
      averageFileSize: totalFiles > 0 ? Math.round(totalSize / totalFiles) : 0,
      settings: {
        downloadExpiration: bucket.downloadExpiration,
        minUploadFileSize: bucket.minUploadFileSize,
        maxUploadFileSize: bucket.maxUploadFileSize,
        cacheControl: bucket.cacheControl,
        presignedUrlsEnabled: bucket.presignedUrlsEnabled
      },
      createdAt: bucket.createdAt,
      updatedAt: bucket.updatedAt,
      deletedAt: bucket.deletedAt
    };
  }
  static async findDeleted() {
    const repo = remult.repo(StorageBucket);
    return await repo.find({
      where: { deletedAt: { $ne: void 0 } },
      include: {
        files: true
      },
      orderBy: { deletedAt: "desc" }
    });
  }
  static async bulkUpdateSettings(bucketIds, settings) {
    const repo = remult.repo(StorageBucket);
    const results = [];
    for (const id of bucketIds) {
      const item = await repo.findId(id);
      if (item) {
        const updated = await repo.save({
          ...item,
          ...settings
        });
        results.push(updated);
      }
    }
    return results;
  }
  static async createDefault() {
    const repo = remult.repo(StorageBucket);
    return await repo.insert({
      downloadExpiration: 30,
      minUploadFileSize: 1,
      maxUploadFileSize: 5e7,
      // 50MB
      cacheControl: "max-age=3600",
      presignedUrlsEnabled: true
    });
  }
}
__decorateClass$c([
  BackendMethod({ allowed: RolesType.Admin })
], StorageBucketController, "findAll");
__decorateClass$c([
  BackendMethod({ allowed: RolesType.Admin })
], StorageBucketController, "findById");
__decorateClass$c([
  BackendMethod({ allowed: RolesType.Admin })
], StorageBucketController, "findActive");
__decorateClass$c([
  BackendMethod({ allowed: RolesType.Admin })
], StorageBucketController, "insert");
__decorateClass$c([
  BackendMethod({ allowed: RolesType.Admin })
], StorageBucketController, "update");
__decorateClass$c([
  BackendMethod({ allowed: RolesType.Admin })
], StorageBucketController, "delete");
__decorateClass$c([
  BackendMethod({ allowed: RolesType.Admin })
], StorageBucketController, "deleteById");
__decorateClass$c([
  BackendMethod({ allowed: RolesType.Admin })
], StorageBucketController, "softDelete");
__decorateClass$c([
  BackendMethod({ allowed: RolesType.Admin })
], StorageBucketController, "restore");
__decorateClass$c([
  BackendMethod({ allowed: RolesType.Admin })
], StorageBucketController, "updateSettings");
__decorateClass$c([
  BackendMethod({ allowed: RolesType.Admin })
], StorageBucketController, "getStats");
__decorateClass$c([
  BackendMethod({ allowed: RolesType.Admin })
], StorageBucketController, "findDeleted");
__decorateClass$c([
  BackendMethod({ allowed: RolesType.Admin })
], StorageBucketController, "bulkUpdateSettings");
__decorateClass$c([
  BackendMethod({ allowed: RolesType.Admin })
], StorageBucketController, "createDefault");
var __defProp$b = Object.defineProperty;
var __getOwnPropDesc$b = Object.getOwnPropertyDescriptor;
var __decorateClass$b = (decorators, target, key, kind) => {
  var result = __getOwnPropDesc$b(target, key);
  for (var i = decorators.length - 1, decorator; i >= 0; i--)
    if (decorator = decorators[i])
      result = decorator(target, key, result) || result;
  if (result) __defProp$b(target, key, result);
  return result;
};
class StorageFileController {
  static async findAll() {
    const repo = remult.repo(StorageFile);
    return await repo.find({
      include: {
        bucket: true
      },
      orderBy: { createdAt: "desc" }
    });
  }
  static async findById(id) {
    const repo = remult.repo(StorageFile);
    return await repo.findId(id, {
      include: {
        bucket: true
      }
    });
  }
  static async findByBucket(bucketId) {
    const repo = remult.repo(StorageFile);
    return await repo.find({
      where: { bucketId },
      include: {
        bucket: true
      },
      orderBy: { createdAt: "desc" }
    });
  }
  static async findUploaded() {
    const repo = remult.repo(StorageFile);
    return await repo.find({
      where: { isUploaded: true },
      include: {
        bucket: true
      },
      orderBy: { createdAt: "desc" }
    });
  }
  static async findPending() {
    const repo = remult.repo(StorageFile);
    return await repo.find({
      where: { isUploaded: false },
      include: {
        bucket: true
      },
      orderBy: { createdAt: "desc" }
    });
  }
  static async findByUser(uploadedByUserId) {
    const repo = remult.repo(StorageFile);
    return await repo.find({
      where: { uploadedByUserId },
      include: {
        bucket: true
      },
      orderBy: { createdAt: "desc" }
    });
  }
  static async findByMimeType(mimeType) {
    const repo = remult.repo(StorageFile);
    return await repo.find({
      where: { mimeType },
      include: {
        bucket: true
      },
      orderBy: { createdAt: "desc" }
    });
  }
  static async findImages() {
    const repo = remult.repo(StorageFile);
    return await repo.find({
      where: {
        mimeType: { $contains: "image/" },
        isUploaded: true
      },
      include: {
        bucket: true
      },
      orderBy: { createdAt: "desc" }
    });
  }
  static async findDocuments() {
    const repo = remult.repo(StorageFile);
    return await repo.find({
      where: {
        $or: [
          { mimeType: { $contains: "application/pdf" } },
          { mimeType: { $contains: "application/msword" } },
          { mimeType: { $contains: "application/vnd.openxmlformats-officedocument" } },
          { mimeType: { $contains: "text/" } }
        ],
        isUploaded: true
      },
      include: {
        bucket: true
      },
      orderBy: { createdAt: "desc" }
    });
  }
  static async insert(file) {
    const repo = remult.repo(StorageFile);
    if (!file.uploadedByUserId && remult.user) {
      file.uploadedByUserId = remult.user.id;
    }
    return await repo.insert(file);
  }
  static async update(id, file) {
    const repo = remult.repo(StorageFile);
    const existingFile = await repo.findId(id);
    if (!existingFile) {
      throw new Error("File not found");
    }
    if (!remult.user?.roles?.includes("admin") && existingFile.uploadedByUserId !== remult.user?.id) {
      throw new Error("You can only update your own files");
    }
    return await repo.update(id, file);
  }
  static async delete(file) {
    const repo = remult.repo(StorageFile);
    if (!remult.user?.roles?.includes("admin") && file.uploadedByUserId !== remult.user?.id) {
      throw new Error("You can only delete your own files");
    }
    return await repo.delete(file);
  }
  static async deleteById(id) {
    const repo = remult.repo(StorageFile);
    const item = await repo.findId(id);
    if (item) {
      if (!remult.user?.roles?.includes("admin") && item.uploadedByUserId !== remult.user?.id) {
        throw new Error("You can only delete your own files");
      }
      return await repo.delete(item);
    }
    throw new Error("Storage file not found");
  }
  static async markAsUploaded(id, fileInfo) {
    const repo = remult.repo(StorageFile);
    const item = await repo.findId(id);
    if (item) {
      if (!remult.user?.roles?.includes("admin") && item.uploadedByUserId !== remult.user?.id) {
        throw new Error("You can only update your own files");
      }
      return await repo.save({
        ...item,
        isUploaded: true,
        ...fileInfo
      });
    }
    throw new Error("Storage file not found");
  }
  static async updateMetadata(id, metadata) {
    const repo = remult.repo(StorageFile);
    const item = await repo.findId(id);
    if (item) {
      if (!remult.user?.roles?.includes("admin") && item.uploadedByUserId !== remult.user?.id) {
        throw new Error("You can only update your own files");
      }
      return await repo.save({
        ...item,
        metadata: { ...item.metadata, ...metadata }
      });
    }
    throw new Error("Storage file not found");
  }
  static async search(query) {
    const repo = remult.repo(StorageFile);
    return await repo.find({
      where: {
        $or: [
          { name: { $contains: query } },
          { mimeType: { $contains: query } }
        ],
        isUploaded: true
      },
      include: {
        bucket: true
      },
      orderBy: { createdAt: "desc" }
    });
  }
  static async getStats(bucketId) {
    const repo = remult.repo(StorageFile);
    const whereClause = bucketId ? { bucketId } : {};
    const files = await repo.find({
      where: whereClause
    });
    const totalFiles = files.length;
    const uploadedFiles = files.filter((f) => f.isUploaded).length;
    const totalSize = files.reduce((sum, f) => sum + (f.size || 0), 0);
    const mimeTypes = {};
    files.forEach((f) => {
      if (f.mimeType) {
        const category = f.mimeType.split("/")[0];
        mimeTypes[category] = (mimeTypes[category] || 0) + 1;
      }
    });
    return {
      totalFiles,
      uploadedFiles,
      pendingFiles: totalFiles - uploadedFiles,
      totalSize,
      averageFileSize: totalFiles > 0 ? Math.round(totalSize / totalFiles) : 0,
      mimeTypeDistribution: mimeTypes
    };
  }
  static async bulkDelete(fileIds) {
    const repo = remult.repo(StorageFile);
    const results = [];
    for (const id of fileIds) {
      const item = await repo.findId(id);
      if (item) {
        await repo.delete(item);
        results.push(id);
      }
    }
    return results;
  }
  static async cleanupPendingFiles(olderThanDays = 7) {
    const repo = remult.repo(StorageFile);
    const cutoffDate = /* @__PURE__ */ new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);
    const pendingFiles = await repo.find({
      where: {
        isUploaded: false,
        createdAt: { $lt: cutoffDate }
      }
    });
    const results = [];
    for (const file of pendingFiles) {
      await repo.delete(file);
      results.push(file.id);
    }
    return results;
  }
}
__decorateClass$b([
  BackendMethod({ allowed: Allow.authenticated })
], StorageFileController, "findAll");
__decorateClass$b([
  BackendMethod({ allowed: Allow.authenticated })
], StorageFileController, "findById");
__decorateClass$b([
  BackendMethod({ allowed: Allow.authenticated })
], StorageFileController, "findByBucket");
__decorateClass$b([
  BackendMethod({ allowed: Allow.authenticated })
], StorageFileController, "findUploaded");
__decorateClass$b([
  BackendMethod({ allowed: Allow.authenticated })
], StorageFileController, "findPending");
__decorateClass$b([
  BackendMethod({ allowed: Allow.authenticated })
], StorageFileController, "findByUser");
__decorateClass$b([
  BackendMethod({ allowed: Allow.authenticated })
], StorageFileController, "findByMimeType");
__decorateClass$b([
  BackendMethod({ allowed: Allow.authenticated })
], StorageFileController, "findImages");
__decorateClass$b([
  BackendMethod({ allowed: Allow.authenticated })
], StorageFileController, "findDocuments");
__decorateClass$b([
  BackendMethod({ allowed: Allow.authenticated })
], StorageFileController, "insert");
__decorateClass$b([
  BackendMethod({ allowed: Allow.authenticated })
], StorageFileController, "update");
__decorateClass$b([
  BackendMethod({ allowed: Allow.authenticated })
], StorageFileController, "delete");
__decorateClass$b([
  BackendMethod({ allowed: Allow.authenticated })
], StorageFileController, "deleteById");
__decorateClass$b([
  BackendMethod({ allowed: Allow.authenticated })
], StorageFileController, "markAsUploaded");
__decorateClass$b([
  BackendMethod({ allowed: Allow.authenticated })
], StorageFileController, "updateMetadata");
__decorateClass$b([
  BackendMethod({ allowed: Allow.authenticated })
], StorageFileController, "search");
__decorateClass$b([
  BackendMethod({ allowed: Allow.authenticated })
], StorageFileController, "getStats");
__decorateClass$b([
  BackendMethod({ allowed: "admin" })
], StorageFileController, "bulkDelete");
__decorateClass$b([
  BackendMethod({ allowed: "admin" })
], StorageFileController, "cleanupPendingFiles");
var __defProp$a = Object.defineProperty;
var __getOwnPropDesc$a = Object.getOwnPropertyDescriptor;
var __decorateClass$a = (decorators, target, key, kind) => {
  var result = __getOwnPropDesc$a(target, key);
  for (var i = decorators.length - 1, decorator; i >= 0; i--)
    if (decorator = decorators[i])
      result = decorator(target, key, result) || result;
  if (result) __defProp$a(target, key, result);
  return result;
};
class RoleController {
  static async findAll() {
    const repo = remult.repo(Role);
    return await repo.find({
      orderBy: { role: "asc" }
    });
  }
  static async findByRole(role) {
    const repo = remult.repo(Role);
    return await repo.findFirst({ role });
  }
  static async insert(roleData) {
    const repo = remult.repo(Role);
    return await repo.insert(roleData);
  }
  static async update(id, roleData) {
    const repo = remult.repo(Role);
    return await repo.update(id, roleData);
  }
  static async delete(roleData) {
    const repo = remult.repo(Role);
    return await repo.delete(roleData);
  }
  static async deleteByRole(role) {
    const repo = remult.repo(Role);
    const item = await repo.findFirst({ role });
    if (item) {
      return await repo.delete(item);
    }
    throw new Error("Role not found");
  }
  static async seed() {
    const repo = remult.repo(Role);
    const defaultRoles = rolesTypes;
    const results = [];
    for (const roleName of defaultRoles) {
      const existing = await repo.findFirst({ role: roleName });
      if (!existing) {
        const created = await repo.insert({ role: roleName });
        results.push(created);
      }
    }
    return results;
  }
  static async getAvailableRoles() {
    const repo = remult.repo(Role);
    const roles2 = await repo.find({
      orderBy: { role: "asc" }
    });
    return roles2.map((r) => ({
      role: r.role,
      displayName: r.role.split("-").map(
        (word) => word.charAt(0).toUpperCase() + word.slice(1)
      ).join(" "),
      level: getRoleLevel(r.role)
    }));
  }
}
__decorateClass$a([
  BackendMethod({ allowed: RolesType.Admin })
], RoleController, "findAll");
__decorateClass$a([
  BackendMethod({ allowed: RolesType.Admin })
], RoleController, "findByRole");
__decorateClass$a([
  BackendMethod({ allowed: RolesType.Admin })
], RoleController, "insert");
__decorateClass$a([
  BackendMethod({ allowed: RolesType.Admin })
], RoleController, "update");
__decorateClass$a([
  BackendMethod({ allowed: RolesType.Admin })
], RoleController, "delete");
__decorateClass$a([
  BackendMethod({ allowed: RolesType.Admin })
], RoleController, "deleteByRole");
__decorateClass$a([
  BackendMethod({ allowed: RolesType.Admin })
], RoleController, "seed");
__decorateClass$a([
  BackendMethod({ allowed: Allow.authenticated })
], RoleController, "getAvailableRoles");
function getRoleLevel(role) {
  const levels = {
    [RolesType.Admin]: 100,
    [RolesType.OrgAdmin]: 75,
    [RolesType.TeamLocationAdmin]: 50,
    [RolesType.TeamMember]: 25,
    [RolesType.User]: 10,
    "guest": 1
  };
  return levels[role] || 0;
}
var __defProp$9 = Object.defineProperty;
var __getOwnPropDesc$9 = Object.getOwnPropertyDescriptor;
var __decorateClass$9 = (decorators, target, key, kind) => {
  var result = kind > 1 ? void 0 : kind ? __getOwnPropDesc$9(target, key) : target;
  for (var i = decorators.length - 1, decorator; i >= 0; i--)
    if (decorator = decorators[i])
      result = (kind ? decorator(target, key, result) : decorator(result)) || result;
  if (kind && result) __defProp$9(target, key, result);
  return result;
};
let UserRole2 = class {
  id = "";
  userId = "";
  role = "";
  createdAt;
  updatedAt;
  deletedAt;
  user;
  roleEntity;
};
__decorateClass$9([
  Fields.uuid()
], UserRole2.prototype, "id", 2);
__decorateClass$9([
  Fields.string({ dbName: "user_id" })
], UserRole2.prototype, "userId", 2);
__decorateClass$9([
  Fields.string()
], UserRole2.prototype, "role", 2);
__decorateClass$9([
  Fields.createdAt({
    dbName: "created_at",
    required: true,
    defaultValue: () => /* @__PURE__ */ new Date(),
    allowApiUpdate: false
  })
], UserRole2.prototype, "createdAt", 2);
__decorateClass$9([
  Fields.updatedAt({
    dbName: "updated_at",
    required: true,
    defaultValue: () => /* @__PURE__ */ new Date(),
    allowApiUpdate: false
  })
], UserRole2.prototype, "updatedAt", 2);
__decorateClass$9([
  Fields.date({ dbName: "deleted_at" })
], UserRole2.prototype, "deletedAt", 2);
__decorateClass$9([
  Relations.toOne(() => User$1, "userId")
], UserRole2.prototype, "user", 2);
__decorateClass$9([
  Relations.toOne(() => Role, "role")
], UserRole2.prototype, "roleEntity", 2);
UserRole2 = __decorateClass$9([
  Entity("user_roles", {
    allowApiCrud: RolesType.Admin
  })
], UserRole2);
var __defProp$8 = Object.defineProperty;
var __getOwnPropDesc$8 = Object.getOwnPropertyDescriptor;
var __decorateClass$8 = (decorators, target, key, kind) => {
  var result = __getOwnPropDesc$8(target, key);
  for (var i = decorators.length - 1, decorator; i >= 0; i--)
    if (decorator = decorators[i])
      result = decorator(target, key, result) || result;
  if (result) __defProp$8(target, key, result);
  return result;
};
class UserRoleController {
  static async findAll() {
    const repo = remult.repo(UserRole2);
    return await repo.find({
      include: {
        user: true,
        roleEntity: true
      },
      orderBy: { createdAt: "desc" }
    });
  }
  static async findById(id) {
    const repo = remult.repo(UserRole2);
    return await repo.findId(id, {
      include: {
        user: true,
        roleEntity: true
      }
    });
  }
  static async findByUser(userId) {
    const repo = remult.repo(UserRole2);
    return await repo.find({
      where: { userId },
      include: {
        user: true,
        roleEntity: true
      },
      orderBy: { createdAt: "desc" }
    });
  }
  static async findByRole(role) {
    const repo = remult.repo(UserRole2);
    return await repo.find({
      where: { role },
      include: {
        user: true,
        roleEntity: true
      },
      orderBy: { createdAt: "desc" }
    });
  }
  static async insert(userRole2) {
    const repo = remult.repo(UserRole2);
    return await repo.insert(userRole2);
  }
  static async update(id, userRole2) {
    const repo = remult.repo(UserRole2);
    return await repo.update(id, userRole2);
  }
  static async delete(userRole2) {
    const repo = remult.repo(UserRole2);
    return await repo.delete(userRole2);
  }
  static async deleteById(id) {
    const repo = remult.repo(UserRole2);
    const item = await repo.findId(id);
    if (item) {
      return await repo.delete(item);
    }
    throw new Error("User role not found");
  }
  static async assignRole(userId, role) {
    const repo = remult.repo(UserRole2);
    const existing = await repo.findFirst({
      userId,
      role
    });
    if (existing) {
      throw new Error("User already has this role");
    }
    return await repo.insert({
      userId,
      role
    });
  }
  static async removeRole(userId, role) {
    const repo = remult.repo(UserRole2);
    const item = await repo.findFirst({
      userId,
      role
    });
    if (item) {
      return await repo.delete(item);
    }
    throw new Error("User role assignment not found");
  }
  static async getUserRoles(userId) {
    const repo = remult.repo(UserRole2);
    const userRoles = await repo.find({
      where: { userId },
      include: {
        roleEntity: true
      }
    });
    return userRoles.map((ur) => ur.role);
  }
  static async bulkAssignRole(userIds, role) {
    const repo = remult.repo(UserRole2);
    const results = [];
    for (const userId of userIds) {
      const existing = await repo.findFirst({
        userId,
        role
      });
      if (!existing) {
        const created = await repo.insert({
          userId,
          role
        });
        results.push(created);
      }
    }
    return results;
  }
  static async bulkRemoveRole(userIds, role) {
    const repo = remult.repo(UserRole2);
    const results = [];
    for (const userId of userIds) {
      const item = await repo.findFirst({
        userId,
        role
      });
      if (item) {
        await repo.delete(item);
        results.push(userId);
      }
    }
    return results;
  }
  static async replaceUserRoles(userId, roles2) {
    const repo = remult.repo(UserRole2);
    const existingRoles = await repo.find({
      where: { userId }
    });
    for (const existingRole of existingRoles) {
      await repo.delete(existingRole);
    }
    const results = [];
    for (const role of roles2) {
      const created = await repo.insert({
        userId,
        role
      });
      results.push(created);
    }
    return results;
  }
  static async getRoleStats() {
    const repo = remult.repo(UserRole2);
    const userRoles = await repo.find();
    const roleStats = {};
    userRoles.forEach((ur) => {
      roleStats[ur.role] = (roleStats[ur.role] || 0) + 1;
    });
    return {
      totalAssignments: userRoles.length,
      roleDistribution: roleStats,
      uniqueUsers: new Set(userRoles.map((ur) => ur.userId)).size
    };
  }
}
__decorateClass$8([
  BackendMethod({ allowed: RolesType.Admin })
], UserRoleController, "findAll");
__decorateClass$8([
  BackendMethod({ allowed: RolesType.Admin })
], UserRoleController, "findById");
__decorateClass$8([
  BackendMethod({ allowed: RolesType.Admin })
], UserRoleController, "findByUser");
__decorateClass$8([
  BackendMethod({ allowed: RolesType.Admin })
], UserRoleController, "findByRole");
__decorateClass$8([
  BackendMethod({ allowed: RolesType.Admin })
], UserRoleController, "insert");
__decorateClass$8([
  BackendMethod({ allowed: RolesType.Admin })
], UserRoleController, "update");
__decorateClass$8([
  BackendMethod({ allowed: RolesType.Admin })
], UserRoleController, "delete");
__decorateClass$8([
  BackendMethod({ allowed: RolesType.Admin })
], UserRoleController, "deleteById");
__decorateClass$8([
  BackendMethod({ allowed: RolesType.Admin })
], UserRoleController, "assignRole");
__decorateClass$8([
  BackendMethod({ allowed: RolesType.Admin })
], UserRoleController, "removeRole");
__decorateClass$8([
  BackendMethod({ allowed: RolesType.Admin })
], UserRoleController, "getUserRoles");
__decorateClass$8([
  BackendMethod({ allowed: RolesType.Admin })
], UserRoleController, "bulkAssignRole");
__decorateClass$8([
  BackendMethod({ allowed: RolesType.Admin })
], UserRoleController, "bulkRemoveRole");
__decorateClass$8([
  BackendMethod({ allowed: RolesType.Admin })
], UserRoleController, "replaceUserRoles");
__decorateClass$8([
  BackendMethod({ allowed: RolesType.Admin })
], UserRoleController, "getRoleStats");
const timeGreeting = () => {
  let time = "morning";
  const today = /* @__PURE__ */ new Date();
  const currentHour = getHours(today);
  if (currentHour >= 12 && currentHour < 18) {
    time = "afternoon";
  } else if (currentHour >= 18) {
    time = "evening";
  }
  return `Good ${time}`;
};
const getRandomColorName = () => {
  const colors = [
    "bg-red-900",
    "bg-orange-900",
    "bg-yellow-900",
    "bg-green-900",
    "bg-indigo-900",
    "bg-violet-900",
    "bg-pink-900",
    "bg-brown-900",
    "bg-gray-900",
    "bg-black-900",
    "bg-cyan-900",
    "bg-teal-900",
    "bg-lime-900",
    "bg-amber-900",
    "bg-purple-900"
  ];
  const randomIndex = Math.floor(Math.random() * colors.length);
  return colors[randomIndex];
};
const getCopyRight = () => {
  const today = /* @__PURE__ */ new Date();
  return `© ${getYear(today)} SpenDeed.AI. All rights reserved.`;
};
const getInitials = (firstName, surname) => {
  return `${firstName?.substring(0, 1)?.toUpperCase()} ${surname?.substring(0, 1)?.toUpperCase()}`;
};
const getDomain = (email2) => {
  const atIndex = email2.indexOf("@");
  if (atIndex === -1) {
    throw new Error("Invalid email address");
  }
  return email2.slice(atIndex + 1);
};
const saveEmailLocally = (email2) => localStorage.setItem("email", email2);
const formatTimeWithAmPm = (timeStr) => {
  const [hours, minutes] = timeStr.split(":").map(Number);
  const date2 = /* @__PURE__ */ new Date();
  date2.setHours(hours, minutes);
  return date2.toLocaleString("en-US", {
    hour: "numeric",
    minute: "2-digit",
    hour12: true
  }).toLowerCase();
};
const initSuperJson = () => {
  superjson.registerCustom(
    {
      isApplicable: (v) => Decimal.isDecimal(v),
      serialize: (v) => v.toJSON(),
      deserialize: (v) => new Decimal(v)
    },
    "decimal"
  );
};
const getISessionData = (user, session) => {
  if (!user || !session) return null;
  const sessionData = {
    ...user,
    id: user.id,
    name: user.name,
    email: user.email,
    roles: user.roles,
    phoneNumber: user.phoneNumber,
    isAdmin: isUserInRole(user, RolesType.Admin),
    isOrgAdmin: isUserInRole(user, RolesType.OrgAdmin),
    isTeamLocationAdmin: isUserInRole(user, RolesType.TeamLocationAdmin),
    teamConfigIsSetup: !!user.teamId,
    expires: new Date(session.expiresAt),
    organizationId: user.organizationId,
    teamId: user.teamId
  };
  return sessionData;
};
const isUserInRole = (user, role) => {
  return user.roles.any((k) => k === role);
};
const PermissionActions = {
  CREATE: "create",
  READ: "read",
  UPDATE: "update",
  DELETE: "delete",
  MANAGE: "manage",
  CANCEL: "cancel",
  JOIN: "join",
  LEAVE: "leave",
  EXPORT: "export"
};
const statement = {
  // Default organization permissions
  organization: [PermissionActions.CREATE, PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.DELETE],
  member: [PermissionActions.CREATE, PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.DELETE],
  invitation: [PermissionActions.CREATE, PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.DELETE, PermissionActions.CANCEL],
  // Custom business resources
  appointment: [PermissionActions.CREATE, PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.DELETE, PermissionActions.MANAGE],
  appointmentType: [PermissionActions.CREATE, PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.DELETE, PermissionActions.MANAGE],
  calendarBlock: [PermissionActions.CREATE, PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.DELETE, PermissionActions.MANAGE],
  teamPayment: [PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.MANAGE],
  orgBankDetail: [PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.MANAGE],
  workdaySetting: [PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.MANAGE],
  // Team-specific permissions
  team: [PermissionActions.CREATE, PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.DELETE, PermissionActions.MANAGE, PermissionActions.JOIN, PermissionActions.LEAVE],
  // Analytics and reporting
  analytics: [PermissionActions.READ, PermissionActions.EXPORT],
  reports: [PermissionActions.READ, PermissionActions.CREATE, PermissionActions.EXPORT],
  // Settings and configuration
  settings: [PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.MANAGE]
};
const ac = createAccessControl(statement);
const admin = ac.newRole({
  // Full organization permissions (admin can update but not delete organizations)
  organization: [PermissionActions.UPDATE],
  member: [PermissionActions.CREATE, PermissionActions.UPDATE, PermissionActions.DELETE],
  invitation: [PermissionActions.CREATE, PermissionActions.CANCEL],
  // Full team permissions
  team: [PermissionActions.CREATE, PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.DELETE, PermissionActions.MANAGE],
  // Full business permissions
  appointment: [PermissionActions.CREATE, PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.DELETE, PermissionActions.MANAGE],
  appointmentType: [PermissionActions.CREATE, PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.DELETE, PermissionActions.MANAGE],
  calendarBlock: [PermissionActions.CREATE, PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.DELETE, PermissionActions.MANAGE],
  teamPayment: [PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.MANAGE],
  orgBankDetail: [PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.MANAGE],
  workdaySetting: [PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.MANAGE],
  // Full analytics and reporting
  analytics: [PermissionActions.READ, PermissionActions.EXPORT],
  reports: [PermissionActions.READ, PermissionActions.CREATE, PermissionActions.EXPORT],
  // Full settings access
  settings: [PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.MANAGE]
});
const organisationAdmin = ac.newRole({
  // Organization owner permissions (can update and delete organization)
  organization: [PermissionActions.UPDATE, PermissionActions.DELETE],
  member: [PermissionActions.CREATE, PermissionActions.UPDATE, PermissionActions.DELETE],
  invitation: [PermissionActions.CREATE, PermissionActions.CANCEL],
  // Full team permissions within organization
  team: [PermissionActions.CREATE, PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.DELETE, PermissionActions.MANAGE],
  // Full business permissions within organization
  appointment: [PermissionActions.CREATE, PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.DELETE, PermissionActions.MANAGE],
  appointmentType: [PermissionActions.CREATE, PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.DELETE, PermissionActions.MANAGE],
  calendarBlock: [PermissionActions.CREATE, PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.DELETE, PermissionActions.MANAGE],
  teamPayment: [PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.MANAGE],
  orgBankDetail: [PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.MANAGE],
  workdaySetting: [PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.MANAGE],
  // Full analytics and reporting
  analytics: [PermissionActions.READ, PermissionActions.EXPORT],
  reports: [PermissionActions.READ, PermissionActions.CREATE, PermissionActions.EXPORT],
  // Organization settings access
  settings: [PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.MANAGE]
});
const teamAdmin = ac.newRole({
  // Limited organization permissions - can invite to their team
  // member: [PermissionActions.READ, PermissionActions.UPDATE], // Can invite and update members for their team
  // invitation: [PermissionActions.CREATE, PermissionActions.CANCEL], // Can manage invitations for their team
  // Team permissions - limited to their team
  team: [PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.MANAGE],
  // Cannot create/delete teams, only manage assigned team
  // Business permissions - team-level management
  appointment: [PermissionActions.CREATE, PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.DELETE],
  appointmentType: [PermissionActions.CREATE, PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.DELETE],
  calendarBlock: [PermissionActions.CREATE, PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.DELETE],
  teamPayment: [PermissionActions.READ, PermissionActions.UPDATE],
  // Limited financial access
  workdaySetting: [PermissionActions.READ, PermissionActions.UPDATE],
  // Team-level analytics and reporting
  analytics: [PermissionActions.READ, PermissionActions.EXPORT],
  reports: [PermissionActions.READ, PermissionActions.CREATE],
  // Limited settings access
  settings: [PermissionActions.READ]
});
const userRole = ac.newRole({
  appointment: [PermissionActions.READ, PermissionActions.UPDATE, PermissionActions.CREATE]
});
const memberRole = ac.newRole({
  // No organization management permissions
  // Basic team permissions
  team: [PermissionActions.READ, PermissionActions.JOIN, PermissionActions.LEAVE],
  // Limited business permissions - can manage own work
  appointment: [PermissionActions.CREATE, PermissionActions.READ, PermissionActions.UPDATE],
  // Can manage their own appointments
  appointmentType: [PermissionActions.READ],
  // Can view appointment types
  calendarBlock: [PermissionActions.READ],
  // Can view calendar
  // No financial access
  // No administrative settings access
  // Basic analytics - own data only
  analytics: [PermissionActions.READ]
});
const roles = {
  [RolesType.Admin]: admin,
  [RolesType.OrgAdmin]: organisationAdmin,
  [RolesType.TeamLocationAdmin]: teamAdmin,
  [RolesType.TeamMember]: memberRole,
  [RolesType.User]: userRole
};
const adminOnly = (remult2) => {
  return isUserInRole(remult2.user, RolesType.Admin);
};
const orgAdminOnly = (remult2) => {
  return isUserInRole(remult2.user, RolesType.OrgAdmin) || adminOnly(remult2);
};
var __defProp$7 = Object.defineProperty;
var __getOwnPropDesc$7 = Object.getOwnPropertyDescriptor;
var __decorateClass$7 = (decorators, target, key, kind) => {
  var result = __getOwnPropDesc$7(target, key);
  for (var i = decorators.length - 1, decorator; i >= 0; i--)
    if (decorator = decorators[i])
      result = decorator(target, key, result) || result;
  if (result) __defProp$7(target, key, result);
  return result;
};
class UserController {
  static async findAll() {
    const repo = remult.repo(User$1);
    return await repo.find({
      include: {
        organization: true,
        team: true,
        roleEntity: true
      },
      orderBy: { name: "asc" }
    });
  }
  static async findById(id) {
    const repo = remult.repo(User$1);
    return await repo.findId(id, {
      include: {
        organization: true,
        team: true,
        roleEntity: true,
        userRoles: true
      }
    });
  }
  static async findByEmail(email2) {
    const repo = remult.repo(User$1);
    return await repo.findFirst({
      email: email2
    }, {
      include: {
        organization: true,
        team: true,
        roleEntity: true
      }
    });
  }
  static async findByOrganization(organizationId) {
    const repo = remult.repo(User$1);
    return await repo.find({
      where: { organizationId },
      include: {
        organization: true,
        team: true,
        roleEntity: true
      },
      orderBy: { name: "asc" }
    });
  }
  static async findByTeam(teamId) {
    const repo = remult.repo(User$1);
    return await repo.find({
      where: { teamId },
      include: {
        organization: true,
        team: true,
        roleEntity: true
      },
      orderBy: { name: "asc" }
    });
  }
  static async findByRole(role) {
    const repo = remult.repo(User$1);
    return await repo.find({
      where: { roles: { $contains: role } },
      include: {
        organization: true,
        team: true,
        roleEntity: true
      },
      orderBy: { name: "asc" }
    });
  }
  static async findActive() {
    const repo = remult.repo(User$1);
    return await repo.find({
      where: {
        disabled: false,
        banned: false,
        userStatus: "Active"
      },
      include: {
        organization: true,
        team: true,
        roleEntity: true
      },
      orderBy: { name: "asc" }
    });
  }
  static async findAdmins() {
    const repo = remult.repo(User$1);
    return await repo.find({
      where: { roles: { $contains: RolesType.Admin } },
      include: {
        organization: true,
        team: true,
        roleEntity: true
      },
      orderBy: { name: "asc" }
    });
  }
  static async insert(user) {
    const repo = remult.repo(User$1);
    return await repo.insert(user);
  }
  static async update(id, user) {
    const repo = remult.repo(User$1);
    if (remult.user?.id === id) {
      return await repo.update(id, user);
    } else {
      throw new Error("You can only update your own profile");
    }
  }
  static async delete(user) {
    const repo = remult.repo(User$1);
    return await repo.delete(user);
  }
  static async deleteById(id) {
    const repo = remult.repo(User$1);
    const item = await repo.findId(id);
    if (item) {
      return await repo.delete(item);
    }
    throw new Error("User not found");
  }
  static async toggleDisabled(id) {
    const repo = remult.repo(User$1);
    const item = await repo.findId(id);
    if (item) {
      return await repo.save({
        ...item,
        disabled: !item.disabled
      });
    }
    throw new Error("User not found");
  }
  static async banUser(id, banReason, banExpires) {
    const repo = remult.repo(User$1);
    const item = await repo.findId(id);
    if (item) {
      return await repo.save({
        ...item,
        banned: true,
        banReason,
        banExpires,
        userStatus: "Banned"
      });
    }
    throw new Error("User not found");
  }
  static async unbanUser(id) {
    const repo = remult.repo(User$1);
    const item = await repo.findId(id);
    if (item) {
      return await repo.save({
        ...item,
        banned: false,
        banReason: void 0,
        banExpires: void 0,
        userStatus: "Active"
      });
    }
    throw new Error("User not found");
  }
  static async updateProfile(id, profileData) {
    const repo = remult.repo(User$1);
    if (remult.user?.id !== id && !remult.user?.roles?.includes("admin")) {
      throw new Error("You can only update your own profile");
    }
    const item = await repo.findId(id);
    if (item) {
      const allowedFields = ["name", "givenName", "familyName", "displayName", "phoneNumber", "avatarUrl", "locale"];
      const updateData = {};
      if (remult.user?.roles?.includes("admin")) {
        Object.assign(updateData, profileData);
      } else {
        for (const field of allowedFields) {
          if (field in profileData) {
            updateData[field] = profileData[field];
          }
        }
      }
      return await repo.update(id, {
        ...item,
        ...updateData
      });
    }
    throw new Error("User not found");
  }
}
__decorateClass$7([
  BackendMethod({ allowed: Allow.authenticated })
], UserController, "findAll");
__decorateClass$7([
  BackendMethod({ allowed: Allow.authenticated })
], UserController, "findById");
__decorateClass$7([
  BackendMethod({ allowed: Allow.authenticated })
], UserController, "findByEmail");
__decorateClass$7([
  BackendMethod({ allowed: Allow.authenticated })
], UserController, "findByOrganization");
__decorateClass$7([
  BackendMethod({ allowed: Allow.authenticated })
], UserController, "findByTeam");
__decorateClass$7([
  BackendMethod({ allowed: Allow.authenticated })
], UserController, "findByRole");
__decorateClass$7([
  BackendMethod({ allowed: Allow.authenticated })
], UserController, "findActive");
__decorateClass$7([
  BackendMethod({ allowed: Allow.authenticated })
], UserController, "findAdmins");
__decorateClass$7([
  BackendMethod({ allowed: RolesType.Admin })
], UserController, "insert");
__decorateClass$7([
  BackendMethod({ allowed: orgAdminOnly })
], UserController, "update");
__decorateClass$7([
  BackendMethod({ allowed: RolesType.Admin })
], UserController, "delete");
__decorateClass$7([
  BackendMethod({ allowed: RolesType.Admin })
], UserController, "deleteById");
__decorateClass$7([
  BackendMethod({ allowed: RolesType.Admin })
], UserController, "toggleDisabled");
__decorateClass$7([
  BackendMethod({ allowed: RolesType.Admin })
], UserController, "banUser");
__decorateClass$7([
  BackendMethod({ allowed: RolesType.Admin })
], UserController, "unbanUser");
__decorateClass$7([
  BackendMethod({ allowed: Allow.authenticated })
], UserController, "updateProfile");
var __defProp$6 = Object.defineProperty;
var __getOwnPropDesc$6 = Object.getOwnPropertyDescriptor;
var __decorateClass$6 = (decorators, target, key, kind) => {
  var result = __getOwnPropDesc$6(target, key);
  for (var i = decorators.length - 1, decorator; i >= 0; i--)
    if (decorator = decorators[i])
      result = decorator(target, key, result) || result;
  if (result) __defProp$6(target, key, result);
  return result;
};
class OrganizationController {
  static async findAll() {
    const repo = remult.repo(Organization$1);
    return await repo.find({
      include: {
        members: true,
        teams: true
      },
      orderBy: { name: "asc" }
    });
  }
  static async findById(id) {
    const repo = remult.repo(Organization$1);
    return await repo.findId(id, {
      include: {
        members: true,
        invitations: true,
        teams: true,
        calendarBlocks: true,
        appointmentTypes: true,
        appointments: true,
        paymentProvider: true,
        workdaySetting: true,
        bankDetail: true,
        memberInvites: true
      }
    });
  }
  static async findBySlug(slug) {
    const repo = remult.repo(Organization$1);
    return await repo.findFirst({
      slug
    }, {
      include: {
        members: true,
        teams: true
      }
    });
  }
  static async findActive() {
    const repo = remult.repo(Organization$1);
    return await repo.find({
      where: { isActive: true },
      include: {
        members: true,
        teams: true
      },
      orderBy: { name: "asc" }
    });
  }
  static async findByPlan(currentPlan) {
    const repo = remult.repo(Organization$1);
    return await repo.find({
      where: { currentPlan },
      include: {
        members: true,
        teams: true
      },
      orderBy: { name: "asc" }
    });
  }
  static async findByBusinessEmail(businessEmail) {
    const repo = remult.repo(Organization$1);
    return await repo.findFirst({
      businessEmail
    }, {
      include: {
        members: true,
        teams: true
      }
    });
  }
  static async insert(organization) {
    const repo = remult.repo(Organization$1);
    return await repo.insert(organization);
  }
  static async update(id, organization) {
    const repo = remult.repo(Organization$1);
    if (!remult.user?.roles?.includes(RolesType.Admin)) {
      const userOrganizationId = remult.user?.organizationId;
      if (userOrganizationId !== organization.id) {
        throw new Error("You can only update your own organization");
      }
    }
    return await repo.update(id, organization);
  }
  static async delete(organization) {
    const repo = remult.repo(Organization$1);
    return await repo.delete(organization);
  }
  static async deleteById(id) {
    const repo = remult.repo(Organization$1);
    const item = await repo.findId(id);
    if (item) {
      return await repo.delete(item);
    }
    throw new Error("Organization not found");
  }
  static async toggleActive(id) {
    const repo = remult.repo(Organization$1);
    const item = await repo.findId(id);
    if (item) {
      return await repo.save({
        ...item,
        isActive: !item.isActive
      });
    }
    throw new Error("Organization not found");
  }
  static async updatePlan(id, currentPlan) {
    const repo = remult.repo(Organization$1);
    const item = await repo.findId(id);
    if (item) {
      return await repo.save({
        ...item,
        currentPlan
      });
    }
    throw new Error("Organization not found");
  }
  static async updateBusinessInfo(id, businessInfo) {
    const repo = remult.repo(Organization$1);
    if (!remult.user?.roles?.includes(RolesType.Admin)) {
      const userOrganizationId = remult.user?.organizationId;
      if (userOrganizationId !== id) {
        throw new Error("You can only update your own organization");
      }
    }
    const item = await repo.findId(id);
    if (item) {
      return await repo.save({
        ...item,
        ...businessInfo
      });
    }
    throw new Error("Organization not found");
  }
  static async updateLogo(id, logo) {
    const repo = remult.repo(Organization$1);
    if (!remult.user?.roles?.includes(RolesType.Admin)) {
      const userOrganizationId = remult.user?.organizationId;
      if (userOrganizationId !== id) {
        throw new Error("You can only update your own organization");
      }
    }
    const item = await repo.findId(id);
    if (item) {
      return await repo.save({
        ...item,
        logo
      });
    }
    throw new Error("Organization not found");
  }
  static async getStats(id) {
    const repo = remult.repo(Organization$1);
    const org = await repo.findId(id, {
      include: {
        members: true,
        teams: true,
        appointments: true,
        appointmentTypes: true
      }
    });
    if (!org) {
      throw new Error("Organization not found");
    }
    return {
      id: org.id,
      name: org.name,
      memberCount: org.members?.length || 0,
      teamCount: org.teams?.length || 0,
      appointmentCount: org.appointments?.length || 0,
      appointmentTypeCount: org.appointmentTypes?.length || 0,
      currentPlan: org.currentPlan,
      isActive: org.isActive,
      createdAt: org.createdAt
    };
  }
  static async search(query) {
    const repo = remult.repo(Organization$1);
    return await repo.find({
      where: {
        $or: [
          { name: { $contains: query } },
          { slug: { $contains: query } },
          { businessEmail: { $contains: query } }
        ],
        isActive: true
      },
      include: {
        members: true,
        teams: true
      },
      orderBy: { name: "asc" }
    });
  }
  static async findInactive() {
    const repo = remult.repo(Organization$1);
    return await repo.find({
      where: { isActive: false },
      include: {
        members: true,
        teams: true
      },
      orderBy: { updatedAt: "desc" }
    });
  }
}
__decorateClass$6([
  BackendMethod({ allowed: Allow.authenticated })
], OrganizationController, "findAll");
__decorateClass$6([
  BackendMethod({ allowed: Allow.authenticated })
], OrganizationController, "findById");
__decorateClass$6([
  BackendMethod({ allowed: Allow.authenticated })
], OrganizationController, "findBySlug");
__decorateClass$6([
  BackendMethod({ allowed: Allow.authenticated })
], OrganizationController, "findActive");
__decorateClass$6([
  BackendMethod({ allowed: Allow.authenticated })
], OrganizationController, "findByPlan");
__decorateClass$6([
  BackendMethod({ allowed: Allow.authenticated })
], OrganizationController, "findByBusinessEmail");
__decorateClass$6([
  BackendMethod({ allowed: RolesType.Admin })
], OrganizationController, "insert");
__decorateClass$6([
  BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin] })
], OrganizationController, "update");
__decorateClass$6([
  BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin] })
], OrganizationController, "delete");
__decorateClass$6([
  BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin] })
], OrganizationController, "deleteById");
__decorateClass$6([
  BackendMethod({ allowed: RolesType.Admin })
], OrganizationController, "toggleActive");
__decorateClass$6([
  BackendMethod({ allowed: RolesType.Admin })
], OrganizationController, "updatePlan");
__decorateClass$6([
  BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin] })
], OrganizationController, "updateBusinessInfo");
__decorateClass$6([
  BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin] })
], OrganizationController, "updateLogo");
__decorateClass$6([
  BackendMethod({ allowed: Allow.authenticated })
], OrganizationController, "getStats");
__decorateClass$6([
  BackendMethod({ allowed: Allow.authenticated })
], OrganizationController, "search");
__decorateClass$6([
  BackendMethod({ allowed: RolesType.Admin })
], OrganizationController, "findInactive");
var __defProp$5 = Object.defineProperty;
var __getOwnPropDesc$5 = Object.getOwnPropertyDescriptor;
var __decorateClass$5 = (decorators, target, key, kind) => {
  var result = __getOwnPropDesc$5(target, key);
  for (var i = decorators.length - 1, decorator; i >= 0; i--)
    if (decorator = decorators[i])
      result = decorator(target, key, result) || result;
  if (result) __defProp$5(target, key, result);
  return result;
};
class TeamController {
  static async findAll() {
    const repo = remult.repo(Team$1);
    return await repo.find({
      include: {
        organization: true,
        members: true
      },
      orderBy: { name: "asc" }
    });
  }
  static async findById(id) {
    const repo = remult.repo(Team$1);
    return await repo.findId(id, {
      include: {
        organization: true,
        members: true,
        calendarBlocks: true,
        appointments: true
      }
    });
  }
  static async findByOrganization(organizationId) {
    const repo = remult.repo(Team$1);
    return await repo.find({
      where: { organizationId, isActive: true },
      include: {
        organization: true,
        members: true
      },
      orderBy: { name: "asc" }
    });
  }
  static async findActive() {
    const repo = remult.repo(Team$1);
    return await repo.find({
      where: { isActive: true },
      include: {
        organization: true,
        members: true
      },
      orderBy: { name: "asc" }
    });
  }
  static async findDefault() {
    const repo = remult.repo(Team$1);
    return await repo.find({
      where: { isDefault: true, isActive: true },
      include: {
        organization: true,
        members: true
      },
      orderBy: { name: "asc" }
    });
  }
  static async insert(team) {
    const repo = remult.repo(Team$1);
    if (!remult.user?.roles?.includes(RolesType.Admin)) {
      const userOrganizationId = remult.user?.organizationId;
      if (userOrganizationId !== team.organizationId) {
        throw new Error("You can only create teams in your own organization");
      }
    }
    return await repo.insert(team);
  }
  static async update(id, team) {
    const repo = remult.repo(Team$1);
    if (!remult.user?.roles?.includes(RolesType.Admin)) {
      const existingTeam = await repo.findId(id);
      if (!existingTeam) {
        throw new Error("Team not found");
      }
      const userOrganizationId = remult.user?.organizationId;
      if (userOrganizationId !== existingTeam.organizationId) {
        throw new Error("You can only update teams in your own organization");
      }
    }
    return await repo.update(id, team);
  }
  static async delete(team) {
    const repo = remult.repo(Team$1);
    return await repo.delete(team);
  }
  static async deleteById(id) {
    const repo = remult.repo(Team$1);
    const item = await repo.findId(id);
    if (item) {
      return await repo.delete(item);
    }
    throw new Error("Team not found");
  }
  static async toggleActive(id) {
    const repo = remult.repo(Team$1);
    const item = await repo.findId(id);
    if (item) {
      if (!remult.user?.roles?.includes(RolesType.Admin)) {
        const userOrganizationId = remult.user?.organizationId;
        if (userOrganizationId !== item.organizationId) {
          throw new Error("You can only modify teams in your own organization");
        }
      }
      return await repo.save({
        ...item,
        isActive: !item.isActive
      });
    }
    throw new Error("Team not found");
  }
  static async setAsDefault(id) {
    const repo = remult.repo(Team$1);
    const item = await repo.findId(id);
    if (item) {
      if (!remult.user?.roles?.includes(RolesType.Admin)) {
        const userOrganizationId = remult.user?.organizationId;
        if (userOrganizationId !== item.organizationId) {
          throw new Error("You can only modify teams in your own organization");
        }
      }
      const allTeams = await repo.find({
        where: { organizationId: item.organizationId, isDefault: true }
      });
      for (const team of allTeams) {
        if (team.id !== id) {
          await repo.save({
            ...team,
            isDefault: false
          });
        }
      }
      return await repo.save({
        ...item,
        isDefault: true
      });
    }
    throw new Error("Team not found");
  }
  static async updateLocation(id, locationInfo) {
    const repo = remult.repo(Team$1);
    const item = await repo.findId(id);
    if (item) {
      if (!remult.user?.roles?.includes("admin")) {
        const userOrganizationId = remult.user?.organizationId;
        if (userOrganizationId !== item.organizationId) {
          throw new Error("You can only modify teams in your own organization");
        }
      }
      return await repo.save({
        ...item,
        ...locationInfo
      });
    }
    throw new Error("Team not found");
  }
  static async getStats(id) {
    const repo = remult.repo(Team$1);
    const team = await repo.findId(id, {
      include: {
        organization: true,
        members: true,
        appointments: true
      }
    });
    if (!team) {
      throw new Error("Team not found");
    }
    return {
      id: team.id,
      name: team.name,
      organizationName: team.organization?.name,
      memberCount: team.members?.length || 0,
      appointmentCount: team.appointments?.length || 0,
      isDefault: team.isDefault,
      isActive: team.isActive,
      createdAt: team.createdAt,
      locationAddress: team.locationAddress,
      locationEmail: team.locationEmail,
      locationPhoneNumber: team.locationPhoneNumber
    };
  }
  static async search(query, organizationId) {
    const repo = remult.repo(Team$1);
    const whereClause = {
      name: { $contains: query },
      isActive: true
    };
    if (organizationId) {
      whereClause.organizationId = organizationId;
    }
    return await repo.find({
      where: whereClause,
      include: {
        organization: true,
        members: true
      },
      orderBy: { name: "asc" }
    });
  }
  static async findInactive() {
    const repo = remult.repo(Team$1);
    return await repo.find({
      where: { isActive: false },
      include: {
        organization: true,
        members: true
      },
      orderBy: { updatedAt: "desc" }
    });
  }
  static async bulkToggleActive(teamIds, isActive) {
    const repo = remult.repo(Team$1);
    const results = [];
    for (const id of teamIds) {
      const item = await repo.findId(id);
      if (item) {
        if (!remult.user?.roles?.includes("admin")) {
          const userOrganizationId = remult.user?.organizationId;
          if (userOrganizationId !== item.organizationId) {
            continue;
          }
        }
        const updated = await repo.save({
          ...item,
          isActive
        });
        results.push(updated);
      }
    }
    return results;
  }
}
__decorateClass$5([
  BackendMethod({ allowed: Allow.authenticated })
], TeamController, "findAll");
__decorateClass$5([
  BackendMethod({ allowed: Allow.authenticated })
], TeamController, "findById");
__decorateClass$5([
  BackendMethod({ allowed: Allow.authenticated })
], TeamController, "findByOrganization");
__decorateClass$5([
  BackendMethod({ allowed: Allow.authenticated })
], TeamController, "findActive");
__decorateClass$5([
  BackendMethod({ allowed: Allow.authenticated })
], TeamController, "findDefault");
__decorateClass$5([
  BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin] })
], TeamController, "insert");
__decorateClass$5([
  BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin] })
], TeamController, "update");
__decorateClass$5([
  BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin] })
], TeamController, "delete");
__decorateClass$5([
  BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin] })
], TeamController, "deleteById");
__decorateClass$5([
  BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin] })
], TeamController, "toggleActive");
__decorateClass$5([
  BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin] })
], TeamController, "setAsDefault");
__decorateClass$5([
  BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin] })
], TeamController, "updateLocation");
__decorateClass$5([
  BackendMethod({ allowed: Allow.authenticated })
], TeamController, "getStats");
__decorateClass$5([
  BackendMethod({ allowed: Allow.authenticated })
], TeamController, "search");
__decorateClass$5([
  BackendMethod({ allowed: RolesType.Admin })
], TeamController, "findInactive");
__decorateClass$5([
  BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin] })
], TeamController, "bulkToggleActive");
var __defProp$4 = Object.defineProperty;
var __getOwnPropDesc$4 = Object.getOwnPropertyDescriptor;
var __decorateClass$4 = (decorators, target, key, kind) => {
  var result = __getOwnPropDesc$4(target, key);
  for (var i = decorators.length - 1, decorator; i >= 0; i--)
    if (decorator = decorators[i])
      result = decorator(target, key, result) || result;
  if (result) __defProp$4(target, key, result);
  return result;
};
class TeamMemberController {
  static async findAll() {
    const repo = remult.repo(TeamMember);
    return await repo.find({
      include: {
        team: true,
        user: true
      },
      orderBy: { createdAt: "desc" }
    });
  }
  static async findById(id) {
    const repo = remult.repo(TeamMember);
    return await repo.findId(id, {
      include: {
        team: true,
        user: true
      }
    });
  }
  static async findByTeam(teamId) {
    const repo = remult.repo(TeamMember);
    return await repo.find({
      where: { teamId },
      include: {
        team: true,
        user: true
      },
      orderBy: { createdAt: "desc" }
    });
  }
  static async findByUser(userId) {
    const repo = remult.repo(TeamMember);
    return await repo.find({
      where: { userId },
      include: {
        team: true,
        user: true
      },
      orderBy: { createdAt: "desc" }
    });
  }
  static async insert(teamMember) {
    const repo = remult.repo(TeamMember);
    const existing = await repo.findFirst({
      teamId: teamMember.teamId,
      userId: teamMember.userId,
      deletedAt: void 0
      // Only check non-deleted records
    });
    if (existing) {
      throw new Error("User is already a member of this team");
    }
    return await repo.insert(teamMember);
  }
  static async update(id, teamMember) {
    const repo = remult.repo(TeamMember);
    return await repo.update(id, teamMember);
  }
  static async delete(id) {
    const repo = remult.repo(TeamMember);
    const teamMember = await repo.findId(id);
    if (teamMember) {
      return await repo.delete(teamMember);
    }
    throw new Error("TeamMember not found");
  }
  static async softDelete(id) {
    const repo = remult.repo(TeamMember);
    const teamMember = await repo.findId(id);
    if (teamMember) {
      return await repo.save({
        ...teamMember,
        deletedAt: /* @__PURE__ */ new Date()
      });
    }
    throw new Error("TeamMember not found");
  }
  static async restore(id) {
    const repo = remult.repo(TeamMember);
    const teamMember = await repo.findId(id);
    if (teamMember) {
      return await repo.save({
        ...teamMember,
        deletedAt: void 0
      });
    }
    throw new Error("TeamMember not found");
  }
  static async addUserToTeam(userId, teamId) {
    return await this.insert({ userId, teamId });
  }
  static async removeUserFromTeam(userId, teamId) {
    const repo = remult.repo(TeamMember);
    const teamMember = await repo.findFirst({
      userId,
      teamId,
      deletedAt: void 0
    });
    if (teamMember) {
      return await this.softDelete(teamMember.id);
    }
    throw new Error("User is not a member of this team");
  }
}
__decorateClass$4([
  BackendMethod({ allowed: Allow.authenticated })
], TeamMemberController, "findAll");
__decorateClass$4([
  BackendMethod({ allowed: Allow.authenticated })
], TeamMemberController, "findById");
__decorateClass$4([
  BackendMethod({ allowed: Allow.authenticated })
], TeamMemberController, "findByTeam");
__decorateClass$4([
  BackendMethod({ allowed: Allow.authenticated })
], TeamMemberController, "findByUser");
__decorateClass$4([
  BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin] })
], TeamMemberController, "insert");
__decorateClass$4([
  BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin] })
], TeamMemberController, "update");
__decorateClass$4([
  BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin] })
], TeamMemberController, "delete");
__decorateClass$4([
  BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin] })
], TeamMemberController, "softDelete");
__decorateClass$4([
  BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin] })
], TeamMemberController, "restore");
__decorateClass$4([
  BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin] })
], TeamMemberController, "addUserToTeam");
__decorateClass$4([
  BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin] })
], TeamMemberController, "removeUserFromTeam");
var __defProp$3 = Object.defineProperty;
var __getOwnPropDesc$3 = Object.getOwnPropertyDescriptor;
var __decorateClass$3 = (decorators, target, key, kind) => {
  var result = __getOwnPropDesc$3(target, key);
  for (var i = decorators.length - 1, decorator; i >= 0; i--)
    if (decorator = decorators[i])
      result = decorator(target, key, result) || result;
  if (result) __defProp$3(target, key, result);
  return result;
};
class TimeZoneController {
  static async findAll() {
    const repo = remult.repo(TimeZone);
    return await repo.find({
      orderBy: { timeZoneName: "asc" }
    });
  }
  static async findById(id) {
    const repo = remult.repo(TimeZone);
    return await repo.findId(id);
  }
  static async findByName(name) {
    const repo = remult.repo(TimeZone);
    return await repo.findFirst({ timeZoneName: name });
  }
  static async findByOffset(offsetHours) {
    const repo = remult.repo(TimeZone);
    return await repo.find({
      where: { offsetHours },
      orderBy: { timeZoneName: "asc" }
    });
  }
  static async findByRegion(region) {
    const repo = remult.repo(TimeZone);
    return await repo.find({
      where: { timeZoneName: { $contains: region } },
      orderBy: { timeZoneName: "asc" }
    });
  }
  static async findCommon() {
    const repo = remult.repo(TimeZone);
    const commonTimezones = [
      "UTC",
      "America/New_York",
      "America/Chicago",
      "America/Denver",
      "America/Los_Angeles",
      "Europe/London",
      "Europe/Paris",
      "Europe/Berlin",
      "Asia/Tokyo",
      "Asia/Shanghai",
      "Australia/Sydney"
    ];
    return await repo.find({
      where: { timeZoneName: { $in: commonTimezones } },
      orderBy: { timeZoneName: "asc" }
    });
  }
  static async insert(timezone) {
    const repo = remult.repo(TimeZone);
    return await repo.insert(timezone);
  }
  static async update(id, timezone) {
    const repo = remult.repo(TimeZone);
    return await repo.update(id, timezone);
  }
  static async delete(timezone) {
    const repo = remult.repo(TimeZone);
    return await repo.delete(timezone);
  }
  static async deleteById(id) {
    const repo = remult.repo(TimeZone);
    const item = await repo.findId(id);
    if (item) {
      return await repo.delete(item);
    }
    throw new Error("Timezone not found");
  }
  static async seed() {
    const repo = remult.repo(TimeZone);
    const timezones = [
      { timeZoneName: "UTC", offsetHours: 0 },
      { timeZoneName: "America/New_York", offsetHours: -5 },
      // EST (UTC-5)
      { timeZoneName: "America/Chicago", offsetHours: -6 },
      // CST (UTC-6)
      { timeZoneName: "America/Denver", offsetHours: -7 },
      // MST (UTC-7)
      { timeZoneName: "America/Los_Angeles", offsetHours: -8 },
      // PST (UTC-8)
      { timeZoneName: "America/Toronto", offsetHours: -5 },
      // EST (UTC-5)
      { timeZoneName: "America/Vancouver", offsetHours: -8 },
      // PST (UTC-8)
      { timeZoneName: "Europe/London", offsetHours: 0 },
      // GMT (UTC+0)
      { timeZoneName: "Europe/Paris", offsetHours: 1 },
      // CET (UTC+1)
      { timeZoneName: "Europe/Berlin", offsetHours: 1 },
      // CET (UTC+1)
      { timeZoneName: "Europe/Rome", offsetHours: 1 },
      // CET (UTC+1)
      { timeZoneName: "Europe/Madrid", offsetHours: 1 },
      // CET (UTC+1)
      { timeZoneName: "Europe/Amsterdam", offsetHours: 1 },
      // CET (UTC+1)
      { timeZoneName: "Europe/Stockholm", offsetHours: 1 },
      // CET (UTC+1)
      { timeZoneName: "Europe/Moscow", offsetHours: 3 },
      // MSK (UTC+3)
      { timeZoneName: "Asia/Tokyo", offsetHours: 9 },
      // JST (UTC+9)
      { timeZoneName: "Asia/Shanghai", offsetHours: 8 },
      // CST (UTC+8)
      { timeZoneName: "Asia/Hong_Kong", offsetHours: 8 },
      // HKT (UTC+8)
      { timeZoneName: "Asia/Singapore", offsetHours: 8 },
      // SGT (UTC+8)
      { timeZoneName: "Asia/Seoul", offsetHours: 9 },
      // KST (UTC+9)
      { timeZoneName: "Asia/Mumbai", offsetHours: 5.5 },
      // IST (UTC+5:30)
      { timeZoneName: "Asia/Dubai", offsetHours: 4 },
      // GST (UTC+4)
      { timeZoneName: "Australia/Sydney", offsetHours: 10 },
      // AEST (UTC+10)
      { timeZoneName: "Australia/Melbourne", offsetHours: 10 },
      // AEST (UTC+10)
      { timeZoneName: "Australia/Perth", offsetHours: 8 },
      // AWST (UTC+8)
      { timeZoneName: "Pacific/Auckland", offsetHours: 12 },
      // NZST (UTC+12)
      { timeZoneName: "America/Sao_Paulo", offsetHours: -3 },
      // BRT (UTC-3)
      { timeZoneName: "America/Mexico_City", offsetHours: -6 },
      // CST (UTC-6)
      { timeZoneName: "Africa/Cairo", offsetHours: 2 },
      // EET (UTC+2)
      { timeZoneName: "Africa/Johannesburg", offsetHours: 2 }
      // SAST (UTC+2)
    ];
    const results = [];
    for (const tz of timezones) {
      const existing = await repo.findFirst({ timeZoneName: tz.timeZoneName });
      if (!existing) {
        const created = await repo.insert(tz);
        results.push(created);
      }
    }
    return results;
  }
  static async search(query) {
    const repo = remult.repo(TimeZone);
    return await repo.find({
      where: {
        timeZoneName: { $contains: query }
      },
      orderBy: { timeZoneName: "asc" }
    });
  }
  static async getByCurrentTime() {
    const repo = remult.repo(TimeZone);
    const now = /* @__PURE__ */ new Date();
    const currentOffset = -now.getTimezoneOffset() / 60;
    return await repo.find({
      where: { offsetHours: currentOffset },
      orderBy: { timeZoneName: "asc" }
    });
  }
  static async findPositiveOffsets() {
    const repo = remult.repo(TimeZone);
    return await repo.find({
      where: { offsetHours: { $gt: 0 } },
      orderBy: { offsetHours: "asc" }
    });
  }
  static async findNegativeOffsets() {
    const repo = remult.repo(TimeZone);
    return await repo.find({
      where: { offsetHours: { $lt: 0 } },
      orderBy: { offsetHours: "desc" }
    });
  }
  static async bulkInsert(timezones) {
    const repo = remult.repo(TimeZone);
    const results = [];
    for (const tz of timezones) {
      if (tz.timeZoneName) {
        const existing = await repo.findFirst({ timeZoneName: tz.timeZoneName });
        if (!existing) {
          const created = await repo.insert(tz);
          results.push(created);
        }
      }
    }
    return results;
  }
  static async updateOffset(name, offsetHours) {
    const repo = remult.repo(TimeZone);
    const item = await repo.findFirst({ timeZoneName: name });
    if (item) {
      return await repo.update(item.id, {
        offsetHours
      });
    }
    throw new Error("Timezone not found");
  }
}
__decorateClass$3([
  BackendMethod({ allowed: true })
], TimeZoneController, "findAll");
__decorateClass$3([
  BackendMethod({ allowed: true })
], TimeZoneController, "findById");
__decorateClass$3([
  BackendMethod({ allowed: true })
], TimeZoneController, "findByName");
__decorateClass$3([
  BackendMethod({ allowed: true })
], TimeZoneController, "findByOffset");
__decorateClass$3([
  BackendMethod({ allowed: true })
], TimeZoneController, "findByRegion");
__decorateClass$3([
  BackendMethod({ allowed: true })
], TimeZoneController, "findCommon");
__decorateClass$3([
  BackendMethod({ allowed: "admin" })
], TimeZoneController, "insert");
__decorateClass$3([
  BackendMethod({ allowed: "admin" })
], TimeZoneController, "update");
__decorateClass$3([
  BackendMethod({ allowed: "admin" })
], TimeZoneController, "delete");
__decorateClass$3([
  BackendMethod({ allowed: "admin" })
], TimeZoneController, "deleteById");
__decorateClass$3([
  BackendMethod({ allowed: "admin" })
], TimeZoneController, "seed");
__decorateClass$3([
  BackendMethod({ allowed: true })
], TimeZoneController, "search");
__decorateClass$3([
  BackendMethod({ allowed: true })
], TimeZoneController, "getByCurrentTime");
__decorateClass$3([
  BackendMethod({ allowed: true })
], TimeZoneController, "findPositiveOffsets");
__decorateClass$3([
  BackendMethod({ allowed: true })
], TimeZoneController, "findNegativeOffsets");
__decorateClass$3([
  BackendMethod({ allowed: "admin" })
], TimeZoneController, "bulkInsert");
__decorateClass$3([
  BackendMethod({ allowed: "admin" })
], TimeZoneController, "updateOffset");
var __defProp$2 = Object.defineProperty;
var __getOwnPropDesc$2 = Object.getOwnPropertyDescriptor;
var __decorateClass$2 = (decorators, target, key, kind) => {
  var result = __getOwnPropDesc$2(target, key);
  for (var i = decorators.length - 1, decorator; i >= 0; i--)
    if (decorator = decorators[i])
      result = decorator(target, key, result) || result;
  if (result) __defProp$2(target, key, result);
  return result;
};
class WorkdaySettingController {
  static async findAll() {
    const repo = remult.repo(WorkdaySetting$1);
    return await repo.find({
      include: {
        organization: true
      },
      orderBy: { createdAt: "desc" }
    });
  }
  static async findById(id) {
    const repo = remult.repo(WorkdaySetting$1);
    return await repo.findId(id, {
      include: {
        organization: true
      }
    });
  }
  static async findByOrganization(organizationId) {
    const repo = remult.repo(WorkdaySetting$1);
    return await repo.find({
      where: { organizationId },
      include: {
        organization: true
      },
      orderBy: { createdAt: "desc" }
    });
  }
  static async findByDayOfWeek(dayOfWeek) {
    const repo = remult.repo(WorkdaySetting$1);
    return await repo.find({
      where: { dayOfWeek },
      include: {
        organization: true
      },
      orderBy: { startTime: "asc" }
    });
  }
  static async findActive() {
    const repo = remult.repo(WorkdaySetting$1);
    return await repo.find({
      where: { isActive: true },
      include: {
        organization: true
      },
      orderBy: { dayOfWeek: "asc", startTime: "asc" }
    });
  }
  static async insert(workdaySetting) {
    const repo = remult.repo(WorkdaySetting$1);
    if (!remult.user?.roles?.includes(RolesType.Admin)) {
      const userOrganizationId = remult.user?.organizationId;
      if (userOrganizationId !== workdaySetting.organizationId) {
        throw new Error("You can only create workday settings for your own organization");
      }
    }
    return await repo.insert(workdaySetting);
  }
  static async update(id, workdaySetting) {
    const repo = remult.repo(WorkdaySetting$1);
    if (!remult.user?.roles?.includes(RolesType.Admin)) {
      const existingWorkdaySetting = await repo.findId(id);
      if (!existingWorkdaySetting) {
        throw new Error("Workday setting not found");
      }
      const userOrganizationId = remult.user?.organizationId;
      if (userOrganizationId !== existingWorkdaySetting.organizationId) {
        throw new Error("You can only update workday settings for your own organization");
      }
    }
    return await repo.update(id, workdaySetting);
  }
  static async delete(workdaySetting) {
    const repo = remult.repo(WorkdaySetting$1);
    if (!remult.user?.roles?.includes(RolesType.Admin)) {
      const userOrganizationId = remult.user?.organizationId;
      if (userOrganizationId !== workdaySetting.organizationId) {
        throw new Error("You can only delete workday settings for your own organization");
      }
    }
    return await repo.delete(workdaySetting);
  }
  static async deleteById(id) {
    const repo = remult.repo(WorkdaySetting$1);
    const item = await repo.findId(id);
    if (item) {
      if (!remult.user?.roles?.includes(RolesType.Admin)) {
        const userOrganizationId = remult.user?.organizationId;
        if (userOrganizationId !== item.organizationId) {
          throw new Error("You can only delete workday settings for your own organization");
        }
      }
      return await repo.delete(item);
    }
    throw new Error("Workday setting not found");
  }
  static async toggleActive(id) {
    const repo = remult.repo(WorkdaySetting$1);
    const item = await repo.findId(id);
    if (item) {
      if (!remult.user?.roles?.includes(RolesType.Admin)) {
        const userOrganizationId = remult.user?.organizationId;
        if (userOrganizationId !== item.organizationId) {
          throw new Error("You can only modify workday settings for your own organization");
        }
      }
      return await repo.save({
        ...item,
        isActive: !item.isActive
      });
    }
    throw new Error("Workday setting not found");
  }
  static async updateHours(id, startTime, endTime) {
    const repo = remult.repo(WorkdaySetting$1);
    const item = await repo.findId(id);
    if (item) {
      if (!remult.user?.roles?.includes("admin")) {
        const userOrganizationId = remult.user?.organizationId;
        if (userOrganizationId !== item.organizationId) {
          throw new Error("You can only modify workday settings for your own organization");
        }
      }
      return await repo.save({
        ...item,
        startTime,
        endTime
      });
    }
    throw new Error("Workday setting not found");
  }
  static async getWeekSchedule(organizationId) {
    const repo = remult.repo(WorkdaySetting$1);
    const settings = await repo.find({
      where: { organizationId, isActive: true },
      orderBy: { dayOfWeek: "asc" }
    });
    const weekSchedule = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"].map((dayName, index) => ({
      dayOfWeek: index,
      dayName,
      settings: settings.filter((s) => s.dayOfWeek === dayName)
    }));
    return weekSchedule;
  }
  static async createWeekSchedule(organizationId, schedule) {
    const repo = remult.repo(WorkdaySetting$1);
    if (!remult.user?.roles?.includes("admin")) {
      const userOrganizationId = remult.user?.organizationId;
      if (userOrganizationId !== organizationId) {
        throw new Error("You can only create workday settings for your own organization");
      }
    }
    const results = [];
    for (const daySchedule of schedule) {
      const created = await repo.insert({
        organizationId,
        ...daySchedule
      });
      results.push(created);
    }
    return results;
  }
  static async updateWeekSchedule(organizationId, schedule) {
    const repo = remult.repo(WorkdaySetting$1);
    if (!remult.user?.roles?.includes("admin")) {
      const userOrganizationId = remult.user?.organizationId;
      if (userOrganizationId !== organizationId) {
        throw new Error("You can only update workday settings for your own organization");
      }
    }
    const existingSettings = await repo.find({
      where: { organizationId }
    });
    for (const setting of existingSettings) {
      await repo.delete(setting);
    }
    const results = [];
    for (const daySchedule of schedule) {
      const created = await repo.insert({
        organizationId,
        ...daySchedule
      });
      results.push(created);
    }
    return results;
  }
  static async getBusinessHours(organizationId) {
    const repo = remult.repo(WorkdaySetting$1);
    const settings = await repo.find({
      where: { organizationId, isActive: true },
      orderBy: { dayOfWeek: "asc", startTime: "asc" }
    });
    const businessHours = {};
    settings.forEach((setting) => {
      if (!businessHours[setting.dayOfWeek]) {
        businessHours[setting.dayOfWeek] = {
          start: setting.startTime,
          end: setting.endTime
        };
        if (setting.startTime < businessHours[setting.dayOfWeek].start) {
          businessHours[setting.dayOfWeek].start = setting.startTime;
        }
        if (setting.endTime > businessHours[setting.dayOfWeek].end) {
          businessHours[setting.dayOfWeek].end = setting.endTime;
        }
      }
    });
    return businessHours;
  }
  static async copySchedule(fromOrganizationId, toOrganizationId) {
    const repo = remult.repo(WorkdaySetting$1);
    if (!remult.user?.roles?.includes("admin")) {
      const userOrganizationId = remult.user?.organizationId;
      if (userOrganizationId !== fromOrganizationId && userOrganizationId !== toOrganizationId) {
        throw new Error("You can only copy schedules for your own organization");
      }
    }
    const sourceSettings = await repo.find({
      where: { organizationId: fromOrganizationId }
    });
    const results = [];
    for (const setting of sourceSettings) {
      const copied = await repo.insert({
        organizationId: toOrganizationId,
        dayOfWeek: setting.dayOfWeek,
        startTime: setting.startTime,
        endTime: setting.endTime,
        isActive: setting.isActive
      });
      results.push(copied);
    }
    return results;
  }
}
__decorateClass$2([
  BackendMethod({ allowed: Allow.authenticated })
], WorkdaySettingController, "findAll");
__decorateClass$2([
  BackendMethod({ allowed: Allow.authenticated })
], WorkdaySettingController, "findById");
__decorateClass$2([
  BackendMethod({ allowed: Allow.authenticated })
], WorkdaySettingController, "findByOrganization");
__decorateClass$2([
  BackendMethod({ allowed: Allow.authenticated })
], WorkdaySettingController, "findByDayOfWeek");
__decorateClass$2([
  BackendMethod({ allowed: Allow.authenticated })
], WorkdaySettingController, "findActive");
__decorateClass$2([
  BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin] })
], WorkdaySettingController, "insert");
__decorateClass$2([
  BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin] })
], WorkdaySettingController, "update");
__decorateClass$2([
  BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin] })
], WorkdaySettingController, "delete");
__decorateClass$2([
  BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin] })
], WorkdaySettingController, "deleteById");
__decorateClass$2([
  BackendMethod({ allowed: [RolesType.Admin, RolesType.OrgAdmin, RolesType.TeamLocationAdmin] })
], WorkdaySettingController, "toggleActive");
__decorateClass$2([
  BackendMethod({ allowed: ["admin", "organisation-admin", "team-location-admin"] })
], WorkdaySettingController, "updateHours");
__decorateClass$2([
  BackendMethod({ allowed: Allow.authenticated })
], WorkdaySettingController, "getWeekSchedule");
__decorateClass$2([
  BackendMethod({ allowed: ["admin", "organisation-admin", "team-location-admin"] })
], WorkdaySettingController, "createWeekSchedule");
__decorateClass$2([
  BackendMethod({ allowed: ["admin", "organisation-admin", "team-location-admin"] })
], WorkdaySettingController, "updateWeekSchedule");
__decorateClass$2([
  BackendMethod({ allowed: Allow.authenticated })
], WorkdaySettingController, "getBusinessHours");
__decorateClass$2([
  BackendMethod({ allowed: ["admin", "organisation-admin", "team-location-admin"] })
], WorkdaySettingController, "copySchedule");
var __defProp$1 = Object.defineProperty;
var __getOwnPropDesc$1 = Object.getOwnPropertyDescriptor;
var __decorateClass$1 = (decorators, target, key, kind) => {
  var result = __getOwnPropDesc$1(target, key);
  for (var i = decorators.length - 1, decorator; i >= 0; i--)
    if (decorator = decorators[i])
      result = decorator(target, key, result) || result;
  if (result) __defProp$1(target, key, result);
  return result;
};
class OrgBankDetailController {
  static async findAll() {
    const repo = remult.repo(OrgBankDetail$1);
    return await repo.find({
      include: {
        organization: true
      },
      orderBy: { createdAt: "desc" }
    });
  }
  static async findById(id) {
    const repo = remult.repo(OrgBankDetail$1);
    return await repo.findId(id, {
      include: {
        organization: true
      }
    });
  }
  static async findByOrganization(organizationId) {
    const repo = remult.repo(OrgBankDetail$1);
    return await repo.find({
      where: { organizationId },
      include: {
        organization: true
      },
      orderBy: { createdAt: "desc" }
    });
  }
  static async findByBankName(bankName) {
    const repo = remult.repo(OrgBankDetail$1);
    return await repo.find({
      where: { bankName: { $contains: bankName } },
      include: {
        organization: true
      },
      orderBy: { bankName: "asc" }
    });
  }
  static async findByAccountType(accountType) {
    const repo = remult.repo(OrgBankDetail$1);
    return await repo.find({
      where: { accountType },
      include: {
        organization: true
      },
      orderBy: { bankName: "asc" }
    });
  }
  static async insert(bankDetail) {
    const repo = remult.repo(OrgBankDetail$1);
    if (!remult.user?.roles?.includes("admin")) {
      const userOrganizationId = remult.user?.organizationId;
      if (userOrganizationId !== bankDetail.organizationId) {
        throw new Error("You can only create bank details for your own organization");
      }
    }
    return await repo.insert(bankDetail);
  }
  static async update(id, bankDetail) {
    const repo = remult.repo(OrgBankDetail$1);
    if (!remult.user?.roles?.includes("admin")) {
      const existingBankDetail = await repo.findId(id);
      if (!existingBankDetail) {
        throw new Error("Bank detail not found");
      }
      const userOrganizationId = remult.user?.organizationId;
      if (userOrganizationId !== existingBankDetail.organizationId) {
        throw new Error("You can only update bank details for your own organization");
      }
    }
    return await repo.update(id, bankDetail);
  }
  static async delete(bankDetail) {
    const repo = remult.repo(OrgBankDetail$1);
    if (!remult.user?.roles?.includes("admin")) {
      const userOrganizationId = remult.user?.organizationId;
      if (userOrganizationId !== bankDetail.organizationId) {
        throw new Error("You can only delete bank details for your own organization");
      }
    }
    return await repo.delete(bankDetail);
  }
  static async deleteById(id) {
    const repo = remult.repo(OrgBankDetail$1);
    const item = await repo.findId(id);
    if (item) {
      if (!remult.user?.roles?.includes("admin")) {
        const userOrganizationId = remult.user?.organizationId;
        if (userOrganizationId !== item.organizationId) {
          throw new Error("You can only delete bank details for your own organization");
        }
      }
      return await repo.delete(item);
    }
    throw new Error("Bank detail not found");
  }
  static async updateAccountInfo(id, accountInfo) {
    const repo = remult.repo(OrgBankDetail$1);
    const item = await repo.findId(id);
    if (item) {
      if (!remult.user?.roles?.includes("admin")) {
        const userOrganizationId = remult.user?.organizationId;
        if (userOrganizationId !== item.organizationId) {
          throw new Error("You can only modify bank details for your own organization");
        }
      }
      return await repo.save({
        ...item,
        ...accountInfo
      });
    }
    throw new Error("Bank detail not found");
  }
  static async updateBankInfo(id, bankInfo) {
    const repo = remult.repo(OrgBankDetail$1);
    const item = await repo.findId(id);
    if (item) {
      if (!remult.user?.roles?.includes("admin")) {
        const userOrganizationId = remult.user?.organizationId;
        if (userOrganizationId !== item.organizationId) {
          throw new Error("You can only modify bank details for your own organization");
        }
      }
      return await repo.save({
        ...item,
        ...bankInfo
      });
    }
    throw new Error("Bank detail not found");
  }
  static async getStats(organizationId) {
    const repo = remult.repo(OrgBankDetail$1);
    const whereClause = organizationId ? { organizationId } : {};
    const bankDetails = await repo.find({
      where: whereClause
    });
    const accountTypeStats = {};
    bankDetails.forEach((bd) => {
      if (bd.accountType) {
        accountTypeStats[bd.accountType] = (accountTypeStats[bd.accountType] || 0) + 1;
      }
    });
    const countryStats = {};
    bankDetails.forEach((bd) => {
      if (bd.bankCountry) {
        countryStats[bd.bankCountry] = (countryStats[bd.bankCountry] || 0) + 1;
      }
    });
    return {
      totalBankDetails: bankDetails.length,
      accountTypeDistribution: accountTypeStats,
      countryDistribution: countryStats
    };
  }
  static async validateAccountNumber(id) {
    const repo = remult.repo(OrgBankDetail$1);
    const item = await repo.findId(id);
    if (item) {
      if (!remult.user?.roles?.includes("admin")) {
        const userOrganizationId = remult.user?.organizationId;
        if (userOrganizationId !== item.organizationId) {
          throw new Error("You can only validate bank details for your own organization");
        }
      }
      const isValid = item.accountNumber && item.accountNumber.length >= 8;
      return {
        isValid,
        message: isValid ? "Account number format is valid" : "Invalid account number format",
        accountNumber: item.accountNumber,
        bankName: item.bankName,
        timestamp: /* @__PURE__ */ new Date()
      };
    }
    throw new Error("Bank detail not found");
  }
  static async getSupportedAccountTypes() {
    return [
      { value: "checking", label: "Checking Account", description: "Standard checking account" },
      { value: "savings", label: "Savings Account", description: "Standard savings account" },
      { value: "business_checking", label: "Business Checking", description: "Business checking account" },
      { value: "business_savings", label: "Business Savings", description: "Business savings account" },
      { value: "money_market", label: "Money Market", description: "Money market account" },
      { value: "certificate_deposit", label: "Certificate of Deposit", description: "CD account" }
    ];
  }
  static async maskSensitiveData(bankDetail) {
    return {
      ...bankDetail,
      accountNumber: bankDetail.accountNumber ? "****" + bankDetail.accountNumber.slice(-4) : void 0,
      routingNumber: bankDetail.routingNumber ? "****" + bankDetail.routingNumber.slice(-4) : void 0,
      swiftCode: bankDetail.swiftCode ? "****" + bankDetail.swiftCode.slice(-4) : void 0,
      iban: bankDetail.iban ? "****" + bankDetail.iban.slice(-4) : void 0
    };
  }
}
__decorateClass$1([
  BackendMethod({ allowed: Allow.authenticated })
], OrgBankDetailController, "findAll");
__decorateClass$1([
  BackendMethod({ allowed: Allow.authenticated })
], OrgBankDetailController, "findById");
__decorateClass$1([
  BackendMethod({ allowed: Allow.authenticated })
], OrgBankDetailController, "findByOrganization");
__decorateClass$1([
  BackendMethod({ allowed: Allow.authenticated })
], OrgBankDetailController, "findByBankName");
__decorateClass$1([
  BackendMethod({ allowed: Allow.authenticated })
], OrgBankDetailController, "findByAccountType");
__decorateClass$1([
  BackendMethod({ allowed: ["admin", "organisation-admin"] })
], OrgBankDetailController, "insert");
__decorateClass$1([
  BackendMethod({ allowed: ["admin", "organisation-admin"] })
], OrgBankDetailController, "update");
__decorateClass$1([
  BackendMethod({ allowed: ["admin", "organisation-admin"] })
], OrgBankDetailController, "delete");
__decorateClass$1([
  BackendMethod({ allowed: ["admin", "organisation-admin"] })
], OrgBankDetailController, "deleteById");
__decorateClass$1([
  BackendMethod({ allowed: ["admin", "organisation-admin"] })
], OrgBankDetailController, "updateAccountInfo");
__decorateClass$1([
  BackendMethod({ allowed: ["admin", "organisation-admin"] })
], OrgBankDetailController, "updateBankInfo");
__decorateClass$1([
  BackendMethod({ allowed: Allow.authenticated })
], OrgBankDetailController, "getStats");
__decorateClass$1([
  BackendMethod({ allowed: ["admin", "organisation-admin"] })
], OrgBankDetailController, "validateAccountNumber");
__decorateClass$1([
  BackendMethod({ allowed: "admin" })
], OrgBankDetailController, "getSupportedAccountTypes");
__decorateClass$1([
  BackendMethod({ allowed: ["admin", "organisation-admin"] })
], OrgBankDetailController, "maskSensitiveData");
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __decorateClass = (decorators, target, key, kind) => {
  var result = __getOwnPropDesc(target, key);
  for (var i = decorators.length - 1, decorator; i >= 0; i--)
    if (decorator = decorators[i])
      result = decorator(target, key, result) || result;
  if (result) __defProp(target, key, result);
  return result;
};
class OrgPaymentProviderController {
  static async findAll() {
    const repo = remult.repo(OrgPaymentProvider$1);
    return await repo.find({
      include: {
        organization: true
      },
      orderBy: { createdAt: "desc" }
    });
  }
  static async findById(id) {
    const repo = remult.repo(OrgPaymentProvider$1);
    return await repo.findId(id, {
      include: {
        organization: true
      }
    });
  }
  static async findByOrganization(organizationId) {
    const repo = remult.repo(OrgPaymentProvider$1);
    return await repo.find({
      where: { organizationId },
      include: {
        organization: true
      },
      orderBy: { createdAt: "desc" }
    });
  }
  static async findByProvider(providerName) {
    const repo = remult.repo(OrgPaymentProvider$1);
    return await repo.find({
      where: { providerName },
      include: {
        organization: true
      },
      orderBy: { createdAt: "desc" }
    });
  }
  static async findActive() {
    const repo = remult.repo(OrgPaymentProvider$1);
    return await repo.find({
      where: { isActive: true },
      include: {
        organization: true
      },
      orderBy: { createdAt: "desc" }
    });
  }
  static async insert(paymentProvider) {
    const repo = remult.repo(OrgPaymentProvider$1);
    if (!remult.user?.roles?.includes("admin")) {
      const userOrganizationId = remult.user?.organizationId;
      if (userOrganizationId !== paymentProvider.organizationId) {
        throw new Error("You can only create payment providers for your own organization");
      }
    }
    return await repo.insert(paymentProvider);
  }
  static async update(id, paymentProvider) {
    const repo = remult.repo(OrgPaymentProvider$1);
    if (!remult.user?.roles?.includes("admin")) {
      const existingPaymentProvider = await repo.findId(id);
      if (!existingPaymentProvider) {
        throw new Error("Payment provider not found");
      }
      const userOrganizationId = remult.user?.organizationId;
      if (userOrganizationId !== existingPaymentProvider.organizationId) {
        throw new Error("You can only update payment providers for your own organization");
      }
    }
    return await repo.update(id, paymentProvider);
  }
  static async delete(paymentProvider) {
    const repo = remult.repo(OrgPaymentProvider$1);
    if (!remult.user?.roles?.includes("admin")) {
      const userOrganizationId = remult.user?.organizationId;
      if (userOrganizationId !== paymentProvider.organizationId) {
        throw new Error("You can only delete payment providers for your own organization");
      }
    }
    return await repo.delete(paymentProvider);
  }
  static async deleteById(id) {
    const repo = remult.repo(OrgPaymentProvider$1);
    const item = await repo.findId(id);
    if (item) {
      if (!remult.user?.roles?.includes("admin")) {
        const userOrganizationId = remult.user?.organizationId;
        if (userOrganizationId !== item.organizationId) {
          throw new Error("You can only delete payment providers for your own organization");
        }
      }
      return await repo.delete(item);
    }
    throw new Error("Payment provider not found");
  }
  static async toggleActive(id) {
    const repo = remult.repo(OrgPaymentProvider$1);
    const item = await repo.findId(id);
    if (item) {
      if (!remult.user?.roles?.includes("admin")) {
        const userOrganizationId = remult.user?.organizationId;
        if (userOrganizationId !== item.organizationId) {
          throw new Error("You can only modify payment providers for your own organization");
        }
      }
      return await repo.save({
        ...item,
        isActive: !item.isActive
      });
    }
    throw new Error("Payment provider not found");
  }
  static async updateCredentials(id, credentials) {
    const repo = remult.repo(OrgPaymentProvider$1);
    const item = await repo.findId(id);
    if (item) {
      if (!remult.user?.roles?.includes("admin")) {
        const userOrganizationId = remult.user?.organizationId;
        if (userOrganizationId !== item.organizationId) {
          throw new Error("You can only modify payment providers for your own organization");
        }
      }
      return await repo.save({
        ...item,
        ...credentials
      });
    }
    throw new Error("Payment provider not found");
  }
  static async testConnection(id) {
    const repo = remult.repo(OrgPaymentProvider$1);
    const item = await repo.findId(id);
    if (item) {
      if (!remult.user?.roles?.includes("admin")) {
        const userOrganizationId = remult.user?.organizationId;
        if (userOrganizationId !== item.organizationId) {
          throw new Error("You can only test payment providers for your own organization");
        }
      }
      return {
        success: true,
        message: "Connection test successful",
        provider: item.providerName,
        timestamp: /* @__PURE__ */ new Date()
      };
    }
    throw new Error("Payment provider not found");
  }
  static async getStats(organizationId) {
    const repo = remult.repo(OrgPaymentProvider$1);
    const whereClause = organizationId ? { organizationId } : {};
    const providers = await repo.find({
      where: whereClause
    });
    const providerStats = {};
    providers.forEach((p) => {
      providerStats[p.providerName] = (providerStats[p.providerName] || 0) + 1;
    });
    return {
      totalProviders: providers.length,
      activeProviders: providers.filter((p) => p.isActive).length,
      providerDistribution: providerStats
    };
  }
  static async getSupportedProviders() {
    return [
      { name: "stripe", displayName: "Stripe", description: "Online payment processing" },
      { name: "paypal", displayName: "PayPal", description: "PayPal payment gateway" },
      { name: "square", displayName: "Square", description: "Square payment processing" },
      { name: "razorpay", displayName: "Razorpay", description: "Indian payment gateway" },
      { name: "flutterwave", displayName: "Flutterwave", description: "African payment gateway" },
      { name: "mollie", displayName: "Mollie", description: "European payment gateway" }
    ];
  }
}
__decorateClass([
  BackendMethod({ allowed: Allow.authenticated })
], OrgPaymentProviderController, "findAll");
__decorateClass([
  BackendMethod({ allowed: Allow.authenticated })
], OrgPaymentProviderController, "findById");
__decorateClass([
  BackendMethod({ allowed: Allow.authenticated })
], OrgPaymentProviderController, "findByOrganization");
__decorateClass([
  BackendMethod({ allowed: Allow.authenticated })
], OrgPaymentProviderController, "findByProvider");
__decorateClass([
  BackendMethod({ allowed: Allow.authenticated })
], OrgPaymentProviderController, "findActive");
__decorateClass([
  BackendMethod({ allowed: ["admin", "organisation-admin"] })
], OrgPaymentProviderController, "insert");
__decorateClass([
  BackendMethod({ allowed: ["admin", "organisation-admin"] })
], OrgPaymentProviderController, "update");
__decorateClass([
  BackendMethod({ allowed: ["admin", "organisation-admin"] })
], OrgPaymentProviderController, "delete");
__decorateClass([
  BackendMethod({ allowed: ["admin", "organisation-admin"] })
], OrgPaymentProviderController, "deleteById");
__decorateClass([
  BackendMethod({ allowed: ["admin", "organisation-admin"] })
], OrgPaymentProviderController, "toggleActive");
__decorateClass([
  BackendMethod({ allowed: ["admin", "organisation-admin"] })
], OrgPaymentProviderController, "updateCredentials");
__decorateClass([
  BackendMethod({ allowed: ["admin", "organisation-admin"] })
], OrgPaymentProviderController, "testConnection");
__decorateClass([
  BackendMethod({ allowed: Allow.authenticated })
], OrgPaymentProviderController, "getStats");
__decorateClass([
  BackendMethod({ allowed: "admin" })
], OrgPaymentProviderController, "getSupportedProviders");
function cn(...classes) {
  return classes.filter(Boolean).join(" ");
}
const isBrowser = typeof document !== "undefined";
let toastsCounter = 0;
class ToastState {
  toasts = [];
  heights = [];
  #findToastIdx = (id) => {
    const idx = this.toasts.findIndex((toast2) => toast2.id === id);
    if (idx === -1) return null;
    return idx;
  };
  addToast = (data) => {
    if (!isBrowser) return;
    this.toasts.unshift(data);
  };
  updateToast = ({ id, data, type, message }) => {
    const toastIdx = this.toasts.findIndex((toast2) => toast2.id === id);
    const toastToUpdate = this.toasts[toastIdx];
    this.toasts[toastIdx] = {
      ...toastToUpdate,
      ...data,
      id,
      title: message,
      type,
      updated: true
    };
  };
  create = (data) => {
    const { message, ...rest } = data;
    const id = typeof data?.id === "number" || data.id && data.id?.length > 0 ? data.id : toastsCounter++;
    const dismissable = data.dismissable === void 0 ? true : data.dismissable;
    const type = data.type === void 0 ? "default" : data.type;
    run(() => {
      const alreadyExists = this.toasts.find((toast2) => toast2.id === id);
      if (alreadyExists) {
        this.updateToast({ id, data, type, message, dismissable });
      } else {
        this.addToast({ ...rest, id, title: message, dismissable, type });
      }
    });
    return id;
  };
  dismiss = (id) => {
    run(() => {
      if (id === void 0) {
        this.toasts = this.toasts.map((toast2) => ({ ...toast2, dismiss: true }));
        return;
      }
      const toastIdx = this.toasts.findIndex((toast2) => toast2.id === id);
      if (this.toasts[toastIdx]) {
        this.toasts[toastIdx] = { ...this.toasts[toastIdx], dismiss: true };
      }
    });
    return id;
  };
  remove = (id) => {
    if (id === void 0) {
      this.toasts = [];
      return;
    }
    const toastIdx = this.#findToastIdx(id);
    if (toastIdx === null) return;
    this.toasts.splice(toastIdx, 1);
    return id;
  };
  message = (message, data) => {
    return this.create({ ...data, type: "default", message });
  };
  error = (message, data) => {
    return this.create({ ...data, type: "error", message });
  };
  success = (message, data) => {
    return this.create({ ...data, type: "success", message });
  };
  info = (message, data) => {
    return this.create({ ...data, type: "info", message });
  };
  warning = (message, data) => {
    return this.create({ ...data, type: "warning", message });
  };
  loading = (message, data) => {
    return this.create({ ...data, type: "loading", message });
  };
  promise = (promise, data) => {
    if (!data) {
      return;
    }
    let id = void 0;
    if (data.loading !== void 0) {
      id = this.create({
        ...data,
        promise,
        type: "loading",
        message: typeof data.loading === "string" ? data.loading : data.loading()
      });
    }
    const p = promise instanceof Promise ? promise : promise();
    let shouldDismiss = id !== void 0;
    p.then((response) => {
      if (typeof response === "object" && response && "ok" in response && typeof response.ok === "boolean" && !response.ok) {
        shouldDismiss = false;
        const message = constructPromiseErrorMessage(response);
        this.create({ id, type: "error", message });
      } else if (data.success !== void 0) {
        shouldDismiss = false;
        const message = typeof data.success === "function" ? data.success(response) : data.success;
        this.create({ id, type: "success", message });
      }
    }).catch((error) => {
      if (data.error !== void 0) {
        shouldDismiss = false;
        const message = typeof data.error === "function" ? data.error(error) : data.error;
        this.create({ id, type: "error", message });
      }
    }).finally(() => {
      if (shouldDismiss) {
        this.dismiss(id);
        id = void 0;
      }
      data.finally?.();
    });
    return id;
  };
  custom = (component, data) => {
    const id = data?.id || toastsCounter++;
    this.create({ component, id, ...data });
    return id;
  };
  removeHeight = (id) => {
    this.heights = this.heights.filter((height) => height.toastId !== id);
  };
  setHeight = (data) => {
    const toastIdx = this.#findToastIdx(data.toastId);
    if (toastIdx === null) {
      this.heights.push(data);
      return;
    }
    this.heights[toastIdx] = data;
  };
  reset = () => {
    this.toasts = [];
    this.heights = [];
  };
}
function constructPromiseErrorMessage(response) {
  if (response && typeof response === "object" && "status" in response) {
    return `HTTP error! Status: ${response.status}`;
  }
  return `Error! ${response}`;
}
const toastState = new ToastState();
function toastFunction(message, data) {
  return toastState.create({ message, ...data });
}
class SonnerState {
  /**
   * A derived state of the toasts that are not dismissed.
   */
  #activeToasts = derived(() => toastState.toasts.filter((toast2) => !toast2.dismiss));
  get toasts() {
    return this.#activeToasts();
  }
}
const basicToast = toastFunction;
const toast = Object.assign(basicToast, {
  success: toastState.success,
  info: toastState.info,
  warning: toastState.warning,
  error: toastState.error,
  custom: toastState.custom,
  message: toastState.message,
  promise: toastState.promise,
  dismiss: toastState.dismiss,
  loading: toastState.loading,
  getActiveToasts: () => {
    return toastState.toasts.filter((toast2) => !toast2.dismiss);
  }
});
const authClient = createAuthClient({
  plugins: [
    passkeyClient(),
    // Passkey authentication
    emailOTPClient(),
    // Email OTP authentication
    //jwtClient(), // JWT token support
    organizationClient({
      $inferAuth: {},
      teams: {
        enabled: true
      },
      ac,
      roles,
      schema: inferOrgAdditionalFields({
        organization: {
          additionalFields: {
            businessEmail: {
              type: "string",
              required: true,
              input: true
            },
            businessAddress: {
              type: "string",
              required: false,
              input: true
            },
            id: {
              type: "string",
              required: false,
              input: true
            }
          }
        }
      })
    }),
    // Organization management with teams and custom roles
    adminClient({
      ac,
      roles
    }),
    // Admin plugin for user management
    //sessionDataClientPlugin(), // Session data plugin
    inferAdditionalFields({
      user: {
        roles: { type: "string[]" },
        // Core user info
        givenName: { type: "string" },
        familyName: { type: "string" },
        displayName: { type: "string" },
        // Contact info
        phoneNumber: { type: "string" },
        phoneNumberVerified: { type: "boolean" },
        // Profile info
        image: { type: "string" },
        avatarUrl: { type: "string" },
        // Status
        userStatus: { type: "string" },
        userType: { type: "string" },
        acceptTerms: { type: "boolean" },
        authType: { type: "string" },
        metadata: { type: "string" },
        gender: { type: "string" }
      },
      session: {
        activeOrganizationId: { type: "string" },
        impersonatedBy: { type: "string" }
      }
    })
  ]
});
const {
  signIn,
  signUp,
  signOut,
  useSession,
  getSession
} = authClient;
const setupPasskey = async () => {
  const webAuthNSupport = await checkWebAuthnSupport();
  if (webAuthNSupport.isAvailable) {
    const passkeyList = await authClient.passkey.listUserPasskeys();
    if (passkeyList?.data && passkeyList.data.length > 0) return true;
    await authClient.passkey.addPasskey({
      authenticatorAttachment: "cross-platform",
      name: `Spendeed-${uuidv7()}`
    }, {
      onSuccess: () => {
        toast.success("Passkey registered successfully!");
      },
      onError: (error) => {
        console.warn("Passkey registration failed:", error?.error.message);
        toast.warning(
          "Passkey registration failed. You can set it up later in settings."
        );
      }
    });
  } else {
    return true;
  }
};
export {
  AppointmentType$1 as $,
  AppointmentController as A,
  TeamMember as B,
  CalendarViewTypes as C,
  Member$1 as D,
  Team$1 as E,
  Organization$1 as F,
  GenderTypes as G,
  User$1 as H,
  Invitation as I,
  Jwks as J,
  Session as K,
  Account as L,
  MemberInvite as M,
  Gender as N,
  OrgPaymentProviderController as O,
  Passkey as P,
  Role as Q,
  RolesType as R,
  SonnerState as S,
  TeamPlans as T,
  UserAccountTypes as U,
  Verification as V,
  TimeZone as W,
  StorageBucket as X,
  StorageFile as Y,
  UserRole$1 as Z,
  CalendarBlock$1 as _,
  SheetTypes as a,
  Appointment$1 as a0,
  OrgPaymentProvider$1 as a1,
  OrgBankDetail$1 as a2,
  WorkdaySetting$1 as a3,
  roles as a4,
  ac as a5,
  getDomain as a6,
  UserController as a7,
  TeamController as a8,
  getISessionData as a9,
  genderTypes as aa,
  rolesTypes as ab,
  timeGreeting as ac,
  getRandomColorName as ad,
  useSession as ae,
  settingsFormSchema as af,
  OrganizationController as ag,
  TeamMemberStatuses as ah,
  authClient as b,
  cn as c,
  toast as d,
  saveEmailLocally as e,
  signUpCodeFormSchema as f,
  getCopyRight as g,
  setupPasskey as h,
  initSuperJson as i,
  isUserInRole as j,
  getInitials as k,
  loginFormSchema as l,
  logOutFormSchema as m,
  newAppointmentFormSchema as n,
  addMemberFormSchema as o,
  paymentProviderFormSchema as p,
  OrgBankDetailController as q,
  bankAccountFormSchema as r,
  signUpFormSchema as s,
  toastState as t,
  formatTimeWithAmPm as u,
  appointmentStatuses as v,
  AppointmentTypeController as w,
  createAppointmentTypeFormSchema as x,
  appointmentRepeatTypes as y,
  deleteAppointmentTypeFormSchema as z
};
