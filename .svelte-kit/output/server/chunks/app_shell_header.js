import { A as head, I as attr_class, J as clsx, y as pop, w as push, B as escape_html } from "./index2.js";
import { c as cn } from "./shadcn.utils.js";
import "clsx";
function App_shell($$payload, $$props) {
  push();
  let { children, pageTitle = "", className = void 0 } = $$props;
  const currentPageTitle = `SpenDeed | ${pageTitle}`;
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>${escape_html(currentPageTitle)}</title>`;
  });
  $$payload.out.push(`<div${attr_class(clsx(cn("grid items-start gap-8", className)))}>`);
  if (children) {
    $$payload.out.push("<!--[-->");
    children?.($$payload);
    $$payload.out.push(`<!---->`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--></div>`);
  pop();
}
function App_shell_header($$payload, $$props) {
  let { children = null, heading, subHeading } = $$props;
  $$payload.out.push(`<div class="flex items-center justify-between"><div class="grid gap-1"><h1 class="text-lg font-semibold md:text-xl">${escape_html(heading)}</h1> `);
  if (subHeading) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<p class="md:text-md text-sm text-secondary-foreground">${escape_html(subHeading)}</p>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--></div> `);
  if (children) {
    $$payload.out.push("<!--[-->");
    children?.($$payload);
    $$payload.out.push(`<!---->`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--></div>`);
}
export {
  App_shell as A,
  App_shell_header as a
};
