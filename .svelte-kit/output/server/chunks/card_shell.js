import { B as escape_html } from "./index2.js";
import "clsx";
import { C as Card, a as Card_content } from "./card-content.js";
import { C as Card_header, a as Card_title, b as Card_description } from "./card-title.js";
function Card_shell($$payload, $$props) {
  let { children, rightSlot, heading, subHeading = "" } = $$props;
  $$payload.out.push(`<div class="grid gap-10"><!---->`);
  Card($$payload, {
    class: "w-full rounded-none",
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->`);
      Card_header($$payload2, {
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Card_title($$payload3, {
            class: "flex items-center justify-between",
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->${escape_html(heading)} `);
              if (rightSlot) {
                $$payload4.out.push("<!--[-->");
                $$payload4.out.push(`<div class="flex items-center space-x-2">`);
                rightSlot?.($$payload4);
                $$payload4.out.push(`<!----></div>`);
              } else {
                $$payload4.out.push("<!--[!-->");
              }
              $$payload4.out.push(`<!--]-->`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> <!---->`);
          Card_description($$payload3, {
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->${escape_html(subHeading)}`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!----> <!---->`);
      Card_content($$payload2, {
        children: ($$payload3) => {
          children?.($$payload3);
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!----></div>`);
}
export {
  Card_shell as C
};
