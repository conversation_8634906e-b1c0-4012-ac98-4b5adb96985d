import { w as push, Q as copy_payload, R as assign_payload, P as bind_props, y as pop, G as spread_props, F as spread_attributes, B as escape_html, J as clsx } from "./index2.js";
import { g as getUtilsStore } from "./sheet-content.js";
import { R as Root, D as Dialog_content, a as Dialog_header, b as Dialog_title, c as Dialog_description } from "./index9.js";
import { D as Dialog_trigger$1 } from "./dialog-trigger.js";
import { c as cn } from "./shadcn.utils.js";
import { u as useId, c as useDescription, d as box, m as mergeProps } from "./index5.js";
import "./states.svelte.js";
function Dialog_trigger($$payload, $$props) {
  push();
  let { ref = null, $$slots, $$events, ...restProps } = $$props;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out.push(`<!---->`);
    Dialog_trigger$1($$payload2, spread_props([
      { "data-slot": "dialog-trigger" },
      restProps,
      {
        get ref() {
          return ref;
        },
        set ref($$value) {
          ref = $$value;
          $$settled = false;
        }
      }
    ]));
    $$payload2.out.push(`<!---->`);
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref });
  pop();
}
function Description($$payload, $$props) {
  push();
  let {
    id = useId(),
    ref = null,
    children,
    child,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const descriptionState = useDescription({
    id: box.with(() => id),
    ref: box.with(() => ref, (v) => ref = v)
  });
  const mergedProps = mergeProps(restProps, descriptionState.props);
  if (child) {
    $$payload.out.push("<!--[-->");
    child($$payload, { props: mergedProps });
    $$payload.out.push(`<!---->`);
  } else {
    $$payload.out.push("<!--[!-->");
    $$payload.out.push(`<div${spread_attributes({ ...mergedProps }, null)}>`);
    children?.($$payload);
    $$payload.out.push(`<!----></div>`);
  }
  $$payload.out.push(`<!--]-->`);
  bind_props($$props, { ref });
  pop();
}
function Dynamic_modal($$payload, $$props) {
  push();
  let { componentsCollection } = $$props;
  const utilsStore = getUtilsStore();
  let CurrentComponent = null;
  let modalTitle = null;
  let dialogClass = "";
  let localComponentProps = {};
  let openModal = false;
  let modalDescription = null;
  let canClose = utilsStore.st?.dynamicModalItem?.canClose;
  const openStateChanged = (isOpen) => {
    if (!openModal) {
      utilsStore.closeDynamicSheet();
    }
  };
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out.push(`<!---->`);
    Root($$payload2, {
      onOpenChange: openStateChanged,
      get open() {
        return openModal;
      },
      set open($$value) {
        openModal = $$value;
        $$settled = false;
      },
      children: ($$payload3) => {
        $$payload3.out.push(`<!---->`);
        Dialog_trigger($$payload3, {});
        $$payload3.out.push(`<!----> <!---->`);
        Dialog_content($$payload3, {
          class: dialogClass,
          showCloseButton: canClose ?? false,
          interactOutsideBehavior: canClose ? "close" : "ignore",
          children: ($$payload4) => {
            $$payload4.out.push(`<!---->`);
            Dialog_header($$payload4, {
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->`);
                Dialog_title($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out.push(`<!---->${escape_html(modalTitle)}`);
                  },
                  $$slots: { default: true }
                });
                $$payload5.out.push(`<!----> <!---->`);
                Dialog_description($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out.push(`<!---->${escape_html(modalDescription)}`);
                  },
                  $$slots: { default: true }
                });
                $$payload5.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!----> <!---->`);
            CurrentComponent($$payload4, spread_props([localComponentProps]));
            $$payload4.out.push(`<!---->`);
          },
          $$slots: { default: true }
        });
        $$payload3.out.push(`<!---->`);
      },
      $$slots: { default: true }
    });
    $$payload2.out.push(`<!---->`);
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  pop();
}
function Card_footer($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    children,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  $$payload.out.push(`<div${spread_attributes(
    {
      "data-slot": "card-footer",
      class: clsx(cn("[.border-t]:pt-6 flex items-center px-6", className)),
      ...restProps
    },
    null
  )}>`);
  children?.($$payload);
  $$payload.out.push(`<!----></div>`);
  bind_props($$props, { ref });
  pop();
}
function Form_description($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out.push(`<!---->`);
    Description($$payload2, spread_props([
      {
        "data-slot": "form-description",
        class: cn("text-muted-foreground text-sm", className)
      },
      restProps,
      {
        get ref() {
          return ref;
        },
        set ref($$value) {
          ref = $$value;
          $$settled = false;
        }
      }
    ]));
    $$payload2.out.push(`<!---->`);
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref });
  pop();
}
export {
  Card_footer as C,
  Dynamic_modal as D,
  Form_description as F
};
