import { w as push, O as props_id, F as spread_attributes, P as bind_props, y as pop, Q as copy_payload, R as assign_payload, G as spread_props } from "./index2.js";
import { c as cn } from "./shadcn.utils.js";
import { b as createId, d as box, m as mergeProps } from "./create-id.js";
import "./states.svelte.js";
import "clsx";
import { e as CommandListState } from "./command-input.js";
function Command_list$1($$payload, $$props) {
  push();
  const uid = props_id($$payload);
  let {
    id = createId(uid),
    ref = null,
    child,
    children,
    "aria-label": ariaLabel,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const listState = CommandListState.create({
    id: box.with(() => id),
    ref: box.with(() => ref, (v) => ref = v),
    ariaLabel: box.with(() => ariaLabel ?? "Suggestions...")
  });
  const mergedProps = mergeProps(restProps, listState.props);
  $$payload.out.push(`<!---->`);
  {
    if (child) {
      $$payload.out.push("<!--[-->");
      child($$payload, { props: mergedProps });
      $$payload.out.push(`<!---->`);
    } else {
      $$payload.out.push("<!--[!-->");
      $$payload.out.push(`<div${spread_attributes({ ...mergedProps }, null)}>`);
      children?.($$payload);
      $$payload.out.push(`<!----></div>`);
    }
    $$payload.out.push(`<!--]-->`);
  }
  $$payload.out.push(`<!---->`);
  bind_props($$props, { ref });
  pop();
}
function Command_list($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out.push(`<!---->`);
    Command_list$1($$payload2, spread_props([
      {
        "data-slot": "command-list",
        class: cn("max-h-[300px] scroll-py-1 overflow-y-auto overflow-x-hidden", className)
      },
      restProps,
      {
        get ref() {
          return ref;
        },
        set ref($$value) {
          ref = $$value;
          $$settled = false;
        }
      }
    ]));
    $$payload2.out.push(`<!---->`);
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref });
  pop();
}
export {
  Command_list as C
};
