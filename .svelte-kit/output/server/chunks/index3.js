import "clsx";
import "@sveltejs/kit/internal";
import "./exports.js";
import "./state.svelte.js";
import { B as TeamMember, I as Invitation, M as MemberInvite, D as Member, E as Team, F as Organization, H as User, J as Jwks, P as Passkey, V as Verification, K as Session, L as Account, N as Gender, Q as Role, W as TimeZone, X as StorageBucket, Y as StorageFile, Z as UserRole, _ as CalendarBlock, $ as AppointmentType, a0 as Appointment, a1 as OrgPaymentProvider, a2 as OrgBankDetail, a3 as WorkdaySetting, a4 as roles, a5 as ac, R as RolesType, g as getCopyRight$1, U as UserAccountTypes, a6 as getDomain, a7 as UserController, a8 as TeamController, j as isUserInRole, a9 as getISessionData, aa as genderTypes, ab as rolesTypes } from "./auth-client.js";
import "superjson";
import "@oslojs/crypto/sha2";
import "decimal.js";
import "linq-extensions";
import { repo, remult } from "remult";
import { createPostgresDataProvider } from "remult/postgres";
import { remultApi } from "remult/remult-sveltekit";
import { betterAuth } from "better-auth";
import { APIError } from "better-auth/api";
import { remultAdapter } from "@nerdfolio/remult-better-auth";
import { emailOTP, organization, admin } from "better-auth/plugins";
import { StatusCodes } from "http-status-codes";
import { passkey } from "better-auth/plugins/passkey";
import { jsx, jsxs } from "react/jsx-runtime";
import "react";
import { render, Html, Tailwind, Head, Container, Section, Heading, Img, Text, Button, Hr } from "@react-email/components";
import { Module } from "remult/server";
import "better-auth/svelte-kit";
import { createStep, createWorkflow, Mastra } from "@mastra/core";
import { openai } from "@ai-sdk/openai";
import { Agent } from "@mastra/core/agent";
import { z } from "zod";
import { createTool } from "@mastra/core/tools";
import { Memory } from "@mastra/memory";
import { PostgresStore, PgVector } from "@mastra/pg";
import { PinoLogger } from "@mastra/loggers";
import { fastembed } from "@mastra/fastembed";
const entities = [
  // Level 1: Base entities with no dependencies
  Gender,
  Role,
  Account,
  Session,
  Verification,
  Passkey,
  Jwks,
  // Level 2: Entities with minimal dependencies
  TimeZone,
  StorageBucket,
  // Level 3: Entities depending on Level 1-2
  StorageFile,
  // Level 4: Core entities
  User,
  UserRole,
  // Level 5: Organization entities
  Organization,
  Team,
  // Level 6: Membership entities
  Member,
  TeamMember,
  Invitation,
  // Level 7: Business logic entities
  CalendarBlock,
  AppointmentType,
  // Level 8: Complex entities
  Appointment,
  MemberInvite,
  OrgPaymentProvider,
  OrgBankDetail,
  WorkdaySetting
];
const authEntities = {
  // Base auth entities
  Account,
  Session,
  Verification,
  Passkey,
  Jwks,
  // Core entities needed for auth
  User,
  Organization,
  Team,
  Member,
  MemberInvite,
  Invitation,
  TeamMember
};
const entityGroups = {
  // Authentication entities (in dependency order)
  auth: [
    Account,
    Session,
    Verification,
    Passkey,
    Jwks,
    User,
    Organization,
    Team,
    Member,
    MemberInvite,
    Invitation,
    TeamMember
  ]
};
const adminConfig = {
  // Default role for new users
  defaultRole: RolesType.User,
  // Roles that are considered admin roles
  adminRoles: [RolesType.Admin, RolesType.OrgAdmin],
  // Custom access control and roles - using full access control with all resources
  ac,
  // Type assertion to work around TypeScript compatibility
  roles
};
const AUTH_SECRET = "SFvEfAs3NyQxjvXfEhzSyUqWaRg9vQ7P44gctEYvKRAf4fUitV3TZrybGFq8myO3Dg9ddv0MdD1Uq/vLbDYlbw==";
const PLUNK_SECRET_KEY = "sk_ff62efe4af4ccb65e2593ee7ddef23b3bc3bffe9e623aaea";
const DATABASE_URL = "postgres://postgres:postgres@localhost:5439/spendeed";
const BETTER_AUTH_TRUSTED_ORIGINS = "http://localhost:5173,http://localhost:6420";
const AUTH_RP_ID = "localhost";
const SUPER_ADMIN_EMAILS = "<EMAIL>";
const PLUNK_API_URL = "https://api.useplunk.com/v1/";
const PUBLIC_APP_NAME = "SpenDeed";
const PUBLIC_FRONTEND_URL = "http://localhost:5173";
const ReceiptTextIcon = ({ className = "w-6 h-6" }) => /* @__PURE__ */ jsxs(
  "svg",
  {
    xmlns: "http://www.w3.org/2000/svg",
    width: "24",
    height: "24",
    viewBox: "0 0 24 24",
    fill: "none",
    stroke: "currentColor",
    strokeWidth: "2",
    strokeLinecap: "round",
    strokeLinejoin: "round",
    className,
    children: [
      /* @__PURE__ */ jsx("path", { d: "M4 2v20l2-1 2 1 2-1 2 1 2-1 2 1 2-1 2 1V2l-2 1-2-1-2 1-2-1-2 1-2-1-2 1Z" }),
      /* @__PURE__ */ jsx("path", { d: "M16 8h-6a2 2 0 1 0 0 4h4a2 2 0 1 1 0 4H8" }),
      /* @__PURE__ */ jsx("path", { d: "M12 18V6" })
    ]
  }
);
const SpendeedLogo = ({
  size = "md",
  className = "",
  showIcon = true,
  href = "https://spendeed.ai"
}) => {
  const sizeConfig = {
    sm: {
      icon: "w-4 h-4",
      text: "text-sm",
      spacing: "ml-1"
    },
    md: {
      icon: "w-6 h-6",
      text: "text-xl",
      spacing: "ml-2"
    },
    lg: {
      icon: "w-8 h-8",
      text: "text-2xl",
      spacing: "ml-3"
    },
    xl: {
      icon: "w-12 h-12",
      text: "text-4xl",
      spacing: "ml-4"
    }
  };
  const config = sizeConfig[size];
  const LogoContent = () => /* @__PURE__ */ jsxs("div", { className: `flex items-center justify-center ${className}`, children: [
    showIcon && /* @__PURE__ */ jsx(ReceiptTextIcon, { className: `text-primary ${config.icon}` }),
    /* @__PURE__ */ jsxs("span", { className: `${config.spacing} truncate ${config.text} font-bold`, children: [
      /* @__PURE__ */ jsx("span", { className: "text-primary", children: "Spen" }),
      /* @__PURE__ */ jsx("span", { className: "text-muted-foreground -ml-1", children: "Deed" })
    ] })
  ] });
  if (href) {
    return /* @__PURE__ */ jsx(
      "a",
      {
        href,
        className: "transition-opacity hover:opacity-80 focus:outline-none",
        style: { textDecoration: "none" },
        children: /* @__PURE__ */ jsx(LogoContent, {})
      }
    );
  }
  return /* @__PURE__ */ jsx(LogoContent, {});
};
function InvitationEmail({
  inviterName,
  organizationName,
  teamName,
  role,
  inviteLink,
  logoUrl
}) {
  const teamText = teamName ? ` to the ${teamName} team` : "";
  const roleText = role ? ` as ${role}` : "";
  return /* @__PURE__ */ jsx(Html, { children: /* @__PURE__ */ jsxs(Tailwind, { children: [
    /* @__PURE__ */ jsx(Head, {}),
    /* @__PURE__ */ jsxs(Container, { className: "mx-auto max-w-2xl bg-white font-sans", children: [
      /* @__PURE__ */ jsx(Section, { className: "bg-gradient-to-r from-blue-600 to-purple-600 px-8 py-6 text-center", children: /* @__PURE__ */ jsx(SpendeedLogo, { size: "lg", className: "text-white", href: "https://spendeed.ai" }) }),
      /* @__PURE__ */ jsxs(Container, { className: "px-8 py-8", children: [
        /* @__PURE__ */ jsxs(Heading, { className: "mb-6 text-center text-2xl font-bold text-gray-900", children: [
          "You're invited to join ",
          organizationName
        ] }),
        logoUrl && /* @__PURE__ */ jsx(Section, { className: "mb-6 text-center", children: /* @__PURE__ */ jsx(
          Img,
          {
            src: logoUrl,
            alt: `${organizationName} logo`,
            className: "mx-auto h-16 w-16 rounded-lg border border-gray-200"
          }
        ) }),
        /* @__PURE__ */ jsxs(Section, { className: "my-8 rounded-lg border border-gray-200 bg-blue-50 p-6", children: [
          /* @__PURE__ */ jsxs(Text, { className: "mb-4 text-center text-lg font-semibold text-gray-900", children: [
            inviterName,
            " has invited you to join ",
            organizationName,
            teamText,
            roleText
          ] }),
          /* @__PURE__ */ jsx(Text, { className: "mb-6 text-center text-gray-600", children: "Accept this invitation to start collaborating with your team on SpenDeed." }),
          /* @__PURE__ */ jsx(Section, { className: "text-center", children: /* @__PURE__ */ jsx(
            Button,
            {
              href: inviteLink,
              className: "text-decoration-none inline-block rounded-lg bg-blue-600 px-8 py-3 text-center font-semibold text-white hover:bg-blue-700",
              children: "Accept Invitation"
            }
          ) })
        ] }),
        /* @__PURE__ */ jsxs(Section, { className: "my-6 rounded-lg border border-gray-200 bg-gray-50 p-4", children: [
          /* @__PURE__ */ jsx(Text, { className: "mb-2 text-sm font-semibold text-gray-700", children: "Invitation Details:" }),
          /* @__PURE__ */ jsxs(Text, { className: "mb-1 text-sm text-gray-600", children: [
            /* @__PURE__ */ jsx("span", { className: "font-medium", children: "Organization:" }),
            " ",
            organizationName
          ] }),
          teamName && /* @__PURE__ */ jsxs(Text, { className: "mb-1 text-sm text-gray-600", children: [
            /* @__PURE__ */ jsx("span", { className: "font-medium", children: "Team:" }),
            " ",
            teamName
          ] }),
          /* @__PURE__ */ jsxs(Text, { className: "mb-1 text-sm text-gray-600", children: [
            /* @__PURE__ */ jsx("span", { className: "font-medium", children: "Role:" }),
            " ",
            role
          ] }),
          /* @__PURE__ */ jsxs(Text, { className: "text-sm text-gray-600", children: [
            /* @__PURE__ */ jsx("span", { className: "font-medium", children: "Invited by:" }),
            " ",
            inviterName
          ] })
        ] }),
        /* @__PURE__ */ jsxs(Section, { className: "my-6", children: [
          /* @__PURE__ */ jsx(Text, { className: "mb-2 text-sm text-gray-600", children: "If the button above doesn't work, copy and paste this link into your browser:" }),
          /* @__PURE__ */ jsx(Text, { className: "text-sm break-all text-blue-600", children: inviteLink })
        ] }),
        /* @__PURE__ */ jsx(Section, { className: "my-6 rounded-lg border border-amber-200 bg-amber-50 p-4", children: /* @__PURE__ */ jsxs(Text, { className: "text-sm text-amber-800", children: [
          /* @__PURE__ */ jsx("span", { className: "font-semibold", children: "Security Notice:" }),
          " This invitation will expire in 48 hours. If you didn't expect this invitation or don't know",
          " ",
          inviterName,
          ", you can safely ignore this email."
        ] }) }),
        /* @__PURE__ */ jsx(Hr, { className: "my-6 border-gray-200" }),
        /* @__PURE__ */ jsxs(Section, { className: "text-center", children: [
          /* @__PURE__ */ jsxs(Text, { className: "mb-2 text-xs text-gray-400", children: [
            "This invitation was sent by ",
            inviterName,
            " from ",
            organizationName
          ] }),
          /* @__PURE__ */ jsx(Text, { className: "text-xs text-gray-400", children: getCopyRight$1() })
        ] })
      ] })
    ] })
  ] }) });
}
const getInvitationEmail = async (props) => {
  return await render(/* @__PURE__ */ jsx(InvitationEmail, { ...props }));
};
const getCopyRight = () => {
  const today = /* @__PURE__ */ new Date();
  return `© ${today.getFullYear()} SpenDeed.AI. All rights reserved.`;
};
function NewOrganizationEmail({
  userName,
  userEmail,
  organizationName,
  organizationId,
  dashboardUrl,
  logoUrl
}) {
  return /* @__PURE__ */ jsx(Html, { children: /* @__PURE__ */ jsxs(Tailwind, { children: [
    /* @__PURE__ */ jsx(Head, {}),
    /* @__PURE__ */ jsxs(Container, { className: "mx-auto max-w-2xl bg-white font-sans", children: [
      /* @__PURE__ */ jsx(Section, { className: "bg-gradient-to-r from-blue-600 to-purple-600 px-8 py-6 text-center", children: /* @__PURE__ */ jsx(SpendeedLogo, { size: "lg", className: "text-white", href: "https://spendeed.ai" }) }),
      /* @__PURE__ */ jsxs(Container, { className: "px-8 py-8", children: [
        /* @__PURE__ */ jsx(Heading, { className: "mb-6 text-center text-2xl font-bold text-gray-900", children: "🎉 Welcome to SpenDeed!" }),
        /* @__PURE__ */ jsxs(Text, { className: "mb-6 text-center text-lg text-gray-600", children: [
          "Congratulations ",
          userName,
          "! Your organization has been successfully created."
        ] }),
        logoUrl && /* @__PURE__ */ jsx(Section, { className: "mb-6 text-center", children: /* @__PURE__ */ jsx(
          Img,
          {
            src: logoUrl,
            alt: `${organizationName} logo`,
            className: "mx-auto h-20 w-20 rounded-lg border border-gray-200 shadow-sm"
          }
        ) }),
        /* @__PURE__ */ jsxs(Section, { className: "my-8 rounded-lg border border-green-200 bg-green-50 p-6", children: [
          /* @__PURE__ */ jsxs(Text, { className: "mb-4 text-center text-xl font-semibold text-green-900", children: [
            "🏢 ",
            organizationName
          ] }),
          /* @__PURE__ */ jsx(Text, { className: "mb-6 text-center text-gray-700", children: "Your organization is now ready! You can start managing your expenses, inviting team members, and tracking your financial goals." }),
          /* @__PURE__ */ jsx(Section, { className: "text-center", children: /* @__PURE__ */ jsx(
            Button,
            {
              href: dashboardUrl,
              className: "text-decoration-none inline-block rounded-lg bg-green-600 px-8 py-3 text-center font-semibold text-white hover:bg-green-700",
              children: "Access Your Dashboard"
            }
          ) })
        ] }),
        /* @__PURE__ */ jsxs(Section, { className: "my-6 rounded-lg border border-gray-200 bg-gray-50 p-4", children: [
          /* @__PURE__ */ jsx(Text, { className: "mb-2 text-sm font-semibold text-gray-700", children: "Organization Details:" }),
          /* @__PURE__ */ jsxs(Text, { className: "mb-1 text-sm text-gray-600", children: [
            /* @__PURE__ */ jsx("span", { className: "font-medium", children: "Name:" }),
            " ",
            organizationName
          ] }),
          /* @__PURE__ */ jsxs(Text, { className: "mb-1 text-sm text-gray-600", children: [
            /* @__PURE__ */ jsx("span", { className: "font-medium", children: "Admin:" }),
            " ",
            userName,
            " (",
            userEmail,
            ")"
          ] }),
          /* @__PURE__ */ jsxs(Text, { className: "text-sm text-gray-600", children: [
            /* @__PURE__ */ jsx("span", { className: "font-medium", children: "Organization ID:" }),
            " ",
            organizationId
          ] })
        ] }),
        /* @__PURE__ */ jsxs(Section, { className: "my-8 rounded-lg border border-blue-200 bg-blue-50 p-6", children: [
          /* @__PURE__ */ jsx(Text, { className: "mb-4 text-lg font-semibold text-blue-900", children: "🚀 What's Next?" }),
          /* @__PURE__ */ jsxs(Text, { className: "mb-3 text-sm text-gray-700", children: [
            /* @__PURE__ */ jsx("span", { className: "font-medium", children: "1. Set up your profile:" }),
            " Complete your organization profile and upload your logo"
          ] }),
          /* @__PURE__ */ jsxs(Text, { className: "mb-3 text-sm text-gray-700", children: [
            /* @__PURE__ */ jsx("span", { className: "font-medium", children: "2. Invite team members:" }),
            " Add your team to start collaborating on expense management"
          ] }),
          /* @__PURE__ */ jsxs(Text, { className: "mb-3 text-sm text-gray-700", children: [
            /* @__PURE__ */ jsx("span", { className: "font-medium", children: "3. Configure settings:" }),
            " Set up your expense categories, approval workflows, and integrations"
          ] }),
          /* @__PURE__ */ jsxs(Text, { className: "text-sm text-gray-700", children: [
            /* @__PURE__ */ jsx("span", { className: "font-medium", children: "4. Start tracking:" }),
            " Begin recording and managing your organization's expenses"
          ] })
        ] }),
        /* @__PURE__ */ jsxs(Section, { className: "my-6", children: [
          /* @__PURE__ */ jsx(Text, { className: "mb-2 text-sm text-gray-600", children: "If the button above doesn't work, copy and paste this link into your browser:" }),
          /* @__PURE__ */ jsx(Text, { className: "break-all text-sm text-blue-600", children: dashboardUrl })
        ] }),
        /* @__PURE__ */ jsxs(Section, { className: "my-6 rounded-lg border border-purple-200 bg-purple-50 p-4", children: [
          /* @__PURE__ */ jsx(Text, { className: "mb-2 text-sm font-semibold text-purple-900", children: "💬 Need Help?" }),
          /* @__PURE__ */ jsxs(Text, { className: "text-sm text-purple-800", children: [
            "Our support team is here to help you get started. Visit our",
            " ",
            /* @__PURE__ */ jsx("a", { href: "https://spendeed.ai/help", className: "text-purple-600 underline", children: "Help Center" }),
            " ",
            "or contact us at",
            " ",
            /* @__PURE__ */ jsx("a", { href: "mailto:<EMAIL>", className: "text-purple-600 underline", children: "<EMAIL>" })
          ] })
        ] }),
        /* @__PURE__ */ jsx(Hr, { className: "my-6 border-gray-200" }),
        /* @__PURE__ */ jsxs(Section, { className: "text-center", children: [
          /* @__PURE__ */ jsx(Text, { className: "mb-2 text-xs text-gray-400", children: "This email was sent because you created an organization on SpenDeed" }),
          /* @__PURE__ */ jsx(Text, { className: "text-xs text-gray-400", children: getCopyRight() })
        ] })
      ] })
    ] })
  ] }) });
}
const getNewOrganizationEmail = async (props) => {
  return await render(/* @__PURE__ */ jsx(NewOrganizationEmail, { ...props }));
};
const emailTitle = {
  "sign-in": "Sign in to SpenDeed",
  "email-verification": "SignUp: Verify your email address"
};
const descriptionText = {
  "sign-in": "Use this code to complete your sign-in to SpenDeed.",
  "email-verification": "Use this code to verify your email address and complete your account setup."
};
function OTPCodeEmail({ otp, type, email }) {
  const isSignIn = type === "sign-in";
  const title = emailTitle[type];
  const description = descriptionText[type];
  return /* @__PURE__ */ jsx(Html, { children: /* @__PURE__ */ jsxs(Tailwind, { children: [
    /* @__PURE__ */ jsx(Head, {}),
    /* @__PURE__ */ jsxs(Container, { className: "mx-auto max-w-2xl bg-white font-sans", children: [
      /* @__PURE__ */ jsx(Section, { className: "bg-gradient-to-r from-blue-600 to-purple-600 px-8 py-6 text-center", children: /* @__PURE__ */ jsx(SpendeedLogo, { size: "lg", className: "text-white", href: "https://spendeed.ai" }) }),
      /* @__PURE__ */ jsxs(Container, { className: "px-8 py-8", children: [
        /* @__PURE__ */ jsx(Heading, { className: "mb-6 text-center text-2xl font-bold text-gray-900", children: title }),
        /* @__PURE__ */ jsx(Text, { className: "mb-6 text-center text-gray-600", children: description }),
        /* @__PURE__ */ jsxs(Text, { className: "mb-2 text-sm text-gray-500", children: [
          "Email: ",
          email
        ] }),
        /* @__PURE__ */ jsxs(Section, { className: "my-8 rounded-lg border border-gray-200 bg-gray-50 p-6 text-center", children: [
          /* @__PURE__ */ jsx(Text, { className: "mb-2 text-sm font-semibold tracking-wide text-gray-700 uppercase", children: "Your verification code" }),
          /* @__PURE__ */ jsx(Text, { className: "mb-4 text-4xl font-bold tracking-widest text-blue-600", children: otp }),
          /* @__PURE__ */ jsx(Text, { className: "text-sm text-gray-500", children: "This code will expire in 10 minutes" })
        ] }),
        /* @__PURE__ */ jsxs(Section, { className: "my-6", children: [
          /* @__PURE__ */ jsx(Text, { className: "mb-4 text-sm text-gray-600", children: isSignIn ? "Enter this code on the sign-in page to access your account." : "Enter this code to verify your email address and activate your account." }),
          /* @__PURE__ */ jsx(Text, { className: "text-sm text-gray-500", children: "If you didn't request this code, you can safely ignore this email." })
        ] }),
        /* @__PURE__ */ jsx(Hr, { className: "my-6 border-gray-200" }),
        /* @__PURE__ */ jsxs(Section, { className: "text-center", children: [
          /* @__PURE__ */ jsxs(Text, { className: "mb-2 text-xs text-gray-400", children: [
            "This email was sent to ",
            email
          ] }),
          /* @__PURE__ */ jsx(Text, { className: "text-xs text-gray-400", children: getCopyRight$1() })
        ] })
      ] })
    ] })
  ] }) });
}
const getOTPCodeEmail = async (otp, type, email) => {
  return await render(/* @__PURE__ */ jsx(OTPCodeEmail, { otp, type, email }));
};
const sendMail = async ({
  json,
  url,
  ...options
}) => {
  const apiUrl = PLUNK_API_URL;
  const apiKey = PLUNK_SECRET_KEY;
  const res = await fetch(
    new URL(url, apiUrl).toString(),
    {
      ...options,
      headers: {
        Authorization: `Bearer ${apiKey}`,
        ...json && { "Content-Type": "application/json" },
        ...options.headers
      },
      body: json ? JSON.stringify(json) : void 0
    }
  );
  const text = await res.text();
  const data = JSON.parse(text);
  if (res?.status === 401) {
    throw new Error(` 401 Error : ${res.statusText}: ${data?.message}`);
  }
  if (res?.status === 404) {
    throw new Error(`404 Error : ${res.statusText}: ${data?.message}`);
  }
  if (!res.ok) {
    throw new Error(`Unknown API Error : ${res.statusText}: ${data?.message}`);
  }
  return data;
};
const plunk = {
  events: {
    /**
     * Publishes an event to Plunk
     * @param {string} event.event - The event you want to publish
     * @param {string} event.email - The email associated with this event
     * @param {Object=} event.data - The user data associated with this event
     * @param {boolean=true} event.subscribed - Whether the user is subscribed to marketing emails
     */
    track: async (event) => {
      return await sendMail({
        method: "POST",
        url: "track",
        json: { ...event }
      });
    }
  },
  emails: {
    /**
     * Sends a transactional email with Plunk
     *
     * @param {string} body.to - The email you want to send to
     * @param {string} body.subject - The subject of the email
     * @param {string} body.body - The body of the email
     * @param {string=} body.from - The email you want to send from
     * @param {string=} body.name - The name you want to send as
     * @param {string=html} body.type - The type of email you want to send
     * @param {boolean=false} body.subscribed - Whether the user is subscribed to marketing emails
     */
    send: async (body) => {
      return await sendMail({
        method: "POST",
        url: "send",
        json: {
          ...body
        }
      });
    }
  }
};
const addRolesToUser = async (emails, roles2) => {
  const users = await repo(User).find({
    where: {
      email: emails
    }
  });
  const userNotFounds = emails.filter((e) => !users.some((u) => u.email === e));
  for (const nf of userNotFounds) {
  }
  const usersAlreadyHaveRoles = [];
  for (const user of users) {
    const rolesToAdd = roles2.filter((r) => !user.roles?.includes(r));
    if (rolesToAdd.length > 0) {
      await repo(User).update(user.id, {
        roles: [.../* @__PURE__ */ new Set([...user.roles, ...rolesToAdd])]
      });
    } else {
      usersAlreadyHaveRoles.push(user.email);
    }
  }
};
const doesOrgExists = async (domain) => {
  const org = await repo(Organization).count({
    businessEmail: { $contains: domain }
  });
  return !!org;
};
const auth = betterAuth({
  database: remultAdapter({
    // When you `npm run auth:generate` you need to have `authEntities: {}`
    // It generates all entites needed for better-auth. You might need to check diffs in GIT.
    // Help: https://github.com/nerdfolio/remult-better-auth
    authEntities,
    // authEntities: {},
    usePlural: true
  }),
  secret: AUTH_SECRET,
  trustedOrigins: BETTER_AUTH_TRUSTED_ORIGINS?.split(",").map((origin) => origin.trim()),
  advanced: {
    database: {
      generateId: false
    }
  },
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: true,
    autoSignIn: false
  },
  // emailVerification: {
  // sendVerificationEmail: async ({ user, url }: { user: any; url: string, token: string }) => {
  //   // Generate OTP email template with JSX Email
  //   const expires = new Date(Date.now() + 10 * 60 * 1000) // 10 minutes from now
  //   const emailHtml = await getVerificationCodeEmail(url, expires)
  //   // Send email using plunk
  //   await plunk.emails.send({
  //     to: user.email,
  //     subject: "🔐 Verify your SpenDeed account",
  //     body: emailHtml,
  //     type: "html"
  //   })
  // },
  // autoSignInAfterVerification: true,
  // sendOnSignIn: false,
  //},
  session: {
    expiresIn: 60 * 60 * 24 * 7,
    // 7 days
    updateAge: 60 * 60 * 24,
    // 1 day,
    additionalFields: {
      activeOrganizationId: {
        type: "string",
        required: false,
        input: false
      },
      impersonatedBy: {
        type: "string",
        required: false,
        input: false
      }
    }
  },
  user: {
    additionalFields: {
      roles: { type: "string[]" },
      givenName: {
        type: "string",
        required: true,
        input: true
      },
      familyName: {
        type: "string",
        required: true,
        input: true
      },
      displayName: {
        type: "string",
        required: true,
        input: false
      },
      // Contact info - fields sent during signup
      phoneNumber: {
        type: "string",
        required: false,
        input: true
      },
      phoneNumberVerified: {
        type: "boolean",
        required: false,
        defaultValue: false,
        input: false
      },
      // Profile info
      image: {
        type: "string",
        required: false,
        input: false
      },
      avatarUrl: {
        type: "string",
        required: false,
        input: true
      },
      // Status - fields sent during signup
      userStatus: {
        type: "string",
        required: false,
        defaultValue: "Active",
        input: false
      },
      userType: {
        type: "string",
        required: true,
        input: true
      },
      acceptTerms: {
        type: "boolean",
        required: false,
        input: false
      },
      authType: {
        type: "string",
        required: false,
        input: false
      },
      metadata: {
        type: "string",
        required: false,
        input: true
      },
      teamId: {
        type: "string",
        required: false,
        input: false
      },
      organizationId: {
        type: "string",
        required: false,
        input: false
      },
      gender: {
        type: "string",
        required: false,
        input: false
      }
    }
  },
  plugins: [
    passkey({
      rpID: AUTH_RP_ID,
      rpName: PUBLIC_APP_NAME,
      origin: PUBLIC_FRONTEND_URL,
      // SvelteKit dev server URL
      authenticatorSelection: {
        residentKey: "required",
        userVerification: "required",
        authenticatorAttachment: "cross-platform"
      }
    }),
    emailOTP({
      overrideDefaultEmailVerification: true,
      async sendVerificationOTP({ email, otp, type }) {
        const emailType = type;
        const emailHtml = await getOTPCodeEmail(otp, emailType, email);
        await plunk.emails.send({
          to: email,
          subject: type === "sign-in" ? "🔐 Your SpenDeed sign-in code" : type === "email-verification" ? "🔐 Verify your SpenDeed email" : "🔑 Reset your SpenDeed password",
          body: emailHtml,
          type: "html"
        });
      },
      otpLength: 6,
      // 6-digit OTP
      expiresIn: 600,
      // 10 minutes
      allowedAttempts: 3,
      // Allow 3 attempts before invalidating
      disableSignUp: true
      // Prevent signup for users not previously registered
      // sendVerificationOnSignUp: true
    }),
    organization({
      creatorRole: RolesType.OrgAdmin,
      organizationLimit: 5,
      // Maximum organizations per user
      membershipLimit: 100,
      // Maximum members per organization
      schema: {
        organization: {
          additionalFields: {
            businessEmail: {
              type: "string",
              required: true,
              input: true
            },
            businessAddress: {
              type: "string",
              required: false,
              input: true
            },
            id: {
              type: "string",
              required: false,
              input: true
            }
          }
        }
      },
      // Custom roles and permissions - using full access control with all resources
      ac,
      // Type assertion to work around TypeScript compatibility
      roles,
      allowUserToCreateOrganization: (user) => {
        const isTrue = isUserInRole(user, RolesType.OrgAdmin);
        return isTrue;
      },
      // Organization creation hooks - using proper better-auth API
      organizationCreation: {
        disabled: false,
        beforeCreate: async ({ organization: organization2, user }) => {
          return {
            data: {
              ...organization2,
              // Additional metadata can be added here if needed
              metadata: JSON.stringify({
                createdVia: "signup",
                businessType: "standard",
                createdBy: user.id
              })
            }
          };
        },
        afterCreate: async ({ organization: organization2, user, member }) => {
          await UserController.update(user.id, {
            teamId: member.teamId,
            organizationId: organization2.id
          });
          await TeamController.update(member.teamId, {
            isDefault: true,
            name: "HQ"
          });
          try {
            const dashboardUrl = `${PUBLIC_FRONTEND_URL}/dashboard?org=${organization2.id}`;
            const emailHtml = await getNewOrganizationEmail({
              userName: user.name || user.email,
              userEmail: user.email,
              organizationName: organization2.name,
              organizationId: organization2.id,
              dashboardUrl,
              logoUrl: organization2.logo || void 0
            });
            await plunk.emails.send({
              to: user.email,
              subject: `🎉 Welcome to SpenDeed! Your organization "${organization2.name}" is ready`,
              body: emailHtml,
              type: "html"
            });
          } catch (emailError) {
            console.error("Failed to send welcome email:", emailError);
          }
        }
      },
      // Organization deletion hooks
      organizationDeletion: {
        disabled: false
      },
      // Teams configuration
      teams: {
        enabled: true,
        maximumTeams: 300,
        // Allow up to 20 teams per organization
        allowRemovingAllTeams: false,
        // Prevent removing the last team
        maximumMembersPerTeam: 500
        // Maximum members per team
      },
      // Invitation configuration
      invitationExpiresIn: 48 * 60 * 60,
      // 48 hours in seconds
      cancelPendingInvitationsOnReInvite: true,
      invitationLimit: 100,
      // Maximum invitations per user
      // Email invitation handler
      async sendInvitationEmail(data) {
        const inviteLink = `${PUBLIC_FRONTEND_URL}/invitation/${data.id}`;
        try {
          const emailHtml = await getInvitationEmail({
            inviterName: data.inviter.user.name || data.inviter.user.email,
            organizationName: data.organization.name,
            teamName: data.invitation.teamId ? "Team Member" : void 0,
            // We don't have team name in data, but we know if they're assigned to a team
            role: Array.isArray(data.role) ? data.role.join(", ") : data.role,
            inviteLink,
            logoUrl: data.organization.logo || void 0
          });
          await plunk.emails.send({
            to: data.email,
            subject: `You're invited to join ${data.organization.name}`,
            body: emailHtml,
            type: "html"
          });
        } catch (emailError) {
          console.error("Failed to send invitation email:", emailError);
        }
      }
    }),
    admin(adminConfig)
  ],
  databaseHooks: {
    user: {
      create: {
        before: async (ctx) => {
          try {
            if (ctx["userType"] === UserAccountTypes.Staff && ctx.email) {
              const emailDomain = getDomain(ctx.email);
              const orgExists = await doesOrgExists(emailDomain);
              if (orgExists) {
                throw new APIError(StatusCodes.BAD_REQUEST, {
                  message: "Business already registered, please use an invite link to sign up",
                  status: StatusCodes.BAD_REQUEST
                });
              }
            }
            return;
          } catch (error) {
            console.error("Before hook error:", error);
            if (error instanceof APIError) {
              throw error;
            }
            throw new APIError(StatusCodes.INTERNAL_SERVER_ERROR, {
              message: "Failed to validate signup request",
              status: StatusCodes.INTERNAL_SERVER_ERROR
            });
          }
        },
        after: async (ctx) => {
          try {
            const userCtx = ctx;
            if (userCtx.metadata) {
              let metadata = userCtx.metadata;
              if (typeof metadata === "string") {
                metadata = JSON.parse(metadata);
              }
              if (metadata.organizationId) {
                const userRepo = remult.repo(User);
                await userRepo.update(ctx.id, {
                  organizationId: metadata.organizationId
                });
              }
            }
          } catch (error) {
            console.error("After user create hook error:", error);
          }
        }
      }
    }
  }
});
const authModule = () => new Module({
  key: "auth",
  entities: entityGroups.auth,
  initApi: async () => {
    const emails = SUPER_ADMIN_EMAILS.split(",").map((c) => c.trim()).filter(Boolean);
    await addRolesToUser(emails, Object.values(RolesType));
  },
  initRequest: async () => {
    const s = await auth.api.getSession({
      headers: new Headers(remult.context.headers?.getAll())
    });
    if (s) {
      const user = s.user;
      const session = s.session;
      const sessionData = getISessionData(user, session);
      if (!sessionData) return;
      remult.user = {
        ...sessionData
      };
    } else {
      remult.user = void 0;
    }
  }
});
const TIMEZONES_DATA = [
  // UTC
  "UTC",
  // North America - Eastern
  "America/New_York",
  "America/Toronto",
  // North America - Central
  "America/Chicago",
  "America/Mexico_City",
  // North America - Mountain
  "America/Denver",
  "America/Phoenix",
  // North America - Pacific
  "America/Los_Angeles",
  "America/Vancouver",
  // Europe - Western
  "Europe/London",
  "Europe/Dublin",
  // Europe - Central
  "Europe/Paris",
  "Europe/Berlin",
  // Europe - Eastern
  "Europe/Moscow",
  "Europe/Istanbul",
  // Asia - Middle East
  "Asia/Dubai",
  "Asia/Tehran",
  // Asia - South
  "Asia/Kolkata",
  "Asia/Karachi",
  // Asia - Southeast
  "Asia/Singapore",
  "Asia/Bangkok",
  // Asia - East
  "Asia/Tokyo",
  "Asia/Shanghai",
  // Australia/Oceania
  "Australia/Sydney",
  "Pacific/Auckland",
  // Africa
  "Africa/Lagos",
  "Africa/Cairo",
  // South America
  "America/Sao_Paulo",
  "America/Buenos_Aires"
];
const api = remultApi({
  dataProvider: createPostgresDataProvider({
    connectionString: DATABASE_URL
  }),
  admin: true,
  entities: [...entities],
  modules: [authModule()],
  async initApi(remult2) {
    try {
      const timeZoneRepo = remult2.repo(TimeZone);
      const timeZoneCount = await timeZoneRepo.count();
      if (timeZoneCount === 0) {
        for (const timezone of TIMEZONES_DATA) {
          await timeZoneRepo.insert({
            timeZoneName: timezone,
            offsetHours: 0
            // Default offset, can be updated later
          });
        }
      }
      const genderRepo = remult2.repo(Gender);
      const genderCount = await genderRepo.count();
      if (genderCount === 0) {
        for (const genderType of genderTypes) {
          await genderRepo.insert({ name: genderType });
        }
      }
      const roleRepo = remult2.repo(Role);
      const roleCount = await roleRepo.count();
      if (roleCount === 0) {
        for (const roleType of rolesTypes) {
          await roleRepo.insert({ role: roleType });
        }
      }
    } catch (error) {
      console.error("❌ Error seeding initial data:", error);
    }
  }
});
const llm = openai("gpt-4o");
const agent = new Agent({
  name: "Weather Agent",
  model: llm,
  instructions: `
        You are a local activities and travel expert who excels at weather-based planning. Analyze the weather data and provide practical activity recommendations.

        For each day in the forecast, structure your response exactly as follows:

        📅 [Day, Month Date, Year]
        ═══════════════════════════

        🌡️ WEATHER SUMMARY
        • Conditions: [brief description]
        • Temperature: [X°C/Y°F to A°C/B°F]
        • Precipitation: [X% chance]

        🌅 MORNING ACTIVITIES
        Outdoor:
        • [Activity Name] - [Brief description including specific location/route]
          Best timing: [specific time range]
          Note: [relevant weather consideration]

        🌞 AFTERNOON ACTIVITIES
        Outdoor:
        • [Activity Name] - [Brief description including specific location/route]
          Best timing: [specific time range]
          Note: [relevant weather consideration]

        🏠 INDOOR ALTERNATIVES
        • [Activity Name] - [Brief description including specific venue]
          Ideal for: [weather condition that would trigger this alternative]

        ⚠️ SPECIAL CONSIDERATIONS
        • [Any relevant weather warnings, UV index, wind conditions, etc.]

        Guidelines:
        - Suggest 2-3 time-specific outdoor activities per day
        - Include 1-2 indoor backup options
        - For precipitation >50%, lead with indoor activities
        - All activities must be specific to the location
        - Include specific venues, trails, or locations
        - Consider activity intensity based on temperature
        - Keep descriptions concise but informative

        Maintain this exact formatting for consistency, using the emoji and section headers as shown.
      `
});
const forecastSchema = z.array(
  z.object({
    date: z.string(),
    maxTemp: z.number(),
    minTemp: z.number(),
    precipitationChance: z.number(),
    condition: z.string(),
    location: z.string()
  })
);
const fetchWeather = createStep({
  id: "fetch-weather",
  description: "Fetches weather forecast for a given city",
  inputSchema: z.object({
    city: z.string().describe("The city to get the weather for")
  }),
  outputSchema: forecastSchema,
  execute: async ({ inputData }) => {
    const triggerData = inputData;
    if (!triggerData) {
      throw new Error("Trigger data not found");
    }
    const geocodingUrl = `https://geocoding-api.open-meteo.com/v1/search?name=${encodeURIComponent(triggerData.city)}&count=1`;
    const geocodingResponse = await fetch(geocodingUrl);
    const geocodingData = await geocodingResponse.json();
    if (!geocodingData.results?.[0]) {
      throw new Error(`Location '${triggerData.city}' not found`);
    }
    const { latitude, longitude, name } = geocodingData.results[0];
    const weatherUrl = `https://api.open-meteo.com/v1/forecast?latitude=${latitude}&longitude=${longitude}&daily=temperature_2m_max,temperature_2m_min,precipitation_probability_mean,weathercode&timezone=auto`;
    const response = await fetch(weatherUrl);
    const data = await response.json();
    const forecast = data.daily.time.map((date, index) => ({
      date,
      maxTemp: data.daily.temperature_2m_max[index],
      minTemp: data.daily.temperature_2m_min[index],
      precipitationChance: data.daily.precipitation_probability_mean[index],
      condition: getWeatherCondition$1(data.daily.weathercode[index]),
      location: name
    }));
    return forecast;
  }
});
const planActivities = createStep({
  id: "plan-activities",
  description: "Suggests activities based on weather conditions",
  inputSchema: {},
  outputSchema: z.object({
    activities: z.string()
  }),
  execute: async ({ inputData, mastra }) => {
    const forecast = inputData;
    if (!forecast || forecast.length === 0) {
      throw new Error("Forecast data not found");
    }
    const prompt = `Based on the following weather forecast for ${forecast[0]?.location}, suggest appropriate activities:
      ${JSON.stringify(forecast, null, 2)}
      `;
    const response = await agent.stream([
      {
        role: "user",
        content: prompt
      }
    ]);
    let activitiesText = "";
    for await (const chunk of response.textStream) {
      process.stdout.write(chunk);
      activitiesText += chunk;
    }
    return {
      activities: activitiesText
    };
  }
});
function getWeatherCondition$1(code) {
  const conditions = {
    0: "Clear sky",
    1: "Mainly clear",
    2: "Partly cloudy",
    3: "Overcast",
    45: "Foggy",
    48: "Depositing rime fog",
    51: "Light drizzle",
    53: "Moderate drizzle",
    55: "Dense drizzle",
    61: "Slight rain",
    63: "Moderate rain",
    65: "Heavy rain",
    71: "Slight snow fall",
    73: "Moderate snow fall",
    75: "Heavy snow fall",
    95: "Thunderstorm"
  };
  return conditions[code] || "Unknown";
}
const weatherWorkflow = createWorkflow({
  id: "weather-workflow",
  inputSchema: z.object({
    city: z.string().describe("The city to get the weather for")
  }),
  outputSchema: z.object({
    activities: z.string()
  }),
  steps: [fetchWeather]
}).then(planActivities).commit();
const weatherTool = createTool({
  id: "get-weather",
  description: "Get current weather for a location",
  inputSchema: z.object({
    location: z.string().describe("City name")
  }),
  outputSchema: z.object({
    temperature: z.number(),
    feelsLike: z.number(),
    humidity: z.number(),
    windSpeed: z.number(),
    windGust: z.number(),
    conditions: z.string(),
    location: z.string()
  }),
  execute: async ({ context }) => {
    return await getWeather(context.location);
  }
});
const getWeather = async (location) => {
  const geocodingUrl = `https://geocoding-api.open-meteo.com/v1/search?name=${encodeURIComponent(location)}&count=1`;
  const geocodingResponse = await fetch(geocodingUrl);
  const geocodingData = await geocodingResponse.json();
  if (!geocodingData.results?.[0]) {
    throw new Error(`Location '${location}' not found`);
  }
  const { latitude, longitude, name } = geocodingData.results[0];
  const weatherUrl = `https://api.open-meteo.com/v1/forecast?latitude=${latitude}&longitude=${longitude}&current=temperature_2m,apparent_temperature,relative_humidity_2m,wind_speed_10m,wind_gusts_10m,weather_code`;
  const response = await fetch(weatherUrl);
  const data = await response.json();
  return {
    temperature: data.current.temperature_2m,
    feelsLike: data.current.apparent_temperature,
    humidity: data.current.relative_humidity_2m,
    windSpeed: data.current.wind_speed_10m,
    windGust: data.current.wind_gusts_10m,
    conditions: getWeatherCondition(data.current.weather_code),
    location: name
  };
};
function getWeatherCondition(code) {
  const conditions = {
    0: "Clear sky",
    1: "Mainly clear",
    2: "Partly cloudy",
    3: "Overcast",
    45: "Foggy",
    48: "Depositing rime fog",
    51: "Light drizzle",
    53: "Moderate drizzle",
    55: "Dense drizzle",
    56: "Light freezing drizzle",
    57: "Dense freezing drizzle",
    61: "Slight rain",
    63: "Moderate rain",
    65: "Heavy rain",
    66: "Light freezing rain",
    67: "Heavy freezing rain",
    71: "Slight snow fall",
    73: "Moderate snow fall",
    75: "Heavy snow fall",
    77: "Snow grains",
    80: "Slight rain showers",
    81: "Moderate rain showers",
    82: "Violent rain showers",
    85: "Slight snow showers",
    86: "Heavy snow showers",
    95: "Thunderstorm",
    96: "Thunderstorm with slight hail",
    99: "Thunderstorm with heavy hail"
  };
  return conditions[code] || "Unknown";
}
new PinoLogger({
  name: "mastra",
  level: "debug"
});
const storage = new PostgresStore({
  connectionString: DATABASE_URL,
  schemaName: "mastra"
});
const vector = new PgVector({
  connectionString: DATABASE_URL
});
const memory = new Memory({
  storage,
  vector,
  embedder: fastembed,
  options: {
    semanticRecall: true,
    // Number of recent messages to include
    lastMessages: 20,
    // Working memory configuration
    workingMemory: {
      enabled: true
    },
    // Thread options
    threads: {
      generateTitle: true
    }
  }
});
const weatherAgent = new Agent({
  name: "Weather Agent",
  instructions: `
      You are a helpful weather assistant that provides accurate weather information.

      Your primary function is to help users get weather details for specific locations. When responding:
      - Always ask for a location if none is provided
      - If the location name isn’t in English, please translate it
      - If giving a location with multiple parts (e.g. "New York, NY"), use the most relevant part (e.g. "New York")
      - Include relevant details like humidity, wind conditions, and precipitation
      - Keep responses concise but informative

      Use the weatherTool to fetch current weather data.
`,
  model: openai("gpt-4o"),
  tools: { weatherTool },
  memory
});
new Mastra({
  workflows: { weatherWorkflow },
  agents: { weatherAgent },
  //logger,
  storage
  // server: {
  //   apiRoutes: [
  //     {
  //       path: '/',
  //       method: 'GET',
  //       handler: async (c) => {
  //         // Access the mastra instance
  //         const mastra = c.get('mastra')
  //         // Use mastra instance to access agents, workflows, etc.
  //         const agent = await mastra.getAgent('my-agent')
  //         return c.json({ message: 'Hello, world!' })
  //       }
  //     }
  //   ],
  //   middleware: [
  //     async (c, next) => {
  //       c.set('mastra', mastra)
  //       await next()
  //     }
  //   ]
  // }
});
export {
  api as a,
  auth as b
};
