import { S as SchemaError, a as schemaInfo, m as merge$1, b as assertSchema, c as SuperFormError, d as schemaShape, e as baseMemoize } from "./string.js";
import { getDefault, safeParseAsync } from "valibot";
function defaultValues(schema, isOptional = false, path = []) {
  return _defaultValues(schema, isOptional, path);
}
function _defaultValues(schema, isOptional, path) {
  if (!schema) {
    throw new SchemaError("Schema was undefined", path);
  }
  const info = schemaInfo(schema, isOptional, path);
  if (!info)
    return void 0;
  let objectDefaults = void 0;
  if ("default" in schema) {
    if (info.types.includes("object") && schema.default && typeof schema.default == "object" && !Array.isArray(schema.default)) {
      objectDefaults = schema.default;
    } else {
      if (info.types.length > 1) {
        if (info.types.includes("unix-time") && (info.types.includes("integer") || info.types.includes("number")))
          throw new SchemaError("Cannot resolve a default value with a union that includes a date and a number/integer.", path);
      }
      const [type] = info.types;
      return formatDefaultValue(type, schema.default);
    }
  }
  let _multiType;
  const isMultiTypeUnion = () => {
    if (!info.union || info.union.length < 2)
      return false;
    if (info.union.some((i) => i.enum))
      return true;
    if (!_multiType) {
      _multiType = new Set(info.types.map((i) => {
        return ["integer", "unix-time"].includes(i) ? "number" : i;
      }));
    }
    return _multiType.size > 1;
  };
  let output = void 0;
  if (!objectDefaults && info.union) {
    const singleDefault = info.union.filter((s) => typeof s !== "boolean" && s.default !== void 0);
    if (singleDefault.length == 1) {
      return _defaultValues(singleDefault[0], isOptional, path);
    } else if (singleDefault.length > 1) {
      throw new SchemaError("Only one default value can exist in a union, or set a default value for the whole union.", path);
    } else {
      if (info.isNullable)
        return null;
      if (info.isOptional)
        return void 0;
      if (isMultiTypeUnion()) {
        throw new SchemaError("Multi-type unions must have a default value, or exactly one of the union types must have.", path);
      }
      if (info.union.length) {
        if (info.types[0] == "object") {
          if (output === void 0)
            output = {};
          output = info.union.length > 1 ? merge$1.withOptions({ allowUndefinedOverrides: true }, ...info.union.map((s) => _defaultValues(s, isOptional, path))) : _defaultValues(info.union[0], isOptional, path);
        } else {
          return _defaultValues(info.union[0], isOptional, path);
        }
      }
    }
  }
  if (!objectDefaults) {
    if (info.isNullable)
      return null;
    if (info.isOptional)
      return void 0;
  }
  if (info.properties) {
    for (const [key, objSchema] of Object.entries(info.properties)) {
      assertSchema(objSchema, [...path, key]);
      const def = objectDefaults && objectDefaults[key] !== void 0 ? objectDefaults[key] : _defaultValues(objSchema, !info.required?.includes(key), [...path, key]);
      if (output === void 0)
        output = {};
      output[key] = def;
    }
  } else if (objectDefaults) {
    return objectDefaults;
  }
  if (schema.enum) {
    return schema.enum[0];
  }
  if ("const" in schema) {
    return schema.const;
  }
  if (isMultiTypeUnion()) {
    throw new SchemaError("Default values cannot have more than one type.", path);
  } else if (info.types.length == 0) {
    return void 0;
  }
  const [formatType] = info.types;
  return output ?? defaultValue(formatType, schema.enum);
}
function formatDefaultValue(type, value) {
  switch (type) {
    case "set":
      return Array.isArray(value) ? new Set(value) : value;
    case "Date":
    case "date":
    case "unix-time":
      if (typeof value === "string" || typeof value === "number")
        return new Date(value);
      break;
    case "bigint":
      if (typeof value === "string" || typeof value === "number")
        return BigInt(value);
      break;
    case "symbol":
      if (typeof value === "string" || typeof value === "number")
        return Symbol(value);
      break;
  }
  return value;
}
function defaultValue(type, enumType) {
  switch (type) {
    case "string":
      return enumType && enumType.length > 0 ? enumType[0] : "";
    case "number":
    case "integer":
      return enumType && enumType.length > 0 ? enumType[0] : 0;
    case "boolean":
      return false;
    case "array":
      return [];
    case "object":
      return {};
    case "null":
      return null;
    case "Date":
    case "date":
    case "unix-time":
      return void 0;
    case "int64":
    case "bigint":
      return BigInt(0);
    case "set":
      return /* @__PURE__ */ new Set();
    case "symbol":
      return Symbol();
    case "undefined":
    case "any":
      return void 0;
    default:
      throw new SchemaError("Schema type or format not supported, requires explicit default value: " + type);
  }
}
function defaults(data, adapter, options) {
  if (data && "superFormValidationLibrary" in data) {
    options = adapter;
    adapter = data;
    data = null;
  }
  const validator = adapter;
  const optionDefaults = options?.defaults ?? validator.defaults;
  return {
    id: options?.id ?? validator.id ?? "",
    valid: false,
    posted: false,
    errors: {},
    data: { ...optionDefaults, ...data },
    constraints: validator.constraints,
    shape: validator.shape
  };
}
function constraints(schema) {
  return _constraints(schemaInfo(schema, false, []), []);
}
function merge(...constraints2) {
  const filtered = constraints2.filter((c) => !!c);
  if (!filtered.length)
    return void 0;
  if (filtered.length == 1)
    return filtered[0];
  return merge$1(...filtered);
}
function _constraints(info, path) {
  if (!info)
    return void 0;
  let output = void 0;
  if (info.union && info.union.length) {
    const infos = info.union.map((s) => schemaInfo(s, info.isOptional, path));
    const merged = infos.map((i) => _constraints(i, path));
    output = merge(output, ...merged);
    if (output && (info.isNullable || info.isOptional || infos.some((i) => i?.isNullable || i?.isOptional))) {
      delete output.required;
    }
  }
  if (info.array) {
    output = merge(output, ...info.array.map((i) => _constraints(schemaInfo(i, info.isOptional, path), path)));
  }
  if (info.properties) {
    const obj = {};
    for (const [key, prop] of Object.entries(info.properties)) {
      const propInfo = schemaInfo(prop, !info.required?.includes(key) || prop.default !== void 0, [key]);
      const propConstraint = _constraints(propInfo, [...path, key]);
      if (typeof propConstraint === "object" && Object.values(propConstraint).length > 0) {
        obj[key] = propConstraint;
      }
    }
    output = merge(output, obj);
  }
  return output ?? constraint(info);
}
function constraint(info) {
  const output = {};
  const schema = info.schema;
  const type = schema.type;
  const format = schema.format;
  if (type == "integer" && format == "unix-time") {
    const date = schema;
    if (date.minimum !== void 0)
      output.min = new Date(date.minimum).toISOString();
    if (date.maximum !== void 0)
      output.max = new Date(date.maximum).toISOString();
  } else if (type == "string") {
    const str = schema;
    const patterns = [
      str.pattern,
      ...str.allOf ? str.allOf.map((s) => typeof s == "boolean" ? void 0 : s.pattern) : []
    ].filter((s) => s !== void 0);
    if (patterns.length > 0)
      output.pattern = patterns[0];
    if (str.minLength !== void 0)
      output.minlength = str.minLength;
    if (str.maxLength !== void 0)
      output.maxlength = str.maxLength;
  } else if (type == "number" || type == "integer") {
    const num = schema;
    if (num.minimum !== void 0)
      output.min = num.minimum;
    else if (num.exclusiveMinimum !== void 0)
      output.min = num.exclusiveMinimum + (type == "integer" ? 1 : Number.MIN_VALUE);
    if (num.maximum !== void 0)
      output.max = num.maximum;
    else if (num.exclusiveMaximum !== void 0)
      output.max = num.exclusiveMaximum - (type == "integer" ? 1 : Number.MIN_VALUE);
    if (num.multipleOf !== void 0)
      output.step = num.multipleOf;
  } else if (type == "array") {
    const arr = schema;
    if (arr.minItems !== void 0)
      output.min = arr.minItems;
    if (arr.maxItems !== void 0)
      output.max = arr.maxItems;
  }
  if (!info.isNullable && !info.isOptional) {
    output.required = true;
  }
  return Object.keys(output).length > 0 ? output : void 0;
}
function schemaHash(schema) {
  return hashCode(_schemaHash(schemaInfo(schema, false, []), 0, []));
}
function _schemaHash(info, depth, path) {
  if (!info)
    return "";
  function tab() {
    return "  ".repeat(depth);
  }
  function mapSchemas(schemas) {
    return schemas.map((s) => _schemaHash(schemaInfo(s, info?.isOptional ?? false, path), depth + 1, path)).filter((s) => s).join("|");
  }
  function nullish() {
    const output = [];
    if (info?.isNullable)
      output.push("null");
    if (info?.isOptional)
      output.push("undefined");
    return !output.length ? "" : "|" + output.join("|");
  }
  if (info.union) {
    return "Union {\n  " + tab() + mapSchemas(info.union) + "\n" + tab() + "}" + nullish();
  }
  if (info.properties) {
    const output = [];
    for (const [key, prop] of Object.entries(info.properties)) {
      const propInfo = schemaInfo(prop, !info.required?.includes(key) || prop.default !== void 0, [key]);
      output.push(key + ": " + _schemaHash(propInfo, depth + 1, path));
    }
    return "Object {\n  " + tab() + output.join(",\n  ") + "\n" + tab() + "}" + nullish();
  }
  if (info.array) {
    return "Array[" + mapSchemas(info.array) + "]" + nullish();
  }
  return info.types.join("|") + nullish();
}
function hashCode(str) {
  let hash = 0;
  for (let i = 0, len = str.length; i < len; i++) {
    const chr = str.charCodeAt(i);
    hash = (hash << 5) - hash + chr;
    hash |= 0;
  }
  if (hash < 0)
    hash = hash >>> 0;
  return hash.toString(36);
}
// @__NO_SIDE_EFFECTS__
function createAdapter(adapter, jsonSchema) {
  if (!adapter || !("superFormValidationLibrary" in adapter)) {
    throw new SuperFormError('Superforms v2 requires a validation adapter for the schema. Import one of your choice from "sveltekit-superforms/adapters" and wrap the schema with it.');
  }
  if (!jsonSchema)
    jsonSchema = adapter.jsonSchema;
  return {
    ...adapter,
    constraints: adapter.constraints ?? constraints(jsonSchema),
    defaults: adapter.defaults ?? defaultValues(jsonSchema),
    shape: schemaShape(jsonSchema),
    id: schemaHash(jsonSchema)
  };
}
const memoize = baseMemoize;
var JSON_SCHEMA_FEATURES_KEY = "__json_schema_features";
function getJSONSchemaFeatures(schema) {
  return schema[JSON_SCHEMA_FEATURES_KEY];
}
function assignExtraJSONSchemaFeatures(schema, converted) {
  const jsonSchemaFeatures = getJSONSchemaFeatures(schema);
  if (jsonSchemaFeatures) {
    Object.assign(converted, jsonSchemaFeatures);
  }
}
function assert(value, predicate, message) {
  if (!predicate(value)) throw new Error(message.replace("%", String(value)));
  return value;
}
var $schema = "http://json-schema.org/draft-07/schema#";
function isJSONLiteral(value) {
  return typeof value === "number" && !Number.isNaN(value) || typeof value === "string" || typeof value === "boolean" || value === null;
}
var assertJSONLiteral = (v) => assert(v, isJSONLiteral, "Unsupported literal value type: %");
function isEqual(obj1, obj2) {
  if (obj1 === obj2) return true;
  if (typeof obj1 === "object" && typeof obj2 === "object") {
    const keys1 = Object.keys(obj1);
    const keys2 = Object.keys(obj2);
    if (keys1.length !== keys2.length) return false;
    return keys1.every((key1) => isEqual(obj1[key1], obj2[key1]));
  }
  return false;
}
function isSchemaType(type) {
  return (schema) => {
    return !!schema && schema.type === type;
  };
}
var isNullishSchema = isSchemaType("nullish");
var isOptionalSchema = isSchemaType("optional");
var isStringSchema = isSchemaType("string");
var isNeverSchema = isSchemaType("never");
var toDefinitionURI = (name) => `#/definitions/${name}`;
var SCHEMA_CONVERTERS = {
  any: () => ({}),
  // Core types
  null: () => ({ const: null }),
  literal: ({ literal }) => ({ const: assertJSONLiteral(literal) }),
  number: () => ({ type: "number" }),
  string: () => ({ type: "string" }),
  boolean: () => ({ type: "boolean" }),
  // Compositions
  optional: (schema, convert) => {
    const output = convert(schema.wrapped);
    const defaultValue2 = getDefault(schema);
    if (defaultValue2 !== void 0) output.default = defaultValue2;
    return output;
  },
  nullish: (schema, convert) => {
    const output = { anyOf: [{ const: null }, convert(schema.wrapped)] };
    const defaultValue2 = getDefault(schema);
    if (defaultValue2 !== void 0) output.default = defaultValue2;
    return output;
  },
  nullable: (schema, convert) => {
    const output = { anyOf: [{ const: null }, convert(schema.wrapped)] };
    const defaultValue2 = getDefault(schema);
    if (defaultValue2 !== void 0) output.default = defaultValue2;
    return output;
  },
  picklist: ({ options }) => ({ enum: options.map(assertJSONLiteral) }),
  enum: (options) => ({ enum: Object.values(options.enum).map(assertJSONLiteral) }),
  union: ({ options }, convert) => ({ anyOf: options.map(convert) }),
  intersect: ({ options }, convert) => ({ allOf: options.map(convert) }),
  // Complex types
  array: ({ item }, convert) => ({ type: "array", items: convert(item) }),
  tuple_with_rest({ items: originalItems, rest }, convert) {
    const minItems = originalItems.length;
    let maxItems;
    let items = originalItems.map(convert);
    let additionalItems;
    if (isNeverSchema(rest)) {
      maxItems = minItems;
    } else if (rest) {
      const restItems = convert(rest);
      if (items.length === 1 && isEqual(items[0], restItems)) {
        items = items[0];
      } else {
        additionalItems = restItems;
      }
    }
    return {
      type: "array",
      items,
      ...additionalItems && { additionalItems },
      ...minItems && { minItems },
      ...maxItems && { maxItems }
    };
  },
  strict_tuple({ items: originalItems }, convert) {
    const items = originalItems.map(convert);
    return { type: "array", items, minItems: items.length, maxItems: items.length };
  },
  tuple({ items: originalItems }, convert, context) {
    const items = originalItems.map(convert);
    return { type: "array", items, minItems: items.length };
  },
  object_with_rest({ entries, rest }, convert, context) {
    const properties = {};
    const required = [];
    for (const [propKey, propValue] of Object.entries(entries)) {
      const propSchema = propValue;
      if (!isOptionalSchema(propSchema) && !isNullishSchema(propSchema)) {
        required.push(propKey);
      }
      properties[propKey] = convert(propSchema);
      assignExtraJSONSchemaFeatures(propValue, properties[propKey]);
    }
    let additionalProperties;
    if (rest) {
      additionalProperties = isNeverSchema(rest) ? false : convert(rest);
    } else if (context.strictObjectTypes) {
      additionalProperties = false;
    }
    const output = { type: "object", properties };
    if (additionalProperties !== void 0) output.additionalProperties = additionalProperties;
    if (required.length) output.required = required;
    return output;
  },
  object(schema, convert, context) {
    return SCHEMA_CONVERTERS.object_with_rest(schema, convert, context);
  },
  strict_object(schema, convert, context) {
    const object = SCHEMA_CONVERTERS.object_with_rest(schema, convert, context);
    return { ...object, additionalProperties: false };
  },
  record({ key, value }, convert) {
    assert(key, isStringSchema, "Unsupported record key type: %");
    return { type: "object", additionalProperties: convert(value) };
  },
  lazy(schema, _, context) {
    const nested = schema.getter({});
    const defName = context.defNameMap.get(nested);
    if (!defName) {
      throw new Error("Type inside lazy schema must be provided in the definitions");
    }
    return { $ref: toDefinitionURI(defName) };
  },
  date(_, __, context) {
    if (!context.dateStrategy) {
      throw new Error('The "dateStrategy" option must be set to handle date validators');
    }
    switch (context.dateStrategy) {
      case "integer":
        return { type: "integer", format: "unix-time" };
      case "string":
        return { type: "string", format: "date-time" };
    }
  },
  undefined(_, __, context) {
    if (!context.undefinedStrategy) {
      throw new Error('The "undefinedStrategy" option must be set to handle the `undefined` schema');
    }
    switch (context.undefinedStrategy) {
      case "any":
        return {};
      case "null":
        return { type: "null" };
    }
  },
  bigint(_, __, context) {
    if (!context.bigintStrategy) {
      throw new Error('The "bigintStrategy" option must be set to handle `bigint` validators');
    }
    switch (context.bigintStrategy) {
      case "integer":
        return { type: "integer", format: "int64" };
      case "string":
        return { type: "string" };
    }
  },
  variant({ options }, ...args) {
    return SCHEMA_CONVERTERS.union({ options }, ...args);
  }
};
var METADATA_BY_TYPE = {
  description: ({ description }) => ({ description }),
  "@gcornut/to-json-schema/json_schema_metadata": ({ metadata }) => metadata
};
function asDateRequirement(type, requirement, context) {
  assert(requirement, () => context.dateStrategy === "integer", `${type} validation is only available with 'integer' date strategy`);
  assert(requirement, (r) => r instanceof Date, `Non-date value used for ${type} validation`);
  return requirement.getTime();
}
var VALIDATION_BY_SCHEMA = {
  array: {
    length: ({ requirement }) => ({ minItems: requirement, maxItems: requirement }),
    min_length: ({ requirement }) => ({ minItems: requirement }),
    max_length: ({ requirement }) => ({ maxItems: requirement })
  },
  string: {
    value: ({ requirement }) => ({ const: requirement }),
    length: ({ requirement }) => ({ minLength: requirement, maxLength: requirement }),
    min_length: ({ requirement }) => ({ minLength: requirement }),
    max_length: ({ requirement }) => ({ maxLength: requirement }),
    // TODO: validate RegExp features are compatible with json schema ?
    regex: ({ requirement }) => ({ pattern: requirement.source }),
    email: () => ({ format: "email" }),
    iso_date: () => ({ format: "date" }),
    iso_timestamp: () => ({ format: "date-time" }),
    ipv4: () => ({ format: "ipv4" }),
    ipv6: () => ({ format: "ipv6" }),
    uuid: () => ({ format: "uuid" })
  },
  number: {
    value: ({ requirement }) => ({ const: requirement }),
    min_value: ({ requirement }) => ({ minimum: requirement }),
    max_value: ({ requirement }) => ({ maximum: requirement }),
    multiple_of: ({ requirement }) => ({ multipleOf: requirement }),
    integer: () => ({ type: "integer" })
  },
  boolean: {
    value: ({ requirement }) => ({ const: requirement })
  },
  date: {
    value: ({ requirement }, context) => ({ const: asDateRequirement("value", requirement, context) }),
    min_value: ({ requirement }, context) => ({ minimum: asDateRequirement("minValue", requirement, context) }),
    max_value: ({ requirement }, context) => ({ maximum: asDateRequirement("maxValue", requirement, context) })
  }
};
function convertPipe(schemaType, pipe, context) {
  const [schema, ...pipeItems] = pipe || [];
  if (!schema) return {};
  const childPipe = convertPipe(schemaType, schema == null ? void 0 : schema.pipe, context);
  function convertPipeItem(def, validation) {
    var _a, _b, _c;
    const validationType = validation.type;
    const validationConverter = ((_b = (_a = context.customValidationConversion) == null ? void 0 : _a[schemaType]) == null ? void 0 : _b[validationType]) || ((_c = VALIDATION_BY_SCHEMA[schemaType]) == null ? void 0 : _c[validationType]) || METADATA_BY_TYPE[validationType];
    if (!validationConverter && context.ignoreUnknownValidation) return {};
    assert(validationConverter, Boolean, `Unsupported valibot validation \`${validationType}\` for schema \`${schemaType}\``);
    const converted = validationConverter(validation, context);
    return Object.assign(def, converted);
  }
  return pipeItems.reduce(convertPipeItem, childPipe);
}
function getDefNameMap(definitions = {}) {
  const map = /* @__PURE__ */ new Map();
  for (const [name, definition] of Object.entries(definitions)) {
    map.set(definition, name);
  }
  return map;
}
function createConverter(context) {
  const definitions = {};
  function converter(schema) {
    var _a;
    const defName = context.defNameMap.get(schema);
    const defURI = defName && toDefinitionURI(defName);
    if (defURI && defURI in definitions) {
      return { $ref: defURI };
    }
    const schemaConverter = ((_a = context.customSchemaConversion) == null ? void 0 : _a[schema.type]) || SCHEMA_CONVERTERS[schema.type];
    assert(schemaConverter, Boolean, `Unsupported valibot schema: ${(schema == null ? void 0 : schema.type) || schema}`);
    let converted = schemaConverter(schema, converter, context) || {};
    const convertedValidation = convertPipe(schema.type, schema.pipe, context);
    converted = { ...converted, ...convertedValidation };
    assignExtraJSONSchemaFeatures(schema, converted);
    if (defURI) {
      definitions[defName] = converted;
      return { $ref: defURI };
    }
    return converted;
  }
  return { definitions, converter };
}
function toJSONSchema(options) {
  const { schema, definitions: inputDefinitions, ...more } = options;
  const defNameMap = getDefNameMap(inputDefinitions);
  const { definitions, converter } = createConverter({ defNameMap, ...more });
  if (!schema && !inputDefinitions) {
    throw new Error("No main schema or definitions provided.");
  }
  if (inputDefinitions) {
    Object.values(inputDefinitions).forEach(converter);
  }
  const mainConverted = schema && converter(schema);
  const mainDefName = schema && defNameMap.get(schema);
  const out = { $schema };
  if (mainDefName) {
    out.$ref = toDefinitionURI(mainDefName);
  } else {
    Object.assign(out, mainConverted);
  }
  if (Object.keys(definitions).length) {
    out.definitions = definitions;
  }
  return out;
}
const defaultOptions = {
  strictObjectTypes: true,
  dateStrategy: "integer",
  bigintStrategy: "integer",
  ignoreUnknownValidation: true,
  customSchemaConversion: {
    custom: () => ({}),
    instance: () => ({}),
    file: () => ({}),
    blob: () => ({})
  }
};
const valibotToJSONSchema = /* @__NO_SIDE_EFFECTS__ */ (options) => {
  return toJSONSchema({ ...defaultOptions, ...options });
};
async function _validate(schema, data, config) {
  const result = await safeParseAsync(schema, data, config);
  if (result.success) {
    return {
      data: result.output,
      success: true
    };
  }
  return {
    issues: result.issues.map(({ message, path }) => ({
      message,
      path: path?.map(({ key }) => key)
    })),
    success: false
  };
}
function _valibot(schema, options = {}) {
  return /* @__PURE__ */ createAdapter({
    superFormValidationLibrary: "valibot",
    validate: async (data) => _validate(schema, data, options?.config),
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    jsonSchema: options?.jsonSchema ?? /* @__PURE__ */ valibotToJSONSchema({ schema, ...options }),
    defaults: "defaults" in options ? options.defaults : void 0
  });
}
function _valibotClient(schema, options = {}) {
  return {
    superFormValidationLibrary: "valibot",
    validate: async (data) => _validate(schema, data, options?.config)
  };
}
const valibot = /* @__PURE__ */ memoize(_valibot);
const valibotClient = /* @__PURE__ */ memoize(_valibotClient);
export {
  valibotClient as a,
  defaults as d,
  valibot as v
};
