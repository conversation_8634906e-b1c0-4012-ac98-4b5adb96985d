import { N as derived, w as push, O as props_id, F as spread_attributes, P as bind_props, y as pop, E as ensure_array_like, G as spread_props, Q as copy_payload, R as assign_payload, K as attr_style, D as stringify } from "./index2.js";
import { scaleBand, scaleTime, scaleLinear } from "d3-scale";
import { stack, stackOffsetExpand, stackOffsetDiverging, stackOffsetNone } from "d3-shape";
import { c as cls } from "./index4.js";
import { g as getChartContext, n as chartDataArray, B as Group, E as layerClass, F as Bar, j as extractLayerProps, l as SeriesState, c as accessor, I as isScaleTime, s as setTooltipMetaContext, o as Chart, p as defaultChartPadding, L as Layer, q as asAny, G as Grid, t as ChartClipPath, u as ChartAnnotations, R as Rule, A as Axis, H as Highlight, v as Labels, w as Legend, D as DefaultTooltip, x as format, y as createLegendProps } from "./chart-tooltip.js";
import { c as cn } from "./shadcn.utils.js";
import { a as attachRef, c as createBitsAttrs, b as createId, d as box, m as mergeProps } from "./create-id.js";
import "./states.svelte.js";
import "clsx";
const progressAttrs = createBitsAttrs({ component: "progress", parts: ["root"] });
class ProgressRootState {
  static create(opts) {
    return new ProgressRootState(opts);
  }
  opts;
  attachment;
  constructor(opts) {
    this.opts = opts;
    this.attachment = attachRef(this.opts.ref);
  }
  #props = derived(() => ({
    role: "progressbar",
    value: this.opts.value.current,
    "aria-valuemin": this.opts.min.current,
    "aria-valuemax": this.opts.max.current,
    "aria-valuenow": this.opts.value.current === null ? void 0 : this.opts.value.current,
    "data-value": this.opts.value.current === null ? void 0 : this.opts.value.current,
    "data-state": getProgressDataState(this.opts.value.current, this.opts.max.current),
    "data-max": this.opts.max.current,
    "data-min": this.opts.min.current,
    "data-indeterminate": this.opts.value.current === null ? "" : void 0,
    [progressAttrs.root]: "",
    ...this.attachment
  }));
  get props() {
    return this.#props();
  }
  set props($$value) {
    return this.#props($$value);
  }
}
function getProgressDataState(value, max) {
  if (value === null) return "indeterminate";
  return value === max ? "loaded" : "loading";
}
function Progress$1($$payload, $$props) {
  push();
  const uid = props_id($$payload);
  let {
    child,
    children,
    value = 0,
    max = 100,
    min = 0,
    id = createId(uid),
    ref = null,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const rootState = ProgressRootState.create({
    value: box.with(() => value),
    max: box.with(() => max),
    min: box.with(() => min),
    id: box.with(() => id),
    ref: box.with(() => ref, (v) => ref = v)
  });
  const mergedProps = mergeProps(restProps, rootState.props);
  if (child) {
    $$payload.out.push("<!--[-->");
    child($$payload, { props: mergedProps });
    $$payload.out.push(`<!---->`);
  } else {
    $$payload.out.push("<!--[!-->");
    $$payload.out.push(`<div${spread_attributes({ ...mergedProps }, null)}>`);
    children?.($$payload);
    $$payload.out.push(`<!----></div>`);
  }
  $$payload.out.push(`<!--]-->`);
  bind_props($$props, { ref });
  pop();
}
function Bars($$payload, $$props) {
  push();
  let {
    fill,
    key = (_, i) => i,
    data: dataProp,
    onBarClick = () => {
    },
    children,
    radius = 0,
    strokeWidth = 0,
    stroke = "black",
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const ctx = getChartContext();
  const data = chartDataArray(dataProp ?? ctx.data);
  Group($$payload, {
    class: layerClass("bars"),
    children: ($$payload2) => {
      if (children) {
        $$payload2.out.push("<!--[-->");
        children($$payload2);
        $$payload2.out.push(`<!---->`);
      } else {
        $$payload2.out.push("<!--[!-->");
        const each_array = ensure_array_like(data);
        $$payload2.out.push(`<!--[-->`);
        for (let i = 0, $$length = each_array.length; i < $$length; i++) {
          let d = each_array[i];
          Bar($$payload2, spread_props([
            {
              data: d,
              radius,
              strokeWidth,
              stroke,
              fill: fill ?? (ctx.config.c ? ctx.cGet(d) : null),
              onclick: (e) => onBarClick(e, { data: d })
            },
            extractLayerProps(restProps, "bars-bar")
          ]));
        }
        $$payload2.out.push(`<!--]-->`);
      }
      $$payload2.out.push(`<!--]-->`);
    },
    $$slots: { default: true }
  });
  pop();
}
function BarChart($$payload, $$props) {
  push();
  let {
    data = [],
    x: xProp,
    y: yProp,
    xDomain,
    radial = false,
    orientation = "vertical",
    series: seriesProp,
    seriesLayout = "overlap",
    axis = true,
    brush = false,
    grid = true,
    labels = false,
    legend = false,
    points = false,
    rule = true,
    onTooltipClick = () => {
    },
    onBarClick = () => {
    },
    props = {},
    renderContext = "svg",
    profile = false,
    debug = false,
    xScale: xScaleProp,
    yScale: yScaleProp,
    bandPadding = radial ? 0 : 0.4,
    groupPadding = 0,
    stackPadding = 0,
    tooltip = true,
    children: childrenProp,
    aboveContext,
    belowContext,
    belowMarks,
    aboveMarks,
    marks,
    highlight = true,
    annotations = [],
    context = void 0,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const series = seriesProp === void 0 ? [
    {
      key: "default",
      value: orientation === "vertical" ? yProp : xProp
    }
  ] : seriesProp;
  const seriesState = new SeriesState(() => series);
  const isVertical = orientation === "vertical";
  const isStackSeries = seriesLayout.startsWith("stack");
  const isGroupSeries = seriesLayout === "group";
  const chartData = (() => {
    let _chartData = seriesState.allSeriesData.length ? seriesState.allSeriesData : chartDataArray(data);
    if (isStackSeries) {
      const seriesKeys = seriesState.visibleSeries.map((s) => s.key);
      const offset = seriesLayout === "stackExpand" ? stackOffsetExpand : seriesLayout === "stackDiverging" ? stackOffsetDiverging : stackOffsetNone;
      const stackData = stack().keys(seriesKeys).value((d, key) => {
        const s = series.find((d2) => d2.key === key);
        return accessor(s.value ?? s.key)(d);
      }).offset(offset)(chartDataArray(data));
      _chartData = _chartData.map((d, i) => {
        return { ...d, stackData: stackData.map((sd) => sd[i]) };
      });
    }
    return _chartData;
  })();
  const xScale = xScaleProp ?? (isVertical ? scaleBand().padding(bandPadding) : accessor(xProp)(chartData[0]) instanceof Date ? (
    // TODO: also check for Array<Date> instances (ex. x={['start', 'end']})
    scaleTime()
  ) : scaleLinear());
  const xBaseline = isVertical || isScaleTime(xScale) ? void 0 : 0;
  const yScale = yScaleProp ?? (isVertical ? accessor(yProp)(chartData[0]) instanceof Date ? (
    // TODO: also check for Array<Date> instances (ex. y={['start', 'end']})
    scaleTime()
  ) : scaleLinear() : scaleBand().padding(bandPadding));
  const yBaseline = isVertical || isScaleTime(yScale) ? 0 : void 0;
  const x1Scale = isGroupSeries && isVertical ? scaleBand().padding(groupPadding) : void 0;
  const x1Domain = isGroupSeries && isVertical ? seriesState.visibleSeries.map((s) => s.key) : void 0;
  const x1Range = isGroupSeries && isVertical ? (
    // TODO: can we do something better here where we don't need to cast this
    // feels fragile!
    ({ xScale: xScale2 }) => [0, xScale2.bandwidth()]
  ) : void 0;
  const y1Scale = isGroupSeries && !isVertical ? scaleBand().padding(groupPadding) : void 0;
  const y1Domain = isGroupSeries && !isVertical ? seriesState.visibleSeries.map((s) => s.key) : void 0;
  const y1Range = isGroupSeries && !isVertical ? (
    // TODO: can we do something better here where we don't need to cast this
    // feels fragile!
    ({ yScale: yScale2 }) => [0, yScale2.bandwidth()]
  ) : void 0;
  function isStackData(d) {
    return d && typeof d === "object" && "stackData" in d;
  }
  function getBarsProps(s, i) {
    const isFirst = i == 0;
    const isLast = i == seriesState.visibleSeries.length - 1;
    const isStackLayout = seriesLayout.startsWith("stack");
    let stackInsets = void 0;
    if (isStackLayout) {
      const stackInset = stackPadding / 2;
      if (isVertical) {
        stackInsets = {
          bottom: isFirst ? void 0 : stackInset,
          top: isLast ? void 0 : stackInset
        };
      } else {
        stackInsets = {
          left: isFirst ? void 0 : stackInset,
          right: isLast ? void 0 : stackInset
        };
      }
    }
    const valueAccessor = isStackSeries ? (d) => d.stackData[i] : s.value ?? (s.data ? void 0 : s.key);
    return {
      data: s.data,
      x: !isVertical ? valueAccessor : void 0,
      y: isVertical ? valueAccessor : void 0,
      x1: isVertical && isGroupSeries ? (d) => s.value ?? s.key : void 0,
      y1: !isVertical && isGroupSeries ? (d) => s.value ?? s.key : void 0,
      rounded: isStackLayout && i !== seriesState.visibleSeries.length - 1 ? "none" : Array.isArray(xProp) || Array.isArray(yProp) ? "all" : "edge",
      radius: 4,
      strokeWidth: 1,
      insets: stackInsets,
      fill: s.color,
      onBarClick: (e, detail) => onBarClick(e, { ...detail, series: s }),
      ...props.bars,
      ...s.props,
      class: cls("transition-opacity", seriesState.highlightKey.current && seriesState.highlightKey.current !== s.key && "opacity-10", props.bars?.class, s.props?.class)
    };
  }
  function getLabelsProps(s, i) {
    return {
      // TODO: Improve placement when using `seriesLayout="group"`
      // data: s.data,
      // y: s.value ?? (s.data ? undefined : s.key),
      ...props.labels,
      ...typeof labels === "object" ? labels : null,
      class: cls("stroke-surface-200 transition-opacity", seriesState.highlightKey.current && seriesState.highlightKey.current !== s.key && "opacity-10", props.labels?.class, typeof labels === "object" && labels.class)
    };
  }
  const brushProps = { ...typeof brush === "object" ? brush : null, ...props.brush };
  function getLegendProps() {
    return createLegendProps({
      seriesState,
      props: {
        ...props.legend,
        ...typeof legend === "object" ? legend : null
      }
    });
  }
  function getGridProps() {
    return {
      x: !isVertical || radial,
      y: isVertical || radial,
      ...typeof grid === "object" ? grid : null,
      ...props.grid
    };
  }
  function getHighlightProps() {
    return { area: true, ...props.highlight };
  }
  function getAxisProps(axisDirection) {
    if (axisDirection === "y") {
      return {
        placement: radial ? "radius" : "left",
        format: isVertical && seriesLayout === "stackExpand" ? (value) => format(value, "percentRound") : void 0,
        ...typeof axis === "object" ? axis : null,
        ...props.yAxis
      };
    }
    return {
      placement: radial ? "angle" : "bottom",
      format: !isVertical && seriesLayout === "stackExpand" ? (value) => format(value, "percentRound") : void 0,
      ...typeof axis === "object" ? axis : null,
      ...props.xAxis
    };
  }
  function getRuleProps() {
    return {
      x: isVertical ? false : 0,
      y: isVertical ? 0 : false,
      ...typeof rule === "object" ? rule : null,
      ...props.rule
    };
  }
  if (profile) {
    console.time("BarChart render");
  }
  setTooltipMetaContext({
    type: "bar",
    get orientation() {
      return orientation;
    },
    get stackSeries() {
      return isStackSeries;
    },
    get visibleSeries() {
      return seriesState.visibleSeries;
    }
  });
  function resolveAccessor(acc) {
    if (acc) return acc;
    if (isStackSeries) {
      return (d) => isStackData(d) ? seriesState.visibleSeries.flatMap((s, i) => d.stackData[i]) : void 0;
    }
    return seriesState.visibleSeries.map((s) => s.value ?? s.key);
  }
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    {
      let children = function($$payload3, { context: context2 }) {
        const snippetProps = {
          context: context2,
          series,
          visibleSeries: seriesState.visibleSeries,
          getBarsProps,
          getLabelsProps,
          getLegendProps,
          getGridProps,
          getHighlightProps,
          getAxisProps,
          getRuleProps,
          highlightKey: seriesState.highlightKey.current,
          setHighlightKey: seriesState.highlightKey.set
        };
        if (childrenProp) {
          $$payload3.out.push("<!--[-->");
          childrenProp($$payload3, snippetProps);
          $$payload3.out.push(`<!---->`);
        } else {
          $$payload3.out.push("<!--[!-->");
          belowContext?.($$payload3, snippetProps);
          $$payload3.out.push(`<!----> `);
          Layer($$payload3, spread_props([
            { type: renderContext },
            asAny(renderContext === "canvas" ? props.canvas : props.svg),
            {
              center: radial,
              debug,
              children: ($$payload4) => {
                if (typeof grid === "function") {
                  $$payload4.out.push("<!--[-->");
                  grid($$payload4, snippetProps);
                  $$payload4.out.push(`<!---->`);
                } else {
                  $$payload4.out.push("<!--[!-->");
                  if (grid) {
                    $$payload4.out.push("<!--[-->");
                    Grid($$payload4, spread_props([getGridProps()]));
                  } else {
                    $$payload4.out.push("<!--[!-->");
                  }
                  $$payload4.out.push(`<!--]-->`);
                }
                $$payload4.out.push(`<!--]--> `);
                ChartClipPath($$payload4, {
                  disabled: !brush,
                  children: ($$payload5) => {
                    ChartAnnotations($$payload5, {
                      annotations,
                      layer: "below",
                      highlightKey: seriesState.highlightKey.current,
                      visibleSeries: seriesState.visibleSeries
                    });
                    $$payload5.out.push(`<!----> `);
                    belowMarks?.($$payload5, snippetProps);
                    $$payload5.out.push(`<!----> `);
                    if (typeof marks === "function") {
                      $$payload5.out.push("<!--[-->");
                      marks($$payload5, snippetProps);
                      $$payload5.out.push(`<!---->`);
                    } else {
                      $$payload5.out.push("<!--[!-->");
                      const each_array = ensure_array_like(seriesState.visibleSeries);
                      $$payload5.out.push(`<!--[-->`);
                      for (let i = 0, $$length = each_array.length; i < $$length; i++) {
                        let s = each_array[i];
                        Bars($$payload5, spread_props([getBarsProps(s, i)]));
                      }
                      $$payload5.out.push(`<!--]-->`);
                    }
                    $$payload5.out.push(`<!--]-->`);
                  },
                  $$slots: { default: true }
                });
                $$payload4.out.push(`<!----> `);
                aboveMarks?.($$payload4, snippetProps);
                $$payload4.out.push(`<!----> `);
                if (typeof axis === "function") {
                  $$payload4.out.push("<!--[-->");
                  axis($$payload4, snippetProps);
                  $$payload4.out.push(`<!----> `);
                  if (typeof rule === "function") {
                    $$payload4.out.push("<!--[-->");
                    rule($$payload4, snippetProps);
                    $$payload4.out.push(`<!---->`);
                  } else {
                    $$payload4.out.push("<!--[!-->");
                    if (rule) {
                      $$payload4.out.push("<!--[-->");
                      Rule($$payload4, spread_props([getRuleProps()]));
                    } else {
                      $$payload4.out.push("<!--[!-->");
                    }
                    $$payload4.out.push(`<!--]-->`);
                  }
                  $$payload4.out.push(`<!--]-->`);
                } else {
                  $$payload4.out.push("<!--[!-->");
                  if (axis) {
                    $$payload4.out.push("<!--[-->");
                    if (axis !== "x") {
                      $$payload4.out.push("<!--[-->");
                      Axis($$payload4, spread_props([getAxisProps("y")]));
                    } else {
                      $$payload4.out.push("<!--[!-->");
                    }
                    $$payload4.out.push(`<!--]--> `);
                    if (axis !== "y") {
                      $$payload4.out.push("<!--[-->");
                      Axis($$payload4, spread_props([getAxisProps("x")]));
                    } else {
                      $$payload4.out.push("<!--[!-->");
                    }
                    $$payload4.out.push(`<!--]--> `);
                    if (typeof rule === "function") {
                      $$payload4.out.push("<!--[-->");
                      rule($$payload4, snippetProps);
                      $$payload4.out.push(`<!---->`);
                    } else {
                      $$payload4.out.push("<!--[!-->");
                      if (rule) {
                        $$payload4.out.push("<!--[-->");
                        Rule($$payload4, spread_props([getRuleProps()]));
                      } else {
                        $$payload4.out.push("<!--[!-->");
                      }
                      $$payload4.out.push(`<!--]-->`);
                    }
                    $$payload4.out.push(`<!--]-->`);
                  } else {
                    $$payload4.out.push("<!--[!-->");
                  }
                  $$payload4.out.push(`<!--]-->`);
                }
                $$payload4.out.push(`<!--]--> `);
                ChartClipPath($$payload4, {
                  disabled: !brush,
                  full: true,
                  children: ($$payload5) => {
                    if (typeof highlight === "function") {
                      $$payload5.out.push("<!--[-->");
                      highlight($$payload5, snippetProps);
                      $$payload5.out.push(`<!---->`);
                    } else {
                      $$payload5.out.push("<!--[!-->");
                      if (highlight) {
                        $$payload5.out.push("<!--[-->");
                        Highlight($$payload5, spread_props([getHighlightProps()]));
                      } else {
                        $$payload5.out.push("<!--[!-->");
                      }
                      $$payload5.out.push(`<!--]-->`);
                    }
                    $$payload5.out.push(`<!--]--> `);
                    if (typeof labels === "function") {
                      $$payload5.out.push("<!--[-->");
                      labels($$payload5, snippetProps);
                      $$payload5.out.push(`<!---->`);
                    } else {
                      $$payload5.out.push("<!--[!-->");
                      if (labels) {
                        $$payload5.out.push("<!--[-->");
                        const each_array_1 = ensure_array_like(seriesState.visibleSeries);
                        $$payload5.out.push(`<!--[-->`);
                        for (let i = 0, $$length = each_array_1.length; i < $$length; i++) {
                          let s = each_array_1[i];
                          Labels($$payload5, spread_props([getLabelsProps(s)]));
                        }
                        $$payload5.out.push(`<!--]-->`);
                      } else {
                        $$payload5.out.push("<!--[!-->");
                      }
                      $$payload5.out.push(`<!--]-->`);
                    }
                    $$payload5.out.push(`<!--]--> `);
                    ChartAnnotations($$payload5, {
                      annotations,
                      layer: "above",
                      highlightKey: seriesState.highlightKey.current,
                      visibleSeries: seriesState.visibleSeries
                    });
                    $$payload5.out.push(`<!---->`);
                  },
                  $$slots: { default: true }
                });
                $$payload4.out.push(`<!---->`);
              },
              $$slots: { default: true }
            }
          ]));
          $$payload3.out.push(`<!----> `);
          aboveContext?.($$payload3, snippetProps);
          $$payload3.out.push(`<!----> `);
          if (typeof legend === "function") {
            $$payload3.out.push("<!--[-->");
            legend($$payload3, snippetProps);
            $$payload3.out.push(`<!---->`);
          } else {
            $$payload3.out.push("<!--[!-->");
            if (legend) {
              $$payload3.out.push("<!--[-->");
              Legend($$payload3, spread_props([getLegendProps()]));
            } else {
              $$payload3.out.push("<!--[!-->");
            }
            $$payload3.out.push(`<!--]-->`);
          }
          $$payload3.out.push(`<!--]--> `);
          if (typeof tooltip === "function") {
            $$payload3.out.push("<!--[-->");
            tooltip($$payload3, snippetProps);
            $$payload3.out.push(`<!---->`);
          } else {
            $$payload3.out.push("<!--[!-->");
            if (tooltip) {
              $$payload3.out.push("<!--[-->");
              DefaultTooltip($$payload3, {
                tooltipProps: props.tooltip,
                canHaveTotal: isStackSeries || isGroupSeries,
                seriesState
              });
            } else {
              $$payload3.out.push("<!--[!-->");
            }
            $$payload3.out.push(`<!--]-->`);
          }
          $$payload3.out.push(`<!--]-->`);
        }
        $$payload3.out.push(`<!--]-->`);
      };
      Chart($$payload2, spread_props([
        {
          data: chartData,
          x: resolveAccessor(xProp),
          xDomain,
          xScale,
          xBaseline,
          xNice: orientation === "horizontal",
          x1Scale,
          x1Domain,
          x1Range,
          y: resolveAccessor(yProp),
          yScale,
          yBaseline,
          yNice: orientation === "vertical",
          y1Scale,
          y1Domain,
          y1Range,
          c: isVertical ? yProp : xProp,
          cRange: ["var(--color-primary)"],
          radial,
          padding: radial ? void 0 : defaultChartPadding(axis, legend)
        },
        restProps,
        {
          tooltip: tooltip === false ? false : {
            mode: "band",
            onclick: onTooltipClick,
            debug,
            ...props.tooltip?.context
          },
          brush: brush && (brush === true || brush.mode == void 0 || brush.mode === "integrated") ? {
            axis: "x",
            resetOnEnd: true,
            xDomain,
            ...brushProps,
            onBrushEnd: (e) => {
              xDomain = e.xDomain;
              brushProps.onBrushEnd?.(e);
            }
          } : false,
          get context() {
            return context;
          },
          set context($$value) {
            context = $$value;
            $$settled = false;
          },
          children,
          $$slots: { default: true }
        }
      ]));
    }
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { context });
  pop();
}
function Progress($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    max = 100,
    value,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out.push(`<!---->`);
    Progress$1($$payload2, spread_props([
      {
        "data-slot": "progress",
        class: cn("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full", className),
        value,
        max
      },
      restProps,
      {
        get ref() {
          return ref;
        },
        set ref($$value) {
          ref = $$value;
          $$settled = false;
        },
        children: ($$payload3) => {
          $$payload3.out.push(`<div data-slot="progress-indicator" class="bg-primary h-full w-full flex-1 transition-all"${attr_style(`transform: translateX(-${stringify(100 - 100 * (value ?? 0) / (max ?? 1))}%)`)}></div>`);
        },
        $$slots: { default: true }
      }
    ]));
    $$payload2.out.push(`<!---->`);
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref });
  pop();
}
export {
  BarChart as B,
  Progress as P
};
