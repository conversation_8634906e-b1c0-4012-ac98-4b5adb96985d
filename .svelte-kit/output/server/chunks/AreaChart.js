import { G as spread_props, y as pop, w as push, F as spread_attributes, Q as copy_payload, R as assign_payload, P as bind_props, E as ensure_array_like } from "./index2.js";
import { I as Icon } from "./states.svelte.js";
import { scaleTime, scaleLinear } from "d3-scale";
import { areaRadial, area, stack, stackOffsetExpand, stackOffsetDiverging, stackOffsetNone } from "d3-shape";
import { i as interpolatePath, c as cls } from "./index4.js";
import { g as getChartContext, b as getRenderContext, c as accessor, i as isScaleBand, e as extractTweenConfig, m as min, d as max, f as createMotion, h as createKey, r as registerCanvasComponent, S as Spline, j as extractLayerProps, k as flattenPathData, l as SeriesState, n as chartDataArray, s as setTooltipMetaContext, o as Chart, p as defaultChartPadding, L as Layer, q as asAny, G as Grid, t as ChartClipPath, u as ChartAnnotations, R as Rule, A as Axis, P as Points, H as Highlight, v as Labels, w as Legend, D as DefaultTooltip, x as format, y as createLegendProps, z as findRelatedData } from "./chart-tooltip.js";
import "clsx";
function Target($$payload, $$props) {
  push();
  /**
   * @license @lucide/svelte v0.539.0 - ISC
   *
   * ISC License
   *
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   *
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   *
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   *
   */
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    ["circle", { "cx": "12", "cy": "12", "r": "10" }],
    ["circle", { "cx": "12", "cy": "12", "r": "6" }],
    ["circle", { "cx": "12", "cy": "12", "r": "2" }]
  ];
  Icon($$payload, spread_props([
    { name: "target" },
    /**
     * @component @name Target
     * @description Lucide SVG icon component, renders SVG Element with children.
     *
     * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSI2IiAvPgogIDxjaXJjbGUgY3g9IjEyIiBjeT0iMTIiIHI9IjIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/target
     * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
     *
     * @param {Object} props - Lucide icons props and any valid SVG attribute
     * @returns {FunctionalComponent} Svelte component
     *
     */
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function Area($$payload, $$props) {
  push();
  const ctx = getChartContext();
  const renderCtx = getRenderContext();
  let {
    clipPath,
    curve,
    data,
    defined,
    fill,
    fillOpacity,
    line = false,
    opacity,
    pathData,
    stroke,
    strokeWidth,
    motion,
    x,
    y0,
    y1,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const xAccessor = x ? accessor(x) : ctx.x;
  const y0Accessor = y0 ? accessor(y0) : (d2) => min(ctx.yDomain);
  const y1Accessor = y1 ? accessor(y1) : ctx.y;
  const xOffset = isScaleBand(ctx.xScale) ? ctx.xScale.bandwidth() / 2 : 0;
  const yOffset = isScaleBand(ctx.yScale) ? ctx.yScale.bandwidth() / 2 : 0;
  const extractedTween = extractTweenConfig(motion);
  const tweenOptions = extractedTween ? {
    type: extractedTween.type,
    options: { interpolate: interpolatePath, ...extractedTween.options }
  } : void 0;
  function defaultPathData() {
    if (!tweenOptions) {
      return "";
    } else if (pathData) {
      return flattenPathData(pathData, Math.min(ctx.yScale(0), ctx.yRange[0]));
    } else if (ctx.config.x) {
      const path = ctx.radial ? areaRadial().angle((d2) => ctx.xScale(xAccessor(d2))).innerRadius(() => Math.min(ctx.yScale(0), ctx.yRange[0])).outerRadius(() => Math.min(ctx.yScale(0), ctx.yRange[0])) : area().x((d2) => ctx.xScale(xAccessor(d2)) + xOffset).y0(() => Math.min(ctx.yScale(0), ctx.yRange[0])).y1(() => Math.min(ctx.yScale(0), ctx.yRange[0]));
      path.defined(defined ?? ((d2) => xAccessor(d2) != null && y1Accessor(d2) != null));
      if (curve) path.curve(curve);
      return path(data ?? ctx.data);
    }
  }
  const d = (() => {
    const _path = ctx.radial ? areaRadial().angle((d2) => ctx.xScale(xAccessor(d2))).innerRadius((d2) => ctx.yScale(y0Accessor(d2))).outerRadius((d2) => ctx.yScale(y1Accessor(d2))) : area().x((d2) => {
      const v = xAccessor(d2);
      return ctx.xScale(v) + xOffset;
    }).y0((d2) => {
      let value = max(ctx.yRange);
      if (y0) {
        value = ctx.yScale(y0Accessor(d2));
      } else if (Array.isArray(ctx.config.y) && ctx.config.y[0] === 0) {
        value = ctx.yScale(ctx.y(d2)[0]);
      }
      return value + yOffset;
    }).y1((d2) => {
      let value = max(ctx.yRange);
      if (y1) {
        value = ctx.yScale(y1Accessor(d2));
      } else if (Array.isArray(ctx.config.y) && ctx.config.y[1] === 1) {
        value = ctx.yScale(ctx.y(d2)[1]);
      } else {
        value = ctx.yScale(ctx.y(d2));
      }
      return value + yOffset;
    });
    _path.defined(defined ?? ((d2) => xAccessor(d2) != null && y1Accessor(d2) != null));
    if (curve) _path.curve(curve);
    return pathData ?? _path(data ?? ctx.data) ?? defaultPathData();
  })();
  const tweenState = createMotion(defaultPathData(), () => d, tweenOptions);
  createKey(() => fill);
  createKey(() => stroke);
  if (renderCtx === "canvas") {
    registerCanvasComponent({
      events: {
        click: restProps.onclick,
        pointerenter: restProps.onpointerenter,
        pointermove: restProps.onpointermove,
        pointerleave: restProps.onpointerleave
      }
    });
  }
  if (line) {
    $$payload.out.push("<!--[-->");
    Spline($$payload, spread_props([
      { data, x, y: y1, curve, defined, motion },
      extractLayerProps(line, "area-line")
    ]));
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]-->`);
  if (renderCtx === "svg") {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<path${spread_attributes(
      {
        d: tweenState.current,
        "clip-path": clipPath,
        fill,
        "fill-opacity": fillOpacity,
        stroke,
        "stroke-width": strokeWidth,
        opacity,
        ...extractLayerProps(restProps, "area-path")
      },
      null,
      void 0,
      void 0,
      3
    )}></path>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]-->`);
  pop();
}
function AreaChart($$payload, $$props) {
  push();
  let {
    data = [],
    x,
    y,
    xDomain,
    radial = false,
    series: seriesProp,
    seriesLayout = "overlap",
    axis = true,
    brush = false,
    grid = true,
    labels = false,
    legend = false,
    points = false,
    tooltip = true,
    highlight = true,
    annotations = [],
    rule = true,
    onTooltipClick = () => {
    },
    onPointClick,
    props = {},
    renderContext = "svg",
    profile = false,
    debug = false,
    xScale: xScaleProp,
    children: childrenProp,
    aboveContext,
    belowContext,
    belowMarks,
    aboveMarks,
    marks,
    context = void 0,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const series = seriesProp === void 0 ? [{ key: "default", value: y, color: "var(--color-primary)" }] : seriesProp;
  const seriesState = new SeriesState(() => series);
  const stackSeries = seriesLayout.startsWith("stack");
  const chartData = (() => {
    let _chartData = seriesState.allSeriesData.length ? seriesState.allSeriesData : chartDataArray(data);
    if (stackSeries) {
      const seriesKeys = seriesState.visibleSeries.map((s) => s.key);
      const offset = seriesLayout === "stackExpand" ? stackOffsetExpand : seriesLayout === "stackDiverging" ? stackOffsetDiverging : stackOffsetNone;
      const stackData = stack().keys(seriesKeys).value((d, key) => {
        const s = series.find((d2) => d2.key === key);
        const value = accessor(s.value ?? y ?? s.key)(d);
        return value;
      }).offset(offset)(_chartData);
      for (let [seriesIndex, s] of series.entries()) {
        if (s.data) {
          s.data = s.data.map((d, i) => {
            return { ...d, stackData: stackData[seriesIndex][i] };
          });
        }
      }
      _chartData = _chartData.map((d, i) => {
        return { ...d, stackData: stackData.map((sd) => sd[i]) };
      });
    }
    return _chartData;
  })();
  const xScale = xScaleProp ?? (accessor(x)(chartData[0]) instanceof Date ? scaleTime() : scaleLinear());
  function isStackData(d) {
    return d && typeof d === "object" && "stackData" in d;
  }
  function getStackData(s, d, i) {
    if (s.data) {
      return d.stackData;
    }
    return d.stackData[i] ?? [];
  }
  function getAreaProps(s, i) {
    const lineProps = {
      ...props.line,
      ...typeof props.area?.line === "object" ? props.area.line : null,
      ...typeof s.props?.line === "object" ? s.props.line : null
    };
    const highlightClass = seriesState.visibleSeries.length > 1 && seriesState.highlightKey.current && seriesState.highlightKey.current !== s.key ? "opacity-10" : "";
    return {
      data: s.data,
      y0: stackSeries ? (d) => getStackData(s, d, i)[0] : Array.isArray(s.value) ? s.value[0] : void 0,
      y1: stackSeries ? (d) => getStackData(s, d, i)[1] : Array.isArray(s.value) ? s.value[1] : s.value ?? (s.data ? void 0 : s.key),
      fill: s.color,
      fillOpacity: 0.3,
      ...props.area,
      ...s.props,
      class: cls(
        "transition-opacity",
        // Checking `visibleSeries.length > 1` fixes re-animated tweened areas on hover
        highlightClass,
        props.area?.class,
        s.props?.class
      ),
      line: {
        stroke: s.color,
        ...lineProps,
        class: cls("transition-opacity", highlightClass, lineProps.class)
      }
    };
  }
  function getPointsProps(s, i) {
    return {
      data: s.data,
      y: stackSeries ? (d) => getStackData(s, d, i)[1] : Array.isArray(s.value) ? s.value[1] : s.value ?? (s.data ? void 0 : s.key),
      fill: s.color,
      ...props.points,
      ...typeof points === "object" ? points : null,
      class: cls("stroke-surface-200 transition-opacity", seriesState.highlightKey.current && seriesState.highlightKey.current !== s.key && "opacity-10", props.points?.class, typeof points === "object" && points.class)
    };
  }
  function getLabelsProps(s, i) {
    return {
      data: s.data,
      y: stackSeries ? (d) => isStackData(d) ? getStackData(s, d, i)[1] : void 0 : Array.isArray(s.value) ? s.value[1] : s.value ?? (s.data ? void 0 : s.key),
      ...props.labels,
      ...typeof labels === "object" ? labels : null,
      class: cls("stroke-surface-200 transition-opacity", seriesState.highlightKey.current && seriesState.highlightKey.current !== s.key && "opacity-10", props.labels?.class, typeof labels === "object" && labels.class)
    };
  }
  const brushProps = { ...typeof brush === "object" ? brush : null, ...props.brush };
  function getHighlightProps(s, i) {
    if (!context) return {};
    const seriesTooltipData = s.data && context.tooltip.data ? findRelatedData(s.data, context.tooltip.data, context.x) : null;
    const highlightPointsProps = typeof props.highlight?.points === "object" ? props.highlight.points : null;
    return {
      data: seriesTooltipData,
      y: stackSeries ? (d) => getStackData(s, d, i)[1] : s.value ?? (s.data ? void 0 : s.key),
      lines: i == 0,
      onPointClick: onPointClick ? (e, detail) => onPointClick(e, { ...detail, series: s }) : void 0,
      onPointEnter: () => seriesState.highlightKey.current = s.key,
      onPointLeave: () => seriesState.highlightKey.current = null,
      ...props.highlight,
      points: props.highlight?.points == false ? false : {
        ...highlightPointsProps,
        fill: s.color,
        class: cls("transition-opacity", seriesState.highlightKey.current && seriesState.highlightKey.current !== s.key && "opacity-10", highlightPointsProps?.class)
      }
    };
  }
  function getLegendProps() {
    return createLegendProps({
      seriesState,
      props: {
        ...props.legend,
        ...typeof legend === "object" ? legend : null
      }
    });
  }
  function getGridProps() {
    return {
      x: radial,
      y: true,
      ...typeof grid === "object" ? grid : null,
      ...props.grid
    };
  }
  if (profile) {
    console.time("AreaChart render");
  }
  setTooltipMetaContext({
    type: "area",
    get stackSeries() {
      return stackSeries;
    },
    get visibleSeries() {
      return seriesState.visibleSeries;
    }
  });
  function resolveAccessor(acc) {
    if (stackSeries) {
      return (d) => isStackData(d) ? seriesState.visibleSeries.flatMap((s, i) => d.stackData[i]) : void 0;
    }
    if (acc) return acc;
    return seriesState.visibleSeries.map((s) => s.value ?? s.key);
  }
  function getAxisProps(axisDirection) {
    if (axisDirection === "y") {
      return {
        placement: radial ? "radius" : "left",
        format: seriesLayout === "stackExpand" ? (value) => format(value, "percentRound") : void 0,
        ...typeof axis === "object" ? axis : null,
        ...props.yAxis
      };
    }
    return {
      placement: radial ? "angle" : "bottom",
      ...typeof axis === "object" ? axis : null,
      ...props.xAxis
    };
  }
  function getRuleProps() {
    return {
      x: 0,
      y: 0,
      ...typeof rule === "object" ? rule : null,
      ...props.rule
    };
  }
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    {
      let children = function($$payload3, { context: context2 }) {
        const snippetProps = {
          context: context2,
          series,
          visibleSeries: seriesState.visibleSeries,
          getAreaProps,
          getLabelsProps,
          getPointsProps,
          getHighlightProps,
          getLegendProps,
          getGridProps,
          getAxisProps,
          getRuleProps,
          highlightKey: seriesState.highlightKey.current,
          setHighlightKey: seriesState.highlightKey.set
        };
        if (childrenProp) {
          $$payload3.out.push("<!--[-->");
          childrenProp($$payload3, snippetProps);
          $$payload3.out.push(`<!---->`);
        } else {
          $$payload3.out.push("<!--[!-->");
          belowContext?.($$payload3, snippetProps);
          $$payload3.out.push(`<!----> `);
          Layer($$payload3, spread_props([
            { type: renderContext },
            asAny(renderContext === "canvas" ? props.canvas : props.svg),
            {
              center: radial,
              debug,
              children: ($$payload4) => {
                if (typeof grid === "function") {
                  $$payload4.out.push("<!--[-->");
                  grid($$payload4, snippetProps);
                  $$payload4.out.push(`<!---->`);
                } else {
                  $$payload4.out.push("<!--[!-->");
                  if (grid) {
                    $$payload4.out.push("<!--[-->");
                    Grid($$payload4, spread_props([getGridProps()]));
                  } else {
                    $$payload4.out.push("<!--[!-->");
                  }
                  $$payload4.out.push(`<!--]-->`);
                }
                $$payload4.out.push(`<!--]--> `);
                ChartClipPath($$payload4, {
                  disabled: !brush,
                  children: ($$payload5) => {
                    ChartAnnotations($$payload5, {
                      annotations,
                      layer: "below",
                      highlightKey: seriesState.highlightKey.current,
                      visibleSeries: seriesState.visibleSeries
                    });
                    $$payload5.out.push(`<!----> `);
                    belowMarks?.($$payload5, snippetProps);
                    $$payload5.out.push(`<!----> `);
                    if (marks) {
                      $$payload5.out.push("<!--[-->");
                      marks($$payload5, snippetProps);
                      $$payload5.out.push(`<!---->`);
                    } else {
                      $$payload5.out.push("<!--[!-->");
                      const each_array = ensure_array_like(seriesState.visibleSeries);
                      $$payload5.out.push(`<!--[-->`);
                      for (let i = 0, $$length = each_array.length; i < $$length; i++) {
                        let s = each_array[i];
                        Area($$payload5, spread_props([getAreaProps(s, i)]));
                      }
                      $$payload5.out.push(`<!--]-->`);
                    }
                    $$payload5.out.push(`<!--]-->`);
                  },
                  $$slots: { default: true }
                });
                $$payload4.out.push(`<!----> `);
                aboveMarks?.($$payload4, snippetProps);
                $$payload4.out.push(`<!----> `);
                if (typeof axis === "function") {
                  $$payload4.out.push("<!--[-->");
                  axis($$payload4, snippetProps);
                  $$payload4.out.push(`<!----> `);
                  if (typeof rule === "function") {
                    $$payload4.out.push("<!--[-->");
                    rule($$payload4, snippetProps);
                    $$payload4.out.push(`<!---->`);
                  } else {
                    $$payload4.out.push("<!--[!-->");
                    if (rule) {
                      $$payload4.out.push("<!--[-->");
                      Rule($$payload4, spread_props([getRuleProps()]));
                    } else {
                      $$payload4.out.push("<!--[!-->");
                    }
                    $$payload4.out.push(`<!--]-->`);
                  }
                  $$payload4.out.push(`<!--]-->`);
                } else {
                  $$payload4.out.push("<!--[!-->");
                  if (axis) {
                    $$payload4.out.push("<!--[-->");
                    if (axis !== "x") {
                      $$payload4.out.push("<!--[-->");
                      Axis($$payload4, spread_props([getAxisProps("y")]));
                    } else {
                      $$payload4.out.push("<!--[!-->");
                    }
                    $$payload4.out.push(`<!--]--> `);
                    if (axis !== "y") {
                      $$payload4.out.push("<!--[-->");
                      Axis($$payload4, spread_props([getAxisProps("x")]));
                    } else {
                      $$payload4.out.push("<!--[!-->");
                    }
                    $$payload4.out.push(`<!--]--> `);
                    if (typeof rule === "function") {
                      $$payload4.out.push("<!--[-->");
                      rule($$payload4, snippetProps);
                      $$payload4.out.push(`<!---->`);
                    } else {
                      $$payload4.out.push("<!--[!-->");
                      if (rule) {
                        $$payload4.out.push("<!--[-->");
                        Rule($$payload4, spread_props([getRuleProps()]));
                      } else {
                        $$payload4.out.push("<!--[!-->");
                      }
                      $$payload4.out.push(`<!--]-->`);
                    }
                    $$payload4.out.push(`<!--]-->`);
                  } else {
                    $$payload4.out.push("<!--[!-->");
                  }
                  $$payload4.out.push(`<!--]-->`);
                }
                $$payload4.out.push(`<!--]--> `);
                ChartClipPath($$payload4, {
                  disabled: !brush,
                  full: true,
                  children: ($$payload5) => {
                    if (typeof points === "function") {
                      $$payload5.out.push("<!--[-->");
                      points($$payload5, snippetProps);
                      $$payload5.out.push(`<!---->`);
                    } else {
                      $$payload5.out.push("<!--[!-->");
                      if (points) {
                        $$payload5.out.push("<!--[-->");
                        const each_array_1 = ensure_array_like(seriesState.visibleSeries);
                        $$payload5.out.push(`<!--[-->`);
                        for (let i = 0, $$length = each_array_1.length; i < $$length; i++) {
                          let s = each_array_1[i];
                          Points($$payload5, spread_props([getPointsProps(s, i)]));
                        }
                        $$payload5.out.push(`<!--]-->`);
                      } else {
                        $$payload5.out.push("<!--[!-->");
                      }
                      $$payload5.out.push(`<!--]-->`);
                    }
                    $$payload5.out.push(`<!--]--> `);
                    if (typeof highlight === "function") {
                      $$payload5.out.push("<!--[-->");
                      highlight($$payload5, snippetProps);
                      $$payload5.out.push(`<!---->`);
                    } else {
                      $$payload5.out.push("<!--[!-->");
                      if (highlight) {
                        $$payload5.out.push("<!--[-->");
                        const each_array_2 = ensure_array_like(seriesState.visibleSeries);
                        $$payload5.out.push(`<!--[-->`);
                        for (let i = 0, $$length = each_array_2.length; i < $$length; i++) {
                          let s = each_array_2[i];
                          Highlight($$payload5, spread_props([getHighlightProps(s, i)]));
                        }
                        $$payload5.out.push(`<!--]-->`);
                      } else {
                        $$payload5.out.push("<!--[!-->");
                      }
                      $$payload5.out.push(`<!--]-->`);
                    }
                    $$payload5.out.push(`<!--]--> `);
                    if (typeof labels === "function") {
                      $$payload5.out.push("<!--[-->");
                      labels($$payload5, snippetProps);
                      $$payload5.out.push(`<!---->`);
                    } else {
                      $$payload5.out.push("<!--[!-->");
                      if (labels) {
                        $$payload5.out.push("<!--[-->");
                        const each_array_3 = ensure_array_like(seriesState.visibleSeries);
                        $$payload5.out.push(`<!--[-->`);
                        for (let i = 0, $$length = each_array_3.length; i < $$length; i++) {
                          let s = each_array_3[i];
                          Labels($$payload5, spread_props([getLabelsProps(s, i)]));
                        }
                        $$payload5.out.push(`<!--]-->`);
                      } else {
                        $$payload5.out.push("<!--[!-->");
                      }
                      $$payload5.out.push(`<!--]-->`);
                    }
                    $$payload5.out.push(`<!--]--> `);
                    ChartAnnotations($$payload5, {
                      annotations,
                      layer: "above",
                      highlightKey: seriesState.highlightKey.current,
                      visibleSeries: seriesState.visibleSeries
                    });
                    $$payload5.out.push(`<!---->`);
                  },
                  $$slots: { default: true }
                });
                $$payload4.out.push(`<!---->`);
              },
              $$slots: { default: true }
            }
          ]));
          $$payload3.out.push(`<!----> `);
          aboveContext?.($$payload3, snippetProps);
          $$payload3.out.push(`<!----> `);
          if (typeof legend === "function") {
            $$payload3.out.push("<!--[-->");
            legend($$payload3, snippetProps);
            $$payload3.out.push(`<!---->`);
          } else {
            $$payload3.out.push("<!--[!-->");
            if (legend) {
              $$payload3.out.push("<!--[-->");
              Legend($$payload3, spread_props([getLegendProps()]));
            } else {
              $$payload3.out.push("<!--[!-->");
            }
            $$payload3.out.push(`<!--]-->`);
          }
          $$payload3.out.push(`<!--]--> `);
          if (typeof tooltip === "function") {
            $$payload3.out.push("<!--[-->");
            tooltip($$payload3, snippetProps);
            $$payload3.out.push(`<!---->`);
          } else {
            $$payload3.out.push("<!--[!-->");
            if (tooltip) {
              $$payload3.out.push("<!--[-->");
              DefaultTooltip($$payload3, {
                tooltipProps: props.tooltip,
                seriesState,
                canHaveTotal: stackSeries
              });
            } else {
              $$payload3.out.push("<!--[!-->");
            }
            $$payload3.out.push(`<!--]-->`);
          }
          $$payload3.out.push(`<!--]-->`);
        }
        $$payload3.out.push(`<!--]-->`);
      };
      Chart($$payload2, spread_props([
        {
          data: chartData,
          x,
          xDomain,
          xScale,
          y: resolveAccessor(y),
          yBaseline: 0,
          yNice: true,
          radial,
          padding: radial ? void 0 : defaultChartPadding(axis, legend)
        },
        restProps,
        {
          tooltip: tooltip === false ? false : {
            mode: "bisect-x",
            onclick: onTooltipClick,
            debug,
            ...props.tooltip?.context
          },
          brush: brush && (brush === true || brush.mode == void 0 || brush.mode === "integrated") ? {
            axis: "x",
            resetOnEnd: true,
            xDomain,
            ...brushProps,
            onBrushEnd: (e) => {
              xDomain = e.xDomain;
              brushProps.onBrushEnd?.(e);
            }
          } : false,
          get context() {
            return context;
          },
          set context($$value) {
            context = $$value;
            $$settled = false;
          },
          children,
          $$slots: { default: true }
        }
      ]));
    }
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { context });
  pop();
}
export {
  AreaChart as A,
  Target as T
};
