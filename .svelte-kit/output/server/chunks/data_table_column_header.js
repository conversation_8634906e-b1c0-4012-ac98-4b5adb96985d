import { w as push, G as spread_props, y as pop, N as derived, O as props_id, F as spread_attributes, P as bind_props, J as clsx, Q as copy_payload, R as assign_payload, E as ensure_array_like, B as escape_html } from "./index2.js";
import { c as cn } from "./shadcn.utils.js";
import { a as attachRef, w as watch, c as createBitsAttrs, E as getDataReadonly, g as getDataDisabled, H as get<PERSON><PERSON><PERSON><PERSON>only, k as getAriaRequired, l as getAriaChecked, b as createId, d as box, m as mergeProps } from "./create-id.js";
import { I as Icon } from "./states.svelte.js";
import "clsx";
import { C as Context, E as ENTER, S as SPACE } from "./scroll-lock.js";
import { s as snapshot } from "./clone.js";
import { H as Hidden_input, R as Root$1, S as Select_trigger, a as Select_content, b as Select_item } from "./index8.js";
import { C as Check } from "./check.js";
import { D as Dropdown_menu_checkbox_item, M as Minus } from "./data-table.svelte.js";
import { b as buttonVariants, B as Button } from "./button.js";
import { f as MenuGroupHeadingState, R as Root, D as Dropdown_menu_trigger, a as Dropdown_menu_content } from "./index6.js";
import { S as Settings_2 } from "./settings-2.js";
import { a as Dropdown_menu_group, D as Dropdown_menu_separator, b as Dropdown_menu_item } from "./dropdown-menu-separator.js";
import { I as Input } from "./input.js";
import { C as Chevron_left, a as Chevron_right } from "./chevron-right.js";
import { C as Chevrons_up_down } from "./chevrons-up-down.js";
function Arrow_down($$payload, $$props) {
  push();
  /**
   * @license @lucide/svelte v0.539.0 - ISC
   *
   * ISC License
   *
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   *
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   *
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   *
   */
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    ["path", { "d": "M12 5v14" }],
    ["path", { "d": "m19 12-7 7-7-7" }]
  ];
  Icon($$payload, spread_props([
    { name: "arrow-down" },
    /**
     * @component @name ArrowDown
     * @description Lucide SVG icon component, renders SVG Element with children.
     *
     * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgNXYxNCIgLz4KICA8cGF0aCBkPSJtMTkgMTItNyA3LTctNyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/arrow-down
     * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
     *
     * @param {Object} props - Lucide icons props and any valid SVG attribute
     * @returns {FunctionalComponent} Svelte component
     *
     */
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function Arrow_up($$payload, $$props) {
  push();
  /**
   * @license @lucide/svelte v0.539.0 - ISC
   *
   * ISC License
   *
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   *
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   *
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   *
   */
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    ["path", { "d": "m5 12 7-7 7 7" }],
    ["path", { "d": "M12 19V5" }]
  ];
  Icon($$payload, spread_props([
    { name: "arrow-up" },
    /**
     * @component @name ArrowUp
     * @description Lucide SVG icon component, renders SVG Element with children.
     *
     * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtNSAxMiA3LTcgNyA3IiAvPgogIDxwYXRoIGQ9Ik0xMiAxOVY1IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/arrow-up
     * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
     *
     * @param {Object} props - Lucide icons props and any valid SVG attribute
     * @returns {FunctionalComponent} Svelte component
     *
     */
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function Chevrons_left($$payload, $$props) {
  push();
  /**
   * @license @lucide/svelte v0.539.0 - ISC
   *
   * ISC License
   *
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   *
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   *
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   *
   */
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    ["path", { "d": "m11 17-5-5 5-5" }],
    ["path", { "d": "m18 17-5-5 5-5" }]
  ];
  Icon($$payload, spread_props([
    { name: "chevrons-left" },
    /**
     * @component @name ChevronsLeft
     * @description Lucide SVG icon component, renders SVG Element with children.
     *
     * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTEgMTctNS01IDUtNSIgLz4KICA8cGF0aCBkPSJtMTggMTctNS01IDUtNSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/chevrons-left
     * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
     *
     * @param {Object} props - Lucide icons props and any valid SVG attribute
     * @returns {FunctionalComponent} Svelte component
     *
     */
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function Chevrons_right($$payload, $$props) {
  push();
  /**
   * @license @lucide/svelte v0.539.0 - ISC
   *
   * ISC License
   *
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   *
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   *
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   *
   */
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    ["path", { "d": "m6 17 5-5-5-5" }],
    ["path", { "d": "m13 17 5-5-5-5" }]
  ];
  Icon($$payload, spread_props([
    { name: "chevrons-right" },
    /**
     * @component @name ChevronsRight
     * @description Lucide SVG icon component, renders SVG Element with children.
     *
     * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtNiAxNyA1LTUtNS01IiAvPgogIDxwYXRoIGQ9Im0xMyAxNyA1LTUtNS01IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/chevrons-right
     * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
     *
     * @param {Object} props - Lucide icons props and any valid SVG attribute
     * @returns {FunctionalComponent} Svelte component
     *
     */
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function Eye_off($$payload, $$props) {
  push();
  /**
   * @license @lucide/svelte v0.539.0 - ISC
   *
   * ISC License
   *
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   *
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   *
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   *
   */
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    [
      "path",
      {
        "d": "M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49"
      }
    ],
    ["path", { "d": "M14.084 14.158a3 3 0 0 1-4.242-4.242" }],
    [
      "path",
      {
        "d": "M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143"
      }
    ],
    ["path", { "d": "m2 2 20 20" }]
  ];
  Icon($$payload, spread_props([
    { name: "eye-off" },
    /**
     * @component @name EyeOff
     * @description Lucide SVG icon component, renders SVG Element with children.
     *
     * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAuNzMzIDUuMDc2YTEwLjc0NCAxMC43NDQgMCAwIDEgMTEuMjA1IDYuNTc1IDEgMSAwIDAgMSAwIC42OTYgMTAuNzQ3IDEwLjc0NyAwIDAgMS0xLjQ0NCAyLjQ5IiAvPgogIDxwYXRoIGQ9Ik0xNC4wODQgMTQuMTU4YTMgMyAwIDAgMS00LjI0Mi00LjI0MiIgLz4KICA8cGF0aCBkPSJNMTcuNDc5IDE3LjQ5OWExMC43NSAxMC43NSAwIDAgMS0xNS40MTctNS4xNTEgMSAxIDAgMCAxIDAtLjY5NiAxMC43NSAxMC43NSAwIDAgMSA0LjQ0Ni01LjE0MyIgLz4KICA8cGF0aCBkPSJtMiAyIDIwIDIwIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/eye-off
     * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
     *
     * @param {Object} props - Lucide icons props and any valid SVG attribute
     * @returns {FunctionalComponent} Svelte component
     *
     */
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
const checkboxAttrs = createBitsAttrs({
  component: "checkbox",
  parts: ["root", "group", "group-label", "input"]
});
const CheckboxGroupContext = new Context("Checkbox.Group");
const CheckboxRootContext = new Context("Checkbox.Root");
class CheckboxRootState {
  static create(opts, group = null) {
    return CheckboxRootContext.set(new CheckboxRootState(opts, group));
  }
  opts;
  group;
  #trueName = derived(() => {
    if (this.group && this.group.opts.name.current) return this.group.opts.name.current;
    return this.opts.name.current;
  });
  get trueName() {
    return this.#trueName();
  }
  set trueName($$value) {
    return this.#trueName($$value);
  }
  #trueRequired = derived(() => {
    if (this.group && this.group.opts.required.current) return true;
    return this.opts.required.current;
  });
  get trueRequired() {
    return this.#trueRequired();
  }
  set trueRequired($$value) {
    return this.#trueRequired($$value);
  }
  #trueDisabled = derived(() => {
    if (this.group && this.group.opts.disabled.current) return true;
    return this.opts.disabled.current;
  });
  get trueDisabled() {
    return this.#trueDisabled();
  }
  set trueDisabled($$value) {
    return this.#trueDisabled($$value);
  }
  #trueReadonly = derived(() => {
    if (this.group && this.group.opts.readonly.current) return true;
    return this.opts.readonly.current;
  });
  get trueReadonly() {
    return this.#trueReadonly();
  }
  set trueReadonly($$value) {
    return this.#trueReadonly($$value);
  }
  attachment;
  constructor(opts, group) {
    this.opts = opts;
    this.group = group;
    this.attachment = attachRef(this.opts.ref);
    this.onkeydown = this.onkeydown.bind(this);
    this.onclick = this.onclick.bind(this);
    watch.pre(
      [
        () => snapshot(this.group?.opts.value.current),
        () => this.opts.value.current
      ],
      ([groupValue, value]) => {
        if (!groupValue || !value) return;
        this.opts.checked.current = groupValue.includes(value);
      }
    );
    watch.pre(() => this.opts.checked.current, (checked) => {
      if (!this.group) return;
      if (checked) {
        this.group?.addValue(this.opts.value.current);
      } else {
        this.group?.removeValue(this.opts.value.current);
      }
    });
  }
  onkeydown(e) {
    if (this.trueDisabled || this.trueReadonly) return;
    if (e.key === ENTER) e.preventDefault();
    if (e.key === SPACE) {
      e.preventDefault();
      this.#toggle();
    }
  }
  #toggle() {
    if (this.opts.indeterminate.current) {
      this.opts.indeterminate.current = false;
      this.opts.checked.current = true;
    } else {
      this.opts.checked.current = !this.opts.checked.current;
    }
  }
  onclick(e) {
    if (this.trueDisabled || this.trueReadonly) return;
    if (this.opts.type.current === "submit") {
      this.#toggle();
      return;
    }
    e.preventDefault();
    this.#toggle();
  }
  #snippetProps = derived(() => ({
    checked: this.opts.checked.current,
    indeterminate: this.opts.indeterminate.current
  }));
  get snippetProps() {
    return this.#snippetProps();
  }
  set snippetProps($$value) {
    return this.#snippetProps($$value);
  }
  #props = derived(() => ({
    id: this.opts.id.current,
    role: "checkbox",
    type: this.opts.type.current,
    disabled: this.trueDisabled,
    "aria-checked": getAriaChecked(this.opts.checked.current, this.opts.indeterminate.current),
    "aria-required": getAriaRequired(this.trueRequired),
    "aria-readonly": getAriaReadonly(this.trueReadonly),
    "data-disabled": getDataDisabled(this.trueDisabled),
    "data-readonly": getDataReadonly(this.trueReadonly),
    "data-state": getCheckboxDataState(this.opts.checked.current, this.opts.indeterminate.current),
    [checkboxAttrs.root]: "",
    onclick: this.onclick,
    onkeydown: this.onkeydown,
    ...this.attachment
  }));
  get props() {
    return this.#props();
  }
  set props($$value) {
    return this.#props($$value);
  }
}
class CheckboxInputState {
  static create() {
    return new CheckboxInputState(CheckboxRootContext.get());
  }
  root;
  #trueChecked = derived(() => {
    if (!this.root.group) return this.root.opts.checked.current;
    if (this.root.opts.value.current !== void 0 && this.root.group.opts.value.current.includes(this.root.opts.value.current)) {
      return true;
    }
    return false;
  });
  get trueChecked() {
    return this.#trueChecked();
  }
  set trueChecked($$value) {
    return this.#trueChecked($$value);
  }
  #shouldRender = derived(() => Boolean(this.root.trueName));
  get shouldRender() {
    return this.#shouldRender();
  }
  set shouldRender($$value) {
    return this.#shouldRender($$value);
  }
  constructor(root) {
    this.root = root;
  }
  #props = derived(() => ({
    type: "checkbox",
    checked: this.root.opts.checked.current === true,
    disabled: this.root.trueDisabled,
    required: this.root.trueRequired,
    name: this.root.trueName,
    value: this.root.opts.value.current,
    readonly: this.root.trueReadonly
  }));
  get props() {
    return this.#props();
  }
  set props($$value) {
    return this.#props($$value);
  }
}
function getCheckboxDataState(checked, indeterminate) {
  if (indeterminate) return "indeterminate";
  return checked ? "checked" : "unchecked";
}
function Checkbox_input($$payload, $$props) {
  push();
  const inputState = CheckboxInputState.create();
  if (inputState.shouldRender) {
    $$payload.out.push("<!--[-->");
    Hidden_input($$payload, spread_props([inputState.props]));
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]-->`);
  pop();
}
function Checkbox$1($$payload, $$props) {
  push();
  const uid = props_id($$payload);
  let {
    checked = false,
    ref = null,
    onCheckedChange,
    children,
    disabled = false,
    required = false,
    name = void 0,
    value = "on",
    id = createId(uid),
    indeterminate = false,
    onIndeterminateChange,
    child,
    type = "button",
    readonly,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const group = CheckboxGroupContext.getOr(null);
  if (group && value) {
    if (group.opts.value.current.includes(value)) {
      checked = true;
    } else {
      checked = false;
    }
  }
  watch.pre(() => value, () => {
    if (group && value) {
      if (group.opts.value.current.includes(value)) {
        checked = true;
      } else {
        checked = false;
      }
    }
  });
  const rootState = CheckboxRootState.create(
    {
      checked: box.with(() => checked, (v) => {
        checked = v;
        onCheckedChange?.(v);
      }),
      disabled: box.with(() => disabled ?? false),
      required: box.with(() => required),
      name: box.with(() => name),
      value: box.with(() => value),
      id: box.with(() => id),
      ref: box.with(() => ref, (v) => ref = v),
      indeterminate: box.with(() => indeterminate, (v) => {
        indeterminate = v;
        onIndeterminateChange?.(v);
      }),
      type: box.with(() => type),
      readonly: box.with(() => Boolean(readonly))
    },
    group
  );
  const mergedProps = mergeProps({ ...restProps }, rootState.props);
  if (child) {
    $$payload.out.push("<!--[-->");
    child($$payload, { props: mergedProps, ...rootState.snippetProps });
    $$payload.out.push(`<!---->`);
  } else {
    $$payload.out.push("<!--[!-->");
    $$payload.out.push(`<button${spread_attributes({ ...mergedProps }, null)}>`);
    children?.($$payload, rootState.snippetProps);
    $$payload.out.push(`<!----></button>`);
  }
  $$payload.out.push(`<!--]--> `);
  Checkbox_input($$payload);
  $$payload.out.push(`<!---->`);
  bind_props($$props, { checked, ref, indeterminate });
  pop();
}
function Menu_group_heading($$payload, $$props) {
  push();
  const uid = props_id($$payload);
  let {
    children,
    child,
    ref = null,
    id = createId(uid),
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const groupHeadingState = MenuGroupHeadingState.create({
    id: box.with(() => id),
    ref: box.with(() => ref, (v) => ref = v)
  });
  const mergedProps = mergeProps(restProps, groupHeadingState.props);
  if (child) {
    $$payload.out.push("<!--[-->");
    child($$payload, { props: mergedProps });
    $$payload.out.push(`<!---->`);
  } else {
    $$payload.out.push("<!--[!-->");
    $$payload.out.push(`<div${spread_attributes({ ...mergedProps }, null)}>`);
    children?.($$payload);
    $$payload.out.push(`<!----></div>`);
  }
  $$payload.out.push(`<!--]-->`);
  bind_props($$props, { ref });
  pop();
}
function Dropdown_menu_shortcut($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    children,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  $$payload.out.push(`<span${spread_attributes(
    {
      "data-slot": "dropdown-menu-shortcut",
      class: clsx(cn("text-muted-foreground ml-auto text-xs tracking-widest", className)),
      ...restProps
    },
    null
  )}>`);
  children?.($$payload);
  $$payload.out.push(`<!----></span>`);
  bind_props($$props, { ref });
  pop();
}
function Dropdown_menu_group_heading($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    inset,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out.push(`<!---->`);
    Menu_group_heading($$payload2, spread_props([
      {
        "data-slot": "dropdown-menu-group-heading",
        "data-inset": inset,
        class: cn("px-2 py-1.5 text-sm font-semibold data-[inset]:pl-8", className)
      },
      restProps,
      {
        get ref() {
          return ref;
        },
        set ref($$value) {
          ref = $$value;
          $$settled = false;
        }
      }
    ]));
    $$payload2.out.push(`<!---->`);
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref });
  pop();
}
function Data_table_view_options($$payload, $$props) {
  push();
  let { table } = $$props;
  let filteredTableColumns = (() => table.getAllColumns().filter((col) => typeof col.accessorFn !== "undefined" && col.getCanHide()))();
  $$payload.out.push(`<!---->`);
  Root($$payload, {
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->`);
      Dropdown_menu_trigger($$payload2, {
        class: buttonVariants({
          variant: "outline",
          size: "sm",
          class: "ml-auto hidden h-8 lg:flex"
        }),
        children: ($$payload3) => {
          Settings_2($$payload3, {});
          $$payload3.out.push(`<!----> View`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!----> <!---->`);
      Dropdown_menu_content($$payload2, {
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Dropdown_menu_group($$payload3, {
            children: ($$payload4) => {
              const each_array = ensure_array_like(filteredTableColumns);
              $$payload4.out.push(`<!---->`);
              Dropdown_menu_group_heading($$payload4, {
                children: ($$payload5) => {
                  $$payload5.out.push(`<!---->Toggle columns`);
                },
                $$slots: { default: true }
              });
              $$payload4.out.push(`<!----> <!---->`);
              Dropdown_menu_separator($$payload4, {});
              $$payload4.out.push(`<!----> <!--[-->`);
              for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
                let column = each_array[$$index];
                $$payload4.out.push(`<!---->`);
                Dropdown_menu_checkbox_item($$payload4, {
                  checked: column.getIsVisible(),
                  onCheckedChange: (v) => column.toggleVisibility(!!v),
                  class: "capitalize",
                  children: ($$payload5) => {
                    $$payload5.out.push(`<!---->${escape_html(column.id)}`);
                  },
                  $$slots: { default: true }
                });
                $$payload4.out.push(`<!---->`);
              }
              $$payload4.out.push(`<!--]-->`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!---->`);
  pop();
}
function Checkbox($$payload, $$props) {
  push();
  let {
    ref = null,
    checked = false,
    indeterminate = false,
    class: className,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out.push(`<!---->`);
    {
      let children = function($$payload3, { checked: checked2, indeterminate: indeterminate2 }) {
        $$payload3.out.push(`<div data-slot="checkbox-indicator" class="text-current transition-none">`);
        if (checked2) {
          $$payload3.out.push("<!--[-->");
          Check($$payload3, { class: "size-3.5" });
        } else {
          $$payload3.out.push("<!--[!-->");
          if (indeterminate2) {
            $$payload3.out.push("<!--[-->");
            Minus($$payload3, { class: "size-3.5" });
          } else {
            $$payload3.out.push("<!--[!-->");
          }
          $$payload3.out.push(`<!--]-->`);
        }
        $$payload3.out.push(`<!--]--></div>`);
      };
      Checkbox$1($$payload2, spread_props([
        {
          "data-slot": "checkbox",
          class: cn("border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive shadow-xs peer flex size-4 shrink-0 items-center justify-center rounded-[4px] border outline-none transition-shadow focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50", className)
        },
        restProps,
        {
          get ref() {
            return ref;
          },
          set ref($$value) {
            ref = $$value;
            $$settled = false;
          },
          get checked() {
            return checked;
          },
          set checked($$value) {
            checked = $$value;
            $$settled = false;
          },
          get indeterminate() {
            return indeterminate;
          },
          set indeterminate($$value) {
            indeterminate = $$value;
            $$settled = false;
          },
          children,
          $$slots: { default: true }
        }
      ]));
    }
    $$payload2.out.push(`<!---->`);
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref, checked, indeterminate });
  pop();
}
function Data_table_checkbox($$payload, $$props) {
  let { checked, $$slots, $$events, ...restProps } = $$props;
  Checkbox($$payload, spread_props([{ checked }, restProps]));
}
function Data_table_toolbar($$payload, $$props) {
  push();
  let {
    table,
    children,
    filterColumn = "title",
    filterPlaceholder = "Filter tasks..."
  } = $$props;
  $$payload.out.push(`<div class="flex items-center justify-between"><div class="flex flex-1 items-center space-x-2">`);
  Input($$payload, {
    placeholder: (
      // TODO: use in calling parent
      // const isFiltered = $derived(table.getState().columnFilters.length > 0)
      // const statusCol = $derived(table.getColumn('status'))
      // const priorityCol = $derived(table.getColumn('priority'))
      filterPlaceholder
    ),
    value: table.getColumn(filterColumn)?.getFilterValue() ?? "",
    oninput: (e) => {
      table.getColumn(filterColumn)?.setFilterValue(e.currentTarget.value);
    },
    onchange: (e) => {
      table.getColumn(filterColumn)?.setFilterValue(e.currentTarget.value);
    },
    class: "h-8 w-[150px] lg:w-[250px]"
  });
  $$payload.out.push(`<!----> `);
  children?.($$payload);
  $$payload.out.push(`<!----></div> `);
  Data_table_view_options($$payload, { table });
  $$payload.out.push(`<!----></div>`);
  pop();
}
function Data_table_pagination($$payload, $$props) {
  push();
  let { table } = $$props;
  $$payload.out.push(`<div class="flex items-center justify-between px-2"><div class="text-muted-foreground flex-1 text-sm">${escape_html(table.getFilteredSelectedRowModel().rows.length)} of
		${escape_html(table.getFilteredRowModel().rows.length)} row(s) selected.</div> <div class="flex items-center space-x-6 lg:space-x-8"><div class="flex items-center space-x-2"><p class="text-sm font-medium">Rows per page</p> <!---->`);
  Root$1($$payload, {
    allowDeselect: false,
    type: "single",
    value: `${table.getState().pagination.pageSize}`,
    onValueChange: (value) => {
      table.setPageSize(Number(value));
    },
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->`);
      Select_trigger($$payload2, {
        class: "h-8 w-[70px]",
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->${escape_html(String(table.getState().pagination.pageSize))}`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!----> <!---->`);
      Select_content($$payload2, {
        side: "top",
        children: ($$payload3) => {
          const each_array = ensure_array_like([10, 20, 30, 40, 50]);
          $$payload3.out.push(`<!--[-->`);
          for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
            let pageSize = each_array[$$index];
            $$payload3.out.push(`<!---->`);
            Select_item($$payload3, {
              value: `${pageSize}`,
              children: ($$payload4) => {
                $$payload4.out.push(`<!---->${escape_html(pageSize)}`);
              },
              $$slots: { default: true }
            });
            $$payload3.out.push(`<!---->`);
          }
          $$payload3.out.push(`<!--]-->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!----></div> <div class="flex w-[100px] items-center justify-center text-sm font-medium">Page ${escape_html(table.getState().pagination.pageIndex + 1)} of
			${escape_html(table.getPageCount())}</div> <div class="flex items-center space-x-2">`);
  Button($$payload, {
    variant: "outline",
    class: "hidden size-8 p-0 lg:flex",
    onclick: () => table.setPageIndex(0),
    disabled: !table.getCanPreviousPage(),
    children: ($$payload2) => {
      $$payload2.out.push(`<span class="sr-only">Go to first page</span> `);
      Chevrons_left($$payload2, {});
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!----> `);
  Button($$payload, {
    variant: "outline",
    class: "size-8 p-0",
    onclick: () => table.previousPage(),
    disabled: !table.getCanPreviousPage(),
    children: ($$payload2) => {
      $$payload2.out.push(`<span class="sr-only">Go to previous page</span> `);
      Chevron_left($$payload2, {});
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!----> `);
  Button($$payload, {
    variant: "outline",
    class: "size-8 p-0",
    onclick: () => table.nextPage(),
    disabled: !table.getCanNextPage(),
    children: ($$payload2) => {
      $$payload2.out.push(`<span class="sr-only">Go to next page</span> `);
      Chevron_right($$payload2, {});
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!----> `);
  Button($$payload, {
    variant: "outline",
    class: "hidden size-8 p-0 lg:flex",
    onclick: () => table.setPageIndex(table.getPageCount() - 1),
    disabled: !table.getCanNextPage(),
    children: ($$payload2) => {
      $$payload2.out.push(`<span class="sr-only">Go to last page</span> `);
      Chevrons_right($$payload2, {});
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!----></div></div></div>`);
  pop();
}
function Data_table_cell($$payload, $$props) {
  let { value } = $$props;
  $$payload.out.push(`<div class="flex space-x-2"><span class="max-w-[500px] truncate font-medium">${escape_html(value)}</span></div>`);
}
function Data_table_column_header($$payload, $$props) {
  push();
  let {
    column,
    class: className,
    title,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  if (!column?.getCanSort()) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div${spread_attributes({ class: clsx(className), ...restProps }, null)}>${escape_html(title)}</div>`);
  } else {
    $$payload.out.push("<!--[!-->");
    $$payload.out.push(`<div${spread_attributes(
      {
        class: clsx(cn("flex items-center", className)),
        ...restProps
      },
      null
    )}><!---->`);
    Root($$payload, {
      children: ($$payload2) => {
        $$payload2.out.push(`<!---->`);
        {
          let child = function($$payload3, { props }) {
            Button($$payload3, spread_props([
              props,
              {
                variant: "ghost",
                size: "sm",
                class: "data-[state=open]:bg-accent -ml-3 h-8",
                children: ($$payload4) => {
                  $$payload4.out.push(`<span>${escape_html(title)}</span> `);
                  if (column.getIsSorted() === "desc") {
                    $$payload4.out.push("<!--[-->");
                    Arrow_down($$payload4, {});
                  } else {
                    $$payload4.out.push("<!--[!-->");
                    if (column.getIsSorted() === "asc") {
                      $$payload4.out.push("<!--[-->");
                      Arrow_up($$payload4, {});
                    } else {
                      $$payload4.out.push("<!--[!-->");
                      Chevrons_up_down($$payload4, {});
                    }
                    $$payload4.out.push(`<!--]-->`);
                  }
                  $$payload4.out.push(`<!--]-->`);
                },
                $$slots: { default: true }
              }
            ]));
          };
          Dropdown_menu_trigger($$payload2, { child, $$slots: { child: true } });
        }
        $$payload2.out.push(`<!----> <!---->`);
        Dropdown_menu_content($$payload2, {
          align: "start",
          children: ($$payload3) => {
            $$payload3.out.push(`<!---->`);
            Dropdown_menu_item($$payload3, {
              onclick: () => column.toggleSorting(false),
              children: ($$payload4) => {
                Arrow_up($$payload4, { class: "text-muted-foreground/70 mr-2 size-3.5" });
                $$payload4.out.push(`<!----> Asc`);
              },
              $$slots: { default: true }
            });
            $$payload3.out.push(`<!----> <!---->`);
            Dropdown_menu_item($$payload3, {
              onclick: () => column.toggleSorting(true),
              children: ($$payload4) => {
                Arrow_down($$payload4, { class: "text-muted-foreground/70 mr-2 size-3.5" });
                $$payload4.out.push(`<!----> Desc`);
              },
              $$slots: { default: true }
            });
            $$payload3.out.push(`<!----> <!---->`);
            Dropdown_menu_separator($$payload3, {});
            $$payload3.out.push(`<!----> <!---->`);
            Dropdown_menu_item($$payload3, {
              onclick: () => column.toggleVisibility(false),
              children: ($$payload4) => {
                Eye_off($$payload4, { class: "text-muted-foreground/70 mr-2 size-3.5" });
                $$payload4.out.push(`<!----> Hide`);
              },
              $$slots: { default: true }
            });
            $$payload3.out.push(`<!---->`);
          },
          $$slots: { default: true }
        });
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    });
    $$payload.out.push(`<!----></div>`);
  }
  $$payload.out.push(`<!--]-->`);
  pop();
}
export {
  Dropdown_menu_shortcut as D,
  Data_table_checkbox as a,
  Data_table_cell as b,
  Data_table_column_header as c,
  Data_table_toolbar as d,
  Data_table_pagination as e
};
