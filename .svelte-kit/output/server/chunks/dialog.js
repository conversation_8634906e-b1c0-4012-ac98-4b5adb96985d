import "clsx";
import { w as writable, g as get$1 } from "./index.js";
import "@sveltejs/kit/internal";
import "./exports.js";
import "./state.svelte.js";
import { o as onDestroy, n as noop } from "./scroll-lock.js";
import { G as Gift } from "./gift.js";
import { C as Calendar } from "./calendar.js";
import { w as push, G as spread_props, y as pop, O as props_id, F as spread_attributes, P as bind_props } from "./index2.js";
import { I as Icon } from "./states.svelte.js";
import { C as Credit_card } from "./credit-card.js";
import { W as Wallet } from "./wallet.js";
import { U as Users } from "./users.js";
import { S as Settings_2 } from "./settings-2.js";
import { b as createId, d as box, m as mergeProps } from "./create-id.js";
import { k as DialogTitleState, l as DialogDescriptionState, m as DialogRootState } from "./sheet-content.js";
function Megaphone($$payload, $$props) {
  push();
  /**
   * @license @lucide/svelte v0.539.0 - ISC
   *
   * ISC License
   *
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   *
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   *
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   *
   */
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    [
      "path",
      {
        "d": "M11 6a13 13 0 0 0 8.4-2.8A1 1 0 0 1 21 4v12a1 1 0 0 1-1.6.8A13 13 0 0 0 11 14H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2z"
      }
    ],
    [
      "path",
      {
        "d": "M6 14a12 12 0 0 0 2.4 7.2 2 2 0 0 0 3.2-2.4A8 8 0 0 1 10 14"
      }
    ],
    ["path", { "d": "M8 6v8" }]
  ];
  Icon($$payload, spread_props([
    { name: "megaphone" },
    /**
     * @component @name Megaphone
     * @description Lucide SVG icon component, renders SVG Element with children.
     *
     * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTEgNmExMyAxMyAwIDAgMCA4LjQtMi44QTEgMSAwIDAgMSAyMSA0djEyYTEgMSAwIDAgMS0xLjYuOEExMyAxMyAwIDAgMCAxMSAxNEg1YTIgMiAwIDAgMS0yLTJWOGEyIDIgMCAwIDEgMi0yeiIgLz4KICA8cGF0aCBkPSJNNiAxNGExMiAxMiAwIDAgMCAyLjQgNy4yIDIgMiAwIDAgMCAzLjItMi40QTggOCAwIDAgMSAxMCAxNCIgLz4KICA8cGF0aCBkPSJNOCA2djgiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/megaphone
     * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
     *
     * @param {Object} props - Lucide icons props and any valid SVG attribute
     * @returns {FunctionalComponent} Svelte component
     *
     */
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function Shopping_bag($$payload, $$props) {
  push();
  /**
   * @license @lucide/svelte v0.539.0 - ISC
   *
   * ISC License
   *
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   *
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   *
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   *
   */
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    ["path", { "d": "M16 10a4 4 0 0 1-8 0" }],
    ["path", { "d": "M3.103 6.034h17.794" }],
    [
      "path",
      {
        "d": "M3.4 5.467a2 2 0 0 0-.4 1.2V20a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6.667a2 2 0 0 0-.4-1.2l-2-2.667A2 2 0 0 0 17 2H7a2 2 0 0 0-1.6.8z"
      }
    ]
  ];
  Icon($$payload, spread_props([
    { name: "shopping-bag" },
    /**
     * @component @name ShoppingBag
     * @description Lucide SVG icon component, renders SVG Element with children.
     *
     * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgMTBhNCA0IDAgMCAxLTggMCIgLz4KICA8cGF0aCBkPSJNMy4xMDMgNi4wMzRoMTcuNzk0IiAvPgogIDxwYXRoIGQ9Ik0zLjQgNS40NjdhMiAyIDAgMCAwLS40IDEuMlYyMGEyIDIgMCAwIDAgMiAyaDE0YTIgMiAwIDAgMCAyLTJWNi42NjdhMiAyIDAgMCAwLS40LTEuMmwtMi0yLjY2N0EyIDIgMCAwIDAgMTcgMkg3YTIgMiAwIDAgMC0xLjYuOHoiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/shopping-bag
     * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
     *
     * @param {Object} props - Lucide icons props and any valid SVG attribute
     * @returns {FunctionalComponent} Svelte component
     *
     */
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function Store($$payload, $$props) {
  push();
  /**
   * @license @lucide/svelte v0.539.0 - ISC
   *
   * ISC License
   *
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   *
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   *
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   *
   */
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    [
      "path",
      { "d": "M15 21v-5a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v5" }
    ],
    [
      "path",
      {
        "d": "M17.774 10.31a1.12 1.12 0 0 0-1.549 0 2.5 2.5 0 0 1-3.451 0 1.12 1.12 0 0 0-1.548 0 2.5 2.5 0 0 1-3.452 0 1.12 1.12 0 0 0-1.549 0 2.5 2.5 0 0 1-3.77-3.248l2.889-4.184A2 2 0 0 1 7 2h10a2 2 0 0 1 1.653.873l2.895 4.192a2.5 2.5 0 0 1-3.774 3.244"
      }
    ],
    [
      "path",
      { "d": "M4 10.95V19a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8.05" }
    ]
  ];
  Icon($$payload, spread_props([
    { name: "store" },
    /**
     * @component @name Store
     * @description Lucide SVG icon component, renders SVG Element with children.
     *
     * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUgMjF2LTVhMSAxIDAgMCAwLTEtMWgtNGExIDEgMCAwIDAtMSAxdjUiIC8+CiAgPHBhdGggZD0iTTE3Ljc3NCAxMC4zMWExLjEyIDEuMTIgMCAwIDAtMS41NDkgMCAyLjUgMi41IDAgMCAxLTMuNDUxIDAgMS4xMiAxLjEyIDAgMCAwLTEuNTQ4IDAgMi41IDIuNSAwIDAgMS0zLjQ1MiAwIDEuMTIgMS4xMiAwIDAgMC0xLjU0OSAwIDIuNSAyLjUgMCAwIDEtMy43Ny0zLjI0OGwyLjg4OS00LjE4NEEyIDIgMCAwIDEgNyAyaDEwYTIgMiAwIDAgMSAxLjY1My44NzNsMi44OTUgNC4xOTJhMi41IDIuNSAwIDAgMS0zLjc3NCAzLjI0NCIgLz4KICA8cGF0aCBkPSJNNCAxMC45NVYxOWEyIDIgMCAwIDAgMiAyaDEyYTIgMiAwIDAgMCAyLTJ2LTguMDUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/store
     * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
     *
     * @param {Object} props - Lucide icons props and any valid SVG attribute
     * @returns {FunctionalComponent} Svelte component
     *
     */
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
const defaultOptions = {
  clearArray: false,
  clearOnNavigate: true,
  clearAfterMs: 0,
  flashCookieOptions: {
    path: "/",
    maxAge: 120,
    httpOnly: false,
    sameSite: "strict"
  }
};
function mergeOptions(parentOptions, options) {
  return {
    ...parentOptions,
    ...options,
    flashCookieOptions: {
      ...parentOptions.flashCookieOptions,
      ...options?.flashCookieOptions
    }
  };
}
class FlashMessage {
  options;
  _message;
  get message() {
    return this._message;
  }
  _flashTimeout = 0;
  get flashTimeout() {
    return this._flashTimeout;
  }
  constructor(message, options) {
    this.options = options ?? defaultOptions;
    this._message = {
      subscribe: message.subscribe,
      set: (value, options2) => message.update(($message) => this.update($message, value, options2?.concatenateArray ?? false)),
      update: (updater, options2) => message.update(($message) => this.update($message, updater($message), options2?.concatenateArray ?? false))
    };
  }
  update(current, newData, concatenateArray = false) {
    if (this._flashTimeout)
      clearTimeout(this.flashTimeout);
    if (concatenateArray && Array.isArray(newData)) {
      if (Array.isArray(current)) {
        if (current.length > 0 && newData.length > 0 && current[current.length - 1] === newData[newData.length - 1]) {
          return current;
        } else {
          return current.concat(newData);
        }
      }
    }
    return newData;
  }
}
class FlashRouter {
  routes = /* @__PURE__ */ new Map();
  messageStore;
  constructor() {
    this.messageStore = writable();
    this.routes.set("", new FlashMessage(this.messageStore));
    onDestroy(() => {
      for (const route of this.routes.values()) {
        clearTimeout(route.flashTimeout);
      }
    });
  }
  get defaultRoute() {
    return this.routes.get("");
  }
  has(routeId) {
    return this.routes.has(routeId);
  }
  getFlashMessage(routeId) {
    if (!routeId)
      return this.defaultRoute;
    if (this.routes.has(routeId))
      return this.routes.get(routeId);
    return this.getClosestRoute(routeId);
  }
  getClosestRoute(routeId) {
    const matchingRoutes = Array.from(this.routes.keys()).filter((key) => routeId.includes(key));
    if (!matchingRoutes.length) {
      return this.defaultRoute;
    }
    const longestRoute = matchingRoutes.reduce((prev, curr) => curr.length > prev.length ? curr : prev);
    return this.routes.get(longestRoute);
  }
  createRoute(routeId, data, options) {
    const closest = this.getClosestRoute(routeId);
    const newRoute = new FlashMessage(this.messageStore, mergeOptions(closest.options, options));
    newRoute.message.set(data);
    this.routes.set(routeId, newRoute);
    return newRoute;
  }
}
const cookieName = "flash";
const routers = /* @__PURE__ */ new WeakMap();
function get(page) {
  return "subscribe" in page ? get$1(page) : page;
}
function getRouter(page, initialData) {
  let router = routers.get(page);
  if (!router) {
    router = new FlashRouter();
    routers.set(page, router);
    router.getFlashMessage(get(page).route.id).message.set(initialData);
  }
  return router;
}
function initFlash(page, options) {
  return _initFlash(page).message;
}
function _initFlash(page, options) {
  {
    return new FlashMessage(writable(get(page).data.flash));
  }
}
function getFlash(page, options) {
  return _initFlash(page).message;
}
async function updateFlash(page, update) {
  if (update) await update();
  const cookieData = parseFlashCookie();
  if (cookieData !== void 0) {
    const flash = getRouter(page).getFlashMessage(get(page).route.id);
    flash.message.set(cookieData, { concatenateArray: !flash.options.clearArray });
    clearFlashCookie(flash.options.flashCookieOptions);
  }
  return !!cookieData;
}
function clearFlashCookie(options) {
}
function parseFlashCookie() {
  const cookieString = document.cookie;
  if (!cookieString || !cookieString.includes(cookieName + "=")) return void 0;
  function parseCookieString(str) {
    const output = {};
    if (!str) return output;
    return str.split(";").map((v) => v.split("=")).reduce(
      (acc, v) => {
        acc[decodeURIComponent(v[0].trim())] = decodeURIComponent(v[1].trim());
        return acc;
      },
      output
    );
  }
  const cookies = parseCookieString(cookieString);
  if (cookies[cookieName]) {
    try {
      return JSON.parse(cookies[cookieName]);
    } catch (e) {
    }
  }
  return void 0;
}
const flashModule = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  getFlash,
  initFlash,
  updateFlash
}, Symbol.toStringTag, { value: "Module" }));
const getFlashModule = () => {
  return {
    flashMessage: {
      module: flashModule,
      onError: ({ result, flashMessage }) => {
        const errorMessage = result.error.message;
        flashMessage.set(errorMessage, "error");
      }
    },
    syncFlashMessage: false
  };
};
const generateRandomPassword = (length = 8) => {
  const finalLength = Math.max(length, 8);
  const uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
  const lowercase = "abcdefghijklmnopqrstuvwxyz";
  const numbers = "0123456789";
  const allChars = uppercase + lowercase + numbers;
  let password = "";
  password += uppercase[Math.floor(Math.random() * uppercase.length)];
  password += lowercase[Math.floor(Math.random() * lowercase.length)];
  password += numbers[Math.floor(Math.random() * numbers.length)];
  for (let i = password.length; i < finalLength; i++) {
    password += allChars[Math.floor(Math.random() * allChars.length)];
  }
  return password.split("").sort(() => Math.random() - 0.5).join("");
};
const menuData = {
  navMain: [
    {
      title: "Rewards",
      url: "/rewards/",
      icon: Gift,
      items: [
        {
          title: "Overview",
          url: "/rewards/"
        },
        {
          title: "Programs",
          url: "/rewards/programs/"
        },
        {
          title: "Analytics",
          url: "/rewards/analytics/"
        }
      ]
    },
    {
      title: "Appointments",
      url: "/appointments/",
      icon: Calendar,
      items: [
        {
          title: "Bookings",
          url: "/appointments/"
        },
        {
          title: "Appointment Types",
          url: "/appointments/appointment-templates/"
        },
        {
          title: "Calendar Blocks",
          url: "/appointments/calendar-blocks/"
        }
      ]
    },
    {
      title: "AI Chat Store",
      url: "/chat-store",
      icon: Store,
      items: [
        {
          title: "Chat",
          url: "/chat-store/chat/"
        },
        {
          title: "Products",
          url: "/chat-store/products/"
        }
      ]
    },
    {
      title: "Laybye Management",
      url: "/laybye/",
      icon: Shopping_bag,
      items: [
        {
          title: "Manage",
          url: "/laybye/"
        },
        {
          title: "Orders",
          url: "/laybye/orders/"
        },
        {
          title: "Payments",
          url: "/laybye/payments/"
        }
      ]
    },
    {
      title: "Campaigns",
      url: "/campaigns/",
      icon: Megaphone,
      items: [
        {
          title: "Campaigns",
          url: "/campaigns/"
        },
        {
          title: "Drafts",
          url: "/campaigns/drafts/"
        },
        {
          title: "Analytics",
          url: "/campaigns/analytics/"
        }
      ]
    },
    {
      title: "Subscriptions",
      url: "/subscriptions/",
      icon: Credit_card,
      items: [
        {
          title: "Plans",
          url: "/subscriptions"
        },
        {
          title: "Customers",
          url: "/subscriptions/customers/"
        },
        {
          title: "Billing",
          url: "/subscriptions/billing/"
        }
      ]
    },
    {
      title: "Wallets",
      url: "/wallets/",
      icon: Wallet,
      items: [
        {
          title: "Overview",
          url: "/wallet/"
        },
        {
          title: "Transactions",
          url: "/wallets/transactions/"
        },
        {
          title: "Settings",
          url: "/wallets/settings/"
        }
      ]
    },
    {
      title: "Memberships",
      url: "/memberships/",
      icon: Users,
      items: [
        {
          title: "Members",
          url: "/memberships/"
        },
        {
          title: "Tiers",
          url: "/memberships/tiers"
        },
        {
          title: "Benefits",
          url: "/memberships/benefits"
        }
      ]
    },
    {
      title: "Settings",
      url: "/settings/",
      icon: Settings_2,
      items: [
        {
          title: "General",
          url: "/settings/"
        },
        {
          title: "Team Members",
          url: "/settings/staff/"
        },
        {
          title: "Team Invites",
          url: "/settings/staff-invites/"
        },
        {
          title: "Team Locations",
          url: "/settings/locations/"
        },
        {
          title: "Payment Engine",
          url: "/settings/payment-engine/"
        }
      ]
    }
  ]
};
function Dialog_title($$payload, $$props) {
  push();
  const uid = props_id($$payload);
  let {
    id = createId(uid),
    ref = null,
    child,
    children,
    level = 2,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const titleState = DialogTitleState.create({
    id: box.with(() => id),
    level: box.with(() => level),
    ref: box.with(() => ref, (v) => ref = v)
  });
  const mergedProps = mergeProps(restProps, titleState.props);
  if (child) {
    $$payload.out.push("<!--[-->");
    child($$payload, { props: mergedProps });
    $$payload.out.push(`<!---->`);
  } else {
    $$payload.out.push("<!--[!-->");
    $$payload.out.push(`<div${spread_attributes({ ...mergedProps }, null)}>`);
    children?.($$payload);
    $$payload.out.push(`<!----></div>`);
  }
  $$payload.out.push(`<!--]-->`);
  bind_props($$props, { ref });
  pop();
}
function Dialog_description($$payload, $$props) {
  push();
  const uid = props_id($$payload);
  let {
    id = createId(uid),
    children,
    child,
    ref = null,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const descriptionState = DialogDescriptionState.create({
    id: box.with(() => id),
    ref: box.with(() => ref, (v) => ref = v)
  });
  const mergedProps = mergeProps(restProps, descriptionState.props);
  if (child) {
    $$payload.out.push("<!--[-->");
    child($$payload, { props: mergedProps });
    $$payload.out.push(`<!---->`);
  } else {
    $$payload.out.push("<!--[!-->");
    $$payload.out.push(`<div${spread_attributes({ ...mergedProps }, null)}>`);
    children?.($$payload);
    $$payload.out.push(`<!----></div>`);
  }
  $$payload.out.push(`<!--]-->`);
  bind_props($$props, { ref });
  pop();
}
function Dialog($$payload, $$props) {
  push();
  let {
    open = false,
    onOpenChange = noop,
    onOpenChangeComplete = noop,
    children
  } = $$props;
  DialogRootState.create({
    variant: box.with(() => "dialog"),
    open: box.with(() => open, (v) => {
      open = v;
      onOpenChange(v);
    }),
    onOpenChangeComplete: box.with(() => onOpenChangeComplete)
  });
  children?.($$payload);
  $$payload.out.push(`<!---->`);
  bind_props($$props, { open });
  pop();
}
export {
  Dialog_title as D,
  Shopping_bag as S,
  Dialog_description as a,
  Dialog as b,
  generateRandomPassword as c,
  getFlashModule as g,
  menuData as m
};
