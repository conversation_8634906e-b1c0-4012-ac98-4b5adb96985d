import { I as attr_class, J as clsx, B as escape_html, y as pop, w as push } from "./index2.js";
import { c as cn } from "./shadcn.utils.js";
function No_data($$payload, $$props) {
  push();
  let {
    children,
    noDataTitle,
    noDataClass,
    noDataDescription,
    titleClass,
    descriptionClass,
    snippet
  } = $$props;
  $$payload.out.push(`<div${attr_class(clsx(cn("animate-in fade-in-50 flex min-h-[400px] flex-col items-center justify-center rounded-md border border-dashed p-8 text-center", noDataClass)))}><div class="mx-auto flex max-w-[420px] flex-col items-center justify-center text-center"><div class="bg-muted flex h-20 w-20 items-center justify-center rounded-full">`);
  snippet?.($$payload);
  $$payload.out.push(`<!----></div> <h2${attr_class(clsx(cn("mt-6 text-xl font-semibold", titleClass)))}>${escape_html(noDataTitle)}</h2> <p${attr_class(clsx(cn("text-muted-foreground mt-2 mb-8 text-center text-sm leading-6 font-normal", descriptionClass)))}>${escape_html(noDataDescription)}</p> `);
  if (children) {
    $$payload.out.push("<!--[-->");
    children?.($$payload);
    $$payload.out.push(`<!---->`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--></div></div>`);
  pop();
}
export {
  No_data as N
};
