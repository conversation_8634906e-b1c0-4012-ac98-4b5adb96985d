import { b as auth, a as api } from "./index3.js";
import "remult";
import "clsx";
import "@sveltejs/kit/internal";
import "./exports.js";
import "./state.svelte.js";
import "./auth-client.js";
import "superjson";
import "@oslojs/crypto/sha2";
import "decimal.js";
import "linq-extensions";
import "remult/server";
import { svelteKitHandler } from "better-auth/svelte-kit";
import { f as building } from "./environment.js";
import "react/jsx-runtime";
import "react";
import { sequence } from "@sveltejs/kit/hooks";
const handleAuth = async ({ event, resolve }) => {
  return svelteKitHandler({ event, resolve, auth, building });
};
const handleDomain = async ({ event, resolve }) => {
  const { request, locals } = event;
  const host = request.headers.get("host");
  let subdomain = null;
  if (host) {
    const parts = host.split(".");
    if (parts.length > 2) {
      subdomain = parts[0];
    }
  }
  locals.subdomain = subdomain;
  return resolve(event);
};
const handle = sequence(handleDomain, api, handleAuth);
export {
  handle
};
