import { u as userPrefersMode, e as derivedMode } from "./states.svelte.js";
import "clsx";
function toggleMode() {
  userPrefersMode.current = derivedMode.current === "dark" ? "light" : "dark";
}
function defineConfig(config) {
  return config;
}
function setInitialMode({ defaultMode = "system", themeColors, darkClassNames = ["dark"], lightClassNames = [], defaultTheme = "", modeStorageKey = "mode-watcher-mode", themeStorageKey = "mode-watcher-theme" }) {
  const rootEl = document.documentElement;
  const mode = localStorage.getItem(modeStorageKey) ?? defaultMode;
  const theme = localStorage.getItem(themeStorageKey) ?? defaultTheme;
  const light = mode === "light" || mode === "system" && window.matchMedia("(prefers-color-scheme: light)").matches;
  if (light) {
    if (darkClassNames.length)
      rootEl.classList.remove(...darkClassNames.filter(Boolean));
    if (lightClassNames.length)
      rootEl.classList.add(...lightClassNames.filter(Boolean));
  } else {
    if (lightClassNames.length)
      rootEl.classList.remove(...lightClassNames.filter(Boolean));
    if (darkClassNames.length)
      rootEl.classList.add(...darkClassNames.filter(Boolean));
  }
  rootEl.style.colorScheme = light ? "light" : "dark";
  if (themeColors) {
    const themeMetaEl = document.querySelector('meta[name="theme-color"]');
    if (themeMetaEl) {
      themeMetaEl.setAttribute("content", mode === "light" ? themeColors.light : themeColors.dark);
    }
  }
  if (theme) {
    rootEl.setAttribute("data-theme", theme);
    localStorage.setItem(themeStorageKey, theme);
  }
  localStorage.setItem(modeStorageKey, mode);
}
export {
  defineConfig as d,
  setInitialMode as s,
  toggleMode as t
};
