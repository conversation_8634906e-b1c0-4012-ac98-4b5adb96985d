import clsx from "clsx";
import { c as createSubscriber } from "./states.svelte.js";
import { parseISO, formatISO, startOfQuarter, endOfQuarter, startOfWeek, endOfWeek, addDays, differenceInDays } from "date-fns";
import { defaultsDeep, memoize } from "lodash-es";
import { extendTailwindMerge } from "tailwind-merge";
const defaultWindow = void 0;
function getActiveElement(document2) {
  let activeElement = document2.activeElement;
  while (activeElement?.shadowRoot) {
    const node = activeElement.shadowRoot.activeElement;
    if (node === activeElement)
      break;
    else
      activeElement = node;
  }
  return activeElement;
}
class ActiveElement {
  #document;
  #subscribe;
  constructor(options = {}) {
    const { window: window2 = defaultWindow, document: document2 = window2?.document } = options;
    if (window2 === void 0) return;
    this.#document = document2;
    this.#subscribe = createSubscriber();
  }
  get current() {
    this.#subscribe?.();
    if (!this.#document) return null;
    return getActiveElement(this.#document);
  }
}
new ActiveElement();
function runWatcher(sources, flush, effect, options = {}) {
  const { lazy = false } = options;
}
function watch(sources, effect, options) {
  runWatcher(sources, "post", effect, options);
}
function watchPre(sources, effect, options) {
  runWatcher(sources, "pre", effect, options);
}
watch.pre = watchPre;
function range$1(start, stop, step) {
  start = +start, stop = +stop, step = (n = arguments.length) < 2 ? (stop = start, start = 0, 1) : n < 3 ? 1 : +step;
  var i = -1, n = Math.max(0, Math.ceil((stop - start) / step)) | 0, range2 = new Array(n);
  while (++i < n) {
    range2[i] = start + i * step;
  }
  return range2;
}
var PeriodType$1;
(function(PeriodType2) {
  PeriodType2[PeriodType2["Custom"] = 1] = "Custom";
  PeriodType2[PeriodType2["Day"] = 10] = "Day";
  PeriodType2[PeriodType2["DayTime"] = 11] = "DayTime";
  PeriodType2[PeriodType2["TimeOnly"] = 15] = "TimeOnly";
  PeriodType2[PeriodType2["Week"] = 20] = "Week";
  PeriodType2[PeriodType2["WeekSun"] = 21] = "WeekSun";
  PeriodType2[PeriodType2["WeekMon"] = 22] = "WeekMon";
  PeriodType2[PeriodType2["WeekTue"] = 23] = "WeekTue";
  PeriodType2[PeriodType2["WeekWed"] = 24] = "WeekWed";
  PeriodType2[PeriodType2["WeekThu"] = 25] = "WeekThu";
  PeriodType2[PeriodType2["WeekFri"] = 26] = "WeekFri";
  PeriodType2[PeriodType2["WeekSat"] = 27] = "WeekSat";
  PeriodType2[PeriodType2["Month"] = 30] = "Month";
  PeriodType2[PeriodType2["MonthYear"] = 31] = "MonthYear";
  PeriodType2[PeriodType2["Quarter"] = 40] = "Quarter";
  PeriodType2[PeriodType2["CalendarYear"] = 50] = "CalendarYear";
  PeriodType2[PeriodType2["FiscalYearOctober"] = 60] = "FiscalYearOctober";
  PeriodType2[PeriodType2["BiWeek1"] = 70] = "BiWeek1";
  PeriodType2[PeriodType2["BiWeek1Sun"] = 71] = "BiWeek1Sun";
  PeriodType2[PeriodType2["BiWeek1Mon"] = 72] = "BiWeek1Mon";
  PeriodType2[PeriodType2["BiWeek1Tue"] = 73] = "BiWeek1Tue";
  PeriodType2[PeriodType2["BiWeek1Wed"] = 74] = "BiWeek1Wed";
  PeriodType2[PeriodType2["BiWeek1Thu"] = 75] = "BiWeek1Thu";
  PeriodType2[PeriodType2["BiWeek1Fri"] = 76] = "BiWeek1Fri";
  PeriodType2[PeriodType2["BiWeek1Sat"] = 77] = "BiWeek1Sat";
  PeriodType2[PeriodType2["BiWeek2"] = 80] = "BiWeek2";
  PeriodType2[PeriodType2["BiWeek2Sun"] = 81] = "BiWeek2Sun";
  PeriodType2[PeriodType2["BiWeek2Mon"] = 82] = "BiWeek2Mon";
  PeriodType2[PeriodType2["BiWeek2Tue"] = 83] = "BiWeek2Tue";
  PeriodType2[PeriodType2["BiWeek2Wed"] = 84] = "BiWeek2Wed";
  PeriodType2[PeriodType2["BiWeek2Thu"] = 85] = "BiWeek2Thu";
  PeriodType2[PeriodType2["BiWeek2Fri"] = 86] = "BiWeek2Fri";
  PeriodType2[PeriodType2["BiWeek2Sat"] = 87] = "BiWeek2Sat";
})(PeriodType$1 || (PeriodType$1 = {}));
var DayOfWeek$1;
(function(DayOfWeek2) {
  DayOfWeek2[DayOfWeek2["Sunday"] = 0] = "Sunday";
  DayOfWeek2[DayOfWeek2["Monday"] = 1] = "Monday";
  DayOfWeek2[DayOfWeek2["Tuesday"] = 2] = "Tuesday";
  DayOfWeek2[DayOfWeek2["Wednesday"] = 3] = "Wednesday";
  DayOfWeek2[DayOfWeek2["Thursday"] = 4] = "Thursday";
  DayOfWeek2[DayOfWeek2["Friday"] = 5] = "Friday";
  DayOfWeek2[DayOfWeek2["Saturday"] = 6] = "Saturday";
})(DayOfWeek$1 || (DayOfWeek$1 = {}));
var DateToken$1;
(function(DateToken2) {
  DateToken2["Year_numeric"] = "yyy";
  DateToken2["Year_2Digit"] = "yy";
  DateToken2["Month_long"] = "MMMM";
  DateToken2["Month_short"] = "MMM";
  DateToken2["Month_2Digit"] = "MM";
  DateToken2["Month_numeric"] = "M";
  DateToken2["Hour_numeric"] = "h";
  DateToken2["Hour_2Digit"] = "hh";
  DateToken2["Hour_wAMPM"] = "a";
  DateToken2["Hour_woAMPM"] = "aaaaaa";
  DateToken2["Minute_numeric"] = "m";
  DateToken2["Minute_2Digit"] = "mm";
  DateToken2["Second_numeric"] = "s";
  DateToken2["Second_2Digit"] = "ss";
  DateToken2["MiliSecond_3"] = "SSS";
  DateToken2["DayOfMonth_numeric"] = "d";
  DateToken2["DayOfMonth_2Digit"] = "dd";
  DateToken2["DayOfMonth_withOrdinal"] = "do";
  DateToken2["DayOfWeek_narrow"] = "eeeee";
  DateToken2["DayOfWeek_long"] = "eeee";
  DateToken2["DayOfWeek_short"] = "eee";
})(DateToken$1 || (DateToken$1 = {}));
function getWeekStartsOnFromIntl$1(locales) {
  if (!locales) {
    return DayOfWeek$1.Sunday;
  }
  const locale = new Intl.Locale(locales);
  const weekInfo = locale.weekInfo ?? locale.getWeekInfo?.();
  return (weekInfo?.firstDay ?? 0) % 7;
}
const defaultLocaleSettings$1 = {
  locale: "en",
  dictionary: {
    Ok: "Ok",
    Cancel: "Cancel",
    Date: {
      Start: "Start",
      End: "End",
      Empty: "Empty",
      Day: "Day",
      DayTime: "Day Time",
      Time: "Time",
      Week: "Week",
      BiWeek: "Bi-Week",
      Month: "Month",
      Quarter: "Quarter",
      CalendarYear: "Calendar Year",
      FiscalYearOct: "Fiscal Year (Oct)",
      PeriodDay: {
        Current: "Today",
        Last: "Yesterday",
        LastX: "Last {0} days"
      },
      PeriodWeek: {
        Current: "This week",
        Last: "Last week",
        LastX: "Last {0} weeks"
      },
      PeriodBiWeek: {
        Current: "This bi-week",
        Last: "Last bi-week",
        LastX: "Last {0} bi-weeks"
      },
      PeriodMonth: {
        Current: "This month",
        Last: "Last month",
        LastX: "Last {0} months"
      },
      PeriodQuarter: {
        Current: "This quarter",
        Last: "Last quarter",
        LastX: "Last {0} quarters"
      },
      PeriodQuarterSameLastyear: "Same quarter last year",
      PeriodYear: {
        Current: "This year",
        Last: "Last year",
        LastX: "Last {0} years"
      },
      PeriodFiscalYear: {
        Current: "This fiscal year",
        Last: "Last fiscal year",
        LastX: "Last {0} fiscal years"
      }
    }
  },
  formats: {
    numbers: {
      defaults: {
        currency: "USD",
        fractionDigits: 2,
        currencyDisplay: "symbol"
      }
    },
    dates: {
      baseParsing: "MM/dd/yyyy",
      weekStartsOn: DayOfWeek$1.Sunday,
      ordinalSuffixes: {
        one: "st",
        two: "nd",
        few: "rd",
        other: "th"
      },
      presets: {
        day: {
          short: [DateToken$1.DayOfMonth_numeric, DateToken$1.Month_numeric],
          default: [DateToken$1.DayOfMonth_numeric, DateToken$1.Month_numeric, DateToken$1.Year_numeric],
          long: [DateToken$1.DayOfMonth_numeric, DateToken$1.Month_short, DateToken$1.Year_numeric]
        },
        dayTime: {
          short: [
            DateToken$1.DayOfMonth_numeric,
            DateToken$1.Month_numeric,
            DateToken$1.Year_numeric,
            DateToken$1.Hour_numeric,
            DateToken$1.Minute_numeric
          ],
          default: [
            DateToken$1.DayOfMonth_numeric,
            DateToken$1.Month_numeric,
            DateToken$1.Year_numeric,
            DateToken$1.Hour_2Digit,
            DateToken$1.Minute_2Digit
          ],
          long: [
            DateToken$1.DayOfMonth_numeric,
            DateToken$1.Month_numeric,
            DateToken$1.Year_numeric,
            DateToken$1.Hour_2Digit,
            DateToken$1.Minute_2Digit,
            DateToken$1.Second_2Digit
          ]
        },
        timeOnly: {
          short: [DateToken$1.Hour_numeric, DateToken$1.Minute_numeric],
          default: [DateToken$1.Hour_2Digit, DateToken$1.Minute_2Digit, DateToken$1.Second_2Digit],
          long: [
            DateToken$1.Hour_2Digit,
            DateToken$1.Minute_2Digit,
            DateToken$1.Second_2Digit,
            DateToken$1.MiliSecond_3
          ]
        },
        week: {
          short: [DateToken$1.DayOfMonth_numeric, DateToken$1.Month_numeric],
          default: [DateToken$1.DayOfMonth_numeric, DateToken$1.Month_numeric, DateToken$1.Year_numeric],
          long: [DateToken$1.DayOfMonth_numeric, DateToken$1.Month_numeric, DateToken$1.Year_numeric]
        },
        month: {
          short: DateToken$1.Month_short,
          default: DateToken$1.Month_short,
          long: DateToken$1.Month_long
        },
        monthsYear: {
          short: [DateToken$1.Month_short, DateToken$1.Year_2Digit],
          default: [DateToken$1.Month_long, DateToken$1.Year_numeric],
          long: [DateToken$1.Month_long, DateToken$1.Year_numeric]
        },
        year: {
          short: DateToken$1.Year_2Digit,
          default: DateToken$1.Year_numeric,
          long: DateToken$1.Year_numeric
        }
      }
    }
  }
};
function createLocaleSettings$1(localeSettings, base = defaultLocaleSettings$1) {
  if (localeSettings.formats?.dates?.ordinalSuffixes) {
    localeSettings.formats.dates.ordinalSuffixes = {
      one: "",
      two: "",
      few: "",
      other: "",
      zero: "",
      many: "",
      ...localeSettings.formats.dates.ordinalSuffixes
    };
  }
  if (localeSettings.formats?.dates?.weekStartsOn === void 0) {
    localeSettings = defaultsDeep(localeSettings, {
      formats: { dates: { weekStartsOn: getWeekStartsOnFromIntl$1(localeSettings.locale) } }
    });
  }
  return defaultsDeep(localeSettings, base);
}
const defaultLocale = createLocaleSettings$1({ locale: "en" });
({
  [PeriodType$1.Custom]: "CUSTOM",
  [PeriodType$1.Day]: "DAY",
  [PeriodType$1.DayTime]: "DAY-TIME",
  [PeriodType$1.TimeOnly]: "TIME",
  [PeriodType$1.WeekSun]: "WEEK-SUN",
  [PeriodType$1.WeekMon]: "WEEK-MON",
  [PeriodType$1.WeekTue]: "WEEK-TUE",
  [PeriodType$1.WeekWed]: "WEEK-WED",
  [PeriodType$1.WeekThu]: "WEEK-THU",
  [PeriodType$1.WeekFri]: "WEEK-FRI",
  [PeriodType$1.WeekSat]: "WEEK-SAT",
  [PeriodType$1.Week]: "WEEK",
  [PeriodType$1.Month]: "MTH",
  [PeriodType$1.MonthYear]: "MTH-CY",
  [PeriodType$1.Quarter]: "QTR",
  [PeriodType$1.CalendarYear]: "CY",
  [PeriodType$1.FiscalYearOctober]: "FY-OCT",
  [PeriodType$1.BiWeek1Sun]: "BIWEEK1-SUN",
  [PeriodType$1.BiWeek1Mon]: "BIWEEK1-MON",
  [PeriodType$1.BiWeek1Tue]: "BIWEEK1-TUE",
  [PeriodType$1.BiWeek1Wed]: "BIWEEK1-WED",
  [PeriodType$1.BiWeek1Thu]: "BIWEEK1-THU",
  [PeriodType$1.BiWeek1Fri]: "BIWEEK1-FRI",
  [PeriodType$1.BiWeek1Sat]: "BIWEEK1-SAT",
  [PeriodType$1.BiWeek1]: "BIWEEK1",
  [PeriodType$1.BiWeek2Sun]: "BIWEEK2-SUN",
  [PeriodType$1.BiWeek2Mon]: "BIWEEK2-MON",
  [PeriodType$1.BiWeek2Tue]: "BIWEEK2-TUE",
  [PeriodType$1.BiWeek2Wed]: "BIWEEK2-WED",
  [PeriodType$1.BiWeek2Thu]: "BIWEEK2-THU",
  [PeriodType$1.BiWeek2Fri]: "BIWEEK2-FRI",
  [PeriodType$1.BiWeek2Sat]: "BIWEEK2-SAT",
  [PeriodType$1.BiWeek2]: "BIWEEK2"
});
function getFiscalYear(date = /* @__PURE__ */ new Date(), options) {
  if (date === null) {
    return NaN;
  }
  const startMonth = 10;
  return date.getMonth() >= startMonth - 1 ? date.getFullYear() + 1 : date.getFullYear();
}
const biweekBaseDates = [/* @__PURE__ */ new Date("1799-12-22T00:00"), /* @__PURE__ */ new Date("1799-12-15T00:00")];
function startOfBiWeek(date, week, startOfWeek2) {
  var weekBaseDate = biweekBaseDates[week - 1];
  var baseDate = addDays(weekBaseDate, startOfWeek2);
  var periodsSince = Math.floor(differenceInDays(date, baseDate) / 14);
  return addDays(baseDate, periodsSince * 14);
}
function endOfBiWeek(date, week, startOfWeek2) {
  return addDays(startOfBiWeek(date, week, startOfWeek2), 13);
}
function formatIntl(settings, dt, tokens_or_intlOptions) {
  const { locale, formats: { dates: { ordinalSuffixes: suffixes } } } = settings;
  function formatIntlOrdinal(formatter2, with_ordinal = false) {
    if (with_ordinal) {
      const rules = new Intl.PluralRules(locale, { type: "ordinal" });
      const splited = formatter2.formatToParts(dt);
      return splited.map((c2) => {
        if (c2.type === "day") {
          const ordinal = rules.select(parseInt(c2.value, 10));
          const suffix = suffixes[ordinal];
          return `${c2.value}${suffix}`;
        }
        return c2.value;
      }).join("");
    }
    return formatter2.format(dt);
  }
  if (typeof tokens_or_intlOptions !== "string" && !Array.isArray(tokens_or_intlOptions)) {
    return formatIntlOrdinal(new Intl.DateTimeFormat(locale, tokens_or_intlOptions), tokens_or_intlOptions.withOrdinal);
  }
  const tokens = Array.isArray(tokens_or_intlOptions) ? tokens_or_intlOptions.join("") : tokens_or_intlOptions;
  const formatter = new Intl.DateTimeFormat(locale, {
    year: tokens.includes(DateToken$1.Year_numeric) ? "numeric" : tokens.includes(DateToken$1.Year_2Digit) ? "2-digit" : void 0,
    month: tokens.includes(DateToken$1.Month_long) ? "long" : tokens.includes(DateToken$1.Month_short) ? "short" : tokens.includes(DateToken$1.Month_2Digit) ? "2-digit" : tokens.includes(DateToken$1.Month_numeric) ? "numeric" : void 0,
    day: tokens.includes(DateToken$1.DayOfMonth_2Digit) ? "2-digit" : tokens.includes(DateToken$1.DayOfMonth_numeric) ? "numeric" : void 0,
    hour: tokens.includes(DateToken$1.Hour_2Digit) ? "2-digit" : tokens.includes(DateToken$1.Hour_numeric) ? "numeric" : void 0,
    hour12: tokens.includes(DateToken$1.Hour_woAMPM) ? false : tokens.includes(DateToken$1.Hour_wAMPM) ? true : void 0,
    minute: tokens.includes(DateToken$1.Minute_2Digit) ? "2-digit" : tokens.includes(DateToken$1.Minute_numeric) ? "numeric" : void 0,
    second: tokens.includes(DateToken$1.Second_2Digit) ? "2-digit" : tokens.includes(DateToken$1.Second_numeric) ? "numeric" : void 0,
    fractionalSecondDigits: tokens.includes(DateToken$1.MiliSecond_3) ? 3 : void 0,
    weekday: tokens.includes(DateToken$1.DayOfWeek_narrow) ? "narrow" : tokens.includes(DateToken$1.DayOfWeek_long) ? "long" : tokens.includes(DateToken$1.DayOfWeek_short) ? "short" : void 0
  });
  return formatIntlOrdinal(formatter, tokens.includes(DateToken$1.DayOfMonth_withOrdinal));
}
function range(settings, date, weekStartsOn, formatToUse, biWeek = void 0) {
  const start = biWeek === void 0 ? startOfWeek(date, { weekStartsOn }) : startOfBiWeek(date, biWeek, weekStartsOn);
  const end = biWeek === void 0 ? endOfWeek(date, { weekStartsOn }) : endOfBiWeek(date, biWeek, weekStartsOn);
  return formatIntl(settings, start, formatToUse) + " - " + formatIntl(settings, end, formatToUse);
}
function updatePeriodTypeWithWeekStartsOn(weekStartsOn, periodType) {
  if (periodType === PeriodType$1.Week) {
    periodType = [
      PeriodType$1.WeekSun,
      PeriodType$1.WeekMon,
      PeriodType$1.WeekTue,
      PeriodType$1.WeekWed,
      PeriodType$1.WeekThu,
      PeriodType$1.WeekFri,
      PeriodType$1.WeekSat
    ][weekStartsOn];
  } else if (periodType === PeriodType$1.BiWeek1) {
    periodType = [
      PeriodType$1.BiWeek1Sun,
      PeriodType$1.BiWeek1Mon,
      PeriodType$1.BiWeek1Tue,
      PeriodType$1.BiWeek1Wed,
      PeriodType$1.BiWeek1Thu,
      PeriodType$1.BiWeek1Fri,
      PeriodType$1.BiWeek1Sat
    ][weekStartsOn];
  } else if (periodType === PeriodType$1.BiWeek2) {
    periodType = [
      PeriodType$1.BiWeek2Sun,
      PeriodType$1.BiWeek2Mon,
      PeriodType$1.BiWeek2Tue,
      PeriodType$1.BiWeek2Wed,
      PeriodType$1.BiWeek2Thu,
      PeriodType$1.BiWeek2Fri,
      PeriodType$1.BiWeek2Sat
    ][weekStartsOn];
  }
  return periodType;
}
function formatDateWithLocale(settings, date, periodType, options = {}) {
  if (typeof date === "string") {
    date = parseISO(date);
  }
  if (date == null || isNaN(date)) {
    return "";
  }
  const weekStartsOn = options.weekStartsOn ?? settings.formats.dates.weekStartsOn;
  const { day, dayTime, timeOnly, week, month, monthsYear, year } = settings.formats.dates.presets;
  periodType = updatePeriodTypeWithWeekStartsOn(weekStartsOn, periodType) ?? periodType;
  function rv(preset) {
    if (options.variant === "custom") {
      return options.custom ?? preset.default;
    } else if (options.custom && !options.variant) {
      return options.custom;
    }
    return preset[options.variant ?? "default"];
  }
  switch (periodType) {
    case PeriodType$1.Custom:
      return formatIntl(settings, date, options.custom);
    case PeriodType$1.Day:
      return formatIntl(settings, date, rv(day));
    case PeriodType$1.DayTime:
      return formatIntl(settings, date, rv(dayTime));
    case PeriodType$1.TimeOnly:
      return formatIntl(settings, date, rv(timeOnly));
    case PeriodType$1.Week:
    //Should never happen, but to make types happy
    case PeriodType$1.WeekSun:
      return range(settings, date, 0, rv(week));
    case PeriodType$1.WeekMon:
      return range(settings, date, 1, rv(week));
    case PeriodType$1.WeekTue:
      return range(settings, date, 2, rv(week));
    case PeriodType$1.WeekWed:
      return range(settings, date, 3, rv(week));
    case PeriodType$1.WeekThu:
      return range(settings, date, 4, rv(week));
    case PeriodType$1.WeekFri:
      return range(settings, date, 5, rv(week));
    case PeriodType$1.WeekSat:
      return range(settings, date, 6, rv(week));
    case PeriodType$1.Month:
      return formatIntl(settings, date, rv(month));
    case PeriodType$1.MonthYear:
      return formatIntl(settings, date, rv(monthsYear));
    case PeriodType$1.Quarter:
      return [
        formatIntl(settings, startOfQuarter(date), rv(month)),
        formatIntl(settings, endOfQuarter(date), rv(monthsYear))
      ].join(" - ");
    case PeriodType$1.CalendarYear:
      return formatIntl(settings, date, rv(year));
    case PeriodType$1.FiscalYearOctober:
      const fDate = new Date(getFiscalYear(date), 0, 1);
      return formatIntl(settings, fDate, rv(year));
    case PeriodType$1.BiWeek1:
    //Should never happen, but to make types happy
    case PeriodType$1.BiWeek1Sun:
      return range(settings, date, 0, rv(week), 1);
    case PeriodType$1.BiWeek1Mon:
      return range(settings, date, 1, rv(week), 1);
    case PeriodType$1.BiWeek1Tue:
      return range(settings, date, 2, rv(week), 1);
    case PeriodType$1.BiWeek1Wed:
      return range(settings, date, 3, rv(week), 1);
    case PeriodType$1.BiWeek1Thu:
      return range(settings, date, 4, rv(week), 1);
    case PeriodType$1.BiWeek1Fri:
      return range(settings, date, 5, rv(week), 1);
    case PeriodType$1.BiWeek1Sat:
      return range(settings, date, 6, rv(week), 1);
    case PeriodType$1.BiWeek2:
    //Should never happen, but to make types happy
    case PeriodType$1.BiWeek2Sun:
      return range(settings, date, 0, rv(week), 2);
    case PeriodType$1.BiWeek2Mon:
      return range(settings, date, 1, rv(week), 2);
    case PeriodType$1.BiWeek2Tue:
      return range(settings, date, 2, rv(week), 2);
    case PeriodType$1.BiWeek2Wed:
      return range(settings, date, 3, rv(week), 2);
    case PeriodType$1.BiWeek2Thu:
      return range(settings, date, 4, rv(week), 2);
    case PeriodType$1.BiWeek2Fri:
      return range(settings, date, 5, rv(week), 2);
    case PeriodType$1.BiWeek2Sat:
      return range(settings, date, 6, rv(week), 2);
    default:
      return formatISO(date);
  }
}
const DATE_FORMAT = /^\d{4}-\d{2}-\d{2}(T\d{2}:\d{2}:\d{2}(.\d+|)(Z|(-|\+)\d{2}:\d{2}))?$/;
function isStringDate(value) {
  return DATE_FORMAT.test(value);
}
var DurationUnits$1;
(function(DurationUnits2) {
  DurationUnits2[DurationUnits2["Year"] = 0] = "Year";
  DurationUnits2[DurationUnits2["Day"] = 1] = "Day";
  DurationUnits2[DurationUnits2["Hour"] = 2] = "Hour";
  DurationUnits2[DurationUnits2["Minute"] = 3] = "Minute";
  DurationUnits2[DurationUnits2["Second"] = 4] = "Second";
  DurationUnits2[DurationUnits2["Millisecond"] = 5] = "Millisecond";
})(DurationUnits$1 || (DurationUnits$1 = {}));
class Duration {
  #milliseconds = 0;
  #seconds = 0;
  #minutes = 0;
  #hours = 0;
  #days = 0;
  #years = 0;
  constructor(options = {}) {
    const startDate = typeof options.start === "string" ? parseISO(options.start) : options.start;
    const endDate = typeof options.end === "string" ? parseISO(options.end) : options.end;
    const differenceInMs = startDate ? Math.abs(Number(endDate || /* @__PURE__ */ new Date()) - Number(startDate)) : void 0;
    if (!Number.isFinite(differenceInMs) && options.duration == null) {
      return;
    }
    this.#milliseconds = options.duration?.milliseconds ?? differenceInMs ?? 0;
    this.#seconds = options.duration?.seconds ?? 0;
    this.#minutes = options.duration?.minutes ?? 0;
    this.#hours = options.duration?.hours ?? 0;
    this.#days = options.duration?.days ?? 0;
    this.#years = options.duration?.years ?? 0;
    if (this.#milliseconds >= 1e3) {
      const carrySeconds = (this.#milliseconds - this.#milliseconds % 1e3) / 1e3;
      this.#seconds += carrySeconds;
      this.#milliseconds = this.#milliseconds - carrySeconds * 1e3;
    }
    if (this.#seconds >= 60) {
      const carryMinutes = (this.#seconds - this.#seconds % 60) / 60;
      this.#minutes += carryMinutes;
      this.#seconds = this.#seconds - carryMinutes * 60;
    }
    if (this.#minutes >= 60) {
      const carryHours = (this.#minutes - this.#minutes % 60) / 60;
      this.#hours += carryHours;
      this.#minutes = this.#minutes - carryHours * 60;
    }
    if (this.#hours >= 24) {
      const carryDays = (this.#hours - this.#hours % 24) / 24;
      this.#days += carryDays;
      this.#hours = this.#hours - carryDays * 24;
    }
    if (this.#days >= 365) {
      const carryYears = (this.#days - this.#days % 365) / 365;
      this.#years += carryYears;
      this.#days = this.#days - carryYears * 365;
    }
  }
  get years() {
    return this.#years;
  }
  get days() {
    return this.#days;
  }
  get hours() {
    return this.#hours;
  }
  get minutes() {
    return this.#minutes;
  }
  get seconds() {
    return this.#seconds;
  }
  get milliseconds() {
    return this.#milliseconds;
  }
  valueOf() {
    return this.#milliseconds + this.#seconds * 1e3 + this.#minutes * 60 * 1e3 + this.#hours * 60 * 60 * 1e3 + this.#days * 24 * 60 * 60 * 1e3 + this.#years * 365 * 24 * 60 * 60 * 1e3;
  }
  toJSON() {
    return {
      years: this.#years,
      days: this.#days,
      hours: this.#hours,
      minutes: this.#minutes,
      seconds: this.#seconds,
      milliseconds: this.#milliseconds
    };
  }
  format(options = {}) {
    const { minUnits, totalUnits = 99, variant = "short" } = options;
    var sentenceArr = [];
    var unitNames = variant === "short" ? ["y", "d", "h", "m", "s", "ms"] : ["years", "days", "hours", "minutes", "seconds", "milliseconds"];
    var unitNums = [
      this.years,
      this.days,
      this.hours,
      this.minutes,
      this.seconds,
      this.milliseconds
    ].filter((x, i2) => i2 <= (minUnits ?? 99));
    for (var i in unitNums) {
      if (sentenceArr.length >= totalUnits) {
        break;
      }
      const unitNum = unitNums[i];
      let unitName = unitNames[i];
      if (unitNum !== 0 || sentenceArr.length === 0 && Number(i) === unitNums.length - 1) {
        switch (variant) {
          case "short":
            sentenceArr.push(unitNum + unitName);
            break;
          case "long":
            if (unitNum === 1) {
              unitName = unitName.slice(0, -1);
            }
            sentenceArr.push(unitNum + " " + unitName);
            break;
        }
      }
    }
    const sentence = sentenceArr.join(variant === "long" ? " and " : " ");
    return sentence;
  }
  toString() {
    return this.format();
  }
}
const parseNumber = (color, len) => {
  if (typeof color !== "number") return;
  if (len === 3) {
    return {
      mode: "rgb",
      r: (color >> 8 & 15 | color >> 4 & 240) / 255,
      g: (color >> 4 & 15 | color & 240) / 255,
      b: (color & 15 | color << 4 & 240) / 255
    };
  }
  if (len === 4) {
    return {
      mode: "rgb",
      r: (color >> 12 & 15 | color >> 8 & 240) / 255,
      g: (color >> 8 & 15 | color >> 4 & 240) / 255,
      b: (color >> 4 & 15 | color & 240) / 255,
      alpha: (color & 15 | color << 4 & 240) / 255
    };
  }
  if (len === 6) {
    return {
      mode: "rgb",
      r: (color >> 16 & 255) / 255,
      g: (color >> 8 & 255) / 255,
      b: (color & 255) / 255
    };
  }
  if (len === 8) {
    return {
      mode: "rgb",
      r: (color >> 24 & 255) / 255,
      g: (color >> 16 & 255) / 255,
      b: (color >> 8 & 255) / 255,
      alpha: (color & 255) / 255
    };
  }
};
const named = {
  aliceblue: 15792383,
  antiquewhite: 16444375,
  aqua: 65535,
  aquamarine: 8388564,
  azure: 15794175,
  beige: 16119260,
  bisque: 16770244,
  black: 0,
  blanchedalmond: 16772045,
  blue: 255,
  blueviolet: 9055202,
  brown: 10824234,
  burlywood: 14596231,
  cadetblue: 6266528,
  chartreuse: 8388352,
  chocolate: 13789470,
  coral: 16744272,
  cornflowerblue: 6591981,
  cornsilk: 16775388,
  crimson: 14423100,
  cyan: 65535,
  darkblue: 139,
  darkcyan: 35723,
  darkgoldenrod: 12092939,
  darkgray: 11119017,
  darkgreen: 25600,
  darkgrey: 11119017,
  darkkhaki: 12433259,
  darkmagenta: 9109643,
  darkolivegreen: 5597999,
  darkorange: 16747520,
  darkorchid: 10040012,
  darkred: 9109504,
  darksalmon: 15308410,
  darkseagreen: 9419919,
  darkslateblue: 4734347,
  darkslategray: 3100495,
  darkslategrey: 3100495,
  darkturquoise: 52945,
  darkviolet: 9699539,
  deeppink: 16716947,
  deepskyblue: 49151,
  dimgray: 6908265,
  dimgrey: 6908265,
  dodgerblue: 2003199,
  firebrick: 11674146,
  floralwhite: 16775920,
  forestgreen: 2263842,
  fuchsia: 16711935,
  gainsboro: 14474460,
  ghostwhite: 16316671,
  gold: 16766720,
  goldenrod: 14329120,
  gray: 8421504,
  green: 32768,
  greenyellow: 11403055,
  grey: 8421504,
  honeydew: 15794160,
  hotpink: 16738740,
  indianred: 13458524,
  indigo: 4915330,
  ivory: 16777200,
  khaki: 15787660,
  lavender: 15132410,
  lavenderblush: 16773365,
  lawngreen: 8190976,
  lemonchiffon: 16775885,
  lightblue: 11393254,
  lightcoral: 15761536,
  lightcyan: 14745599,
  lightgoldenrodyellow: 16448210,
  lightgray: 13882323,
  lightgreen: 9498256,
  lightgrey: 13882323,
  lightpink: 16758465,
  lightsalmon: 16752762,
  lightseagreen: 2142890,
  lightskyblue: 8900346,
  lightslategray: 7833753,
  lightslategrey: 7833753,
  lightsteelblue: 11584734,
  lightyellow: 16777184,
  lime: 65280,
  limegreen: 3329330,
  linen: 16445670,
  magenta: 16711935,
  maroon: 8388608,
  mediumaquamarine: 6737322,
  mediumblue: 205,
  mediumorchid: 12211667,
  mediumpurple: 9662683,
  mediumseagreen: 3978097,
  mediumslateblue: 8087790,
  mediumspringgreen: 64154,
  mediumturquoise: 4772300,
  mediumvioletred: 13047173,
  midnightblue: 1644912,
  mintcream: 16121850,
  mistyrose: 16770273,
  moccasin: 16770229,
  navajowhite: 16768685,
  navy: 128,
  oldlace: 16643558,
  olive: 8421376,
  olivedrab: 7048739,
  orange: 16753920,
  orangered: 16729344,
  orchid: 14315734,
  palegoldenrod: 15657130,
  palegreen: 10025880,
  paleturquoise: 11529966,
  palevioletred: 14381203,
  papayawhip: 16773077,
  peachpuff: 16767673,
  peru: 13468991,
  pink: 16761035,
  plum: 14524637,
  powderblue: 11591910,
  purple: 8388736,
  // Added in CSS Colors Level 4:
  // https://drafts.csswg.org/css-color/#changes-from-3
  rebeccapurple: 6697881,
  red: 16711680,
  rosybrown: 12357519,
  royalblue: 4286945,
  saddlebrown: 9127187,
  salmon: 16416882,
  sandybrown: 16032864,
  seagreen: 3050327,
  seashell: 16774638,
  sienna: 10506797,
  silver: 12632256,
  skyblue: 8900331,
  slateblue: 6970061,
  slategray: 7372944,
  slategrey: 7372944,
  snow: 16775930,
  springgreen: 65407,
  steelblue: 4620980,
  tan: 13808780,
  teal: 32896,
  thistle: 14204888,
  tomato: 16737095,
  turquoise: 4251856,
  violet: 15631086,
  wheat: 16113331,
  white: 16777215,
  whitesmoke: 16119285,
  yellow: 16776960,
  yellowgreen: 10145074
};
const parseNamed = (color) => {
  return parseNumber(named[color.toLowerCase()], 6);
};
const hex = /^#?([0-9a-f]{8}|[0-9a-f]{6}|[0-9a-f]{4}|[0-9a-f]{3})$/i;
const parseHex = (color) => {
  let match;
  return (match = color.match(hex)) ? parseNumber(parseInt(match[1], 16), match[1].length) : void 0;
};
const num$1 = "([+-]?\\d*\\.?\\d+(?:[eE][+-]?\\d+)?)";
const per = `${num$1}%`;
const num_per = `(?:${num$1}%|${num$1})`;
const hue$1 = `(?:${num$1}(deg|grad|rad|turn)|${num$1})`;
const c = `\\s*,\\s*`;
const rgb_num_old = new RegExp(
  `^rgba?\\(\\s*${num$1}${c}${num$1}${c}${num$1}\\s*(?:,\\s*${num_per}\\s*)?\\)$`
);
const rgb_per_old = new RegExp(
  `^rgba?\\(\\s*${per}${c}${per}${c}${per}\\s*(?:,\\s*${num_per}\\s*)?\\)$`
);
const parseRgbLegacy = (color) => {
  let res = { mode: "rgb" };
  let match;
  if (match = color.match(rgb_num_old)) {
    if (match[1] !== void 0) {
      res.r = match[1] / 255;
    }
    if (match[2] !== void 0) {
      res.g = match[2] / 255;
    }
    if (match[3] !== void 0) {
      res.b = match[3] / 255;
    }
  } else if (match = color.match(rgb_per_old)) {
    if (match[1] !== void 0) {
      res.r = match[1] / 100;
    }
    if (match[2] !== void 0) {
      res.g = match[2] / 100;
    }
    if (match[3] !== void 0) {
      res.b = match[3] / 100;
    }
  } else {
    return void 0;
  }
  if (match[4] !== void 0) {
    res.alpha = Math.max(0, Math.min(1, match[4] / 100));
  } else if (match[5] !== void 0) {
    res.alpha = Math.max(0, Math.min(1, +match[5]));
  }
  return res;
};
const prepare = (color, mode) => color === void 0 ? void 0 : typeof color !== "object" ? parse(color) : color.mode !== void 0 ? color : mode ? { ...color, mode } : void 0;
const converter = (target_mode = "rgb") => (color) => (color = prepare(color, target_mode)) !== void 0 ? (
  // if the color's mode corresponds to our target mode
  color.mode === target_mode ? (
    // then just return the color
    color
  ) : (
    // otherwise check to see if we have a dedicated
    // converter for the target mode
    converters[color.mode][target_mode] ? (
      // and return its result...
      converters[color.mode][target_mode](color)
    ) : (
      // ...otherwise pass through RGB as an intermediary step.
      // if the target mode is RGB...
      target_mode === "rgb" ? (
        // just return the RGB
        converters[color.mode].rgb(color)
      ) : (
        // otherwise convert color.mode -> RGB -> target_mode
        converters.rgb[target_mode](converters[color.mode].rgb(color))
      )
    )
  )
) : void 0;
const converters = {};
const modes = {};
const parsers = [];
const colorProfiles = {};
const identity = (v) => v;
const useMode = (definition2) => {
  converters[definition2.mode] = {
    ...converters[definition2.mode],
    ...definition2.toMode
  };
  Object.keys(definition2.fromMode || {}).forEach((k2) => {
    if (!converters[k2]) {
      converters[k2] = {};
    }
    converters[k2][definition2.mode] = definition2.fromMode[k2];
  });
  if (!definition2.ranges) {
    definition2.ranges = {};
  }
  if (!definition2.difference) {
    definition2.difference = {};
  }
  definition2.channels.forEach((channel) => {
    if (definition2.ranges[channel] === void 0) {
      definition2.ranges[channel] = [0, 1];
    }
    if (!definition2.interpolate[channel]) {
      throw new Error(`Missing interpolator for: ${channel}`);
    }
    if (typeof definition2.interpolate[channel] === "function") {
      definition2.interpolate[channel] = {
        use: definition2.interpolate[channel]
      };
    }
    if (!definition2.interpolate[channel].fixup) {
      definition2.interpolate[channel].fixup = identity;
    }
  });
  modes[definition2.mode] = definition2;
  (definition2.parse || []).forEach((parser) => {
    useParser(parser, definition2.mode);
  });
  return converter(definition2.mode);
};
const getMode = (mode) => modes[mode];
const useParser = (parser, mode) => {
  if (typeof parser === "string") {
    if (!mode) {
      throw new Error(`'mode' required when 'parser' is a string`);
    }
    colorProfiles[parser] = mode;
  } else if (typeof parser === "function") {
    if (parsers.indexOf(parser) < 0) {
      parsers.push(parser);
    }
  }
};
const IdentStartCodePoint = /[^\x00-\x7F]|[a-zA-Z_]/;
const IdentCodePoint = /[^\x00-\x7F]|[-\w]/;
const Tok = {
  Function: "function",
  Ident: "ident",
  Number: "number",
  Percentage: "percentage",
  ParenClose: ")",
  None: "none",
  Hue: "hue",
  Alpha: "alpha"
};
let _i = 0;
function is_num(chars) {
  let ch = chars[_i];
  let ch1 = chars[_i + 1];
  if (ch === "-" || ch === "+") {
    return /\d/.test(ch1) || ch1 === "." && /\d/.test(chars[_i + 2]);
  }
  if (ch === ".") {
    return /\d/.test(ch1);
  }
  return /\d/.test(ch);
}
function is_ident(chars) {
  if (_i >= chars.length) {
    return false;
  }
  let ch = chars[_i];
  if (IdentStartCodePoint.test(ch)) {
    return true;
  }
  if (ch === "-") {
    if (chars.length - _i < 2) {
      return false;
    }
    let ch1 = chars[_i + 1];
    if (ch1 === "-" || IdentStartCodePoint.test(ch1)) {
      return true;
    }
    return false;
  }
  return false;
}
const huenits = {
  deg: 1,
  rad: 180 / Math.PI,
  grad: 9 / 10,
  turn: 360
};
function num(chars) {
  let value = "";
  if (chars[_i] === "-" || chars[_i] === "+") {
    value += chars[_i++];
  }
  value += digits(chars);
  if (chars[_i] === "." && /\d/.test(chars[_i + 1])) {
    value += chars[_i++] + digits(chars);
  }
  if (chars[_i] === "e" || chars[_i] === "E") {
    if ((chars[_i + 1] === "-" || chars[_i + 1] === "+") && /\d/.test(chars[_i + 2])) {
      value += chars[_i++] + chars[_i++] + digits(chars);
    } else if (/\d/.test(chars[_i + 1])) {
      value += chars[_i++] + digits(chars);
    }
  }
  if (is_ident(chars)) {
    let id = ident(chars);
    if (id === "deg" || id === "rad" || id === "turn" || id === "grad") {
      return { type: Tok.Hue, value: value * huenits[id] };
    }
    return void 0;
  }
  if (chars[_i] === "%") {
    _i++;
    return { type: Tok.Percentage, value: +value };
  }
  return { type: Tok.Number, value: +value };
}
function digits(chars) {
  let v = "";
  while (/\d/.test(chars[_i])) {
    v += chars[_i++];
  }
  return v;
}
function ident(chars) {
  let v = "";
  while (_i < chars.length && IdentCodePoint.test(chars[_i])) {
    v += chars[_i++];
  }
  return v;
}
function identlike(chars) {
  let v = ident(chars);
  if (chars[_i] === "(") {
    _i++;
    return { type: Tok.Function, value: v };
  }
  if (v === "none") {
    return { type: Tok.None, value: void 0 };
  }
  return { type: Tok.Ident, value: v };
}
function tokenize(str = "") {
  let chars = str.trim();
  let tokens = [];
  let ch;
  _i = 0;
  while (_i < chars.length) {
    ch = chars[_i++];
    if (ch === "\n" || ch === "	" || ch === " ") {
      while (_i < chars.length && (chars[_i] === "\n" || chars[_i] === "	" || chars[_i] === " ")) {
        _i++;
      }
      continue;
    }
    if (ch === ",") {
      return void 0;
    }
    if (ch === ")") {
      tokens.push({ type: Tok.ParenClose });
      continue;
    }
    if (ch === "+") {
      _i--;
      if (is_num(chars)) {
        tokens.push(num(chars));
        continue;
      }
      return void 0;
    }
    if (ch === "-") {
      _i--;
      if (is_num(chars)) {
        tokens.push(num(chars));
        continue;
      }
      if (is_ident(chars)) {
        tokens.push({ type: Tok.Ident, value: ident(chars) });
        continue;
      }
      return void 0;
    }
    if (ch === ".") {
      _i--;
      if (is_num(chars)) {
        tokens.push(num(chars));
        continue;
      }
      return void 0;
    }
    if (ch === "/") {
      while (_i < chars.length && (chars[_i] === "\n" || chars[_i] === "	" || chars[_i] === " ")) {
        _i++;
      }
      let alpha;
      if (is_num(chars)) {
        alpha = num(chars);
        if (alpha.type !== Tok.Hue) {
          tokens.push({ type: Tok.Alpha, value: alpha });
          continue;
        }
      }
      if (is_ident(chars)) {
        if (ident(chars) === "none") {
          tokens.push({
            type: Tok.Alpha,
            value: { type: Tok.None, value: void 0 }
          });
          continue;
        }
      }
      return void 0;
    }
    if (/\d/.test(ch)) {
      _i--;
      tokens.push(num(chars));
      continue;
    }
    if (IdentStartCodePoint.test(ch)) {
      _i--;
      tokens.push(identlike(chars));
      continue;
    }
    return void 0;
  }
  return tokens;
}
function parseColorSyntax(tokens) {
  tokens._i = 0;
  let token = tokens[tokens._i++];
  if (!token || token.type !== Tok.Function || token.value !== "color") {
    return void 0;
  }
  token = tokens[tokens._i++];
  if (token.type !== Tok.Ident) {
    return void 0;
  }
  const mode = colorProfiles[token.value];
  if (!mode) {
    return void 0;
  }
  const res = { mode };
  const coords = consumeCoords(tokens, false);
  if (!coords) {
    return void 0;
  }
  const channels = getMode(mode).channels;
  for (let ii = 0, c2, ch; ii < channels.length; ii++) {
    c2 = coords[ii];
    ch = channels[ii];
    if (c2.type !== Tok.None) {
      res[ch] = c2.type === Tok.Number ? c2.value : c2.value / 100;
      if (ch === "alpha") {
        res[ch] = Math.max(0, Math.min(1, res[ch]));
      }
    }
  }
  return res;
}
function consumeCoords(tokens, includeHue) {
  const coords = [];
  let token;
  while (tokens._i < tokens.length) {
    token = tokens[tokens._i++];
    if (token.type === Tok.None || token.type === Tok.Number || token.type === Tok.Alpha || token.type === Tok.Percentage || includeHue && token.type === Tok.Hue) {
      coords.push(token);
      continue;
    }
    if (token.type === Tok.ParenClose) {
      if (tokens._i < tokens.length) {
        return void 0;
      }
      continue;
    }
    return void 0;
  }
  if (coords.length < 3 || coords.length > 4) {
    return void 0;
  }
  if (coords.length === 4) {
    if (coords[3].type !== Tok.Alpha) {
      return void 0;
    }
    coords[3] = coords[3].value;
  }
  if (coords.length === 3) {
    coords.push({ type: Tok.None, value: void 0 });
  }
  return coords.every((c2) => c2.type !== Tok.Alpha) ? coords : void 0;
}
function parseModernSyntax(tokens, includeHue) {
  tokens._i = 0;
  let token = tokens[tokens._i++];
  if (!token || token.type !== Tok.Function) {
    return void 0;
  }
  let coords = consumeCoords(tokens, includeHue);
  if (!coords) {
    return void 0;
  }
  coords.unshift(token.value);
  return coords;
}
const parse = (color) => {
  if (typeof color !== "string") {
    return void 0;
  }
  const tokens = tokenize(color);
  const parsed = tokens ? parseModernSyntax(tokens, true) : void 0;
  let result = void 0;
  let i = 0;
  let len = parsers.length;
  while (i < len) {
    if ((result = parsers[i++](color, parsed)) !== void 0) {
      return result;
    }
  }
  return tokens ? parseColorSyntax(tokens) : void 0;
};
function parseRgb(color, parsed) {
  if (!parsed || parsed[0] !== "rgb" && parsed[0] !== "rgba") {
    return void 0;
  }
  const res = { mode: "rgb" };
  const [, r, g, b, alpha] = parsed;
  if (r.type === Tok.Hue || g.type === Tok.Hue || b.type === Tok.Hue) {
    return void 0;
  }
  if (r.type !== Tok.None) {
    res.r = r.type === Tok.Number ? r.value / 255 : r.value / 100;
  }
  if (g.type !== Tok.None) {
    res.g = g.type === Tok.Number ? g.value / 255 : g.value / 100;
  }
  if (b.type !== Tok.None) {
    res.b = b.type === Tok.Number ? b.value / 255 : b.value / 100;
  }
  if (alpha.type !== Tok.None) {
    res.alpha = Math.min(
      1,
      Math.max(
        0,
        alpha.type === Tok.Number ? alpha.value : alpha.value / 100
      )
    );
  }
  return res;
}
const parseTransparent = (c2) => c2 === "transparent" ? { mode: "rgb", r: 0, g: 0, b: 0, alpha: 0 } : void 0;
const lerp = (a, b, t) => a + t * (b - a);
const get_classes = (arr) => {
  let classes = [];
  for (let i = 0; i < arr.length - 1; i++) {
    let a = arr[i];
    let b = arr[i + 1];
    if (a === void 0 && b === void 0) {
      classes.push(void 0);
    } else if (a !== void 0 && b !== void 0) {
      classes.push([a, b]);
    } else {
      classes.push(a !== void 0 ? [a, a] : [b, b]);
    }
  }
  return classes;
};
const interpolatorPiecewise = (interpolator) => (arr) => {
  let classes = get_classes(arr);
  return (t) => {
    let cls2 = t * classes.length;
    let idx = t >= 1 ? classes.length - 1 : Math.max(Math.floor(cls2), 0);
    let pair = classes[idx];
    return pair === void 0 ? void 0 : interpolator(pair[0], pair[1], cls2 - idx);
  };
};
const interpolatorLinear = interpolatorPiecewise(lerp);
const fixupAlpha = (arr) => {
  let some_defined = false;
  let res = arr.map((v) => {
    if (v !== void 0) {
      some_defined = true;
      return v;
    }
    return 1;
  });
  return some_defined ? res : arr;
};
const definition$r = {
  mode: "rgb",
  channels: ["r", "g", "b", "alpha"],
  parse: [
    parseRgb,
    parseHex,
    parseRgbLegacy,
    parseNamed,
    parseTransparent,
    "srgb"
  ],
  serialize: "srgb",
  interpolate: {
    r: interpolatorLinear,
    g: interpolatorLinear,
    b: interpolatorLinear,
    alpha: { use: interpolatorLinear, fixup: fixupAlpha }
  },
  gamut: true,
  white: { r: 1, g: 1, b: 1 },
  black: { r: 0, g: 0, b: 0 }
};
const linearize$2 = (v = 0) => Math.pow(Math.abs(v), 563 / 256) * Math.sign(v);
const convertA98ToXyz65 = (a98) => {
  let r = linearize$2(a98.r);
  let g = linearize$2(a98.g);
  let b = linearize$2(a98.b);
  let res = {
    mode: "xyz65",
    x: 0.5766690429101305 * r + 0.1855582379065463 * g + 0.1882286462349947 * b,
    y: 0.297344975250536 * r + 0.6273635662554661 * g + 0.0752914584939979 * b,
    z: 0.0270313613864123 * r + 0.0706888525358272 * g + 0.9913375368376386 * b
  };
  if (a98.alpha !== void 0) {
    res.alpha = a98.alpha;
  }
  return res;
};
const gamma$2 = (v) => Math.pow(Math.abs(v), 256 / 563) * Math.sign(v);
const convertXyz65ToA98 = ({ x, y, z, alpha }) => {
  if (x === void 0) x = 0;
  if (y === void 0) y = 0;
  if (z === void 0) z = 0;
  let res = {
    mode: "a98",
    r: gamma$2(
      x * 2.0415879038107465 - y * 0.5650069742788597 - 0.3447313507783297 * z
    ),
    g: gamma$2(
      x * -0.9692436362808798 + y * 1.8759675015077206 + 0.0415550574071756 * z
    ),
    b: gamma$2(
      x * 0.0134442806320312 - y * 0.1183623922310184 + 1.0151749943912058 * z
    )
  };
  if (alpha !== void 0) {
    res.alpha = alpha;
  }
  return res;
};
const fn$3 = (c2 = 0) => {
  const abs2 = Math.abs(c2);
  if (abs2 <= 0.04045) {
    return c2 / 12.92;
  }
  return (Math.sign(c2) || 1) * Math.pow((abs2 + 0.055) / 1.055, 2.4);
};
const convertRgbToLrgb = ({ r, g, b, alpha }) => {
  let res = {
    mode: "lrgb",
    r: fn$3(r),
    g: fn$3(g),
    b: fn$3(b)
  };
  if (alpha !== void 0) res.alpha = alpha;
  return res;
};
const convertRgbToXyz65 = (rgb) => {
  let { r, g, b, alpha } = convertRgbToLrgb(rgb);
  let res = {
    mode: "xyz65",
    x: 0.4123907992659593 * r + 0.357584339383878 * g + 0.1804807884018343 * b,
    y: 0.2126390058715102 * r + 0.715168678767756 * g + 0.0721923153607337 * b,
    z: 0.0193308187155918 * r + 0.119194779794626 * g + 0.9505321522496607 * b
  };
  if (alpha !== void 0) {
    res.alpha = alpha;
  }
  return res;
};
const fn$2 = (c2 = 0) => {
  const abs2 = Math.abs(c2);
  if (abs2 > 31308e-7) {
    return (Math.sign(c2) || 1) * (1.055 * Math.pow(abs2, 1 / 2.4) - 0.055);
  }
  return c2 * 12.92;
};
const convertLrgbToRgb = ({ r, g, b, alpha }, mode = "rgb") => {
  let res = {
    mode,
    r: fn$2(r),
    g: fn$2(g),
    b: fn$2(b)
  };
  if (alpha !== void 0) res.alpha = alpha;
  return res;
};
const convertXyz65ToRgb = ({ x, y, z, alpha }) => {
  if (x === void 0) x = 0;
  if (y === void 0) y = 0;
  if (z === void 0) z = 0;
  let res = convertLrgbToRgb({
    r: x * 3.2409699419045226 - y * 1.537383177570094 - 0.4986107602930034 * z,
    g: x * -0.9692436362808796 + y * 1.8759675015077204 + 0.0415550574071756 * z,
    b: x * 0.0556300796969936 - y * 0.2039769588889765 + 1.0569715142428784 * z
  });
  if (alpha !== void 0) {
    res.alpha = alpha;
  }
  return res;
};
const definition$q = {
  ...definition$r,
  mode: "a98",
  parse: ["a98-rgb"],
  serialize: "a98-rgb",
  fromMode: {
    rgb: (color) => convertXyz65ToA98(convertRgbToXyz65(color)),
    xyz65: convertXyz65ToA98
  },
  toMode: {
    rgb: (color) => convertXyz65ToRgb(convertA98ToXyz65(color)),
    xyz65: convertA98ToXyz65
  }
};
const normalizeHue = (hue2) => (hue2 = hue2 % 360) < 0 ? hue2 + 360 : hue2;
const hue = (hues, fn2) => {
  return hues.map((hue2, idx, arr) => {
    if (hue2 === void 0) {
      return hue2;
    }
    let normalized = normalizeHue(hue2);
    if (idx === 0 || hues[idx - 1] === void 0) {
      return normalized;
    }
    return fn2(normalized - normalizeHue(arr[idx - 1]));
  }).reduce((acc, curr) => {
    if (!acc.length || curr === void 0 || acc[acc.length - 1] === void 0) {
      acc.push(curr);
      return acc;
    }
    acc.push(curr + acc[acc.length - 1]);
    return acc;
  }, []);
};
const fixupHueShorter = (arr) => hue(arr, (d) => Math.abs(d) <= 180 ? d : d - 360 * Math.sign(d));
const M = [-0.14861, 1.78277, -0.29227, -0.90649, 1.97294, 0];
const degToRad = Math.PI / 180;
const radToDeg = 180 / Math.PI;
let DE = M[3] * M[4];
let BE = M[1] * M[4];
let BCAD = M[1] * M[2] - M[0] * M[3];
const convertRgbToCubehelix = ({ r, g, b, alpha }) => {
  if (r === void 0) r = 0;
  if (g === void 0) g = 0;
  if (b === void 0) b = 0;
  let l = (BCAD * b + r * DE - g * BE) / (BCAD + DE - BE);
  let x = b - l;
  let y = (M[4] * (g - l) - M[2] * x) / M[3];
  let res = {
    mode: "cubehelix",
    l,
    s: l === 0 || l === 1 ? void 0 : Math.sqrt(x * x + y * y) / (M[4] * l * (1 - l))
  };
  if (res.s) res.h = Math.atan2(y, x) * radToDeg - 120;
  if (alpha !== void 0) res.alpha = alpha;
  return res;
};
const convertCubehelixToRgb = ({ h, s, l, alpha }) => {
  let res = { mode: "rgb" };
  h = (h === void 0 ? 0 : h + 120) * degToRad;
  if (l === void 0) l = 0;
  let amp = s === void 0 ? 0 : s * l * (1 - l);
  let cosh = Math.cos(h);
  let sinh = Math.sin(h);
  res.r = l + amp * (M[0] * cosh + M[1] * sinh);
  res.g = l + amp * (M[2] * cosh + M[3] * sinh);
  res.b = l + amp * (M[4] * cosh + M[5] * sinh);
  if (alpha !== void 0) res.alpha = alpha;
  return res;
};
const differenceHueSaturation = (std, smp) => {
  if (std.h === void 0 || smp.h === void 0 || !std.s || !smp.s) {
    return 0;
  }
  let std_h = normalizeHue(std.h);
  let smp_h = normalizeHue(smp.h);
  let dH = Math.sin((smp_h - std_h + 360) / 2 * Math.PI / 180);
  return 2 * Math.sqrt(std.s * smp.s) * dH;
};
const differenceHueNaive = (std, smp) => {
  if (std.h === void 0 || smp.h === void 0) {
    return 0;
  }
  let std_h = normalizeHue(std.h);
  let smp_h = normalizeHue(smp.h);
  if (Math.abs(smp_h - std_h) > 180) {
    return std_h - (smp_h - 360 * Math.sign(smp_h - std_h));
  }
  return smp_h - std_h;
};
const differenceHueChroma = (std, smp) => {
  if (std.h === void 0 || smp.h === void 0 || !std.c || !smp.c) {
    return 0;
  }
  let std_h = normalizeHue(std.h);
  let smp_h = normalizeHue(smp.h);
  let dH = Math.sin((smp_h - std_h + 360) / 2 * Math.PI / 180);
  return 2 * Math.sqrt(std.c * smp.c) * dH;
};
const averageAngle = (val) => {
  let sum = val.reduce(
    (sum2, val2) => {
      if (val2 !== void 0) {
        let rad = val2 * Math.PI / 180;
        sum2.sin += Math.sin(rad);
        sum2.cos += Math.cos(rad);
      }
      return sum2;
    },
    { sin: 0, cos: 0 }
  );
  let angle = Math.atan2(sum.sin, sum.cos) * 180 / Math.PI;
  return angle < 0 ? 360 + angle : angle;
};
const definition$p = {
  mode: "cubehelix",
  channels: ["h", "s", "l", "alpha"],
  parse: ["--cubehelix"],
  serialize: "--cubehelix",
  ranges: {
    h: [0, 360],
    s: [0, 4.614],
    l: [0, 1]
  },
  fromMode: {
    rgb: convertRgbToCubehelix
  },
  toMode: {
    rgb: convertCubehelixToRgb
  },
  interpolate: {
    h: {
      use: interpolatorLinear,
      fixup: fixupHueShorter
    },
    s: interpolatorLinear,
    l: interpolatorLinear,
    alpha: {
      use: interpolatorLinear,
      fixup: fixupAlpha
    }
  },
  difference: {
    h: differenceHueSaturation
  },
  average: {
    h: averageAngle
  }
};
const convertLabToLch = ({ l, a, b, alpha }, mode = "lch") => {
  if (a === void 0) a = 0;
  if (b === void 0) b = 0;
  let c2 = Math.sqrt(a * a + b * b);
  let res = { mode, l, c: c2 };
  if (c2) res.h = normalizeHue(Math.atan2(b, a) * 180 / Math.PI);
  if (alpha !== void 0) res.alpha = alpha;
  return res;
};
const convertLchToLab = ({ l, c: c2, h, alpha }, mode = "lab") => {
  if (h === void 0) h = 0;
  let res = {
    mode,
    l,
    a: c2 ? c2 * Math.cos(h / 180 * Math.PI) : 0,
    b: c2 ? c2 * Math.sin(h / 180 * Math.PI) : 0
  };
  if (alpha !== void 0) res.alpha = alpha;
  return res;
};
const k$1 = Math.pow(29, 3) / Math.pow(3, 3);
const e$1 = Math.pow(6, 3) / Math.pow(29, 3);
const D50 = {
  X: 0.3457 / 0.3585,
  Y: 1,
  Z: (1 - 0.3457 - 0.3585) / 0.3585
};
const D65 = {
  X: 0.3127 / 0.329,
  Y: 1,
  Z: (1 - 0.3127 - 0.329) / 0.329
};
let fn$1 = (v) => Math.pow(v, 3) > e$1 ? Math.pow(v, 3) : (116 * v - 16) / k$1;
const convertLab65ToXyz65 = ({ l, a, b, alpha }) => {
  if (l === void 0) l = 0;
  if (a === void 0) a = 0;
  if (b === void 0) b = 0;
  let fy = (l + 16) / 116;
  let fx = a / 500 + fy;
  let fz = fy - b / 200;
  let res = {
    mode: "xyz65",
    x: fn$1(fx) * D65.X,
    y: fn$1(fy) * D65.Y,
    z: fn$1(fz) * D65.Z
  };
  if (alpha !== void 0) {
    res.alpha = alpha;
  }
  return res;
};
const convertLab65ToRgb = (lab) => convertXyz65ToRgb(convertLab65ToXyz65(lab));
const f$1 = (value) => value > e$1 ? Math.cbrt(value) : (k$1 * value + 16) / 116;
const convertXyz65ToLab65 = ({ x, y, z, alpha }) => {
  if (x === void 0) x = 0;
  if (y === void 0) y = 0;
  if (z === void 0) z = 0;
  let f0 = f$1(x / D65.X);
  let f1 = f$1(y / D65.Y);
  let f2 = f$1(z / D65.Z);
  let res = {
    mode: "lab65",
    l: 116 * f1 - 16,
    a: 500 * (f0 - f1),
    b: 200 * (f1 - f2)
  };
  if (alpha !== void 0) {
    res.alpha = alpha;
  }
  return res;
};
const convertRgbToLab65 = (rgb) => {
  let res = convertXyz65ToLab65(convertRgbToXyz65(rgb));
  if (rgb.r === rgb.b && rgb.b === rgb.g) {
    res.a = res.b = 0;
  }
  return res;
};
const kE = 1;
const kCH = 1;
const θ = 26 / 180 * Math.PI;
const cosθ = Math.cos(θ);
const sinθ = Math.sin(θ);
const factor = 100 / Math.log(139 / 100);
const convertDlchToLab65 = ({ l, c: c2, h, alpha }) => {
  if (l === void 0) l = 0;
  if (c2 === void 0) c2 = 0;
  if (h === void 0) h = 0;
  let res = {
    mode: "lab65",
    l: (Math.exp(l * kE / factor) - 1) / 39e-4
  };
  let G = (Math.exp(0.0435 * c2 * kCH * kE) - 1) / 0.075;
  let e2 = G * Math.cos(h / 180 * Math.PI - θ);
  let f2 = G * Math.sin(h / 180 * Math.PI - θ);
  res.a = e2 * cosθ - f2 / 0.83 * sinθ;
  res.b = e2 * sinθ + f2 / 0.83 * cosθ;
  if (alpha !== void 0) res.alpha = alpha;
  return res;
};
const convertLab65ToDlch = ({ l, a, b, alpha }) => {
  if (l === void 0) l = 0;
  if (a === void 0) a = 0;
  if (b === void 0) b = 0;
  let e2 = a * cosθ + b * sinθ;
  let f2 = 0.83 * (b * cosθ - a * sinθ);
  let G = Math.sqrt(e2 * e2 + f2 * f2);
  let res = {
    mode: "dlch",
    l: factor / kE * Math.log(1 + 39e-4 * l),
    c: Math.log(1 + 0.075 * G) / (0.0435 * kCH * kE)
  };
  if (res.c) {
    res.h = normalizeHue((Math.atan2(f2, e2) + θ) / Math.PI * 180);
  }
  if (alpha !== void 0) res.alpha = alpha;
  return res;
};
const convertDlabToLab65 = (c2) => convertDlchToLab65(convertLabToLch(c2, "dlch"));
const convertLab65ToDlab = (c2) => convertLchToLab(convertLab65ToDlch(c2), "dlab");
const definition$o = {
  mode: "dlab",
  parse: ["--din99o-lab"],
  serialize: "--din99o-lab",
  toMode: {
    lab65: convertDlabToLab65,
    rgb: (c2) => convertLab65ToRgb(convertDlabToLab65(c2))
  },
  fromMode: {
    lab65: convertLab65ToDlab,
    rgb: (c2) => convertLab65ToDlab(convertRgbToLab65(c2))
  },
  channels: ["l", "a", "b", "alpha"],
  ranges: {
    l: [0, 100],
    a: [-40.09, 45.501],
    b: [-40.469, 44.344]
  },
  interpolate: {
    l: interpolatorLinear,
    a: interpolatorLinear,
    b: interpolatorLinear,
    alpha: {
      use: interpolatorLinear,
      fixup: fixupAlpha
    }
  }
};
const definition$n = {
  mode: "dlch",
  parse: ["--din99o-lch"],
  serialize: "--din99o-lch",
  toMode: {
    lab65: convertDlchToLab65,
    dlab: (c2) => convertLchToLab(c2, "dlab"),
    rgb: (c2) => convertLab65ToRgb(convertDlchToLab65(c2))
  },
  fromMode: {
    lab65: convertLab65ToDlch,
    dlab: (c2) => convertLabToLch(c2, "dlch"),
    rgb: (c2) => convertLab65ToDlch(convertRgbToLab65(c2))
  },
  channels: ["l", "c", "h", "alpha"],
  ranges: {
    l: [0, 100],
    c: [0, 51.484],
    h: [0, 360]
  },
  interpolate: {
    l: interpolatorLinear,
    c: interpolatorLinear,
    h: {
      use: interpolatorLinear,
      fixup: fixupHueShorter
    },
    alpha: {
      use: interpolatorLinear,
      fixup: fixupAlpha
    }
  },
  difference: {
    h: differenceHueChroma
  },
  average: {
    h: averageAngle
  }
};
function convertHsiToRgb({ h, s, i, alpha }) {
  h = normalizeHue(h !== void 0 ? h : 0);
  if (s === void 0) s = 0;
  if (i === void 0) i = 0;
  let f2 = Math.abs(h / 60 % 2 - 1);
  let res;
  switch (Math.floor(h / 60)) {
    case 0:
      res = {
        r: i * (1 + s * (3 / (2 - f2) - 1)),
        g: i * (1 + s * (3 * (1 - f2) / (2 - f2) - 1)),
        b: i * (1 - s)
      };
      break;
    case 1:
      res = {
        r: i * (1 + s * (3 * (1 - f2) / (2 - f2) - 1)),
        g: i * (1 + s * (3 / (2 - f2) - 1)),
        b: i * (1 - s)
      };
      break;
    case 2:
      res = {
        r: i * (1 - s),
        g: i * (1 + s * (3 / (2 - f2) - 1)),
        b: i * (1 + s * (3 * (1 - f2) / (2 - f2) - 1))
      };
      break;
    case 3:
      res = {
        r: i * (1 - s),
        g: i * (1 + s * (3 * (1 - f2) / (2 - f2) - 1)),
        b: i * (1 + s * (3 / (2 - f2) - 1))
      };
      break;
    case 4:
      res = {
        r: i * (1 + s * (3 * (1 - f2) / (2 - f2) - 1)),
        g: i * (1 - s),
        b: i * (1 + s * (3 / (2 - f2) - 1))
      };
      break;
    case 5:
      res = {
        r: i * (1 + s * (3 / (2 - f2) - 1)),
        g: i * (1 - s),
        b: i * (1 + s * (3 * (1 - f2) / (2 - f2) - 1))
      };
      break;
    default:
      res = { r: i * (1 - s), g: i * (1 - s), b: i * (1 - s) };
  }
  res.mode = "rgb";
  if (alpha !== void 0) res.alpha = alpha;
  return res;
}
function convertRgbToHsi({ r, g, b, alpha }) {
  if (r === void 0) r = 0;
  if (g === void 0) g = 0;
  if (b === void 0) b = 0;
  let M3 = Math.max(r, g, b), m = Math.min(r, g, b);
  let res = {
    mode: "hsi",
    s: r + g + b === 0 ? 0 : 1 - 3 * m / (r + g + b),
    i: (r + g + b) / 3
  };
  if (M3 - m !== 0)
    res.h = (M3 === r ? (g - b) / (M3 - m) + (g < b) * 6 : M3 === g ? (b - r) / (M3 - m) + 2 : (r - g) / (M3 - m) + 4) * 60;
  if (alpha !== void 0) res.alpha = alpha;
  return res;
}
const definition$m = {
  mode: "hsi",
  toMode: {
    rgb: convertHsiToRgb
  },
  parse: ["--hsi"],
  serialize: "--hsi",
  fromMode: {
    rgb: convertRgbToHsi
  },
  channels: ["h", "s", "i", "alpha"],
  ranges: {
    h: [0, 360]
  },
  gamut: "rgb",
  interpolate: {
    h: { use: interpolatorLinear, fixup: fixupHueShorter },
    s: interpolatorLinear,
    i: interpolatorLinear,
    alpha: { use: interpolatorLinear, fixup: fixupAlpha }
  },
  difference: {
    h: differenceHueSaturation
  },
  average: {
    h: averageAngle
  }
};
function convertHslToRgb({ h, s, l, alpha }) {
  h = normalizeHue(h !== void 0 ? h : 0);
  if (s === void 0) s = 0;
  if (l === void 0) l = 0;
  let m1 = l + s * (l < 0.5 ? l : 1 - l);
  let m2 = m1 - (m1 - l) * 2 * Math.abs(h / 60 % 2 - 1);
  let res;
  switch (Math.floor(h / 60)) {
    case 0:
      res = { r: m1, g: m2, b: 2 * l - m1 };
      break;
    case 1:
      res = { r: m2, g: m1, b: 2 * l - m1 };
      break;
    case 2:
      res = { r: 2 * l - m1, g: m1, b: m2 };
      break;
    case 3:
      res = { r: 2 * l - m1, g: m2, b: m1 };
      break;
    case 4:
      res = { r: m2, g: 2 * l - m1, b: m1 };
      break;
    case 5:
      res = { r: m1, g: 2 * l - m1, b: m2 };
      break;
    default:
      res = { r: 2 * l - m1, g: 2 * l - m1, b: 2 * l - m1 };
  }
  res.mode = "rgb";
  if (alpha !== void 0) res.alpha = alpha;
  return res;
}
function convertRgbToHsl({ r, g, b, alpha }) {
  if (r === void 0) r = 0;
  if (g === void 0) g = 0;
  if (b === void 0) b = 0;
  let M3 = Math.max(r, g, b), m = Math.min(r, g, b);
  let res = {
    mode: "hsl",
    s: M3 === m ? 0 : (M3 - m) / (1 - Math.abs(M3 + m - 1)),
    l: 0.5 * (M3 + m)
  };
  if (M3 - m !== 0)
    res.h = (M3 === r ? (g - b) / (M3 - m) + (g < b) * 6 : M3 === g ? (b - r) / (M3 - m) + 2 : (r - g) / (M3 - m) + 4) * 60;
  if (alpha !== void 0) res.alpha = alpha;
  return res;
}
const hueToDeg = (val, unit) => {
  switch (unit) {
    case "deg":
      return +val;
    case "rad":
      return val / Math.PI * 180;
    case "grad":
      return val / 10 * 9;
    case "turn":
      return val * 360;
  }
};
const hsl_old = new RegExp(
  `^hsla?\\(\\s*${hue$1}${c}${per}${c}${per}\\s*(?:,\\s*${num_per}\\s*)?\\)$`
);
const parseHslLegacy = (color) => {
  let match = color.match(hsl_old);
  if (!match) return;
  let res = { mode: "hsl" };
  if (match[3] !== void 0) {
    res.h = +match[3];
  } else if (match[1] !== void 0 && match[2] !== void 0) {
    res.h = hueToDeg(match[1], match[2]);
  }
  if (match[4] !== void 0) {
    res.s = Math.min(Math.max(0, match[4] / 100), 1);
  }
  if (match[5] !== void 0) {
    res.l = Math.min(Math.max(0, match[5] / 100), 1);
  }
  if (match[6] !== void 0) {
    res.alpha = Math.max(0, Math.min(1, match[6] / 100));
  } else if (match[7] !== void 0) {
    res.alpha = Math.max(0, Math.min(1, +match[7]));
  }
  return res;
};
function parseHsl(color, parsed) {
  if (!parsed || parsed[0] !== "hsl" && parsed[0] !== "hsla") {
    return void 0;
  }
  const res = { mode: "hsl" };
  const [, h, s, l, alpha] = parsed;
  if (h.type !== Tok.None) {
    if (h.type === Tok.Percentage) {
      return void 0;
    }
    res.h = h.value;
  }
  if (s.type !== Tok.None) {
    if (s.type === Tok.Hue) {
      return void 0;
    }
    res.s = s.value / 100;
  }
  if (l.type !== Tok.None) {
    if (l.type === Tok.Hue) {
      return void 0;
    }
    res.l = l.value / 100;
  }
  if (alpha.type !== Tok.None) {
    res.alpha = Math.min(
      1,
      Math.max(
        0,
        alpha.type === Tok.Number ? alpha.value : alpha.value / 100
      )
    );
  }
  return res;
}
const definition$l = {
  mode: "hsl",
  toMode: {
    rgb: convertHslToRgb
  },
  fromMode: {
    rgb: convertRgbToHsl
  },
  channels: ["h", "s", "l", "alpha"],
  ranges: {
    h: [0, 360]
  },
  gamut: "rgb",
  parse: [parseHsl, parseHslLegacy],
  serialize: (c2) => `hsl(${c2.h !== void 0 ? c2.h : "none"} ${c2.s !== void 0 ? c2.s * 100 + "%" : "none"} ${c2.l !== void 0 ? c2.l * 100 + "%" : "none"}${c2.alpha < 1 ? ` / ${c2.alpha}` : ""})`,
  interpolate: {
    h: { use: interpolatorLinear, fixup: fixupHueShorter },
    s: interpolatorLinear,
    l: interpolatorLinear,
    alpha: { use: interpolatorLinear, fixup: fixupAlpha }
  },
  difference: {
    h: differenceHueSaturation
  },
  average: {
    h: averageAngle
  }
};
function convertHsvToRgb({ h, s, v, alpha }) {
  h = normalizeHue(h !== void 0 ? h : 0);
  if (s === void 0) s = 0;
  if (v === void 0) v = 0;
  let f2 = Math.abs(h / 60 % 2 - 1);
  let res;
  switch (Math.floor(h / 60)) {
    case 0:
      res = { r: v, g: v * (1 - s * f2), b: v * (1 - s) };
      break;
    case 1:
      res = { r: v * (1 - s * f2), g: v, b: v * (1 - s) };
      break;
    case 2:
      res = { r: v * (1 - s), g: v, b: v * (1 - s * f2) };
      break;
    case 3:
      res = { r: v * (1 - s), g: v * (1 - s * f2), b: v };
      break;
    case 4:
      res = { r: v * (1 - s * f2), g: v * (1 - s), b: v };
      break;
    case 5:
      res = { r: v, g: v * (1 - s), b: v * (1 - s * f2) };
      break;
    default:
      res = { r: v * (1 - s), g: v * (1 - s), b: v * (1 - s) };
  }
  res.mode = "rgb";
  if (alpha !== void 0) res.alpha = alpha;
  return res;
}
function convertRgbToHsv({ r, g, b, alpha }) {
  if (r === void 0) r = 0;
  if (g === void 0) g = 0;
  if (b === void 0) b = 0;
  let M3 = Math.max(r, g, b), m = Math.min(r, g, b);
  let res = {
    mode: "hsv",
    s: M3 === 0 ? 0 : 1 - m / M3,
    v: M3
  };
  if (M3 - m !== 0)
    res.h = (M3 === r ? (g - b) / (M3 - m) + (g < b) * 6 : M3 === g ? (b - r) / (M3 - m) + 2 : (r - g) / (M3 - m) + 4) * 60;
  if (alpha !== void 0) res.alpha = alpha;
  return res;
}
const definition$k = {
  mode: "hsv",
  toMode: {
    rgb: convertHsvToRgb
  },
  parse: ["--hsv"],
  serialize: "--hsv",
  fromMode: {
    rgb: convertRgbToHsv
  },
  channels: ["h", "s", "v", "alpha"],
  ranges: {
    h: [0, 360]
  },
  gamut: "rgb",
  interpolate: {
    h: { use: interpolatorLinear, fixup: fixupHueShorter },
    s: interpolatorLinear,
    v: interpolatorLinear,
    alpha: { use: interpolatorLinear, fixup: fixupAlpha }
  },
  difference: {
    h: differenceHueSaturation
  },
  average: {
    h: averageAngle
  }
};
function convertHwbToRgb({ h, w, b, alpha }) {
  if (w === void 0) w = 0;
  if (b === void 0) b = 0;
  if (w + b > 1) {
    let s = w + b;
    w /= s;
    b /= s;
  }
  return convertHsvToRgb({
    h,
    s: b === 1 ? 1 : 1 - w / (1 - b),
    v: 1 - b,
    alpha
  });
}
function convertRgbToHwb(rgba) {
  let hsv = convertRgbToHsv(rgba);
  if (hsv === void 0) return void 0;
  let s = hsv.s !== void 0 ? hsv.s : 0;
  let v = hsv.v !== void 0 ? hsv.v : 0;
  let res = {
    mode: "hwb",
    w: (1 - s) * v,
    b: 1 - v
  };
  if (hsv.h !== void 0) res.h = hsv.h;
  if (hsv.alpha !== void 0) res.alpha = hsv.alpha;
  return res;
}
function ParseHwb(color, parsed) {
  if (!parsed || parsed[0] !== "hwb") {
    return void 0;
  }
  const res = { mode: "hwb" };
  const [, h, w, b, alpha] = parsed;
  if (h.type !== Tok.None) {
    if (h.type === Tok.Percentage) {
      return void 0;
    }
    res.h = h.value;
  }
  if (w.type !== Tok.None) {
    if (w.type === Tok.Hue) {
      return void 0;
    }
    res.w = w.value / 100;
  }
  if (b.type !== Tok.None) {
    if (b.type === Tok.Hue) {
      return void 0;
    }
    res.b = b.value / 100;
  }
  if (alpha.type !== Tok.None) {
    res.alpha = Math.min(
      1,
      Math.max(
        0,
        alpha.type === Tok.Number ? alpha.value : alpha.value / 100
      )
    );
  }
  return res;
}
const definition$j = {
  mode: "hwb",
  toMode: {
    rgb: convertHwbToRgb
  },
  fromMode: {
    rgb: convertRgbToHwb
  },
  channels: ["h", "w", "b", "alpha"],
  ranges: {
    h: [0, 360]
  },
  gamut: "rgb",
  parse: [ParseHwb],
  serialize: (c2) => `hwb(${c2.h !== void 0 ? c2.h : "none"} ${c2.w !== void 0 ? c2.w * 100 + "%" : "none"} ${c2.b !== void 0 ? c2.b * 100 + "%" : "none"}${c2.alpha < 1 ? ` / ${c2.alpha}` : ""})`,
  interpolate: {
    h: { use: interpolatorLinear, fixup: fixupHueShorter },
    w: interpolatorLinear,
    b: interpolatorLinear,
    alpha: { use: interpolatorLinear, fixup: fixupAlpha }
  },
  difference: {
    h: differenceHueNaive
  },
  average: {
    h: averageAngle
  }
};
const YW = 203;
const M1 = 0.1593017578125;
const M2 = 78.84375;
const C1 = 0.8359375;
const C2 = 18.8515625;
const C3 = 18.6875;
function transferPqDecode(v) {
  if (v < 0) return 0;
  const c2 = Math.pow(v, 1 / M2);
  return 1e4 * Math.pow(Math.max(0, c2 - C1) / (C2 - C3 * c2), 1 / M1);
}
function transferPqEncode(v) {
  if (v < 0) return 0;
  const c2 = Math.pow(v / 1e4, M1);
  return Math.pow((C1 + C2 * c2) / (1 + C3 * c2), M2);
}
const toRel = (c2) => Math.max(c2 / YW, 0);
const convertItpToXyz65 = ({ i, t, p: p2, alpha }) => {
  if (i === void 0) i = 0;
  if (t === void 0) t = 0;
  if (p2 === void 0) p2 = 0;
  const l = transferPqDecode(
    i + 0.008609037037932761 * t + 0.11102962500302593 * p2
  );
  const m = transferPqDecode(
    i - 0.00860903703793275 * t - 0.11102962500302599 * p2
  );
  const s = transferPqDecode(
    i + 0.5600313357106791 * t - 0.32062717498731885 * p2
  );
  const res = {
    mode: "xyz65",
    x: toRel(
      2.070152218389422 * l - 1.3263473389671556 * m + 0.2066510476294051 * s
    ),
    y: toRel(
      0.3647385209748074 * l + 0.680566024947227 * m - 0.0453045459220346 * s
    ),
    z: toRel(
      -0.049747207535812 * l - 0.0492609666966138 * m + 1.1880659249923042 * s
    )
  };
  if (alpha !== void 0) {
    res.alpha = alpha;
  }
  return res;
};
const toAbs = (c2 = 0) => Math.max(c2 * YW, 0);
const convertXyz65ToItp = ({ x, y, z, alpha }) => {
  const absX = toAbs(x);
  const absY = toAbs(y);
  const absZ = toAbs(z);
  const l = transferPqEncode(
    0.3592832590121217 * absX + 0.6976051147779502 * absY - 0.0358915932320289 * absZ
  );
  const m = transferPqEncode(
    -0.1920808463704995 * absX + 1.1004767970374323 * absY + 0.0753748658519118 * absZ
  );
  const s = transferPqEncode(
    0.0070797844607477 * absX + 0.0748396662186366 * absY + 0.8433265453898765 * absZ
  );
  const i = 0.5 * l + 0.5 * m;
  const t = 1.61376953125 * l - 3.323486328125 * m + 1.709716796875 * s;
  const p2 = 4.378173828125 * l - 4.24560546875 * m - 0.132568359375 * s;
  const res = { mode: "itp", i, t, p: p2 };
  if (alpha !== void 0) {
    res.alpha = alpha;
  }
  return res;
};
const definition$i = {
  mode: "itp",
  channels: ["i", "t", "p", "alpha"],
  parse: ["--ictcp"],
  serialize: "--ictcp",
  toMode: {
    xyz65: convertItpToXyz65,
    rgb: (color) => convertXyz65ToRgb(convertItpToXyz65(color))
  },
  fromMode: {
    xyz65: convertXyz65ToItp,
    rgb: (color) => convertXyz65ToItp(convertRgbToXyz65(color))
  },
  ranges: {
    i: [0, 0.581],
    t: [-0.369, 0.272],
    p: [-0.164, 0.331]
  },
  interpolate: {
    i: interpolatorLinear,
    t: interpolatorLinear,
    p: interpolatorLinear,
    alpha: { use: interpolatorLinear, fixup: fixupAlpha }
  }
};
const p$1 = 134.03437499999998;
const d0$1 = 16295499532821565e-27;
const jabPqEncode = (v) => {
  if (v < 0) return 0;
  let vn2 = Math.pow(v / 1e4, M1);
  return Math.pow((C1 + C2 * vn2) / (1 + C3 * vn2), p$1);
};
const abs = (v = 0) => Math.max(v * 203, 0);
const convertXyz65ToJab = ({ x, y, z, alpha }) => {
  x = abs(x);
  y = abs(y);
  z = abs(z);
  let xp = 1.15 * x - 0.15 * z;
  let yp = 0.66 * y + 0.34 * x;
  let l = jabPqEncode(0.41478972 * xp + 0.579999 * yp + 0.014648 * z);
  let m = jabPqEncode(-0.20151 * xp + 1.120649 * yp + 0.0531008 * z);
  let s = jabPqEncode(-0.0166008 * xp + 0.2648 * yp + 0.6684799 * z);
  let i = (l + m) / 2;
  let res = {
    mode: "jab",
    j: 0.44 * i / (1 - 0.56 * i) - d0$1,
    a: 3.524 * l - 4.066708 * m + 0.542708 * s,
    b: 0.199076 * l + 1.096799 * m - 1.295875 * s
  };
  if (alpha !== void 0) {
    res.alpha = alpha;
  }
  return res;
};
const p = 134.03437499999998;
const d0 = 16295499532821565e-27;
const jabPqDecode = (v) => {
  if (v < 0) return 0;
  let vp = Math.pow(v, 1 / p);
  return 1e4 * Math.pow((C1 - vp) / (C3 * vp - C2), 1 / M1);
};
const rel = (v) => v / 203;
const convertJabToXyz65 = ({ j, a, b, alpha }) => {
  if (j === void 0) j = 0;
  if (a === void 0) a = 0;
  if (b === void 0) b = 0;
  let i = (j + d0) / (0.44 + 0.56 * (j + d0));
  let l = jabPqDecode(i + 0.13860504 * a + 0.058047316 * b);
  let m = jabPqDecode(i - 0.13860504 * a - 0.058047316 * b);
  let s = jabPqDecode(i - 0.096019242 * a - 0.8118919 * b);
  let res = {
    mode: "xyz65",
    x: rel(
      1.661373024652174 * l - 0.914523081304348 * m + 0.23136208173913045 * s
    ),
    y: rel(
      -0.3250758611844533 * l + 1.571847026732543 * m - 0.21825383453227928 * s
    ),
    z: rel(-0.090982811 * l - 0.31272829 * m + 1.5227666 * s)
  };
  if (alpha !== void 0) {
    res.alpha = alpha;
  }
  return res;
};
const convertRgbToJab = (rgb) => {
  let res = convertXyz65ToJab(convertRgbToXyz65(rgb));
  if (rgb.r === rgb.b && rgb.b === rgb.g) {
    res.a = res.b = 0;
  }
  return res;
};
const convertJabToRgb = (color) => convertXyz65ToRgb(convertJabToXyz65(color));
const definition$h = {
  mode: "jab",
  channels: ["j", "a", "b", "alpha"],
  parse: ["--jzazbz"],
  serialize: "--jzazbz",
  fromMode: {
    rgb: convertRgbToJab,
    xyz65: convertXyz65ToJab
  },
  toMode: {
    rgb: convertJabToRgb,
    xyz65: convertJabToXyz65
  },
  ranges: {
    j: [0, 0.222],
    a: [-0.109, 0.129],
    b: [-0.185, 0.134]
  },
  interpolate: {
    j: interpolatorLinear,
    a: interpolatorLinear,
    b: interpolatorLinear,
    alpha: { use: interpolatorLinear, fixup: fixupAlpha }
  }
};
const convertJabToJch = ({ j, a, b, alpha }) => {
  if (a === void 0) a = 0;
  if (b === void 0) b = 0;
  let c2 = Math.sqrt(a * a + b * b);
  let res = {
    mode: "jch",
    j,
    c: c2
  };
  if (c2) {
    res.h = normalizeHue(Math.atan2(b, a) * 180 / Math.PI);
  }
  if (alpha !== void 0) {
    res.alpha = alpha;
  }
  return res;
};
const convertJchToJab = ({ j, c: c2, h, alpha }) => {
  if (h === void 0) h = 0;
  let res = {
    mode: "jab",
    j,
    a: c2 ? c2 * Math.cos(h / 180 * Math.PI) : 0,
    b: c2 ? c2 * Math.sin(h / 180 * Math.PI) : 0
  };
  if (alpha !== void 0) res.alpha = alpha;
  return res;
};
const definition$g = {
  mode: "jch",
  parse: ["--jzczhz"],
  serialize: "--jzczhz",
  toMode: {
    jab: convertJchToJab,
    rgb: (c2) => convertJabToRgb(convertJchToJab(c2))
  },
  fromMode: {
    rgb: (c2) => convertJabToJch(convertRgbToJab(c2)),
    jab: convertJabToJch
  },
  channels: ["j", "c", "h", "alpha"],
  ranges: {
    j: [0, 0.221],
    c: [0, 0.19],
    h: [0, 360]
  },
  interpolate: {
    h: { use: interpolatorLinear, fixup: fixupHueShorter },
    c: interpolatorLinear,
    j: interpolatorLinear,
    alpha: { use: interpolatorLinear, fixup: fixupAlpha }
  },
  difference: {
    h: differenceHueChroma
  },
  average: {
    h: averageAngle
  }
};
const k = Math.pow(29, 3) / Math.pow(3, 3);
const e = Math.pow(6, 3) / Math.pow(29, 3);
let fn = (v) => Math.pow(v, 3) > e ? Math.pow(v, 3) : (116 * v - 16) / k;
const convertLabToXyz50 = ({ l, a, b, alpha }) => {
  if (l === void 0) l = 0;
  if (a === void 0) a = 0;
  if (b === void 0) b = 0;
  let fy = (l + 16) / 116;
  let fx = a / 500 + fy;
  let fz = fy - b / 200;
  let res = {
    mode: "xyz50",
    x: fn(fx) * D50.X,
    y: fn(fy) * D50.Y,
    z: fn(fz) * D50.Z
  };
  if (alpha !== void 0) {
    res.alpha = alpha;
  }
  return res;
};
const convertXyz50ToRgb = ({ x, y, z, alpha }) => {
  if (x === void 0) x = 0;
  if (y === void 0) y = 0;
  if (z === void 0) z = 0;
  let res = convertLrgbToRgb({
    r: x * 3.1341359569958707 - y * 1.6173863321612538 - 0.4906619460083532 * z,
    g: x * -0.978795502912089 + y * 1.916254567259524 + 0.03344273116131949 * z,
    b: x * 0.07195537988411677 - y * 0.2289768264158322 + 1.405386058324125 * z
  });
  if (alpha !== void 0) {
    res.alpha = alpha;
  }
  return res;
};
const convertLabToRgb = (lab) => convertXyz50ToRgb(convertLabToXyz50(lab));
const convertRgbToXyz50 = (rgb) => {
  let { r, g, b, alpha } = convertRgbToLrgb(rgb);
  let res = {
    mode: "xyz50",
    x: 0.436065742824811 * r + 0.3851514688337912 * g + 0.14307845442264197 * b,
    y: 0.22249319175623702 * r + 0.7168870538238823 * g + 0.06061979053616537 * b,
    z: 0.013923904500943465 * r + 0.09708128566574634 * g + 0.7140993584005155 * b
  };
  if (alpha !== void 0) {
    res.alpha = alpha;
  }
  return res;
};
const f = (value) => value > e ? Math.cbrt(value) : (k * value + 16) / 116;
const convertXyz50ToLab = ({ x, y, z, alpha }) => {
  if (x === void 0) x = 0;
  if (y === void 0) y = 0;
  if (z === void 0) z = 0;
  let f0 = f(x / D50.X);
  let f1 = f(y / D50.Y);
  let f2 = f(z / D50.Z);
  let res = {
    mode: "lab",
    l: 116 * f1 - 16,
    a: 500 * (f0 - f1),
    b: 200 * (f1 - f2)
  };
  if (alpha !== void 0) {
    res.alpha = alpha;
  }
  return res;
};
const convertRgbToLab = (rgb) => {
  let res = convertXyz50ToLab(convertRgbToXyz50(rgb));
  if (rgb.r === rgb.b && rgb.b === rgb.g) {
    res.a = res.b = 0;
  }
  return res;
};
function parseLab(color, parsed) {
  if (!parsed || parsed[0] !== "lab") {
    return void 0;
  }
  const res = { mode: "lab" };
  const [, l, a, b, alpha] = parsed;
  if (l.type === Tok.Hue || a.type === Tok.Hue || b.type === Tok.Hue) {
    return void 0;
  }
  if (l.type !== Tok.None) {
    res.l = Math.min(Math.max(0, l.value), 100);
  }
  if (a.type !== Tok.None) {
    res.a = a.type === Tok.Number ? a.value : a.value * 125 / 100;
  }
  if (b.type !== Tok.None) {
    res.b = b.type === Tok.Number ? b.value : b.value * 125 / 100;
  }
  if (alpha.type !== Tok.None) {
    res.alpha = Math.min(
      1,
      Math.max(
        0,
        alpha.type === Tok.Number ? alpha.value : alpha.value / 100
      )
    );
  }
  return res;
}
const definition$f = {
  mode: "lab",
  toMode: {
    xyz50: convertLabToXyz50,
    rgb: convertLabToRgb
  },
  fromMode: {
    xyz50: convertXyz50ToLab,
    rgb: convertRgbToLab
  },
  channels: ["l", "a", "b", "alpha"],
  ranges: {
    l: [0, 100],
    a: [-125, 125],
    b: [-125, 125]
  },
  parse: [parseLab],
  serialize: (c2) => `lab(${c2.l !== void 0 ? c2.l : "none"} ${c2.a !== void 0 ? c2.a : "none"} ${c2.b !== void 0 ? c2.b : "none"}${c2.alpha < 1 ? ` / ${c2.alpha}` : ""})`,
  interpolate: {
    l: interpolatorLinear,
    a: interpolatorLinear,
    b: interpolatorLinear,
    alpha: { use: interpolatorLinear, fixup: fixupAlpha }
  }
};
const definition$e = {
  ...definition$f,
  mode: "lab65",
  parse: ["--lab-d65"],
  serialize: "--lab-d65",
  toMode: {
    xyz65: convertLab65ToXyz65,
    rgb: convertLab65ToRgb
  },
  fromMode: {
    xyz65: convertXyz65ToLab65,
    rgb: convertRgbToLab65
  },
  ranges: {
    l: [0, 100],
    a: [-125, 125],
    b: [-125, 125]
  }
};
function parseLch(color, parsed) {
  if (!parsed || parsed[0] !== "lch") {
    return void 0;
  }
  const res = { mode: "lch" };
  const [, l, c2, h, alpha] = parsed;
  if (l.type !== Tok.None) {
    if (l.type === Tok.Hue) {
      return void 0;
    }
    res.l = Math.min(Math.max(0, l.value), 100);
  }
  if (c2.type !== Tok.None) {
    res.c = Math.max(
      0,
      c2.type === Tok.Number ? c2.value : c2.value * 150 / 100
    );
  }
  if (h.type !== Tok.None) {
    if (h.type === Tok.Percentage) {
      return void 0;
    }
    res.h = h.value;
  }
  if (alpha.type !== Tok.None) {
    res.alpha = Math.min(
      1,
      Math.max(
        0,
        alpha.type === Tok.Number ? alpha.value : alpha.value / 100
      )
    );
  }
  return res;
}
const definition$d = {
  mode: "lch",
  toMode: {
    lab: convertLchToLab,
    rgb: (c2) => convertLabToRgb(convertLchToLab(c2))
  },
  fromMode: {
    rgb: (c2) => convertLabToLch(convertRgbToLab(c2)),
    lab: convertLabToLch
  },
  channels: ["l", "c", "h", "alpha"],
  ranges: {
    l: [0, 100],
    c: [0, 150],
    h: [0, 360]
  },
  parse: [parseLch],
  serialize: (c2) => `lch(${c2.l !== void 0 ? c2.l : "none"} ${c2.c !== void 0 ? c2.c : "none"} ${c2.h !== void 0 ? c2.h : "none"}${c2.alpha < 1 ? ` / ${c2.alpha}` : ""})`,
  interpolate: {
    h: { use: interpolatorLinear, fixup: fixupHueShorter },
    c: interpolatorLinear,
    l: interpolatorLinear,
    alpha: { use: interpolatorLinear, fixup: fixupAlpha }
  },
  difference: {
    h: differenceHueChroma
  },
  average: {
    h: averageAngle
  }
};
const definition$c = {
  ...definition$d,
  mode: "lch65",
  parse: ["--lch-d65"],
  serialize: "--lch-d65",
  toMode: {
    lab65: (c2) => convertLchToLab(c2, "lab65"),
    rgb: (c2) => convertLab65ToRgb(convertLchToLab(c2, "lab65"))
  },
  fromMode: {
    rgb: (c2) => convertLabToLch(convertRgbToLab65(c2), "lch65"),
    lab65: (c2) => convertLabToLch(c2, "lch65")
  },
  ranges: {
    l: [0, 100],
    c: [0, 150],
    h: [0, 360]
  }
};
const convertLuvToLchuv = ({ l, u, v, alpha }) => {
  if (u === void 0) u = 0;
  if (v === void 0) v = 0;
  let c2 = Math.sqrt(u * u + v * v);
  let res = {
    mode: "lchuv",
    l,
    c: c2
  };
  if (c2) {
    res.h = normalizeHue(Math.atan2(v, u) * 180 / Math.PI);
  }
  if (alpha !== void 0) {
    res.alpha = alpha;
  }
  return res;
};
const convertLchuvToLuv = ({ l, c: c2, h, alpha }) => {
  if (h === void 0) h = 0;
  let res = {
    mode: "luv",
    l,
    u: c2 ? c2 * Math.cos(h / 180 * Math.PI) : 0,
    v: c2 ? c2 * Math.sin(h / 180 * Math.PI) : 0
  };
  if (alpha !== void 0) {
    res.alpha = alpha;
  }
  return res;
};
const u_fn$1 = (x, y, z) => 4 * x / (x + 15 * y + 3 * z);
const v_fn$1 = (x, y, z) => 9 * y / (x + 15 * y + 3 * z);
const un$1 = u_fn$1(D50.X, D50.Y, D50.Z);
const vn$1 = v_fn$1(D50.X, D50.Y, D50.Z);
const l_fn = (value) => value <= e ? k * value : 116 * Math.cbrt(value) - 16;
const convertXyz50ToLuv = ({ x, y, z, alpha }) => {
  if (x === void 0) x = 0;
  if (y === void 0) y = 0;
  if (z === void 0) z = 0;
  let l = l_fn(y / D50.Y);
  let u = u_fn$1(x, y, z);
  let v = v_fn$1(x, y, z);
  if (!isFinite(u) || !isFinite(v)) {
    l = u = v = 0;
  } else {
    u = 13 * l * (u - un$1);
    v = 13 * l * (v - vn$1);
  }
  let res = {
    mode: "luv",
    l,
    u,
    v
  };
  if (alpha !== void 0) {
    res.alpha = alpha;
  }
  return res;
};
const u_fn = (x, y, z) => 4 * x / (x + 15 * y + 3 * z);
const v_fn = (x, y, z) => 9 * y / (x + 15 * y + 3 * z);
const un = u_fn(D50.X, D50.Y, D50.Z);
const vn = v_fn(D50.X, D50.Y, D50.Z);
const convertLuvToXyz50 = ({ l, u, v, alpha }) => {
  if (l === void 0) l = 0;
  if (l === 0) {
    return { mode: "xyz50", x: 0, y: 0, z: 0 };
  }
  if (u === void 0) u = 0;
  if (v === void 0) v = 0;
  let up = u / (13 * l) + un;
  let vp = v / (13 * l) + vn;
  let y = D50.Y * (l <= 8 ? l / k : Math.pow((l + 16) / 116, 3));
  let x = y * (9 * up) / (4 * vp);
  let z = y * (12 - 3 * up - 20 * vp) / (4 * vp);
  let res = { mode: "xyz50", x, y, z };
  if (alpha !== void 0) {
    res.alpha = alpha;
  }
  return res;
};
const convertRgbToLchuv = (rgb) => convertLuvToLchuv(convertXyz50ToLuv(convertRgbToXyz50(rgb)));
const convertLchuvToRgb = (lchuv) => convertXyz50ToRgb(convertLuvToXyz50(convertLchuvToLuv(lchuv)));
const definition$b = {
  mode: "lchuv",
  toMode: {
    luv: convertLchuvToLuv,
    rgb: convertLchuvToRgb
  },
  fromMode: {
    rgb: convertRgbToLchuv,
    luv: convertLuvToLchuv
  },
  channels: ["l", "c", "h", "alpha"],
  parse: ["--lchuv"],
  serialize: "--lchuv",
  ranges: {
    l: [0, 100],
    c: [0, 176.956],
    h: [0, 360]
  },
  interpolate: {
    h: { use: interpolatorLinear, fixup: fixupHueShorter },
    c: interpolatorLinear,
    l: interpolatorLinear,
    alpha: { use: interpolatorLinear, fixup: fixupAlpha }
  },
  difference: {
    h: differenceHueChroma
  },
  average: {
    h: averageAngle
  }
};
const definition$a = {
  ...definition$r,
  mode: "lrgb",
  toMode: {
    rgb: convertLrgbToRgb
  },
  fromMode: {
    rgb: convertRgbToLrgb
  },
  parse: ["srgb-linear"],
  serialize: "srgb-linear"
};
const definition$9 = {
  mode: "luv",
  toMode: {
    xyz50: convertLuvToXyz50,
    rgb: (luv) => convertXyz50ToRgb(convertLuvToXyz50(luv))
  },
  fromMode: {
    xyz50: convertXyz50ToLuv,
    rgb: (rgb) => convertXyz50ToLuv(convertRgbToXyz50(rgb))
  },
  channels: ["l", "u", "v", "alpha"],
  parse: ["--luv"],
  serialize: "--luv",
  ranges: {
    l: [0, 100],
    u: [-84.936, 175.042],
    v: [-125.882, 87.243]
  },
  interpolate: {
    l: interpolatorLinear,
    u: interpolatorLinear,
    v: interpolatorLinear,
    alpha: { use: interpolatorLinear, fixup: fixupAlpha }
  }
};
const convertLrgbToOklab = ({ r, g, b, alpha }) => {
  if (r === void 0) r = 0;
  if (g === void 0) g = 0;
  if (b === void 0) b = 0;
  let L = Math.cbrt(
    0.412221469470763 * r + 0.5363325372617348 * g + 0.0514459932675022 * b
  );
  let M3 = Math.cbrt(
    0.2119034958178252 * r + 0.6806995506452344 * g + 0.1073969535369406 * b
  );
  let S = Math.cbrt(
    0.0883024591900564 * r + 0.2817188391361215 * g + 0.6299787016738222 * b
  );
  let res = {
    mode: "oklab",
    l: 0.210454268309314 * L + 0.7936177747023054 * M3 - 0.0040720430116193 * S,
    a: 1.9779985324311684 * L - 2.42859224204858 * M3 + 0.450593709617411 * S,
    b: 0.0259040424655478 * L + 0.7827717124575296 * M3 - 0.8086757549230774 * S
  };
  if (alpha !== void 0) {
    res.alpha = alpha;
  }
  return res;
};
const convertRgbToOklab = (rgb) => {
  let res = convertLrgbToOklab(convertRgbToLrgb(rgb));
  if (rgb.r === rgb.b && rgb.b === rgb.g) {
    res.a = res.b = 0;
  }
  return res;
};
const convertOklabToLrgb = ({ l, a, b, alpha }) => {
  if (l === void 0) l = 0;
  if (a === void 0) a = 0;
  if (b === void 0) b = 0;
  let L = Math.pow(l + 0.3963377773761749 * a + 0.2158037573099136 * b, 3);
  let M3 = Math.pow(l - 0.1055613458156586 * a - 0.0638541728258133 * b, 3);
  let S = Math.pow(l - 0.0894841775298119 * a - 1.2914855480194092 * b, 3);
  let res = {
    mode: "lrgb",
    r: 4.076741636075957 * L - 3.3077115392580616 * M3 + 0.2309699031821044 * S,
    g: -1.2684379732850317 * L + 2.6097573492876887 * M3 - 0.3413193760026573 * S,
    b: -0.0041960761386756 * L - 0.7034186179359362 * M3 + 1.7076146940746117 * S
  };
  if (alpha !== void 0) {
    res.alpha = alpha;
  }
  return res;
};
const convertOklabToRgb = (c2) => convertLrgbToRgb(convertOklabToLrgb(c2));
function toe(x) {
  const k_1 = 0.206;
  const k_2 = 0.03;
  const k_3 = (1 + k_1) / (1 + k_2);
  return 0.5 * (k_3 * x - k_1 + Math.sqrt((k_3 * x - k_1) * (k_3 * x - k_1) + 4 * k_2 * k_3 * x));
}
function toe_inv(x) {
  const k_1 = 0.206;
  const k_2 = 0.03;
  const k_3 = (1 + k_1) / (1 + k_2);
  return (x * x + k_1 * x) / (k_3 * (x + k_2));
}
function compute_max_saturation(a, b) {
  let k0, k1, k2, k3, k4, wl, wm, ws;
  if (-1.88170328 * a - 0.80936493 * b > 1) {
    k0 = 1.19086277;
    k1 = 1.76576728;
    k2 = 0.59662641;
    k3 = 0.75515197;
    k4 = 0.56771245;
    wl = 4.0767416621;
    wm = -3.3077115913;
    ws = 0.2309699292;
  } else if (1.81444104 * a - 1.19445276 * b > 1) {
    k0 = 0.73956515;
    k1 = -0.45954404;
    k2 = 0.08285427;
    k3 = 0.1254107;
    k4 = 0.14503204;
    wl = -1.2684380046;
    wm = 2.6097574011;
    ws = -0.3413193965;
  } else {
    k0 = 1.35733652;
    k1 = -915799e-8;
    k2 = -1.1513021;
    k3 = -0.50559606;
    k4 = 692167e-8;
    wl = -0.0041960863;
    wm = -0.7034186147;
    ws = 1.707614701;
  }
  let S = k0 + k1 * a + k2 * b + k3 * a * a + k4 * a * b;
  let k_l = 0.3963377774 * a + 0.2158037573 * b;
  let k_m = -0.1055613458 * a - 0.0638541728 * b;
  let k_s = -0.0894841775 * a - 1.291485548 * b;
  {
    let l_ = 1 + S * k_l;
    let m_ = 1 + S * k_m;
    let s_ = 1 + S * k_s;
    let l = l_ * l_ * l_;
    let m = m_ * m_ * m_;
    let s = s_ * s_ * s_;
    let l_dS = 3 * k_l * l_ * l_;
    let m_dS = 3 * k_m * m_ * m_;
    let s_dS = 3 * k_s * s_ * s_;
    let l_dS2 = 6 * k_l * k_l * l_;
    let m_dS2 = 6 * k_m * k_m * m_;
    let s_dS2 = 6 * k_s * k_s * s_;
    let f2 = wl * l + wm * m + ws * s;
    let f1 = wl * l_dS + wm * m_dS + ws * s_dS;
    let f22 = wl * l_dS2 + wm * m_dS2 + ws * s_dS2;
    S = S - f2 * f1 / (f1 * f1 - 0.5 * f2 * f22);
  }
  return S;
}
function find_cusp(a, b) {
  let S_cusp = compute_max_saturation(a, b);
  let rgb = convertOklabToLrgb({ l: 1, a: S_cusp * a, b: S_cusp * b });
  let L_cusp = Math.cbrt(1 / Math.max(rgb.r, rgb.g, rgb.b));
  let C_cusp = L_cusp * S_cusp;
  return [L_cusp, C_cusp];
}
function find_gamut_intersection(a, b, L1, C12, L0, cusp = null) {
  if (!cusp) {
    cusp = find_cusp(a, b);
  }
  let t;
  if ((L1 - L0) * cusp[1] - (cusp[0] - L0) * C12 <= 0) {
    t = cusp[1] * L0 / (C12 * cusp[0] + cusp[1] * (L0 - L1));
  } else {
    t = cusp[1] * (L0 - 1) / (C12 * (cusp[0] - 1) + cusp[1] * (L0 - L1));
    {
      let dL = L1 - L0;
      let dC = C12;
      let k_l = 0.3963377774 * a + 0.2158037573 * b;
      let k_m = -0.1055613458 * a - 0.0638541728 * b;
      let k_s = -0.0894841775 * a - 1.291485548 * b;
      let l_dt = dL + dC * k_l;
      let m_dt = dL + dC * k_m;
      let s_dt = dL + dC * k_s;
      {
        let L = L0 * (1 - t) + t * L1;
        let C = t * C12;
        let l_ = L + C * k_l;
        let m_ = L + C * k_m;
        let s_ = L + C * k_s;
        let l = l_ * l_ * l_;
        let m = m_ * m_ * m_;
        let s = s_ * s_ * s_;
        let ldt = 3 * l_dt * l_ * l_;
        let mdt = 3 * m_dt * m_ * m_;
        let sdt = 3 * s_dt * s_ * s_;
        let ldt2 = 6 * l_dt * l_dt * l_;
        let mdt2 = 6 * m_dt * m_dt * m_;
        let sdt2 = 6 * s_dt * s_dt * s_;
        let r = 4.0767416621 * l - 3.3077115913 * m + 0.2309699292 * s - 1;
        let r1 = 4.0767416621 * ldt - 3.3077115913 * mdt + 0.2309699292 * sdt;
        let r2 = 4.0767416621 * ldt2 - 3.3077115913 * mdt2 + 0.2309699292 * sdt2;
        let u_r = r1 / (r1 * r1 - 0.5 * r * r2);
        let t_r = -r * u_r;
        let g = -1.2684380046 * l + 2.6097574011 * m - 0.3413193965 * s - 1;
        let g1 = -1.2684380046 * ldt + 2.6097574011 * mdt - 0.3413193965 * sdt;
        let g2 = -1.2684380046 * ldt2 + 2.6097574011 * mdt2 - 0.3413193965 * sdt2;
        let u_g = g1 / (g1 * g1 - 0.5 * g * g2);
        let t_g = -g * u_g;
        let b2 = -0.0041960863 * l - 0.7034186147 * m + 1.707614701 * s - 1;
        let b1 = -0.0041960863 * ldt - 0.7034186147 * mdt + 1.707614701 * sdt;
        let b22 = -0.0041960863 * ldt2 - 0.7034186147 * mdt2 + 1.707614701 * sdt2;
        let u_b = b1 / (b1 * b1 - 0.5 * b2 * b22);
        let t_b = -b2 * u_b;
        t_r = u_r >= 0 ? t_r : 1e6;
        t_g = u_g >= 0 ? t_g : 1e6;
        t_b = u_b >= 0 ? t_b : 1e6;
        t += Math.min(t_r, Math.min(t_g, t_b));
      }
    }
  }
  return t;
}
function get_ST_max(a_, b_, cusp = null) {
  if (!cusp) {
    cusp = find_cusp(a_, b_);
  }
  let L = cusp[0];
  let C = cusp[1];
  return [C / L, C / (1 - L)];
}
function get_Cs(L, a_, b_) {
  let cusp = find_cusp(a_, b_);
  let C_max = find_gamut_intersection(a_, b_, L, 1, L, cusp);
  let ST_max = get_ST_max(a_, b_, cusp);
  let S_mid = 0.11516993 + 1 / (7.4477897 + 4.1590124 * b_ + a_ * (-2.19557347 + 1.75198401 * b_ + a_ * (-2.13704948 - 10.02301043 * b_ + a_ * (-4.24894561 + 5.38770819 * b_ + 4.69891013 * a_))));
  let T_mid = 0.11239642 + 1 / (1.6132032 - 0.68124379 * b_ + a_ * (0.40370612 + 0.90148123 * b_ + a_ * (-0.27087943 + 0.6122399 * b_ + a_ * (299215e-8 - 0.45399568 * b_ - 0.14661872 * a_))));
  let k2 = C_max / Math.min(L * ST_max[0], (1 - L) * ST_max[1]);
  let C_a = L * S_mid;
  let C_b = (1 - L) * T_mid;
  let C_mid = 0.9 * k2 * Math.sqrt(
    Math.sqrt(
      1 / (1 / (C_a * C_a * C_a * C_a) + 1 / (C_b * C_b * C_b * C_b))
    )
  );
  C_a = L * 0.4;
  C_b = (1 - L) * 0.8;
  let C_0 = Math.sqrt(1 / (1 / (C_a * C_a) + 1 / (C_b * C_b)));
  return [C_0, C_mid, C_max];
}
function convertOklabToOkhsl(lab) {
  const l = lab.l !== void 0 ? lab.l : 0;
  const a = lab.a !== void 0 ? lab.a : 0;
  const b = lab.b !== void 0 ? lab.b : 0;
  const ret = { mode: "okhsl", l: toe(l) };
  if (lab.alpha !== void 0) {
    ret.alpha = lab.alpha;
  }
  let c2 = Math.sqrt(a * a + b * b);
  if (!c2) {
    ret.s = 0;
    return ret;
  }
  let [C_0, C_mid, C_max] = get_Cs(l, a / c2, b / c2);
  let s;
  if (c2 < C_mid) {
    let k_0 = 0;
    let k_1 = 0.8 * C_0;
    let k_2 = 1 - k_1 / C_mid;
    let t = (c2 - k_0) / (k_1 + k_2 * (c2 - k_0));
    s = t * 0.8;
  } else {
    let k_0 = C_mid;
    let k_1 = 0.2 * C_mid * C_mid * 1.25 * 1.25 / C_0;
    let k_2 = 1 - k_1 / (C_max - C_mid);
    let t = (c2 - k_0) / (k_1 + k_2 * (c2 - k_0));
    s = 0.8 + 0.2 * t;
  }
  if (s) {
    ret.s = s;
    ret.h = normalizeHue(Math.atan2(b, a) * 180 / Math.PI);
  }
  return ret;
}
function convertOkhslToOklab(hsl) {
  let h = hsl.h !== void 0 ? hsl.h : 0;
  let s = hsl.s !== void 0 ? hsl.s : 0;
  let l = hsl.l !== void 0 ? hsl.l : 0;
  const ret = { mode: "oklab", l: toe_inv(l) };
  if (hsl.alpha !== void 0) {
    ret.alpha = hsl.alpha;
  }
  if (!s || l === 1) {
    ret.a = ret.b = 0;
    return ret;
  }
  let a_ = Math.cos(h / 180 * Math.PI);
  let b_ = Math.sin(h / 180 * Math.PI);
  let [C_0, C_mid, C_max] = get_Cs(ret.l, a_, b_);
  let t, k_0, k_1, k_2;
  if (s < 0.8) {
    t = 1.25 * s;
    k_0 = 0;
    k_1 = 0.8 * C_0;
    k_2 = 1 - k_1 / C_mid;
  } else {
    t = 5 * (s - 0.8);
    k_0 = C_mid;
    k_1 = 0.2 * C_mid * C_mid * 1.25 * 1.25 / C_0;
    k_2 = 1 - k_1 / (C_max - C_mid);
  }
  let C = k_0 + t * k_1 / (1 - k_2 * t);
  ret.a = C * a_;
  ret.b = C * b_;
  return ret;
}
const modeOkhsl = {
  ...definition$l,
  mode: "okhsl",
  channels: ["h", "s", "l", "alpha"],
  parse: ["--okhsl"],
  serialize: "--okhsl",
  fromMode: {
    oklab: convertOklabToOkhsl,
    rgb: (c2) => convertOklabToOkhsl(convertRgbToOklab(c2))
  },
  toMode: {
    oklab: convertOkhslToOklab,
    rgb: (c2) => convertOklabToRgb(convertOkhslToOklab(c2))
  }
};
function convertOklabToOkhsv(lab) {
  let l = lab.l !== void 0 ? lab.l : 0;
  let a = lab.a !== void 0 ? lab.a : 0;
  let b = lab.b !== void 0 ? lab.b : 0;
  let c2 = Math.sqrt(a * a + b * b);
  let a_ = c2 ? a / c2 : 1;
  let b_ = c2 ? b / c2 : 1;
  let [S_max, T] = get_ST_max(a_, b_);
  let S_0 = 0.5;
  let k2 = 1 - S_0 / S_max;
  let t = T / (c2 + l * T);
  let L_v = t * l;
  let C_v = t * c2;
  let L_vt = toe_inv(L_v);
  let C_vt = C_v * L_vt / L_v;
  let rgb_scale = convertOklabToLrgb({ l: L_vt, a: a_ * C_vt, b: b_ * C_vt });
  let scale_L = Math.cbrt(
    1 / Math.max(rgb_scale.r, rgb_scale.g, rgb_scale.b, 0)
  );
  l = l / scale_L;
  c2 = c2 / scale_L * toe(l) / l;
  l = toe(l);
  const ret = {
    mode: "okhsv",
    s: c2 ? (S_0 + T) * C_v / (T * S_0 + T * k2 * C_v) : 0,
    v: l ? l / L_v : 0
  };
  if (ret.s) {
    ret.h = normalizeHue(Math.atan2(b, a) * 180 / Math.PI);
  }
  if (lab.alpha !== void 0) {
    ret.alpha = lab.alpha;
  }
  return ret;
}
function convertOkhsvToOklab(hsv) {
  const ret = { mode: "oklab" };
  if (hsv.alpha !== void 0) {
    ret.alpha = hsv.alpha;
  }
  const h = hsv.h !== void 0 ? hsv.h : 0;
  const s = hsv.s !== void 0 ? hsv.s : 0;
  const v = hsv.v !== void 0 ? hsv.v : 0;
  const a_ = Math.cos(h / 180 * Math.PI);
  const b_ = Math.sin(h / 180 * Math.PI);
  const [S_max, T] = get_ST_max(a_, b_);
  const S_0 = 0.5;
  const k2 = 1 - S_0 / S_max;
  const L_v = 1 - s * S_0 / (S_0 + T - T * k2 * s);
  const C_v = s * T * S_0 / (S_0 + T - T * k2 * s);
  const L_vt = toe_inv(L_v);
  const C_vt = C_v * L_vt / L_v;
  const rgb_scale = convertOklabToLrgb({
    l: L_vt,
    a: a_ * C_vt,
    b: b_ * C_vt
  });
  const scale_L = Math.cbrt(
    1 / Math.max(rgb_scale.r, rgb_scale.g, rgb_scale.b, 0)
  );
  const L_new = toe_inv(v * L_v);
  const C = C_v * L_new / L_v;
  ret.l = L_new * scale_L;
  ret.a = C * a_ * scale_L;
  ret.b = C * b_ * scale_L;
  return ret;
}
const modeOkhsv = {
  ...definition$k,
  mode: "okhsv",
  channels: ["h", "s", "v", "alpha"],
  parse: ["--okhsv"],
  serialize: "--okhsv",
  fromMode: {
    oklab: convertOklabToOkhsv,
    rgb: (c2) => convertOklabToOkhsv(convertRgbToOklab(c2))
  },
  toMode: {
    oklab: convertOkhsvToOklab,
    rgb: (c2) => convertOklabToRgb(convertOkhsvToOklab(c2))
  }
};
function parseOklab(color, parsed) {
  if (!parsed || parsed[0] !== "oklab") {
    return void 0;
  }
  const res = { mode: "oklab" };
  const [, l, a, b, alpha] = parsed;
  if (l.type === Tok.Hue || a.type === Tok.Hue || b.type === Tok.Hue) {
    return void 0;
  }
  if (l.type !== Tok.None) {
    res.l = Math.min(
      Math.max(0, l.type === Tok.Number ? l.value : l.value / 100),
      1
    );
  }
  if (a.type !== Tok.None) {
    res.a = a.type === Tok.Number ? a.value : a.value * 0.4 / 100;
  }
  if (b.type !== Tok.None) {
    res.b = b.type === Tok.Number ? b.value : b.value * 0.4 / 100;
  }
  if (alpha.type !== Tok.None) {
    res.alpha = Math.min(
      1,
      Math.max(
        0,
        alpha.type === Tok.Number ? alpha.value : alpha.value / 100
      )
    );
  }
  return res;
}
const definition$8 = {
  ...definition$f,
  mode: "oklab",
  toMode: {
    lrgb: convertOklabToLrgb,
    rgb: convertOklabToRgb
  },
  fromMode: {
    lrgb: convertLrgbToOklab,
    rgb: convertRgbToOklab
  },
  ranges: {
    l: [0, 1],
    a: [-0.4, 0.4],
    b: [-0.4, 0.4]
  },
  parse: [parseOklab],
  serialize: (c2) => `oklab(${c2.l !== void 0 ? c2.l : "none"} ${c2.a !== void 0 ? c2.a : "none"} ${c2.b !== void 0 ? c2.b : "none"}${c2.alpha < 1 ? ` / ${c2.alpha}` : ""})`
};
function parseOklch(color, parsed) {
  if (!parsed || parsed[0] !== "oklch") {
    return void 0;
  }
  const res = { mode: "oklch" };
  const [, l, c2, h, alpha] = parsed;
  if (l.type !== Tok.None) {
    if (l.type === Tok.Hue) {
      return void 0;
    }
    res.l = Math.min(
      Math.max(0, l.type === Tok.Number ? l.value : l.value / 100),
      1
    );
  }
  if (c2.type !== Tok.None) {
    res.c = Math.max(
      0,
      c2.type === Tok.Number ? c2.value : c2.value * 0.4 / 100
    );
  }
  if (h.type !== Tok.None) {
    if (h.type === Tok.Percentage) {
      return void 0;
    }
    res.h = h.value;
  }
  if (alpha.type !== Tok.None) {
    res.alpha = Math.min(
      1,
      Math.max(
        0,
        alpha.type === Tok.Number ? alpha.value : alpha.value / 100
      )
    );
  }
  return res;
}
const definition$7 = {
  ...definition$d,
  mode: "oklch",
  toMode: {
    oklab: (c2) => convertLchToLab(c2, "oklab"),
    rgb: (c2) => convertOklabToRgb(convertLchToLab(c2, "oklab"))
  },
  fromMode: {
    rgb: (c2) => convertLabToLch(convertRgbToOklab(c2), "oklch"),
    oklab: (c2) => convertLabToLch(c2, "oklch")
  },
  parse: [parseOklch],
  serialize: (c2) => `oklch(${c2.l !== void 0 ? c2.l : "none"} ${c2.c !== void 0 ? c2.c : "none"} ${c2.h !== void 0 ? c2.h : "none"}${c2.alpha < 1 ? ` / ${c2.alpha}` : ""})`,
  ranges: {
    l: [0, 1],
    c: [0, 0.4],
    h: [0, 360]
  }
};
const convertP3ToXyz65 = (rgb) => {
  let { r, g, b, alpha } = convertRgbToLrgb(rgb);
  let res = {
    mode: "xyz65",
    x: 0.486570948648216 * r + 0.265667693169093 * g + 0.1982172852343625 * b,
    y: 0.2289745640697487 * r + 0.6917385218365062 * g + 0.079286914093745 * b,
    z: 0 * r + 0.0451133818589026 * g + 1.043944368900976 * b
  };
  if (alpha !== void 0) {
    res.alpha = alpha;
  }
  return res;
};
const convertXyz65ToP3 = ({ x, y, z, alpha }) => {
  if (x === void 0) x = 0;
  if (y === void 0) y = 0;
  if (z === void 0) z = 0;
  let res = convertLrgbToRgb(
    {
      r: x * 2.4934969119414263 - y * 0.9313836179191242 - 0.402710784450717 * z,
      g: x * -0.8294889695615749 + y * 1.7626640603183465 + 0.0236246858419436 * z,
      b: x * 0.0358458302437845 - y * 0.0761723892680418 + 0.9568845240076871 * z
    },
    "p3"
  );
  if (alpha !== void 0) {
    res.alpha = alpha;
  }
  return res;
};
const definition$6 = {
  ...definition$r,
  mode: "p3",
  parse: ["display-p3"],
  serialize: "display-p3",
  fromMode: {
    rgb: (color) => convertXyz65ToP3(convertRgbToXyz65(color)),
    xyz65: convertXyz65ToP3
  },
  toMode: {
    rgb: (color) => convertXyz65ToRgb(convertP3ToXyz65(color)),
    xyz65: convertP3ToXyz65
  }
};
const gamma$1 = (v) => {
  let abs2 = Math.abs(v);
  if (abs2 >= 1 / 512) {
    return Math.sign(v) * Math.pow(abs2, 1 / 1.8);
  }
  return 16 * v;
};
const convertXyz50ToProphoto = ({ x, y, z, alpha }) => {
  if (x === void 0) x = 0;
  if (y === void 0) y = 0;
  if (z === void 0) z = 0;
  let res = {
    mode: "prophoto",
    r: gamma$1(
      x * 1.3457868816471585 - y * 0.2555720873797946 - 0.0511018649755453 * z
    ),
    g: gamma$1(
      x * -0.5446307051249019 + y * 1.5082477428451466 + 0.0205274474364214 * z
    ),
    b: gamma$1(x * 0 + y * 0 + 1.2119675456389452 * z)
  };
  if (alpha !== void 0) {
    res.alpha = alpha;
  }
  return res;
};
const linearize$1 = (v = 0) => {
  let abs2 = Math.abs(v);
  if (abs2 >= 16 / 512) {
    return Math.sign(v) * Math.pow(abs2, 1.8);
  }
  return v / 16;
};
const convertProphotoToXyz50 = (prophoto) => {
  let r = linearize$1(prophoto.r);
  let g = linearize$1(prophoto.g);
  let b = linearize$1(prophoto.b);
  let res = {
    mode: "xyz50",
    x: 0.7977666449006423 * r + 0.1351812974005331 * g + 0.0313477341283922 * b,
    y: 0.2880748288194013 * r + 0.7118352342418731 * g + 899369387256e-16 * b,
    z: 0 * r + 0 * g + 0.8251046025104602 * b
  };
  if (prophoto.alpha !== void 0) {
    res.alpha = prophoto.alpha;
  }
  return res;
};
const definition$5 = {
  ...definition$r,
  mode: "prophoto",
  parse: ["prophoto-rgb"],
  serialize: "prophoto-rgb",
  fromMode: {
    xyz50: convertXyz50ToProphoto,
    rgb: (color) => convertXyz50ToProphoto(convertRgbToXyz50(color))
  },
  toMode: {
    xyz50: convertProphotoToXyz50,
    rgb: (color) => convertXyz50ToRgb(convertProphotoToXyz50(color))
  }
};
const α$1 = 1.09929682680944;
const β$1 = 0.018053968510807;
const gamma = (v) => {
  const abs2 = Math.abs(v);
  if (abs2 > β$1) {
    return (Math.sign(v) || 1) * (α$1 * Math.pow(abs2, 0.45) - (α$1 - 1));
  }
  return 4.5 * v;
};
const convertXyz65ToRec2020 = ({ x, y, z, alpha }) => {
  if (x === void 0) x = 0;
  if (y === void 0) y = 0;
  if (z === void 0) z = 0;
  let res = {
    mode: "rec2020",
    r: gamma(
      x * 1.7166511879712683 - y * 0.3556707837763925 - 0.2533662813736599 * z
    ),
    g: gamma(
      x * -0.6666843518324893 + y * 1.6164812366349395 + 0.0157685458139111 * z
    ),
    b: gamma(
      x * 0.0176398574453108 - y * 0.0427706132578085 + 0.9421031212354739 * z
    )
  };
  if (alpha !== void 0) {
    res.alpha = alpha;
  }
  return res;
};
const α = 1.09929682680944;
const β = 0.018053968510807;
const linearize = (v = 0) => {
  let abs2 = Math.abs(v);
  if (abs2 < β * 4.5) {
    return v / 4.5;
  }
  return (Math.sign(v) || 1) * Math.pow((abs2 + α - 1) / α, 1 / 0.45);
};
const convertRec2020ToXyz65 = (rec2020) => {
  let r = linearize(rec2020.r);
  let g = linearize(rec2020.g);
  let b = linearize(rec2020.b);
  let res = {
    mode: "xyz65",
    x: 0.6369580483012911 * r + 0.1446169035862083 * g + 0.1688809751641721 * b,
    y: 0.262700212011267 * r + 0.6779980715188708 * g + 0.059301716469862 * b,
    z: 0 * r + 0.0280726930490874 * g + 1.0609850577107909 * b
  };
  if (rec2020.alpha !== void 0) {
    res.alpha = rec2020.alpha;
  }
  return res;
};
const definition$4 = {
  ...definition$r,
  mode: "rec2020",
  fromMode: {
    xyz65: convertXyz65ToRec2020,
    rgb: (color) => convertXyz65ToRec2020(convertRgbToXyz65(color))
  },
  toMode: {
    xyz65: convertRec2020ToXyz65,
    rgb: (color) => convertXyz65ToRgb(convertRec2020ToXyz65(color))
  },
  parse: ["rec2020"],
  serialize: "rec2020"
};
const bias = 0.0037930732552754493;
const bias_cbrt = Math.cbrt(bias);
const transfer$1 = (v) => Math.cbrt(v) - bias_cbrt;
const convertRgbToXyb = (color) => {
  const { r, g, b, alpha } = convertRgbToLrgb(color);
  const l = transfer$1(0.3 * r + 0.622 * g + 0.078 * b + bias);
  const m = transfer$1(0.23 * r + 0.692 * g + 0.078 * b + bias);
  const s = transfer$1(
    0.2434226892454782 * r + 0.2047674442449682 * g + 0.5518098665095535 * b + bias
  );
  const res = {
    mode: "xyb",
    x: (l - m) / 2,
    y: (l + m) / 2,
    /* Apply default chroma from luma (subtract Y from B) */
    b: s - (l + m) / 2
  };
  if (alpha !== void 0) res.alpha = alpha;
  return res;
};
const transfer = (v) => Math.pow(v + bias_cbrt, 3);
const convertXybToRgb = ({ x, y, b, alpha }) => {
  if (x === void 0) x = 0;
  if (y === void 0) y = 0;
  if (b === void 0) b = 0;
  const l = transfer(x + y) - bias;
  const m = transfer(y - x) - bias;
  const s = transfer(b + y) - bias;
  const res = convertLrgbToRgb({
    r: 11.031566904639861 * l - 9.866943908131562 * m - 0.16462299650829934 * s,
    g: -3.2541473810744237 * l + 4.418770377582723 * m - 0.16462299650829934 * s,
    b: -3.6588512867136815 * l + 2.7129230459360922 * m + 1.9459282407775895 * s
  });
  if (alpha !== void 0) res.alpha = alpha;
  return res;
};
const definition$3 = {
  mode: "xyb",
  channels: ["x", "y", "b", "alpha"],
  parse: ["--xyb"],
  serialize: "--xyb",
  toMode: {
    rgb: convertXybToRgb
  },
  fromMode: {
    rgb: convertRgbToXyb
  },
  ranges: {
    x: [-0.0154, 0.0281],
    y: [0, 0.8453],
    b: [-0.2778, 0.388]
  },
  interpolate: {
    x: interpolatorLinear,
    y: interpolatorLinear,
    b: interpolatorLinear,
    alpha: { use: interpolatorLinear, fixup: fixupAlpha }
  }
};
const definition$2 = {
  mode: "xyz50",
  parse: ["xyz-d50"],
  serialize: "xyz-d50",
  toMode: {
    rgb: convertXyz50ToRgb,
    lab: convertXyz50ToLab
  },
  fromMode: {
    rgb: convertRgbToXyz50,
    lab: convertLabToXyz50
  },
  channels: ["x", "y", "z", "alpha"],
  ranges: {
    x: [0, 0.964],
    y: [0, 0.999],
    z: [0, 0.825]
  },
  interpolate: {
    x: interpolatorLinear,
    y: interpolatorLinear,
    z: interpolatorLinear,
    alpha: { use: interpolatorLinear, fixup: fixupAlpha }
  }
};
const convertXyz65ToXyz50 = (xyz65) => {
  let { x, y, z, alpha } = xyz65;
  if (x === void 0) x = 0;
  if (y === void 0) y = 0;
  if (z === void 0) z = 0;
  let res = {
    mode: "xyz50",
    x: 1.0479298208405488 * x + 0.0229467933410191 * y - 0.0501922295431356 * z,
    y: 0.0296278156881593 * x + 0.990434484573249 * y - 0.0170738250293851 * z,
    z: -0.0092430581525912 * x + 0.0150551448965779 * y + 0.7518742899580008 * z
  };
  if (alpha !== void 0) {
    res.alpha = alpha;
  }
  return res;
};
const convertXyz50ToXyz65 = (xyz50) => {
  let { x, y, z, alpha } = xyz50;
  if (x === void 0) x = 0;
  if (y === void 0) y = 0;
  if (z === void 0) z = 0;
  let res = {
    mode: "xyz65",
    x: 0.9554734527042182 * x - 0.0230985368742614 * y + 0.0632593086610217 * z,
    y: -0.0283697069632081 * x + 1.0099954580058226 * y + 0.021041398966943 * z,
    z: 0.0123140016883199 * x - 0.0205076964334779 * y + 1.3303659366080753 * z
  };
  if (alpha !== void 0) {
    res.alpha = alpha;
  }
  return res;
};
const definition$1 = {
  mode: "xyz65",
  toMode: {
    rgb: convertXyz65ToRgb,
    xyz50: convertXyz65ToXyz50
  },
  fromMode: {
    rgb: convertRgbToXyz65,
    xyz50: convertXyz50ToXyz65
  },
  ranges: {
    x: [0, 0.95],
    y: [0, 1],
    z: [0, 1.088]
  },
  channels: ["x", "y", "z", "alpha"],
  parse: ["xyz", "xyz-d65"],
  serialize: "xyz-d65",
  interpolate: {
    x: interpolatorLinear,
    y: interpolatorLinear,
    z: interpolatorLinear,
    alpha: { use: interpolatorLinear, fixup: fixupAlpha }
  }
};
const convertRgbToYiq = ({ r, g, b, alpha }) => {
  if (r === void 0) r = 0;
  if (g === void 0) g = 0;
  if (b === void 0) b = 0;
  const res = {
    mode: "yiq",
    y: 0.29889531 * r + 0.58662247 * g + 0.11448223 * b,
    i: 0.59597799 * r - 0.2741761 * g - 0.32180189 * b,
    q: 0.21147017 * r - 0.52261711 * g + 0.31114694 * b
  };
  if (alpha !== void 0) res.alpha = alpha;
  return res;
};
const convertYiqToRgb = ({ y, i, q, alpha }) => {
  if (y === void 0) y = 0;
  if (i === void 0) i = 0;
  if (q === void 0) q = 0;
  const res = {
    mode: "rgb",
    r: y + 0.95608445 * i + 0.6208885 * q,
    g: y - 0.27137664 * i - 0.6486059 * q,
    b: y - 1.10561724 * i + 1.70250126 * q
  };
  if (alpha !== void 0) res.alpha = alpha;
  return res;
};
const definition = {
  mode: "yiq",
  toMode: {
    rgb: convertYiqToRgb
  },
  fromMode: {
    rgb: convertRgbToYiq
  },
  channels: ["y", "i", "q", "alpha"],
  parse: ["--yiq"],
  serialize: "--yiq",
  ranges: {
    i: [-0.595, 0.595],
    q: [-0.522, 0.522]
  },
  interpolate: {
    y: interpolatorLinear,
    i: interpolatorLinear,
    q: interpolatorLinear,
    alpha: { use: interpolatorLinear, fixup: fixupAlpha }
  }
};
useMode(definition$q);
useMode(definition$p);
useMode(definition$o);
useMode(definition$n);
useMode(definition$m);
useMode(definition$l);
useMode(definition$k);
useMode(definition$j);
useMode(definition$i);
useMode(definition$h);
useMode(definition$g);
useMode(definition$f);
useMode(definition$e);
useMode(definition$d);
useMode(definition$c);
useMode(definition$b);
useMode(definition$a);
useMode(definition$9);
useMode(modeOkhsl);
useMode(modeOkhsv);
useMode(definition$8);
useMode(definition$7);
useMode(definition$6);
useMode(definition$5);
useMode(definition$4);
useMode(definition$r);
useMode(definition$3);
useMode(definition$2);
useMode(definition$1);
useMode(definition);
var PeriodType;
(function(PeriodType2) {
  PeriodType2[PeriodType2["Custom"] = 1] = "Custom";
  PeriodType2[PeriodType2["Day"] = 10] = "Day";
  PeriodType2[PeriodType2["DayTime"] = 11] = "DayTime";
  PeriodType2[PeriodType2["TimeOnly"] = 15] = "TimeOnly";
  PeriodType2[PeriodType2["Week"] = 20] = "Week";
  PeriodType2[PeriodType2["WeekSun"] = 21] = "WeekSun";
  PeriodType2[PeriodType2["WeekMon"] = 22] = "WeekMon";
  PeriodType2[PeriodType2["WeekTue"] = 23] = "WeekTue";
  PeriodType2[PeriodType2["WeekWed"] = 24] = "WeekWed";
  PeriodType2[PeriodType2["WeekThu"] = 25] = "WeekThu";
  PeriodType2[PeriodType2["WeekFri"] = 26] = "WeekFri";
  PeriodType2[PeriodType2["WeekSat"] = 27] = "WeekSat";
  PeriodType2[PeriodType2["Month"] = 30] = "Month";
  PeriodType2[PeriodType2["MonthYear"] = 31] = "MonthYear";
  PeriodType2[PeriodType2["Quarter"] = 40] = "Quarter";
  PeriodType2[PeriodType2["CalendarYear"] = 50] = "CalendarYear";
  PeriodType2[PeriodType2["FiscalYearOctober"] = 60] = "FiscalYearOctober";
  PeriodType2[PeriodType2["BiWeek1"] = 70] = "BiWeek1";
  PeriodType2[PeriodType2["BiWeek1Sun"] = 71] = "BiWeek1Sun";
  PeriodType2[PeriodType2["BiWeek1Mon"] = 72] = "BiWeek1Mon";
  PeriodType2[PeriodType2["BiWeek1Tue"] = 73] = "BiWeek1Tue";
  PeriodType2[PeriodType2["BiWeek1Wed"] = 74] = "BiWeek1Wed";
  PeriodType2[PeriodType2["BiWeek1Thu"] = 75] = "BiWeek1Thu";
  PeriodType2[PeriodType2["BiWeek1Fri"] = 76] = "BiWeek1Fri";
  PeriodType2[PeriodType2["BiWeek1Sat"] = 77] = "BiWeek1Sat";
  PeriodType2[PeriodType2["BiWeek2"] = 80] = "BiWeek2";
  PeriodType2[PeriodType2["BiWeek2Sun"] = 81] = "BiWeek2Sun";
  PeriodType2[PeriodType2["BiWeek2Mon"] = 82] = "BiWeek2Mon";
  PeriodType2[PeriodType2["BiWeek2Tue"] = 83] = "BiWeek2Tue";
  PeriodType2[PeriodType2["BiWeek2Wed"] = 84] = "BiWeek2Wed";
  PeriodType2[PeriodType2["BiWeek2Thu"] = 85] = "BiWeek2Thu";
  PeriodType2[PeriodType2["BiWeek2Fri"] = 86] = "BiWeek2Fri";
  PeriodType2[PeriodType2["BiWeek2Sat"] = 87] = "BiWeek2Sat";
})(PeriodType || (PeriodType = {}));
var DayOfWeek;
(function(DayOfWeek2) {
  DayOfWeek2[DayOfWeek2["Sunday"] = 0] = "Sunday";
  DayOfWeek2[DayOfWeek2["Monday"] = 1] = "Monday";
  DayOfWeek2[DayOfWeek2["Tuesday"] = 2] = "Tuesday";
  DayOfWeek2[DayOfWeek2["Wednesday"] = 3] = "Wednesday";
  DayOfWeek2[DayOfWeek2["Thursday"] = 4] = "Thursday";
  DayOfWeek2[DayOfWeek2["Friday"] = 5] = "Friday";
  DayOfWeek2[DayOfWeek2["Saturday"] = 6] = "Saturday";
})(DayOfWeek || (DayOfWeek = {}));
var DateToken;
(function(DateToken2) {
  DateToken2["Year_numeric"] = "yyy";
  DateToken2["Year_2Digit"] = "yy";
  DateToken2["Month_long"] = "MMMM";
  DateToken2["Month_short"] = "MMM";
  DateToken2["Month_2Digit"] = "MM";
  DateToken2["Month_numeric"] = "M";
  DateToken2["Hour_numeric"] = "h";
  DateToken2["Hour_2Digit"] = "hh";
  DateToken2["Hour_wAMPM"] = "a";
  DateToken2["Hour_woAMPM"] = "aaaaaa";
  DateToken2["Minute_numeric"] = "m";
  DateToken2["Minute_2Digit"] = "mm";
  DateToken2["Second_numeric"] = "s";
  DateToken2["Second_2Digit"] = "ss";
  DateToken2["MiliSecond_3"] = "SSS";
  DateToken2["DayOfMonth_numeric"] = "d";
  DateToken2["DayOfMonth_2Digit"] = "dd";
  DateToken2["DayOfMonth_withOrdinal"] = "do";
  DateToken2["DayOfWeek_narrow"] = "eeeee";
  DateToken2["DayOfWeek_long"] = "eeee";
  DateToken2["DayOfWeek_short"] = "eee";
})(DateToken || (DateToken = {}));
function getWeekStartsOnFromIntl(locales) {
  if (!locales) {
    return DayOfWeek.Sunday;
  }
  const locale = new Intl.Locale(locales);
  const weekInfo = locale.weekInfo ?? locale.getWeekInfo?.();
  return (weekInfo?.firstDay ?? 0) % 7;
}
const defaultLocaleSettings = {
  locale: "en",
  dictionary: {
    Ok: "Ok",
    Cancel: "Cancel",
    Date: {
      Start: "Start",
      End: "End",
      Empty: "Empty",
      Day: "Day",
      DayTime: "Day Time",
      Time: "Time",
      Week: "Week",
      BiWeek: "Bi-Week",
      Month: "Month",
      Quarter: "Quarter",
      CalendarYear: "Calendar Year",
      FiscalYearOct: "Fiscal Year (Oct)",
      PeriodDay: {
        Current: "Today",
        Last: "Yesterday",
        LastX: "Last {0} days"
      },
      PeriodWeek: {
        Current: "This week",
        Last: "Last week",
        LastX: "Last {0} weeks"
      },
      PeriodBiWeek: {
        Current: "This bi-week",
        Last: "Last bi-week",
        LastX: "Last {0} bi-weeks"
      },
      PeriodMonth: {
        Current: "This month",
        Last: "Last month",
        LastX: "Last {0} months"
      },
      PeriodQuarter: {
        Current: "This quarter",
        Last: "Last quarter",
        LastX: "Last {0} quarters"
      },
      PeriodQuarterSameLastyear: "Same quarter last year",
      PeriodYear: {
        Current: "This year",
        Last: "Last year",
        LastX: "Last {0} years"
      },
      PeriodFiscalYear: {
        Current: "This fiscal year",
        Last: "Last fiscal year",
        LastX: "Last {0} fiscal years"
      }
    }
  },
  formats: {
    numbers: {
      defaults: {
        currency: "USD",
        fractionDigits: 2,
        currencyDisplay: "symbol"
      }
    },
    dates: {
      baseParsing: "MM/dd/yyyy",
      weekStartsOn: DayOfWeek.Sunday,
      ordinalSuffixes: {
        one: "st",
        two: "nd",
        few: "rd",
        other: "th"
      },
      presets: {
        day: {
          short: [DateToken.DayOfMonth_numeric, DateToken.Month_numeric],
          default: [DateToken.DayOfMonth_numeric, DateToken.Month_numeric, DateToken.Year_numeric],
          long: [DateToken.DayOfMonth_numeric, DateToken.Month_short, DateToken.Year_numeric]
        },
        dayTime: {
          short: [
            DateToken.DayOfMonth_numeric,
            DateToken.Month_numeric,
            DateToken.Year_numeric,
            DateToken.Hour_numeric,
            DateToken.Minute_numeric
          ],
          default: [
            DateToken.DayOfMonth_numeric,
            DateToken.Month_numeric,
            DateToken.Year_numeric,
            DateToken.Hour_2Digit,
            DateToken.Minute_2Digit
          ],
          long: [
            DateToken.DayOfMonth_numeric,
            DateToken.Month_numeric,
            DateToken.Year_numeric,
            DateToken.Hour_2Digit,
            DateToken.Minute_2Digit,
            DateToken.Second_2Digit
          ]
        },
        timeOnly: {
          short: [DateToken.Hour_numeric, DateToken.Minute_numeric],
          default: [DateToken.Hour_2Digit, DateToken.Minute_2Digit, DateToken.Second_2Digit],
          long: [
            DateToken.Hour_2Digit,
            DateToken.Minute_2Digit,
            DateToken.Second_2Digit,
            DateToken.MiliSecond_3
          ]
        },
        week: {
          short: [DateToken.DayOfMonth_numeric, DateToken.Month_numeric],
          default: [DateToken.DayOfMonth_numeric, DateToken.Month_numeric, DateToken.Year_numeric],
          long: [DateToken.DayOfMonth_numeric, DateToken.Month_numeric, DateToken.Year_numeric]
        },
        month: {
          short: DateToken.Month_short,
          default: DateToken.Month_short,
          long: DateToken.Month_long
        },
        monthsYear: {
          short: [DateToken.Month_short, DateToken.Year_2Digit],
          default: [DateToken.Month_long, DateToken.Year_numeric],
          long: [DateToken.Month_long, DateToken.Year_numeric]
        },
        year: {
          short: DateToken.Year_2Digit,
          default: DateToken.Year_numeric,
          long: DateToken.Year_numeric
        }
      }
    }
  }
};
function createLocaleSettings(localeSettings, base = defaultLocaleSettings) {
  if (localeSettings.formats?.dates?.ordinalSuffixes) {
    localeSettings.formats.dates.ordinalSuffixes = {
      one: "",
      two: "",
      few: "",
      other: "",
      zero: "",
      many: "",
      ...localeSettings.formats.dates.ordinalSuffixes
    };
  }
  if (localeSettings.formats?.dates?.weekStartsOn === void 0) {
    localeSettings = defaultsDeep(localeSettings, {
      formats: { dates: { weekStartsOn: getWeekStartsOnFromIntl(localeSettings.locale) } }
    });
  }
  return defaultsDeep(localeSettings, base);
}
createLocaleSettings({ locale: "en" });
({
  [PeriodType.Custom]: "CUSTOM",
  [PeriodType.Day]: "DAY",
  [PeriodType.DayTime]: "DAY-TIME",
  [PeriodType.TimeOnly]: "TIME",
  [PeriodType.WeekSun]: "WEEK-SUN",
  [PeriodType.WeekMon]: "WEEK-MON",
  [PeriodType.WeekTue]: "WEEK-TUE",
  [PeriodType.WeekWed]: "WEEK-WED",
  [PeriodType.WeekThu]: "WEEK-THU",
  [PeriodType.WeekFri]: "WEEK-FRI",
  [PeriodType.WeekSat]: "WEEK-SAT",
  [PeriodType.Week]: "WEEK",
  [PeriodType.Month]: "MTH",
  [PeriodType.MonthYear]: "MTH-CY",
  [PeriodType.Quarter]: "QTR",
  [PeriodType.CalendarYear]: "CY",
  [PeriodType.FiscalYearOctober]: "FY-OCT",
  [PeriodType.BiWeek1Sun]: "BIWEEK1-SUN",
  [PeriodType.BiWeek1Mon]: "BIWEEK1-MON",
  [PeriodType.BiWeek1Tue]: "BIWEEK1-TUE",
  [PeriodType.BiWeek1Wed]: "BIWEEK1-WED",
  [PeriodType.BiWeek1Thu]: "BIWEEK1-THU",
  [PeriodType.BiWeek1Fri]: "BIWEEK1-FRI",
  [PeriodType.BiWeek1Sat]: "BIWEEK1-SAT",
  [PeriodType.BiWeek1]: "BIWEEK1",
  [PeriodType.BiWeek2Sun]: "BIWEEK2-SUN",
  [PeriodType.BiWeek2Mon]: "BIWEEK2-MON",
  [PeriodType.BiWeek2Tue]: "BIWEEK2-TUE",
  [PeriodType.BiWeek2Wed]: "BIWEEK2-WED",
  [PeriodType.BiWeek2Thu]: "BIWEEK2-THU",
  [PeriodType.BiWeek2Fri]: "BIWEEK2-FRI",
  [PeriodType.BiWeek2Sat]: "BIWEEK2-SAT",
  [PeriodType.BiWeek2]: "BIWEEK2"
});
var DurationUnits;
(function(DurationUnits2) {
  DurationUnits2[DurationUnits2["Year"] = 0] = "Year";
  DurationUnits2[DurationUnits2["Day"] = 1] = "Day";
  DurationUnits2[DurationUnits2["Hour"] = 2] = "Hour";
  DurationUnits2[DurationUnits2["Minute"] = 3] = "Minute";
  DurationUnits2[DurationUnits2["Second"] = 4] = "Second";
  DurationUnits2[DurationUnits2["Millisecond"] = 5] = "Millisecond";
})(DurationUnits || (DurationUnits = {}));
[50, ...range$1(100, 1e3, 100)];
const twMerge = extendTailwindMerge({
  extend: {
    classGroups: {
      shadow: [
        "shadow-border-l",
        "shadow-border-r",
        "shadow-border-t",
        "shadow-border-b",
        "elevation-none",
        ...range$1(1, 25).map((x) => `elevation-${x}`)
      ]
    }
  }
});
const cls = (...inputs) => twMerge(clsx(...inputs));
const MEASUREMENT_ELEMENT_ID = "__text_measurement_id";
function _getStringWidth(str, style) {
  try {
    let textEl = document.getElementById(MEASUREMENT_ELEMENT_ID);
    if (!textEl) {
      const svg = document.createElementNS("http://www.w3.org/2000/svg", "svg");
      svg.style.width = "0";
      svg.style.height = "0";
      svg.style.position = "absolute";
      svg.style.top = "-100%";
      svg.style.left = "-100%";
      textEl = document.createElementNS("http://www.w3.org/2000/svg", "text");
      textEl.setAttribute("id", MEASUREMENT_ELEMENT_ID);
      svg.appendChild(textEl);
      document.body.appendChild(svg);
    }
    Object.assign(textEl.style, style);
    textEl.textContent = str;
    return textEl.getComputedTextLength();
  } catch (e2) {
    return null;
  }
}
const getStringWidth = memoize(_getStringWidth, (str, style) => `${str}_${JSON.stringify(style)}`);
function toTitleCase(str) {
  return str.replace(/^\w/, (d) => d.toUpperCase());
}
const DEFAULT_ELLIPSIS = "…";
function truncateText(text, { position = "end", ellipsis = DEFAULT_ELLIPSIS, maxWidth, style, maxChars }) {
  if (!text)
    return "";
  if (maxWidth === void 0 && maxChars === void 0)
    return text;
  let workingText = text;
  if (maxChars !== void 0 && text.length > maxChars) {
    if (position === "start") {
      workingText = ellipsis + text.slice(-maxChars);
    } else if (position === "middle") {
      const half = Math.floor(maxChars / 2);
      workingText = text.slice(0, half) + ellipsis + text.slice(-half);
    } else {
      workingText = text.slice(0, maxChars) + ellipsis;
    }
  }
  if (maxWidth !== void 0) {
    const fullWidth = getStringWidth(workingText, style);
    if (fullWidth === null || fullWidth <= maxWidth)
      return workingText;
    const ellipsisWidth = getStringWidth(ellipsis, style) ?? 0;
    let availableWidth = maxWidth - ellipsisWidth;
    if (position === "start") {
      let truncated = workingText.slice(ellipsis.length);
      let truncatedWidth = getStringWidth(truncated, style);
      while (truncatedWidth !== null && truncatedWidth > availableWidth && truncated.length > 0) {
        truncated = truncated.slice(1);
        truncatedWidth = getStringWidth(truncated, style);
      }
      return ellipsis + truncated;
    } else if (position === "middle") {
      const halfWidth = availableWidth / 2;
      let left = "";
      let right = "";
      let bestLeft = "";
      let bestRight = "";
      for (let i = 0, j = workingText.length - 1; i < workingText.length && j >= 0; i++, j--) {
        const leftTest = workingText.slice(0, i + 1);
        const rightTest = workingText.slice(j);
        const leftWidth = getStringWidth(leftTest, style);
        const rightWidth = getStringWidth(rightTest, style);
        if (leftWidth !== null && leftWidth <= halfWidth)
          left = leftTest;
        if (rightWidth !== null && rightWidth <= halfWidth)
          right = rightTest;
        const combinedWidth = getStringWidth(left + ellipsis + right, style);
        if (combinedWidth !== null && combinedWidth <= maxWidth) {
          bestLeft = left;
          bestRight = right;
        } else {
          break;
        }
      }
      return bestLeft + ellipsis + bestRight;
    } else {
      let truncated = workingText.slice(0, -ellipsis.length);
      let truncatedWidth = getStringWidth(truncated + ellipsis, style);
      while (truncatedWidth !== null && truncatedWidth > maxWidth && truncated.length > 0) {
        truncated = truncated.slice(0, -1);
        truncatedWidth = getStringWidth(truncated + ellipsis, style);
      }
      return truncated + ellipsis;
    }
  }
  return workingText;
}
const DEFAULT_FILL = "rgb(0, 0, 0)";
const CANVAS_STYLES_ELEMENT_ID = "__layerchart_canvas_styles_id";
function getComputedStyles(canvas, { styles, classes } = {}) {
  try {
    let svg = document.getElementById(CANVAS_STYLES_ELEMENT_ID);
    if (!svg) {
      svg = document.createElementNS("http://www.w3.org/2000/svg", "svg");
      svg.setAttribute("id", CANVAS_STYLES_ELEMENT_ID);
      svg.style.display = "none";
      canvas.after(svg);
    }
    svg = svg;
    svg.removeAttribute("style");
    svg.removeAttribute("class");
    if (styles) {
      Object.assign(svg.style, styles);
    }
    if (classes) {
      svg.setAttribute("class", cls(classes).split(" ").filter((s) => !s.startsWith("transition-")).join(" "));
    }
    const computedStyles = window.getComputedStyle(svg);
    return computedStyles;
  } catch (e2) {
    console.error("Unable to get computed styles", e2);
    return {};
  }
}
function render(ctx, render2, styleOptions = {}) {
  const computedStyles = getComputedStyles(ctx.canvas, styleOptions);
  const paintOrder = computedStyles?.paintOrder === "stroke" ? ["stroke", "fill"] : ["fill", "stroke"];
  if (computedStyles?.opacity) {
    ctx.globalAlpha = Number(computedStyles?.opacity);
  }
  ctx.font = `${computedStyles.fontWeight} ${computedStyles.fontSize} ${computedStyles.fontFamily}`;
  if (computedStyles.textAnchor === "middle") {
    ctx.textAlign = "center";
  } else if (computedStyles.textAnchor === "end") {
    ctx.textAlign = "right";
  } else {
    ctx.textAlign = computedStyles.textAlign;
  }
  if (computedStyles.strokeDasharray.includes(",")) {
    const dashArray = computedStyles.strokeDasharray.split(",").map((s) => Number(s.replace("px", "")));
    ctx.setLineDash(dashArray);
  }
  for (const attr of paintOrder) {
    if (attr === "fill") {
      const fill = styleOptions.styles?.fill && (styleOptions.styles?.fill instanceof CanvasGradient || styleOptions.styles?.fill instanceof CanvasPattern || !styleOptions.styles?.fill?.includes("var")) ? styleOptions.styles.fill : computedStyles?.fill;
      if (fill && !["none", DEFAULT_FILL].includes(fill)) {
        const currentGlobalAlpha = ctx.globalAlpha;
        const fillOpacity = Number(computedStyles?.fillOpacity);
        const opacity = Number(computedStyles?.opacity);
        ctx.globalAlpha = fillOpacity * opacity;
        ctx.fillStyle = fill;
        render2.fill(ctx);
        ctx.globalAlpha = currentGlobalAlpha;
      }
    } else if (attr === "stroke") {
      const stroke = styleOptions.styles?.stroke && (styleOptions.styles?.stroke instanceof CanvasGradient || !styleOptions.styles?.stroke?.includes("var")) ? styleOptions.styles?.stroke : computedStyles?.stroke;
      if (stroke && !["none"].includes(stroke)) {
        ctx.lineWidth = typeof computedStyles?.strokeWidth === "string" ? Number(computedStyles?.strokeWidth?.replace("px", "")) : computedStyles?.strokeWidth ?? 1;
        ctx.strokeStyle = stroke;
        render2.stroke(ctx);
      }
    }
  }
}
function renderPathData(ctx, pathData, styleOptions = {}) {
  const path = new Path2D(pathData ?? "");
  render(ctx, {
    fill: (ctx2) => ctx2.fill(path),
    stroke: (ctx2) => ctx2.stroke(path)
  }, styleOptions);
}
function renderCircle(ctx, coords, styleOptions = {}) {
  ctx.beginPath();
  ctx.arc(coords.cx, coords.cy, coords.r, 0, 2 * Math.PI);
  render(ctx, {
    fill: (ctx2) => {
      ctx2.fill();
    },
    stroke: (ctx2) => {
      ctx2.stroke();
    }
  }, styleOptions);
  ctx.closePath();
}
function _createLinearGradient(ctx, x0, y0, x1, y1, stops) {
  const gradient = ctx.createLinearGradient(x0, y0, x1, y1);
  for (const { offset, color } of stops) {
    gradient.addColorStop(offset, color);
  }
  return gradient;
}
memoize(_createLinearGradient, (ctx, x0, y0, x1, y1, stops) => {
  const key = JSON.stringify({ x0, y0, x1, y1, stops });
  return key;
});
function _createPattern(ctx, width, height, shapes, background) {
  const patternCanvas = document.createElement("canvas");
  const patternCtx = patternCanvas.getContext("2d");
  ctx.canvas.after(patternCanvas);
  patternCanvas.width = width;
  patternCanvas.height = height;
  if (background) {
    patternCtx.fillStyle = background;
    patternCtx.fillRect(0, 0, width, height);
  }
  for (const shape of shapes) {
    patternCtx.save();
    if (shape.type === "circle") {
      renderCircle(patternCtx, { cx: shape.cx, cy: shape.cy, r: shape.r }, { styles: { fill: shape.fill, opacity: shape.opacity } });
    } else if (shape.type === "line") {
      renderPathData(patternCtx, shape.path, {
        styles: { stroke: shape.stroke, strokeWidth: shape.strokeWidth, opacity: shape.opacity }
      });
    }
    patternCtx.restore();
  }
  const pattern = ctx.createPattern(patternCanvas, "repeat");
  ctx.canvas.parentElement?.removeChild(patternCanvas);
  return pattern;
}
memoize(_createPattern, (ctx, width, height, shapes, background) => {
  const key = JSON.stringify({ width, height, shapes, background });
  return key;
});
function ownKeys(object, enumerableOnly) {
  var keys = Object.keys(object);
  if (Object.getOwnPropertySymbols) {
    var symbols = Object.getOwnPropertySymbols(object);
    if (enumerableOnly) {
      symbols = symbols.filter(function(sym) {
        return Object.getOwnPropertyDescriptor(object, sym).enumerable;
      });
    }
    keys.push.apply(keys, symbols);
  }
  return keys;
}
function _objectSpread2(target) {
  for (var i = 1; i < arguments.length; i++) {
    var source = arguments[i] != null ? arguments[i] : {};
    if (i % 2) {
      ownKeys(Object(source), true).forEach(function(key) {
        _defineProperty(target, key, source[key]);
      });
    } else if (Object.getOwnPropertyDescriptors) {
      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));
    } else {
      ownKeys(Object(source)).forEach(function(key) {
        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));
      });
    }
  }
  return target;
}
function _typeof(obj) {
  "@babel/helpers - typeof";
  if (typeof Symbol === "function" && typeof Symbol.iterator === "symbol") {
    _typeof = function(obj2) {
      return typeof obj2;
    };
  } else {
    _typeof = function(obj2) {
      return obj2 && typeof Symbol === "function" && obj2.constructor === Symbol && obj2 !== Symbol.prototype ? "symbol" : typeof obj2;
    };
  }
  return _typeof(obj);
}
function _defineProperty(obj, key, value) {
  if (key in obj) {
    Object.defineProperty(obj, key, {
      value,
      enumerable: true,
      configurable: true,
      writable: true
    });
  } else {
    obj[key] = value;
  }
  return obj;
}
function _extends() {
  _extends = Object.assign || function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends.apply(this, arguments);
}
function _unsupportedIterableToArray(o, minLen) {
  if (!o) return;
  if (typeof o === "string") return _arrayLikeToArray(o, minLen);
  var n = Object.prototype.toString.call(o).slice(8, -1);
  if (n === "Object" && o.constructor) n = o.constructor.name;
  if (n === "Map" || n === "Set") return Array.from(o);
  if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);
}
function _arrayLikeToArray(arr, len) {
  if (len == null || len > arr.length) len = arr.length;
  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];
  return arr2;
}
function _createForOfIteratorHelper(o, allowArrayLike) {
  var it = typeof Symbol !== "undefined" && o[Symbol.iterator] || o["@@iterator"];
  if (!it) {
    if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike) {
      if (it) o = it;
      var i = 0;
      var F = function() {
      };
      return {
        s: F,
        n: function() {
          if (i >= o.length) return {
            done: true
          };
          return {
            done: false,
            value: o[i++]
          };
        },
        e: function(e2) {
          throw e2;
        },
        f: F
      };
    }
    throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
  }
  var normalCompletion = true, didErr = false, err;
  return {
    s: function() {
      it = it.call(o);
    },
    n: function() {
      var step = it.next();
      normalCompletion = step.done;
      return step;
    },
    e: function(e2) {
      didErr = true;
      err = e2;
    },
    f: function() {
      try {
        if (!normalCompletion && it.return != null) it.return();
      } finally {
        if (didErr) throw err;
      }
    }
  };
}
function decasteljau(points, t) {
  var left = [];
  var right = [];
  function decasteljauRecurse(points2, t2) {
    if (points2.length === 1) {
      left.push(points2[0]);
      right.push(points2[0]);
    } else {
      var newPoints = Array(points2.length - 1);
      for (var i = 0; i < newPoints.length; i++) {
        if (i === 0) {
          left.push(points2[0]);
        }
        if (i === newPoints.length - 1) {
          right.push(points2[i + 1]);
        }
        newPoints[i] = [(1 - t2) * points2[i][0] + t2 * points2[i + 1][0], (1 - t2) * points2[i][1] + t2 * points2[i + 1][1]];
      }
      decasteljauRecurse(newPoints, t2);
    }
  }
  if (points.length) {
    decasteljauRecurse(points, t);
  }
  return {
    left,
    right: right.reverse()
  };
}
function pointsToCommand(points) {
  var command = {};
  if (points.length === 4) {
    command.x2 = points[2][0];
    command.y2 = points[2][1];
  }
  if (points.length >= 3) {
    command.x1 = points[1][0];
    command.y1 = points[1][1];
  }
  command.x = points[points.length - 1][0];
  command.y = points[points.length - 1][1];
  if (points.length === 4) {
    command.type = "C";
  } else if (points.length === 3) {
    command.type = "Q";
  } else {
    command.type = "L";
  }
  return command;
}
function splitCurveAsPoints(points, segmentCount) {
  segmentCount = segmentCount || 2;
  var segments = [];
  var remainingCurve = points;
  var tIncrement = 1 / segmentCount;
  for (var i = 0; i < segmentCount - 1; i++) {
    var tRelative = tIncrement / (1 - tIncrement * i);
    var split = decasteljau(remainingCurve, tRelative);
    segments.push(split.left);
    remainingCurve = split.right;
  }
  segments.push(remainingCurve);
  return segments;
}
function splitCurve(commandStart, commandEnd, segmentCount) {
  var points = [[commandStart.x, commandStart.y]];
  if (commandEnd.x1 != null) {
    points.push([commandEnd.x1, commandEnd.y1]);
  }
  if (commandEnd.x2 != null) {
    points.push([commandEnd.x2, commandEnd.y2]);
  }
  points.push([commandEnd.x, commandEnd.y]);
  return splitCurveAsPoints(points, segmentCount).map(pointsToCommand);
}
var commandTokenRegex = /[MLCSTQAHVZmlcstqahv]|-?[\d.e+-]+/g;
var typeMap = {
  M: ["x", "y"],
  L: ["x", "y"],
  H: ["x"],
  V: ["y"],
  C: ["x1", "y1", "x2", "y2", "x", "y"],
  S: ["x2", "y2", "x", "y"],
  Q: ["x1", "y1", "x", "y"],
  T: ["x", "y"],
  A: ["rx", "ry", "xAxisRotation", "largeArcFlag", "sweepFlag", "x", "y"],
  Z: []
};
Object.keys(typeMap).forEach(function(key) {
  typeMap[key.toLowerCase()] = typeMap[key];
});
function arrayOfLength(length, value) {
  var array = Array(length);
  for (var i = 0; i < length; i++) {
    array[i] = value;
  }
  return array;
}
function commandToString(command) {
  return "".concat(command.type).concat(typeMap[command.type].map(function(p2) {
    return command[p2];
  }).join(","));
}
function convertToSameType(aCommand, bCommand) {
  var conversionMap = {
    x1: "x",
    y1: "y",
    x2: "x",
    y2: "y"
  };
  var readFromBKeys = ["xAxisRotation", "largeArcFlag", "sweepFlag"];
  if (aCommand.type !== bCommand.type && bCommand.type.toUpperCase() !== "M") {
    var aConverted = {};
    Object.keys(bCommand).forEach(function(bKey) {
      var bValue = bCommand[bKey];
      var aValue = aCommand[bKey];
      if (aValue === void 0) {
        if (readFromBKeys.includes(bKey)) {
          aValue = bValue;
        } else {
          if (aValue === void 0 && conversionMap[bKey]) {
            aValue = aCommand[conversionMap[bKey]];
          }
          if (aValue === void 0) {
            aValue = 0;
          }
        }
      }
      aConverted[bKey] = aValue;
    });
    aConverted.type = bCommand.type;
    aCommand = aConverted;
  }
  return aCommand;
}
function splitSegment(commandStart, commandEnd, segmentCount) {
  var segments = [];
  if (commandEnd.type === "L" || commandEnd.type === "Q" || commandEnd.type === "C") {
    segments = segments.concat(splitCurve(commandStart, commandEnd, segmentCount));
  } else {
    var copyCommand = _extends({}, commandStart);
    if (copyCommand.type === "M") {
      copyCommand.type = "L";
    }
    segments = segments.concat(arrayOfLength(segmentCount - 1).map(function() {
      return copyCommand;
    }));
    segments.push(commandEnd);
  }
  return segments;
}
function extend(commandsToExtend, referenceCommands, excludeSegment) {
  var numSegmentsToExtend = commandsToExtend.length - 1;
  var numReferenceSegments = referenceCommands.length - 1;
  var segmentRatio = numSegmentsToExtend / numReferenceSegments;
  var countPointsPerSegment = arrayOfLength(numReferenceSegments).reduce(function(accum, d, i) {
    var insertIndex = Math.floor(segmentRatio * i);
    if (excludeSegment && insertIndex < commandsToExtend.length - 1 && excludeSegment(commandsToExtend[insertIndex], commandsToExtend[insertIndex + 1])) {
      var addToPriorSegment = segmentRatio * i % 1 < 0.5;
      if (accum[insertIndex]) {
        if (addToPriorSegment) {
          if (insertIndex > 0) {
            insertIndex -= 1;
          } else if (insertIndex < commandsToExtend.length - 1) {
            insertIndex += 1;
          }
        } else if (insertIndex < commandsToExtend.length - 1) {
          insertIndex += 1;
        } else if (insertIndex > 0) {
          insertIndex -= 1;
        }
      }
    }
    accum[insertIndex] = (accum[insertIndex] || 0) + 1;
    return accum;
  }, []);
  var extended = countPointsPerSegment.reduce(function(extended2, segmentCount, i) {
    if (i === commandsToExtend.length - 1) {
      var lastCommandCopies = arrayOfLength(segmentCount, _extends({}, commandsToExtend[commandsToExtend.length - 1]));
      if (lastCommandCopies[0].type === "M") {
        lastCommandCopies.forEach(function(d) {
          d.type = "L";
        });
      }
      return extended2.concat(lastCommandCopies);
    }
    return extended2.concat(splitSegment(commandsToExtend[i], commandsToExtend[i + 1], segmentCount));
  }, []);
  extended.unshift(commandsToExtend[0]);
  return extended;
}
function pathCommandsFromString(d) {
  var tokens = (d || "").match(commandTokenRegex) || [];
  var commands = [];
  var commandArgs;
  var command;
  for (var i = 0; i < tokens.length; ++i) {
    commandArgs = typeMap[tokens[i]];
    if (commandArgs) {
      command = {
        type: tokens[i]
      };
      for (var a = 0; a < commandArgs.length; ++a) {
        command[commandArgs[a]] = +tokens[i + a + 1];
      }
      i += commandArgs.length;
      commands.push(command);
    }
  }
  return commands;
}
function interpolatePathCommands(aCommandsInput, bCommandsInput, interpolateOptions) {
  var aCommands = aCommandsInput == null ? [] : aCommandsInput.slice();
  var bCommands = bCommandsInput == null ? [] : bCommandsInput.slice();
  var _ref = _typeof(interpolateOptions) === "object" ? interpolateOptions : {
    excludeSegment: interpolateOptions,
    snapEndsToInput: true
  }, excludeSegment = _ref.excludeSegment, snapEndsToInput = _ref.snapEndsToInput;
  if (!aCommands.length && !bCommands.length) {
    return function nullInterpolator() {
      return [];
    };
  }
  var addZ = (aCommands.length === 0 || aCommands[aCommands.length - 1].type === "Z") && (bCommands.length === 0 || bCommands[bCommands.length - 1].type === "Z");
  if (aCommands.length > 0 && aCommands[aCommands.length - 1].type === "Z") {
    aCommands.pop();
  }
  if (bCommands.length > 0 && bCommands[bCommands.length - 1].type === "Z") {
    bCommands.pop();
  }
  if (!aCommands.length) {
    aCommands.push(bCommands[0]);
  } else if (!bCommands.length) {
    bCommands.push(aCommands[0]);
  }
  var numPointsToExtend = Math.abs(bCommands.length - aCommands.length);
  if (numPointsToExtend !== 0) {
    if (bCommands.length > aCommands.length) {
      aCommands = extend(aCommands, bCommands, excludeSegment);
    } else if (bCommands.length < aCommands.length) {
      bCommands = extend(bCommands, aCommands, excludeSegment);
    }
  }
  aCommands = aCommands.map(function(aCommand, i) {
    return convertToSameType(aCommand, bCommands[i]);
  });
  var interpolatedCommands = aCommands.map(function(aCommand) {
    return _objectSpread2({}, aCommand);
  });
  if (addZ) {
    interpolatedCommands.push({
      type: "Z"
    });
    aCommands.push({
      type: "Z"
    });
  }
  return function pathCommandInterpolator(t) {
    if (t === 1 && snapEndsToInput) {
      return bCommandsInput == null ? [] : bCommandsInput;
    }
    if (t === 0) {
      return aCommands;
    }
    for (var i = 0; i < interpolatedCommands.length; ++i) {
      var aCommand = aCommands[i];
      var bCommand = bCommands[i];
      var interpolatedCommand = interpolatedCommands[i];
      var _iterator = _createForOfIteratorHelper(typeMap[interpolatedCommand.type]), _step;
      try {
        for (_iterator.s(); !(_step = _iterator.n()).done; ) {
          var arg = _step.value;
          interpolatedCommand[arg] = (1 - t) * aCommand[arg] + t * bCommand[arg];
          if (arg === "largeArcFlag" || arg === "sweepFlag") {
            interpolatedCommand[arg] = Math.round(interpolatedCommand[arg]);
          }
        }
      } catch (err) {
        _iterator.e(err);
      } finally {
        _iterator.f();
      }
    }
    return interpolatedCommands;
  };
}
function interpolatePath(a, b, interpolateOptions) {
  var aCommands = pathCommandsFromString(a);
  var bCommands = pathCommandsFromString(b);
  var _ref2 = _typeof(interpolateOptions) === "object" ? interpolateOptions : {
    excludeSegment: interpolateOptions,
    snapEndsToInput: true
  }, excludeSegment = _ref2.excludeSegment, snapEndsToInput = _ref2.snapEndsToInput;
  if (!aCommands.length && !bCommands.length) {
    return function nullInterpolator() {
      return "";
    };
  }
  var commandInterpolator = interpolatePathCommands(aCommands, bCommands, {
    excludeSegment,
    snapEndsToInput
  });
  return function pathStringInterpolator(t) {
    if (t === 1 && snapEndsToInput) {
      return b == null ? "" : b;
    }
    var interpolatedCommands = commandInterpolator(t);
    var interpolatedString = "";
    var _iterator2 = _createForOfIteratorHelper(interpolatedCommands), _step2;
    try {
      for (_iterator2.s(); !(_step2 = _iterator2.n()).done; ) {
        var interpolatedCommand = _step2.value;
        interpolatedString += commandToString(interpolatedCommand);
      }
    } catch (err) {
      _iterator2.e(err);
    } finally {
      _iterator2.f();
    }
    return interpolatedString;
  };
}
var graph;
var hasRequiredGraph;
function requireGraph() {
  if (hasRequiredGraph) return graph;
  hasRequiredGraph = 1;
  var DEFAULT_EDGE_NAME = "\0";
  var GRAPH_NODE = "\0";
  var EDGE_KEY_DELIM = "";
  class Graph {
    _isDirected = true;
    _isMultigraph = false;
    _isCompound = false;
    // Label for the graph itself
    _label;
    // Defaults to be set when creating a new node
    _defaultNodeLabelFn = () => void 0;
    // Defaults to be set when creating a new edge
    _defaultEdgeLabelFn = () => void 0;
    // v -> label
    _nodes = {};
    // v -> edgeObj
    _in = {};
    // u -> v -> Number
    _preds = {};
    // v -> edgeObj
    _out = {};
    // v -> w -> Number
    _sucs = {};
    // e -> edgeObj
    _edgeObjs = {};
    // e -> label
    _edgeLabels = {};
    /* Number of nodes in the graph. Should only be changed by the implementation. */
    _nodeCount = 0;
    /* Number of edges in the graph. Should only be changed by the implementation. */
    _edgeCount = 0;
    _parent;
    _children;
    constructor(opts) {
      if (opts) {
        this._isDirected = Object.hasOwn(opts, "directed") ? opts.directed : true;
        this._isMultigraph = Object.hasOwn(opts, "multigraph") ? opts.multigraph : false;
        this._isCompound = Object.hasOwn(opts, "compound") ? opts.compound : false;
      }
      if (this._isCompound) {
        this._parent = {};
        this._children = {};
        this._children[GRAPH_NODE] = {};
      }
    }
    /* === Graph functions ========= */
    /**
     * Whether graph was created with 'directed' flag set to true or not.
     */
    isDirected() {
      return this._isDirected;
    }
    /**
     * Whether graph was created with 'multigraph' flag set to true or not.
     */
    isMultigraph() {
      return this._isMultigraph;
    }
    /**
     * Whether graph was created with 'compound' flag set to true or not.
     */
    isCompound() {
      return this._isCompound;
    }
    /**
     * Sets the label of the graph.
     */
    setGraph(label) {
      this._label = label;
      return this;
    }
    /**
     * Gets the graph label.
     */
    graph() {
      return this._label;
    }
    /* === Node functions ========== */
    /**
     * Sets the default node label. If newDefault is a function, it will be
     * invoked ach time when setting a label for a node. Otherwise, this label
     * will be assigned as default label in case if no label was specified while
     * setting a node.
     * Complexity: O(1).
     */
    setDefaultNodeLabel(newDefault) {
      this._defaultNodeLabelFn = newDefault;
      if (typeof newDefault !== "function") {
        this._defaultNodeLabelFn = () => newDefault;
      }
      return this;
    }
    /**
     * Gets the number of nodes in the graph.
     * Complexity: O(1).
     */
    nodeCount() {
      return this._nodeCount;
    }
    /**
     * Gets all nodes of the graph. Note, the in case of compound graph subnodes are
     * not included in list.
     * Complexity: O(1).
     */
    nodes() {
      return Object.keys(this._nodes);
    }
    /**
     * Gets list of nodes without in-edges.
     * Complexity: O(|V|).
     */
    sources() {
      var self = this;
      return this.nodes().filter((v) => Object.keys(self._in[v]).length === 0);
    }
    /**
     * Gets list of nodes without out-edges.
     * Complexity: O(|V|).
     */
    sinks() {
      var self = this;
      return this.nodes().filter((v) => Object.keys(self._out[v]).length === 0);
    }
    /**
     * Invokes setNode method for each node in names list.
     * Complexity: O(|names|).
     */
    setNodes(vs, value) {
      var args = arguments;
      var self = this;
      vs.forEach(function(v) {
        if (args.length > 1) {
          self.setNode(v, value);
        } else {
          self.setNode(v);
        }
      });
      return this;
    }
    /**
     * Creates or updates the value for the node v in the graph. If label is supplied
     * it is set as the value for the node. If label is not supplied and the node was
     * created by this call then the default node label will be assigned.
     * Complexity: O(1).
     */
    setNode(v, value) {
      if (Object.hasOwn(this._nodes, v)) {
        if (arguments.length > 1) {
          this._nodes[v] = value;
        }
        return this;
      }
      this._nodes[v] = arguments.length > 1 ? value : this._defaultNodeLabelFn(v);
      if (this._isCompound) {
        this._parent[v] = GRAPH_NODE;
        this._children[v] = {};
        this._children[GRAPH_NODE][v] = true;
      }
      this._in[v] = {};
      this._preds[v] = {};
      this._out[v] = {};
      this._sucs[v] = {};
      ++this._nodeCount;
      return this;
    }
    /**
     * Gets the label of node with specified name.
     * Complexity: O(|V|).
     */
    node(v) {
      return this._nodes[v];
    }
    /**
     * Detects whether graph has a node with specified name or not.
     */
    hasNode(v) {
      return Object.hasOwn(this._nodes, v);
    }
    /**
     * Remove the node with the name from the graph or do nothing if the node is not in
     * the graph. If the node was removed this function also removes any incident
     * edges.
     * Complexity: O(1).
     */
    removeNode(v) {
      var self = this;
      if (Object.hasOwn(this._nodes, v)) {
        var removeEdge = (e2) => self.removeEdge(self._edgeObjs[e2]);
        delete this._nodes[v];
        if (this._isCompound) {
          this._removeFromParentsChildList(v);
          delete this._parent[v];
          this.children(v).forEach(function(child) {
            self.setParent(child);
          });
          delete this._children[v];
        }
        Object.keys(this._in[v]).forEach(removeEdge);
        delete this._in[v];
        delete this._preds[v];
        Object.keys(this._out[v]).forEach(removeEdge);
        delete this._out[v];
        delete this._sucs[v];
        --this._nodeCount;
      }
      return this;
    }
    /**
     * Sets node p as a parent for node v if it is defined, or removes the
     * parent for v if p is undefined. Method throws an exception in case of
     * invoking it in context of noncompound graph.
     * Average-case complexity: O(1).
     */
    setParent(v, parent) {
      if (!this._isCompound) {
        throw new Error("Cannot set parent in a non-compound graph");
      }
      if (parent === void 0) {
        parent = GRAPH_NODE;
      } else {
        parent += "";
        for (var ancestor = parent; ancestor !== void 0; ancestor = this.parent(ancestor)) {
          if (ancestor === v) {
            throw new Error("Setting " + parent + " as parent of " + v + " would create a cycle");
          }
        }
        this.setNode(parent);
      }
      this.setNode(v);
      this._removeFromParentsChildList(v);
      this._parent[v] = parent;
      this._children[parent][v] = true;
      return this;
    }
    _removeFromParentsChildList(v) {
      delete this._children[this._parent[v]][v];
    }
    /**
     * Gets parent node for node v.
     * Complexity: O(1).
     */
    parent(v) {
      if (this._isCompound) {
        var parent = this._parent[v];
        if (parent !== GRAPH_NODE) {
          return parent;
        }
      }
    }
    /**
     * Gets list of direct children of node v.
     * Complexity: O(1).
     */
    children(v = GRAPH_NODE) {
      if (this._isCompound) {
        var children = this._children[v];
        if (children) {
          return Object.keys(children);
        }
      } else if (v === GRAPH_NODE) {
        return this.nodes();
      } else if (this.hasNode(v)) {
        return [];
      }
    }
    /**
     * Return all nodes that are predecessors of the specified node or undefined if node v is not in
     * the graph. Behavior is undefined for undirected graphs - use neighbors instead.
     * Complexity: O(|V|).
     */
    predecessors(v) {
      var predsV = this._preds[v];
      if (predsV) {
        return Object.keys(predsV);
      }
    }
    /**
     * Return all nodes that are successors of the specified node or undefined if node v is not in
     * the graph. Behavior is undefined for undirected graphs - use neighbors instead.
     * Complexity: O(|V|).
     */
    successors(v) {
      var sucsV = this._sucs[v];
      if (sucsV) {
        return Object.keys(sucsV);
      }
    }
    /**
     * Return all nodes that are predecessors or successors of the specified node or undefined if
     * node v is not in the graph.
     * Complexity: O(|V|).
     */
    neighbors(v) {
      var preds = this.predecessors(v);
      if (preds) {
        const union = new Set(preds);
        for (var succ of this.successors(v)) {
          union.add(succ);
        }
        return Array.from(union.values());
      }
    }
    isLeaf(v) {
      var neighbors;
      if (this.isDirected()) {
        neighbors = this.successors(v);
      } else {
        neighbors = this.neighbors(v);
      }
      return neighbors.length === 0;
    }
    /**
     * Creates new graph with nodes filtered via filter. Edges incident to rejected node
     * are also removed. In case of compound graph, if parent is rejected by filter,
     * than all its children are rejected too.
     * Average-case complexity: O(|E|+|V|).
     */
    filterNodes(filter) {
      var copy = new this.constructor({
        directed: this._isDirected,
        multigraph: this._isMultigraph,
        compound: this._isCompound
      });
      copy.setGraph(this.graph());
      var self = this;
      Object.entries(this._nodes).forEach(function([v, value]) {
        if (filter(v)) {
          copy.setNode(v, value);
        }
      });
      Object.values(this._edgeObjs).forEach(function(e2) {
        if (copy.hasNode(e2.v) && copy.hasNode(e2.w)) {
          copy.setEdge(e2, self.edge(e2));
        }
      });
      var parents = {};
      function findParent(v) {
        var parent = self.parent(v);
        if (parent === void 0 || copy.hasNode(parent)) {
          parents[v] = parent;
          return parent;
        } else if (parent in parents) {
          return parents[parent];
        } else {
          return findParent(parent);
        }
      }
      if (this._isCompound) {
        copy.nodes().forEach((v) => copy.setParent(v, findParent(v)));
      }
      return copy;
    }
    /* === Edge functions ========== */
    /**
     * Sets the default edge label or factory function. This label will be
     * assigned as default label in case if no label was specified while setting
     * an edge or this function will be invoked each time when setting an edge
     * with no label specified and returned value * will be used as a label for edge.
     * Complexity: O(1).
     */
    setDefaultEdgeLabel(newDefault) {
      this._defaultEdgeLabelFn = newDefault;
      if (typeof newDefault !== "function") {
        this._defaultEdgeLabelFn = () => newDefault;
      }
      return this;
    }
    /**
     * Gets the number of edges in the graph.
     * Complexity: O(1).
     */
    edgeCount() {
      return this._edgeCount;
    }
    /**
     * Gets edges of the graph. In case of compound graph subgraphs are not considered.
     * Complexity: O(|E|).
     */
    edges() {
      return Object.values(this._edgeObjs);
    }
    /**
     * Establish an edges path over the nodes in nodes list. If some edge is already
     * exists, it will update its label, otherwise it will create an edge between pair
     * of nodes with label provided or default label if no label provided.
     * Complexity: O(|nodes|).
     */
    setPath(vs, value) {
      var self = this;
      var args = arguments;
      vs.reduce(function(v, w) {
        if (args.length > 1) {
          self.setEdge(v, w, value);
        } else {
          self.setEdge(v, w);
        }
        return w;
      });
      return this;
    }
    /**
     * Creates or updates the label for the edge (v, w) with the optionally supplied
     * name. If label is supplied it is set as the value for the edge. If label is not
     * supplied and the edge was created by this call then the default edge label will
     * be assigned. The name parameter is only useful with multigraphs.
     */
    setEdge() {
      var v, w, name, value;
      var valueSpecified = false;
      var arg0 = arguments[0];
      if (typeof arg0 === "object" && arg0 !== null && "v" in arg0) {
        v = arg0.v;
        w = arg0.w;
        name = arg0.name;
        if (arguments.length === 2) {
          value = arguments[1];
          valueSpecified = true;
        }
      } else {
        v = arg0;
        w = arguments[1];
        name = arguments[3];
        if (arguments.length > 2) {
          value = arguments[2];
          valueSpecified = true;
        }
      }
      v = "" + v;
      w = "" + w;
      if (name !== void 0) {
        name = "" + name;
      }
      var e2 = edgeArgsToId(this._isDirected, v, w, name);
      if (Object.hasOwn(this._edgeLabels, e2)) {
        if (valueSpecified) {
          this._edgeLabels[e2] = value;
        }
        return this;
      }
      if (name !== void 0 && !this._isMultigraph) {
        throw new Error("Cannot set a named edge when isMultigraph = false");
      }
      this.setNode(v);
      this.setNode(w);
      this._edgeLabels[e2] = valueSpecified ? value : this._defaultEdgeLabelFn(v, w, name);
      var edgeObj = edgeArgsToObj(this._isDirected, v, w, name);
      v = edgeObj.v;
      w = edgeObj.w;
      Object.freeze(edgeObj);
      this._edgeObjs[e2] = edgeObj;
      incrementOrInitEntry(this._preds[w], v);
      incrementOrInitEntry(this._sucs[v], w);
      this._in[w][e2] = edgeObj;
      this._out[v][e2] = edgeObj;
      this._edgeCount++;
      return this;
    }
    /**
     * Gets the label for the specified edge.
     * Complexity: O(1).
     */
    edge(v, w, name) {
      var e2 = arguments.length === 1 ? edgeObjToId(this._isDirected, arguments[0]) : edgeArgsToId(this._isDirected, v, w, name);
      return this._edgeLabels[e2];
    }
    /**
     * Gets the label for the specified edge and converts it to an object.
     * Complexity: O(1)
     */
    edgeAsObj() {
      const edge = this.edge(...arguments);
      if (typeof edge !== "object") {
        return { label: edge };
      }
      return edge;
    }
    /**
     * Detects whether the graph contains specified edge or not. No subgraphs are considered.
     * Complexity: O(1).
     */
    hasEdge(v, w, name) {
      var e2 = arguments.length === 1 ? edgeObjToId(this._isDirected, arguments[0]) : edgeArgsToId(this._isDirected, v, w, name);
      return Object.hasOwn(this._edgeLabels, e2);
    }
    /**
     * Removes the specified edge from the graph. No subgraphs are considered.
     * Complexity: O(1).
     */
    removeEdge(v, w, name) {
      var e2 = arguments.length === 1 ? edgeObjToId(this._isDirected, arguments[0]) : edgeArgsToId(this._isDirected, v, w, name);
      var edge = this._edgeObjs[e2];
      if (edge) {
        v = edge.v;
        w = edge.w;
        delete this._edgeLabels[e2];
        delete this._edgeObjs[e2];
        decrementOrRemoveEntry(this._preds[w], v);
        decrementOrRemoveEntry(this._sucs[v], w);
        delete this._in[w][e2];
        delete this._out[v][e2];
        this._edgeCount--;
      }
      return this;
    }
    /**
     * Return all edges that point to the node v. Optionally filters those edges down to just those
     * coming from node u. Behavior is undefined for undirected graphs - use nodeEdges instead.
     * Complexity: O(|E|).
     */
    inEdges(v, u) {
      var inV = this._in[v];
      if (inV) {
        var edges = Object.values(inV);
        if (!u) {
          return edges;
        }
        return edges.filter((edge) => edge.v === u);
      }
    }
    /**
     * Return all edges that are pointed at by node v. Optionally filters those edges down to just
     * those point to w. Behavior is undefined for undirected graphs - use nodeEdges instead.
     * Complexity: O(|E|).
     */
    outEdges(v, w) {
      var outV = this._out[v];
      if (outV) {
        var edges = Object.values(outV);
        if (!w) {
          return edges;
        }
        return edges.filter((edge) => edge.w === w);
      }
    }
    /**
     * Returns all edges to or from node v regardless of direction. Optionally filters those edges
     * down to just those between nodes v and w regardless of direction.
     * Complexity: O(|E|).
     */
    nodeEdges(v, w) {
      var inEdges = this.inEdges(v, w);
      if (inEdges) {
        return inEdges.concat(this.outEdges(v, w));
      }
    }
  }
  function incrementOrInitEntry(map, k2) {
    if (map[k2]) {
      map[k2]++;
    } else {
      map[k2] = 1;
    }
  }
  function decrementOrRemoveEntry(map, k2) {
    if (!--map[k2]) {
      delete map[k2];
    }
  }
  function edgeArgsToId(isDirected, v_, w_, name) {
    var v = "" + v_;
    var w = "" + w_;
    if (!isDirected && v > w) {
      var tmp = v;
      v = w;
      w = tmp;
    }
    return v + EDGE_KEY_DELIM + w + EDGE_KEY_DELIM + (name === void 0 ? DEFAULT_EDGE_NAME : name);
  }
  function edgeArgsToObj(isDirected, v_, w_, name) {
    var v = "" + v_;
    var w = "" + w_;
    if (!isDirected && v > w) {
      var tmp = v;
      v = w;
      w = tmp;
    }
    var edgeObj = { v, w };
    if (name) {
      edgeObj.name = name;
    }
    return edgeObj;
  }
  function edgeObjToId(isDirected, edgeObj) {
    return edgeArgsToId(isDirected, edgeObj.v, edgeObj.w, edgeObj.name);
  }
  graph = Graph;
  return graph;
}
var version$1;
var hasRequiredVersion$1;
function requireVersion$1() {
  if (hasRequiredVersion$1) return version$1;
  hasRequiredVersion$1 = 1;
  version$1 = "2.2.4";
  return version$1;
}
var lib;
var hasRequiredLib;
function requireLib() {
  if (hasRequiredLib) return lib;
  hasRequiredLib = 1;
  lib = {
    Graph: requireGraph(),
    version: requireVersion$1()
  };
  return lib;
}
var json;
var hasRequiredJson;
function requireJson() {
  if (hasRequiredJson) return json;
  hasRequiredJson = 1;
  var Graph = requireGraph();
  json = {
    write,
    read
  };
  function write(g) {
    var json2 = {
      options: {
        directed: g.isDirected(),
        multigraph: g.isMultigraph(),
        compound: g.isCompound()
      },
      nodes: writeNodes(g),
      edges: writeEdges(g)
    };
    if (g.graph() !== void 0) {
      json2.value = structuredClone(g.graph());
    }
    return json2;
  }
  function writeNodes(g) {
    return g.nodes().map(function(v) {
      var nodeValue = g.node(v);
      var parent = g.parent(v);
      var node = { v };
      if (nodeValue !== void 0) {
        node.value = nodeValue;
      }
      if (parent !== void 0) {
        node.parent = parent;
      }
      return node;
    });
  }
  function writeEdges(g) {
    return g.edges().map(function(e2) {
      var edgeValue = g.edge(e2);
      var edge = { v: e2.v, w: e2.w };
      if (e2.name !== void 0) {
        edge.name = e2.name;
      }
      if (edgeValue !== void 0) {
        edge.value = edgeValue;
      }
      return edge;
    });
  }
  function read(json2) {
    var g = new Graph(json2.options).setGraph(json2.value);
    json2.nodes.forEach(function(entry) {
      g.setNode(entry.v, entry.value);
      if (entry.parent) {
        g.setParent(entry.v, entry.parent);
      }
    });
    json2.edges.forEach(function(entry) {
      g.setEdge({ v: entry.v, w: entry.w, name: entry.name }, entry.value);
    });
    return g;
  }
  return json;
}
var components_1;
var hasRequiredComponents;
function requireComponents() {
  if (hasRequiredComponents) return components_1;
  hasRequiredComponents = 1;
  components_1 = components;
  function components(g) {
    var visited = {};
    var cmpts = [];
    var cmpt;
    function dfs(v) {
      if (Object.hasOwn(visited, v)) return;
      visited[v] = true;
      cmpt.push(v);
      g.successors(v).forEach(dfs);
      g.predecessors(v).forEach(dfs);
    }
    g.nodes().forEach(function(v) {
      cmpt = [];
      dfs(v);
      if (cmpt.length) {
        cmpts.push(cmpt);
      }
    });
    return cmpts;
  }
  return components_1;
}
var priorityQueue;
var hasRequiredPriorityQueue;
function requirePriorityQueue() {
  if (hasRequiredPriorityQueue) return priorityQueue;
  hasRequiredPriorityQueue = 1;
  class PriorityQueue {
    _arr = [];
    _keyIndices = {};
    /**
     * Returns the number of elements in the queue. Takes `O(1)` time.
     */
    size() {
      return this._arr.length;
    }
    /**
     * Returns the keys that are in the queue. Takes `O(n)` time.
     */
    keys() {
      return this._arr.map(function(x) {
        return x.key;
      });
    }
    /**
     * Returns `true` if **key** is in the queue and `false` if not.
     */
    has(key) {
      return Object.hasOwn(this._keyIndices, key);
    }
    /**
     * Returns the priority for **key**. If **key** is not present in the queue
     * then this function returns `undefined`. Takes `O(1)` time.
     *
     * @param {Object} key
     */
    priority(key) {
      var index = this._keyIndices[key];
      if (index !== void 0) {
        return this._arr[index].priority;
      }
    }
    /**
     * Returns the key for the minimum element in this queue. If the queue is
     * empty this function throws an Error. Takes `O(1)` time.
     */
    min() {
      if (this.size() === 0) {
        throw new Error("Queue underflow");
      }
      return this._arr[0].key;
    }
    /**
     * Inserts a new key into the priority queue. If the key already exists in
     * the queue this function returns `false`; otherwise it will return `true`.
     * Takes `O(n)` time.
     *
     * @param {Object} key the key to add
     * @param {Number} priority the initial priority for the key
     */
    add(key, priority) {
      var keyIndices = this._keyIndices;
      key = String(key);
      if (!Object.hasOwn(keyIndices, key)) {
        var arr = this._arr;
        var index = arr.length;
        keyIndices[key] = index;
        arr.push({ key, priority });
        this._decrease(index);
        return true;
      }
      return false;
    }
    /**
     * Removes and returns the smallest key in the queue. Takes `O(log n)` time.
     */
    removeMin() {
      this._swap(0, this._arr.length - 1);
      var min = this._arr.pop();
      delete this._keyIndices[min.key];
      this._heapify(0);
      return min.key;
    }
    /**
     * Decreases the priority for **key** to **priority**. If the new priority is
     * greater than the previous priority, this function will throw an Error.
     *
     * @param {Object} key the key for which to raise priority
     * @param {Number} priority the new priority for the key
     */
    decrease(key, priority) {
      var index = this._keyIndices[key];
      if (priority > this._arr[index].priority) {
        throw new Error("New priority is greater than current priority. Key: " + key + " Old: " + this._arr[index].priority + " New: " + priority);
      }
      this._arr[index].priority = priority;
      this._decrease(index);
    }
    _heapify(i) {
      var arr = this._arr;
      var l = 2 * i;
      var r = l + 1;
      var largest = i;
      if (l < arr.length) {
        largest = arr[l].priority < arr[largest].priority ? l : largest;
        if (r < arr.length) {
          largest = arr[r].priority < arr[largest].priority ? r : largest;
        }
        if (largest !== i) {
          this._swap(i, largest);
          this._heapify(largest);
        }
      }
    }
    _decrease(index) {
      var arr = this._arr;
      var priority = arr[index].priority;
      var parent;
      while (index !== 0) {
        parent = index >> 1;
        if (arr[parent].priority < priority) {
          break;
        }
        this._swap(index, parent);
        index = parent;
      }
    }
    _swap(i, j) {
      var arr = this._arr;
      var keyIndices = this._keyIndices;
      var origArrI = arr[i];
      var origArrJ = arr[j];
      arr[i] = origArrJ;
      arr[j] = origArrI;
      keyIndices[origArrJ.key] = i;
      keyIndices[origArrI.key] = j;
    }
  }
  priorityQueue = PriorityQueue;
  return priorityQueue;
}
var dijkstra_1;
var hasRequiredDijkstra;
function requireDijkstra() {
  if (hasRequiredDijkstra) return dijkstra_1;
  hasRequiredDijkstra = 1;
  var PriorityQueue = requirePriorityQueue();
  dijkstra_1 = dijkstra;
  var DEFAULT_WEIGHT_FUNC = () => 1;
  function dijkstra(g, source, weightFn, edgeFn) {
    return runDijkstra(
      g,
      String(source),
      weightFn || DEFAULT_WEIGHT_FUNC,
      edgeFn || function(v) {
        return g.outEdges(v);
      }
    );
  }
  function runDijkstra(g, source, weightFn, edgeFn) {
    var results = {};
    var pq = new PriorityQueue();
    var v, vEntry;
    var updateNeighbors = function(edge) {
      var w = edge.v !== v ? edge.v : edge.w;
      var wEntry = results[w];
      var weight = weightFn(edge);
      var distance = vEntry.distance + weight;
      if (weight < 0) {
        throw new Error("dijkstra does not allow negative edge weights. Bad edge: " + edge + " Weight: " + weight);
      }
      if (distance < wEntry.distance) {
        wEntry.distance = distance;
        wEntry.predecessor = v;
        pq.decrease(w, distance);
      }
    };
    g.nodes().forEach(function(v2) {
      var distance = v2 === source ? 0 : Number.POSITIVE_INFINITY;
      results[v2] = { distance };
      pq.add(v2, distance);
    });
    while (pq.size() > 0) {
      v = pq.removeMin();
      vEntry = results[v];
      if (vEntry.distance === Number.POSITIVE_INFINITY) {
        break;
      }
      edgeFn(v).forEach(updateNeighbors);
    }
    return results;
  }
  return dijkstra_1;
}
var dijkstraAll_1;
var hasRequiredDijkstraAll;
function requireDijkstraAll() {
  if (hasRequiredDijkstraAll) return dijkstraAll_1;
  hasRequiredDijkstraAll = 1;
  var dijkstra = requireDijkstra();
  dijkstraAll_1 = dijkstraAll;
  function dijkstraAll(g, weightFunc, edgeFunc) {
    return g.nodes().reduce(function(acc, v) {
      acc[v] = dijkstra(g, v, weightFunc, edgeFunc);
      return acc;
    }, {});
  }
  return dijkstraAll_1;
}
var tarjan_1;
var hasRequiredTarjan;
function requireTarjan() {
  if (hasRequiredTarjan) return tarjan_1;
  hasRequiredTarjan = 1;
  tarjan_1 = tarjan;
  function tarjan(g) {
    var index = 0;
    var stack = [];
    var visited = {};
    var results = [];
    function dfs(v) {
      var entry = visited[v] = {
        onStack: true,
        lowlink: index,
        index: index++
      };
      stack.push(v);
      g.successors(v).forEach(function(w2) {
        if (!Object.hasOwn(visited, w2)) {
          dfs(w2);
          entry.lowlink = Math.min(entry.lowlink, visited[w2].lowlink);
        } else if (visited[w2].onStack) {
          entry.lowlink = Math.min(entry.lowlink, visited[w2].index);
        }
      });
      if (entry.lowlink === entry.index) {
        var cmpt = [];
        var w;
        do {
          w = stack.pop();
          visited[w].onStack = false;
          cmpt.push(w);
        } while (v !== w);
        results.push(cmpt);
      }
    }
    g.nodes().forEach(function(v) {
      if (!Object.hasOwn(visited, v)) {
        dfs(v);
      }
    });
    return results;
  }
  return tarjan_1;
}
var findCycles_1;
var hasRequiredFindCycles;
function requireFindCycles() {
  if (hasRequiredFindCycles) return findCycles_1;
  hasRequiredFindCycles = 1;
  var tarjan = requireTarjan();
  findCycles_1 = findCycles;
  function findCycles(g) {
    return tarjan(g).filter(function(cmpt) {
      return cmpt.length > 1 || cmpt.length === 1 && g.hasEdge(cmpt[0], cmpt[0]);
    });
  }
  return findCycles_1;
}
var floydWarshall_1;
var hasRequiredFloydWarshall;
function requireFloydWarshall() {
  if (hasRequiredFloydWarshall) return floydWarshall_1;
  hasRequiredFloydWarshall = 1;
  floydWarshall_1 = floydWarshall;
  var DEFAULT_WEIGHT_FUNC = () => 1;
  function floydWarshall(g, weightFn, edgeFn) {
    return runFloydWarshall(
      g,
      weightFn || DEFAULT_WEIGHT_FUNC,
      edgeFn || function(v) {
        return g.outEdges(v);
      }
    );
  }
  function runFloydWarshall(g, weightFn, edgeFn) {
    var results = {};
    var nodes = g.nodes();
    nodes.forEach(function(v) {
      results[v] = {};
      results[v][v] = { distance: 0 };
      nodes.forEach(function(w) {
        if (v !== w) {
          results[v][w] = { distance: Number.POSITIVE_INFINITY };
        }
      });
      edgeFn(v).forEach(function(edge) {
        var w = edge.v === v ? edge.w : edge.v;
        var d = weightFn(edge);
        results[v][w] = { distance: d, predecessor: v };
      });
    });
    nodes.forEach(function(k2) {
      var rowK = results[k2];
      nodes.forEach(function(i) {
        var rowI = results[i];
        nodes.forEach(function(j) {
          var ik = rowI[k2];
          var kj = rowK[j];
          var ij = rowI[j];
          var altDistance = ik.distance + kj.distance;
          if (altDistance < ij.distance) {
            ij.distance = altDistance;
            ij.predecessor = kj.predecessor;
          }
        });
      });
    });
    return results;
  }
  return floydWarshall_1;
}
var topsort_1;
var hasRequiredTopsort;
function requireTopsort() {
  if (hasRequiredTopsort) return topsort_1;
  hasRequiredTopsort = 1;
  function topsort(g) {
    var visited = {};
    var stack = {};
    var results = [];
    function visit(node) {
      if (Object.hasOwn(stack, node)) {
        throw new CycleException();
      }
      if (!Object.hasOwn(visited, node)) {
        stack[node] = true;
        visited[node] = true;
        g.predecessors(node).forEach(visit);
        delete stack[node];
        results.push(node);
      }
    }
    g.sinks().forEach(visit);
    if (Object.keys(visited).length !== g.nodeCount()) {
      throw new CycleException();
    }
    return results;
  }
  class CycleException extends Error {
    constructor() {
      super(...arguments);
    }
  }
  topsort_1 = topsort;
  topsort.CycleException = CycleException;
  return topsort_1;
}
var isAcyclic_1;
var hasRequiredIsAcyclic;
function requireIsAcyclic() {
  if (hasRequiredIsAcyclic) return isAcyclic_1;
  hasRequiredIsAcyclic = 1;
  var topsort = requireTopsort();
  isAcyclic_1 = isAcyclic;
  function isAcyclic(g) {
    try {
      topsort(g);
    } catch (e2) {
      if (e2 instanceof topsort.CycleException) {
        return false;
      }
      throw e2;
    }
    return true;
  }
  return isAcyclic_1;
}
var dfs_1;
var hasRequiredDfs;
function requireDfs() {
  if (hasRequiredDfs) return dfs_1;
  hasRequiredDfs = 1;
  dfs_1 = dfs;
  function dfs(g, vs, order) {
    if (!Array.isArray(vs)) {
      vs = [vs];
    }
    var navigation = g.isDirected() ? (v) => g.successors(v) : (v) => g.neighbors(v);
    var orderFunc = order === "post" ? postOrderDfs : preOrderDfs;
    var acc = [];
    var visited = {};
    vs.forEach((v) => {
      if (!g.hasNode(v)) {
        throw new Error("Graph does not have node: " + v);
      }
      orderFunc(v, navigation, visited, acc);
    });
    return acc;
  }
  function postOrderDfs(v, navigation, visited, acc) {
    var stack = [[v, false]];
    while (stack.length > 0) {
      var curr = stack.pop();
      if (curr[1]) {
        acc.push(curr[0]);
      } else {
        if (!Object.hasOwn(visited, curr[0])) {
          visited[curr[0]] = true;
          stack.push([curr[0], true]);
          forEachRight(navigation(curr[0]), (w) => stack.push([w, false]));
        }
      }
    }
  }
  function preOrderDfs(v, navigation, visited, acc) {
    var stack = [v];
    while (stack.length > 0) {
      var curr = stack.pop();
      if (!Object.hasOwn(visited, curr)) {
        visited[curr] = true;
        acc.push(curr);
        forEachRight(navigation(curr), (w) => stack.push(w));
      }
    }
  }
  function forEachRight(array, iteratee) {
    var length = array.length;
    while (length--) {
      iteratee(array[length], length, array);
    }
    return array;
  }
  return dfs_1;
}
var postorder_1;
var hasRequiredPostorder;
function requirePostorder() {
  if (hasRequiredPostorder) return postorder_1;
  hasRequiredPostorder = 1;
  var dfs = requireDfs();
  postorder_1 = postorder;
  function postorder(g, vs) {
    return dfs(g, vs, "post");
  }
  return postorder_1;
}
var preorder_1;
var hasRequiredPreorder;
function requirePreorder() {
  if (hasRequiredPreorder) return preorder_1;
  hasRequiredPreorder = 1;
  var dfs = requireDfs();
  preorder_1 = preorder;
  function preorder(g, vs) {
    return dfs(g, vs, "pre");
  }
  return preorder_1;
}
var prim_1;
var hasRequiredPrim;
function requirePrim() {
  if (hasRequiredPrim) return prim_1;
  hasRequiredPrim = 1;
  var Graph = requireGraph();
  var PriorityQueue = requirePriorityQueue();
  prim_1 = prim;
  function prim(g, weightFunc) {
    var result = new Graph();
    var parents = {};
    var pq = new PriorityQueue();
    var v;
    function updateNeighbors(edge) {
      var w = edge.v === v ? edge.w : edge.v;
      var pri = pq.priority(w);
      if (pri !== void 0) {
        var edgeWeight = weightFunc(edge);
        if (edgeWeight < pri) {
          parents[w] = v;
          pq.decrease(w, edgeWeight);
        }
      }
    }
    if (g.nodeCount() === 0) {
      return result;
    }
    g.nodes().forEach(function(v2) {
      pq.add(v2, Number.POSITIVE_INFINITY);
      result.setNode(v2);
    });
    pq.decrease(g.nodes()[0], 0);
    var init = false;
    while (pq.size() > 0) {
      v = pq.removeMin();
      if (Object.hasOwn(parents, v)) {
        result.setEdge(v, parents[v]);
      } else if (init) {
        throw new Error("Input graph is not connected: " + g);
      } else {
        init = true;
      }
      g.nodeEdges(v).forEach(updateNeighbors);
    }
    return result;
  }
  return prim_1;
}
var alg;
var hasRequiredAlg;
function requireAlg() {
  if (hasRequiredAlg) return alg;
  hasRequiredAlg = 1;
  alg = {
    components: requireComponents(),
    dijkstra: requireDijkstra(),
    dijkstraAll: requireDijkstraAll(),
    findCycles: requireFindCycles(),
    floydWarshall: requireFloydWarshall(),
    isAcyclic: requireIsAcyclic(),
    postorder: requirePostorder(),
    preorder: requirePreorder(),
    prim: requirePrim(),
    tarjan: requireTarjan(),
    topsort: requireTopsort()
  };
  return alg;
}
var graphlib;
var hasRequiredGraphlib;
function requireGraphlib() {
  if (hasRequiredGraphlib) return graphlib;
  hasRequiredGraphlib = 1;
  var lib2 = requireLib();
  graphlib = {
    Graph: lib2.Graph,
    json: requireJson(),
    alg: requireAlg(),
    version: lib2.version
  };
  return graphlib;
}
var list;
var hasRequiredList;
function requireList() {
  if (hasRequiredList) return list;
  hasRequiredList = 1;
  class List {
    constructor() {
      let sentinel = {};
      sentinel._next = sentinel._prev = sentinel;
      this._sentinel = sentinel;
    }
    dequeue() {
      let sentinel = this._sentinel;
      let entry = sentinel._prev;
      if (entry !== sentinel) {
        unlink(entry);
        return entry;
      }
    }
    enqueue(entry) {
      let sentinel = this._sentinel;
      if (entry._prev && entry._next) {
        unlink(entry);
      }
      entry._next = sentinel._next;
      sentinel._next._prev = entry;
      sentinel._next = entry;
      entry._prev = sentinel;
    }
    toString() {
      let strs = [];
      let sentinel = this._sentinel;
      let curr = sentinel._prev;
      while (curr !== sentinel) {
        strs.push(JSON.stringify(curr, filterOutLinks));
        curr = curr._prev;
      }
      return "[" + strs.join(", ") + "]";
    }
  }
  function unlink(entry) {
    entry._prev._next = entry._next;
    entry._next._prev = entry._prev;
    delete entry._next;
    delete entry._prev;
  }
  function filterOutLinks(k2, v) {
    if (k2 !== "_next" && k2 !== "_prev") {
      return v;
    }
  }
  list = List;
  return list;
}
var greedyFas;
var hasRequiredGreedyFas;
function requireGreedyFas() {
  if (hasRequiredGreedyFas) return greedyFas;
  hasRequiredGreedyFas = 1;
  let Graph = requireGraphlib().Graph;
  let List = requireList();
  greedyFas = greedyFAS;
  let DEFAULT_WEIGHT_FN = () => 1;
  function greedyFAS(g, weightFn) {
    if (g.nodeCount() <= 1) {
      return [];
    }
    let state = buildState(g, weightFn || DEFAULT_WEIGHT_FN);
    let results = doGreedyFAS(state.graph, state.buckets, state.zeroIdx);
    return results.flatMap((e2) => g.outEdges(e2.v, e2.w));
  }
  function doGreedyFAS(g, buckets, zeroIdx) {
    let results = [];
    let sources = buckets[buckets.length - 1];
    let sinks = buckets[0];
    let entry;
    while (g.nodeCount()) {
      while (entry = sinks.dequeue()) {
        removeNode(g, buckets, zeroIdx, entry);
      }
      while (entry = sources.dequeue()) {
        removeNode(g, buckets, zeroIdx, entry);
      }
      if (g.nodeCount()) {
        for (let i = buckets.length - 2; i > 0; --i) {
          entry = buckets[i].dequeue();
          if (entry) {
            results = results.concat(removeNode(g, buckets, zeroIdx, entry, true));
            break;
          }
        }
      }
    }
    return results;
  }
  function removeNode(g, buckets, zeroIdx, entry, collectPredecessors) {
    let results = collectPredecessors ? [] : void 0;
    g.inEdges(entry.v).forEach((edge) => {
      let weight = g.edge(edge);
      let uEntry = g.node(edge.v);
      if (collectPredecessors) {
        results.push({ v: edge.v, w: edge.w });
      }
      uEntry.out -= weight;
      assignBucket(buckets, zeroIdx, uEntry);
    });
    g.outEdges(entry.v).forEach((edge) => {
      let weight = g.edge(edge);
      let w = edge.w;
      let wEntry = g.node(w);
      wEntry["in"] -= weight;
      assignBucket(buckets, zeroIdx, wEntry);
    });
    g.removeNode(entry.v);
    return results;
  }
  function buildState(g, weightFn) {
    let fasGraph = new Graph();
    let maxIn = 0;
    let maxOut = 0;
    g.nodes().forEach((v) => {
      fasGraph.setNode(v, { v, "in": 0, out: 0 });
    });
    g.edges().forEach((e2) => {
      let prevWeight = fasGraph.edge(e2.v, e2.w) || 0;
      let weight = weightFn(e2);
      let edgeWeight = prevWeight + weight;
      fasGraph.setEdge(e2.v, e2.w, edgeWeight);
      maxOut = Math.max(maxOut, fasGraph.node(e2.v).out += weight);
      maxIn = Math.max(maxIn, fasGraph.node(e2.w)["in"] += weight);
    });
    let buckets = range2(maxOut + maxIn + 3).map(() => new List());
    let zeroIdx = maxIn + 1;
    fasGraph.nodes().forEach((v) => {
      assignBucket(buckets, zeroIdx, fasGraph.node(v));
    });
    return { graph: fasGraph, buckets, zeroIdx };
  }
  function assignBucket(buckets, zeroIdx, entry) {
    if (!entry.out) {
      buckets[0].enqueue(entry);
    } else if (!entry["in"]) {
      buckets[buckets.length - 1].enqueue(entry);
    } else {
      buckets[entry.out - entry["in"] + zeroIdx].enqueue(entry);
    }
  }
  function range2(limit) {
    const range3 = [];
    for (let i = 0; i < limit; i++) {
      range3.push(i);
    }
    return range3;
  }
  return greedyFas;
}
var util$1;
var hasRequiredUtil$1;
function requireUtil$1() {
  if (hasRequiredUtil$1) return util$1;
  hasRequiredUtil$1 = 1;
  let Graph = requireGraphlib().Graph;
  util$1 = {
    addBorderNode,
    addDummyNode,
    applyWithChunking,
    asNonCompoundGraph,
    buildLayerMatrix,
    intersectRect,
    mapValues,
    maxRank,
    normalizeRanks,
    notime,
    partition,
    pick,
    predecessorWeights,
    range: range2,
    removeEmptyRanks,
    simplify,
    successorWeights,
    time,
    uniqueId,
    zipObject
  };
  function addDummyNode(g, type, attrs, name) {
    var v = name;
    while (g.hasNode(v)) {
      v = uniqueId(name);
    }
    attrs.dummy = type;
    g.setNode(v, attrs);
    return v;
  }
  function simplify(g) {
    let simplified = new Graph().setGraph(g.graph());
    g.nodes().forEach((v) => simplified.setNode(v, g.node(v)));
    g.edges().forEach((e2) => {
      let simpleLabel = simplified.edge(e2.v, e2.w) || { weight: 0, minlen: 1 };
      let label = g.edge(e2);
      simplified.setEdge(e2.v, e2.w, {
        weight: simpleLabel.weight + label.weight,
        minlen: Math.max(simpleLabel.minlen, label.minlen)
      });
    });
    return simplified;
  }
  function asNonCompoundGraph(g) {
    let simplified = new Graph({ multigraph: g.isMultigraph() }).setGraph(g.graph());
    g.nodes().forEach((v) => {
      if (!g.children(v).length) {
        simplified.setNode(v, g.node(v));
      }
    });
    g.edges().forEach((e2) => {
      simplified.setEdge(e2, g.edge(e2));
    });
    return simplified;
  }
  function successorWeights(g) {
    let weightMap = g.nodes().map((v) => {
      let sucs = {};
      g.outEdges(v).forEach((e2) => {
        sucs[e2.w] = (sucs[e2.w] || 0) + g.edge(e2).weight;
      });
      return sucs;
    });
    return zipObject(g.nodes(), weightMap);
  }
  function predecessorWeights(g) {
    let weightMap = g.nodes().map((v) => {
      let preds = {};
      g.inEdges(v).forEach((e2) => {
        preds[e2.v] = (preds[e2.v] || 0) + g.edge(e2).weight;
      });
      return preds;
    });
    return zipObject(g.nodes(), weightMap);
  }
  function intersectRect(rect, point) {
    let x = rect.x;
    let y = rect.y;
    let dx = point.x - x;
    let dy = point.y - y;
    let w = rect.width / 2;
    let h = rect.height / 2;
    if (!dx && !dy) {
      throw new Error("Not possible to find intersection inside of the rectangle");
    }
    let sx, sy;
    if (Math.abs(dy) * w > Math.abs(dx) * h) {
      if (dy < 0) {
        h = -h;
      }
      sx = h * dx / dy;
      sy = h;
    } else {
      if (dx < 0) {
        w = -w;
      }
      sx = w;
      sy = w * dy / dx;
    }
    return { x: x + sx, y: y + sy };
  }
  function buildLayerMatrix(g) {
    let layering = range2(maxRank(g) + 1).map(() => []);
    g.nodes().forEach((v) => {
      let node = g.node(v);
      let rank = node.rank;
      if (rank !== void 0) {
        layering[rank][node.order] = v;
      }
    });
    return layering;
  }
  function normalizeRanks(g) {
    let nodeRanks = g.nodes().map((v) => {
      let rank = g.node(v).rank;
      if (rank === void 0) {
        return Number.MAX_VALUE;
      }
      return rank;
    });
    let min = applyWithChunking(Math.min, nodeRanks);
    g.nodes().forEach((v) => {
      let node = g.node(v);
      if (Object.hasOwn(node, "rank")) {
        node.rank -= min;
      }
    });
  }
  function removeEmptyRanks(g) {
    let nodeRanks = g.nodes().map((v) => g.node(v).rank);
    let offset = applyWithChunking(Math.min, nodeRanks);
    let layers = [];
    g.nodes().forEach((v) => {
      let rank = g.node(v).rank - offset;
      if (!layers[rank]) {
        layers[rank] = [];
      }
      layers[rank].push(v);
    });
    let delta = 0;
    let nodeRankFactor = g.graph().nodeRankFactor;
    Array.from(layers).forEach((vs, i) => {
      if (vs === void 0 && i % nodeRankFactor !== 0) {
        --delta;
      } else if (vs !== void 0 && delta) {
        vs.forEach((v) => g.node(v).rank += delta);
      }
    });
  }
  function addBorderNode(g, prefix, rank, order) {
    let node = {
      width: 0,
      height: 0
    };
    if (arguments.length >= 4) {
      node.rank = rank;
      node.order = order;
    }
    return addDummyNode(g, "border", node, prefix);
  }
  function splitToChunks(array, chunkSize = CHUNKING_THRESHOLD) {
    const chunks = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      const chunk = array.slice(i, i + chunkSize);
      chunks.push(chunk);
    }
    return chunks;
  }
  const CHUNKING_THRESHOLD = 65535;
  function applyWithChunking(fn2, argsArray) {
    if (argsArray.length > CHUNKING_THRESHOLD) {
      const chunks = splitToChunks(argsArray);
      return fn2.apply(null, chunks.map((chunk) => fn2.apply(null, chunk)));
    } else {
      return fn2.apply(null, argsArray);
    }
  }
  function maxRank(g) {
    const nodes = g.nodes();
    const nodeRanks = nodes.map((v) => {
      let rank = g.node(v).rank;
      if (rank === void 0) {
        return Number.MIN_VALUE;
      }
      return rank;
    });
    return applyWithChunking(Math.max, nodeRanks);
  }
  function partition(collection, fn2) {
    let result = { lhs: [], rhs: [] };
    collection.forEach((value) => {
      if (fn2(value)) {
        result.lhs.push(value);
      } else {
        result.rhs.push(value);
      }
    });
    return result;
  }
  function time(name, fn2) {
    let start = Date.now();
    try {
      return fn2();
    } finally {
      console.log(name + " time: " + (Date.now() - start) + "ms");
    }
  }
  function notime(name, fn2) {
    return fn2();
  }
  let idCounter = 0;
  function uniqueId(prefix) {
    var id = ++idCounter;
    return prefix + ("" + id);
  }
  function range2(start, limit, step = 1) {
    if (limit == null) {
      limit = start;
      start = 0;
    }
    let endCon = (i) => i < limit;
    if (step < 0) {
      endCon = (i) => limit < i;
    }
    const range3 = [];
    for (let i = start; endCon(i); i += step) {
      range3.push(i);
    }
    return range3;
  }
  function pick(source, keys) {
    const dest = {};
    for (const key of keys) {
      if (source[key] !== void 0) {
        dest[key] = source[key];
      }
    }
    return dest;
  }
  function mapValues(obj, funcOrProp) {
    let func = funcOrProp;
    if (typeof funcOrProp === "string") {
      func = (val) => val[funcOrProp];
    }
    return Object.entries(obj).reduce((acc, [k2, v]) => {
      acc[k2] = func(v, k2);
      return acc;
    }, {});
  }
  function zipObject(props, values) {
    return props.reduce((acc, key, i) => {
      acc[key] = values[i];
      return acc;
    }, {});
  }
  return util$1;
}
var acyclic;
var hasRequiredAcyclic;
function requireAcyclic() {
  if (hasRequiredAcyclic) return acyclic;
  hasRequiredAcyclic = 1;
  let greedyFAS = requireGreedyFas();
  let uniqueId = requireUtil$1().uniqueId;
  acyclic = {
    run,
    undo
  };
  function run(g) {
    let fas = g.graph().acyclicer === "greedy" ? greedyFAS(g, weightFn(g)) : dfsFAS(g);
    fas.forEach((e2) => {
      let label = g.edge(e2);
      g.removeEdge(e2);
      label.forwardName = e2.name;
      label.reversed = true;
      g.setEdge(e2.w, e2.v, label, uniqueId("rev"));
    });
    function weightFn(g2) {
      return (e2) => {
        return g2.edge(e2).weight;
      };
    }
  }
  function dfsFAS(g) {
    let fas = [];
    let stack = {};
    let visited = {};
    function dfs(v) {
      if (Object.hasOwn(visited, v)) {
        return;
      }
      visited[v] = true;
      stack[v] = true;
      g.outEdges(v).forEach((e2) => {
        if (Object.hasOwn(stack, e2.w)) {
          fas.push(e2);
        } else {
          dfs(e2.w);
        }
      });
      delete stack[v];
    }
    g.nodes().forEach(dfs);
    return fas;
  }
  function undo(g) {
    g.edges().forEach((e2) => {
      let label = g.edge(e2);
      if (label.reversed) {
        g.removeEdge(e2);
        let forwardName = label.forwardName;
        delete label.reversed;
        delete label.forwardName;
        g.setEdge(e2.w, e2.v, label, forwardName);
      }
    });
  }
  return acyclic;
}
var normalize;
var hasRequiredNormalize;
function requireNormalize() {
  if (hasRequiredNormalize) return normalize;
  hasRequiredNormalize = 1;
  let util2 = requireUtil$1();
  normalize = {
    run,
    undo
  };
  function run(g) {
    g.graph().dummyChains = [];
    g.edges().forEach((edge) => normalizeEdge(g, edge));
  }
  function normalizeEdge(g, e2) {
    let v = e2.v;
    let vRank = g.node(v).rank;
    let w = e2.w;
    let wRank = g.node(w).rank;
    let name = e2.name;
    let edgeLabel = g.edge(e2);
    let labelRank = edgeLabel.labelRank;
    if (wRank === vRank + 1) return;
    g.removeEdge(e2);
    let dummy, attrs, i;
    for (i = 0, ++vRank; vRank < wRank; ++i, ++vRank) {
      edgeLabel.points = [];
      attrs = {
        width: 0,
        height: 0,
        edgeLabel,
        edgeObj: e2,
        rank: vRank
      };
      dummy = util2.addDummyNode(g, "edge", attrs, "_d");
      if (vRank === labelRank) {
        attrs.width = edgeLabel.width;
        attrs.height = edgeLabel.height;
        attrs.dummy = "edge-label";
        attrs.labelpos = edgeLabel.labelpos;
      }
      g.setEdge(v, dummy, { weight: edgeLabel.weight }, name);
      if (i === 0) {
        g.graph().dummyChains.push(dummy);
      }
      v = dummy;
    }
    g.setEdge(v, w, { weight: edgeLabel.weight }, name);
  }
  function undo(g) {
    g.graph().dummyChains.forEach((v) => {
      let node = g.node(v);
      let origLabel = node.edgeLabel;
      let w;
      g.setEdge(node.edgeObj, origLabel);
      while (node.dummy) {
        w = g.successors(v)[0];
        g.removeNode(v);
        origLabel.points.push({ x: node.x, y: node.y });
        if (node.dummy === "edge-label") {
          origLabel.x = node.x;
          origLabel.y = node.y;
          origLabel.width = node.width;
          origLabel.height = node.height;
        }
        v = w;
        node = g.node(v);
      }
    });
  }
  return normalize;
}
var util;
var hasRequiredUtil;
function requireUtil() {
  if (hasRequiredUtil) return util;
  hasRequiredUtil = 1;
  const { applyWithChunking } = requireUtil$1();
  util = {
    longestPath,
    slack
  };
  function longestPath(g) {
    var visited = {};
    function dfs(v) {
      var label = g.node(v);
      if (Object.hasOwn(visited, v)) {
        return label.rank;
      }
      visited[v] = true;
      let outEdgesMinLens = g.outEdges(v).map((e2) => {
        if (e2 == null) {
          return Number.POSITIVE_INFINITY;
        }
        return dfs(e2.w) - g.edge(e2).minlen;
      });
      var rank = applyWithChunking(Math.min, outEdgesMinLens);
      if (rank === Number.POSITIVE_INFINITY) {
        rank = 0;
      }
      return label.rank = rank;
    }
    g.sources().forEach(dfs);
  }
  function slack(g, e2) {
    return g.node(e2.w).rank - g.node(e2.v).rank - g.edge(e2).minlen;
  }
  return util;
}
var feasibleTree_1;
var hasRequiredFeasibleTree;
function requireFeasibleTree() {
  if (hasRequiredFeasibleTree) return feasibleTree_1;
  hasRequiredFeasibleTree = 1;
  var Graph = requireGraphlib().Graph;
  var slack = requireUtil().slack;
  feasibleTree_1 = feasibleTree;
  function feasibleTree(g) {
    var t = new Graph({ directed: false });
    var start = g.nodes()[0];
    var size = g.nodeCount();
    t.setNode(start, {});
    var edge, delta;
    while (tightTree(t, g) < size) {
      edge = findMinSlackEdge(t, g);
      delta = t.hasNode(edge.v) ? slack(g, edge) : -slack(g, edge);
      shiftRanks(t, g, delta);
    }
    return t;
  }
  function tightTree(t, g) {
    function dfs(v) {
      g.nodeEdges(v).forEach((e2) => {
        var edgeV = e2.v, w = v === edgeV ? e2.w : edgeV;
        if (!t.hasNode(w) && !slack(g, e2)) {
          t.setNode(w, {});
          t.setEdge(v, w, {});
          dfs(w);
        }
      });
    }
    t.nodes().forEach(dfs);
    return t.nodeCount();
  }
  function findMinSlackEdge(t, g) {
    const edges = g.edges();
    return edges.reduce((acc, edge) => {
      let edgeSlack = Number.POSITIVE_INFINITY;
      if (t.hasNode(edge.v) !== t.hasNode(edge.w)) {
        edgeSlack = slack(g, edge);
      }
      if (edgeSlack < acc[0]) {
        return [edgeSlack, edge];
      }
      return acc;
    }, [Number.POSITIVE_INFINITY, null])[1];
  }
  function shiftRanks(t, g, delta) {
    t.nodes().forEach((v) => g.node(v).rank += delta);
  }
  return feasibleTree_1;
}
var networkSimplex_1;
var hasRequiredNetworkSimplex;
function requireNetworkSimplex() {
  if (hasRequiredNetworkSimplex) return networkSimplex_1;
  hasRequiredNetworkSimplex = 1;
  var feasibleTree = requireFeasibleTree();
  var slack = requireUtil().slack;
  var initRank = requireUtil().longestPath;
  var preorder = requireGraphlib().alg.preorder;
  var postorder = requireGraphlib().alg.postorder;
  var simplify = requireUtil$1().simplify;
  networkSimplex_1 = networkSimplex;
  networkSimplex.initLowLimValues = initLowLimValues;
  networkSimplex.initCutValues = initCutValues;
  networkSimplex.calcCutValue = calcCutValue;
  networkSimplex.leaveEdge = leaveEdge;
  networkSimplex.enterEdge = enterEdge;
  networkSimplex.exchangeEdges = exchangeEdges;
  function networkSimplex(g) {
    g = simplify(g);
    initRank(g);
    var t = feasibleTree(g);
    initLowLimValues(t);
    initCutValues(t, g);
    var e2, f2;
    while (e2 = leaveEdge(t)) {
      f2 = enterEdge(t, g, e2);
      exchangeEdges(t, g, e2, f2);
    }
  }
  function initCutValues(t, g) {
    var vs = postorder(t, t.nodes());
    vs = vs.slice(0, vs.length - 1);
    vs.forEach((v) => assignCutValue(t, g, v));
  }
  function assignCutValue(t, g, child) {
    var childLab = t.node(child);
    var parent = childLab.parent;
    t.edge(child, parent).cutvalue = calcCutValue(t, g, child);
  }
  function calcCutValue(t, g, child) {
    var childLab = t.node(child);
    var parent = childLab.parent;
    var childIsTail = true;
    var graphEdge = g.edge(child, parent);
    var cutValue = 0;
    if (!graphEdge) {
      childIsTail = false;
      graphEdge = g.edge(parent, child);
    }
    cutValue = graphEdge.weight;
    g.nodeEdges(child).forEach((e2) => {
      var isOutEdge = e2.v === child, other = isOutEdge ? e2.w : e2.v;
      if (other !== parent) {
        var pointsToHead = isOutEdge === childIsTail, otherWeight = g.edge(e2).weight;
        cutValue += pointsToHead ? otherWeight : -otherWeight;
        if (isTreeEdge(t, child, other)) {
          var otherCutValue = t.edge(child, other).cutvalue;
          cutValue += pointsToHead ? -otherCutValue : otherCutValue;
        }
      }
    });
    return cutValue;
  }
  function initLowLimValues(tree, root) {
    if (arguments.length < 2) {
      root = tree.nodes()[0];
    }
    dfsAssignLowLim(tree, {}, 1, root);
  }
  function dfsAssignLowLim(tree, visited, nextLim, v, parent) {
    var low = nextLim;
    var label = tree.node(v);
    visited[v] = true;
    tree.neighbors(v).forEach((w) => {
      if (!Object.hasOwn(visited, w)) {
        nextLim = dfsAssignLowLim(tree, visited, nextLim, w, v);
      }
    });
    label.low = low;
    label.lim = nextLim++;
    if (parent) {
      label.parent = parent;
    } else {
      delete label.parent;
    }
    return nextLim;
  }
  function leaveEdge(tree) {
    return tree.edges().find((e2) => tree.edge(e2).cutvalue < 0);
  }
  function enterEdge(t, g, edge) {
    var v = edge.v;
    var w = edge.w;
    if (!g.hasEdge(v, w)) {
      v = edge.w;
      w = edge.v;
    }
    var vLabel = t.node(v);
    var wLabel = t.node(w);
    var tailLabel = vLabel;
    var flip = false;
    if (vLabel.lim > wLabel.lim) {
      tailLabel = wLabel;
      flip = true;
    }
    var candidates = g.edges().filter((edge2) => {
      return flip === isDescendant(t, t.node(edge2.v), tailLabel) && flip !== isDescendant(t, t.node(edge2.w), tailLabel);
    });
    return candidates.reduce((acc, edge2) => {
      if (slack(g, edge2) < slack(g, acc)) {
        return edge2;
      }
      return acc;
    });
  }
  function exchangeEdges(t, g, e2, f2) {
    var v = e2.v;
    var w = e2.w;
    t.removeEdge(v, w);
    t.setEdge(f2.v, f2.w, {});
    initLowLimValues(t);
    initCutValues(t, g);
    updateRanks(t, g);
  }
  function updateRanks(t, g) {
    var root = t.nodes().find((v) => !g.node(v).parent);
    var vs = preorder(t, root);
    vs = vs.slice(1);
    vs.forEach((v) => {
      var parent = t.node(v).parent, edge = g.edge(v, parent), flipped = false;
      if (!edge) {
        edge = g.edge(parent, v);
        flipped = true;
      }
      g.node(v).rank = g.node(parent).rank + (flipped ? edge.minlen : -edge.minlen);
    });
  }
  function isTreeEdge(tree, u, v) {
    return tree.hasEdge(u, v);
  }
  function isDescendant(tree, vLabel, rootLabel) {
    return rootLabel.low <= vLabel.lim && vLabel.lim <= rootLabel.lim;
  }
  return networkSimplex_1;
}
var rank_1;
var hasRequiredRank;
function requireRank() {
  if (hasRequiredRank) return rank_1;
  hasRequiredRank = 1;
  var rankUtil = requireUtil();
  var longestPath = rankUtil.longestPath;
  var feasibleTree = requireFeasibleTree();
  var networkSimplex = requireNetworkSimplex();
  rank_1 = rank;
  function rank(g) {
    var ranker = g.graph().ranker;
    if (ranker instanceof Function) {
      return ranker(g);
    }
    switch (g.graph().ranker) {
      case "network-simplex":
        networkSimplexRanker(g);
        break;
      case "tight-tree":
        tightTreeRanker(g);
        break;
      case "longest-path":
        longestPathRanker(g);
        break;
      case "none":
        break;
      default:
        networkSimplexRanker(g);
    }
  }
  var longestPathRanker = longestPath;
  function tightTreeRanker(g) {
    longestPath(g);
    feasibleTree(g);
  }
  function networkSimplexRanker(g) {
    networkSimplex(g);
  }
  return rank_1;
}
var parentDummyChains_1;
var hasRequiredParentDummyChains;
function requireParentDummyChains() {
  if (hasRequiredParentDummyChains) return parentDummyChains_1;
  hasRequiredParentDummyChains = 1;
  parentDummyChains_1 = parentDummyChains;
  function parentDummyChains(g) {
    let postorderNums = postorder(g);
    g.graph().dummyChains.forEach((v) => {
      let node = g.node(v);
      let edgeObj = node.edgeObj;
      let pathData = findPath(g, postorderNums, edgeObj.v, edgeObj.w);
      let path = pathData.path;
      let lca = pathData.lca;
      let pathIdx = 0;
      let pathV = path[pathIdx];
      let ascending = true;
      while (v !== edgeObj.w) {
        node = g.node(v);
        if (ascending) {
          while ((pathV = path[pathIdx]) !== lca && g.node(pathV).maxRank < node.rank) {
            pathIdx++;
          }
          if (pathV === lca) {
            ascending = false;
          }
        }
        if (!ascending) {
          while (pathIdx < path.length - 1 && g.node(pathV = path[pathIdx + 1]).minRank <= node.rank) {
            pathIdx++;
          }
          pathV = path[pathIdx];
        }
        g.setParent(v, pathV);
        v = g.successors(v)[0];
      }
    });
  }
  function findPath(g, postorderNums, v, w) {
    let vPath = [];
    let wPath = [];
    let low = Math.min(postorderNums[v].low, postorderNums[w].low);
    let lim = Math.max(postorderNums[v].lim, postorderNums[w].lim);
    let parent;
    let lca;
    parent = v;
    do {
      parent = g.parent(parent);
      vPath.push(parent);
    } while (parent && (postorderNums[parent].low > low || lim > postorderNums[parent].lim));
    lca = parent;
    parent = w;
    while ((parent = g.parent(parent)) !== lca) {
      wPath.push(parent);
    }
    return { path: vPath.concat(wPath.reverse()), lca };
  }
  function postorder(g) {
    let result = {};
    let lim = 0;
    function dfs(v) {
      let low = lim;
      g.children(v).forEach(dfs);
      result[v] = { low, lim: lim++ };
    }
    g.children().forEach(dfs);
    return result;
  }
  return parentDummyChains_1;
}
var nestingGraph;
var hasRequiredNestingGraph;
function requireNestingGraph() {
  if (hasRequiredNestingGraph) return nestingGraph;
  hasRequiredNestingGraph = 1;
  let util2 = requireUtil$1();
  nestingGraph = {
    run,
    cleanup
  };
  function run(g) {
    let root = util2.addDummyNode(g, "root", {}, "_root");
    let depths = treeDepths(g);
    let depthsArr = Object.values(depths);
    let height = util2.applyWithChunking(Math.max, depthsArr) - 1;
    let nodeSep = 2 * height + 1;
    g.graph().nestingRoot = root;
    g.edges().forEach((e2) => g.edge(e2).minlen *= nodeSep);
    let weight = sumWeights(g) + 1;
    g.children().forEach((child) => dfs(g, root, nodeSep, weight, height, depths, child));
    g.graph().nodeRankFactor = nodeSep;
  }
  function dfs(g, root, nodeSep, weight, height, depths, v) {
    let children = g.children(v);
    if (!children.length) {
      if (v !== root) {
        g.setEdge(root, v, { weight: 0, minlen: nodeSep });
      }
      return;
    }
    let top = util2.addBorderNode(g, "_bt");
    let bottom = util2.addBorderNode(g, "_bb");
    let label = g.node(v);
    g.setParent(top, v);
    label.borderTop = top;
    g.setParent(bottom, v);
    label.borderBottom = bottom;
    children.forEach((child) => {
      dfs(g, root, nodeSep, weight, height, depths, child);
      let childNode = g.node(child);
      let childTop = childNode.borderTop ? childNode.borderTop : child;
      let childBottom = childNode.borderBottom ? childNode.borderBottom : child;
      let thisWeight = childNode.borderTop ? weight : 2 * weight;
      let minlen = childTop !== childBottom ? 1 : height - depths[v] + 1;
      g.setEdge(top, childTop, {
        weight: thisWeight,
        minlen,
        nestingEdge: true
      });
      g.setEdge(childBottom, bottom, {
        weight: thisWeight,
        minlen,
        nestingEdge: true
      });
    });
    if (!g.parent(v)) {
      g.setEdge(root, top, { weight: 0, minlen: height + depths[v] });
    }
  }
  function treeDepths(g) {
    var depths = {};
    function dfs2(v, depth) {
      var children = g.children(v);
      if (children && children.length) {
        children.forEach((child) => dfs2(child, depth + 1));
      }
      depths[v] = depth;
    }
    g.children().forEach((v) => dfs2(v, 1));
    return depths;
  }
  function sumWeights(g) {
    return g.edges().reduce((acc, e2) => acc + g.edge(e2).weight, 0);
  }
  function cleanup(g) {
    var graphLabel = g.graph();
    g.removeNode(graphLabel.nestingRoot);
    delete graphLabel.nestingRoot;
    g.edges().forEach((e2) => {
      var edge = g.edge(e2);
      if (edge.nestingEdge) {
        g.removeEdge(e2);
      }
    });
  }
  return nestingGraph;
}
var addBorderSegments_1;
var hasRequiredAddBorderSegments;
function requireAddBorderSegments() {
  if (hasRequiredAddBorderSegments) return addBorderSegments_1;
  hasRequiredAddBorderSegments = 1;
  let util2 = requireUtil$1();
  addBorderSegments_1 = addBorderSegments;
  function addBorderSegments(g) {
    function dfs(v) {
      let children = g.children(v);
      let node = g.node(v);
      if (children.length) {
        children.forEach(dfs);
      }
      if (Object.hasOwn(node, "minRank")) {
        node.borderLeft = [];
        node.borderRight = [];
        for (let rank = node.minRank, maxRank = node.maxRank + 1; rank < maxRank; ++rank) {
          addBorderNode(g, "borderLeft", "_bl", v, node, rank);
          addBorderNode(g, "borderRight", "_br", v, node, rank);
        }
      }
    }
    g.children().forEach(dfs);
  }
  function addBorderNode(g, prop, prefix, sg, sgNode, rank) {
    let label = { width: 0, height: 0, rank, borderType: prop };
    let prev = sgNode[prop][rank - 1];
    let curr = util2.addDummyNode(g, "border", label, prefix);
    sgNode[prop][rank] = curr;
    g.setParent(curr, sg);
    if (prev) {
      g.setEdge(prev, curr, { weight: 1 });
    }
  }
  return addBorderSegments_1;
}
var coordinateSystem;
var hasRequiredCoordinateSystem;
function requireCoordinateSystem() {
  if (hasRequiredCoordinateSystem) return coordinateSystem;
  hasRequiredCoordinateSystem = 1;
  coordinateSystem = {
    adjust,
    undo
  };
  function adjust(g) {
    let rankDir = g.graph().rankdir.toLowerCase();
    if (rankDir === "lr" || rankDir === "rl") {
      swapWidthHeight(g);
    }
  }
  function undo(g) {
    let rankDir = g.graph().rankdir.toLowerCase();
    if (rankDir === "bt" || rankDir === "rl") {
      reverseY(g);
    }
    if (rankDir === "lr" || rankDir === "rl") {
      swapXY(g);
      swapWidthHeight(g);
    }
  }
  function swapWidthHeight(g) {
    g.nodes().forEach((v) => swapWidthHeightOne(g.node(v)));
    g.edges().forEach((e2) => swapWidthHeightOne(g.edge(e2)));
  }
  function swapWidthHeightOne(attrs) {
    let w = attrs.width;
    attrs.width = attrs.height;
    attrs.height = w;
  }
  function reverseY(g) {
    g.nodes().forEach((v) => reverseYOne(g.node(v)));
    g.edges().forEach((e2) => {
      let edge = g.edge(e2);
      edge.points.forEach(reverseYOne);
      if (Object.hasOwn(edge, "y")) {
        reverseYOne(edge);
      }
    });
  }
  function reverseYOne(attrs) {
    attrs.y = -attrs.y;
  }
  function swapXY(g) {
    g.nodes().forEach((v) => swapXYOne(g.node(v)));
    g.edges().forEach((e2) => {
      let edge = g.edge(e2);
      edge.points.forEach(swapXYOne);
      if (Object.hasOwn(edge, "x")) {
        swapXYOne(edge);
      }
    });
  }
  function swapXYOne(attrs) {
    let x = attrs.x;
    attrs.x = attrs.y;
    attrs.y = x;
  }
  return coordinateSystem;
}
var initOrder_1;
var hasRequiredInitOrder;
function requireInitOrder() {
  if (hasRequiredInitOrder) return initOrder_1;
  hasRequiredInitOrder = 1;
  let util2 = requireUtil$1();
  initOrder_1 = initOrder;
  function initOrder(g) {
    let visited = {};
    let simpleNodes = g.nodes().filter((v) => !g.children(v).length);
    let simpleNodesRanks = simpleNodes.map((v) => g.node(v).rank);
    let maxRank = util2.applyWithChunking(Math.max, simpleNodesRanks);
    let layers = util2.range(maxRank + 1).map(() => []);
    function dfs(v) {
      if (visited[v]) return;
      visited[v] = true;
      let node = g.node(v);
      layers[node.rank].push(v);
      g.successors(v).forEach(dfs);
    }
    let orderedVs = simpleNodes.sort((a, b) => g.node(a).rank - g.node(b).rank);
    orderedVs.forEach(dfs);
    return layers;
  }
  return initOrder_1;
}
var crossCount_1;
var hasRequiredCrossCount;
function requireCrossCount() {
  if (hasRequiredCrossCount) return crossCount_1;
  hasRequiredCrossCount = 1;
  let zipObject = requireUtil$1().zipObject;
  crossCount_1 = crossCount;
  function crossCount(g, layering) {
    let cc = 0;
    for (let i = 1; i < layering.length; ++i) {
      cc += twoLayerCrossCount(g, layering[i - 1], layering[i]);
    }
    return cc;
  }
  function twoLayerCrossCount(g, northLayer, southLayer) {
    let southPos = zipObject(southLayer, southLayer.map((v, i) => i));
    let southEntries = northLayer.flatMap((v) => {
      return g.outEdges(v).map((e2) => {
        return { pos: southPos[e2.w], weight: g.edge(e2).weight };
      }).sort((a, b) => a.pos - b.pos);
    });
    let firstIndex = 1;
    while (firstIndex < southLayer.length) firstIndex <<= 1;
    let treeSize = 2 * firstIndex - 1;
    firstIndex -= 1;
    let tree = new Array(treeSize).fill(0);
    let cc = 0;
    southEntries.forEach((entry) => {
      let index = entry.pos + firstIndex;
      tree[index] += entry.weight;
      let weightSum = 0;
      while (index > 0) {
        if (index % 2) {
          weightSum += tree[index + 1];
        }
        index = index - 1 >> 1;
        tree[index] += entry.weight;
      }
      cc += entry.weight * weightSum;
    });
    return cc;
  }
  return crossCount_1;
}
var barycenter_1;
var hasRequiredBarycenter;
function requireBarycenter() {
  if (hasRequiredBarycenter) return barycenter_1;
  hasRequiredBarycenter = 1;
  barycenter_1 = barycenter;
  function barycenter(g, movable = []) {
    return movable.map((v) => {
      let inV = g.inEdges(v);
      if (!inV.length) {
        return { v };
      } else {
        let result = inV.reduce((acc, e2) => {
          let edge = g.edge(e2), nodeU = g.node(e2.v);
          return {
            sum: acc.sum + edge.weight * nodeU.order,
            weight: acc.weight + edge.weight
          };
        }, { sum: 0, weight: 0 });
        return {
          v,
          barycenter: result.sum / result.weight,
          weight: result.weight
        };
      }
    });
  }
  return barycenter_1;
}
var resolveConflicts_1;
var hasRequiredResolveConflicts;
function requireResolveConflicts() {
  if (hasRequiredResolveConflicts) return resolveConflicts_1;
  hasRequiredResolveConflicts = 1;
  let util2 = requireUtil$1();
  resolveConflicts_1 = resolveConflicts;
  function resolveConflicts(entries, cg) {
    let mappedEntries = {};
    entries.forEach((entry, i) => {
      let tmp = mappedEntries[entry.v] = {
        indegree: 0,
        "in": [],
        out: [],
        vs: [entry.v],
        i
      };
      if (entry.barycenter !== void 0) {
        tmp.barycenter = entry.barycenter;
        tmp.weight = entry.weight;
      }
    });
    cg.edges().forEach((e2) => {
      let entryV = mappedEntries[e2.v];
      let entryW = mappedEntries[e2.w];
      if (entryV !== void 0 && entryW !== void 0) {
        entryW.indegree++;
        entryV.out.push(mappedEntries[e2.w]);
      }
    });
    let sourceSet = Object.values(mappedEntries).filter((entry) => !entry.indegree);
    return doResolveConflicts(sourceSet);
  }
  function doResolveConflicts(sourceSet) {
    let entries = [];
    function handleIn(vEntry) {
      return (uEntry) => {
        if (uEntry.merged) {
          return;
        }
        if (uEntry.barycenter === void 0 || vEntry.barycenter === void 0 || uEntry.barycenter >= vEntry.barycenter) {
          mergeEntries(vEntry, uEntry);
        }
      };
    }
    function handleOut(vEntry) {
      return (wEntry) => {
        wEntry["in"].push(vEntry);
        if (--wEntry.indegree === 0) {
          sourceSet.push(wEntry);
        }
      };
    }
    while (sourceSet.length) {
      let entry = sourceSet.pop();
      entries.push(entry);
      entry["in"].reverse().forEach(handleIn(entry));
      entry.out.forEach(handleOut(entry));
    }
    return entries.filter((entry) => !entry.merged).map((entry) => {
      return util2.pick(entry, ["vs", "i", "barycenter", "weight"]);
    });
  }
  function mergeEntries(target, source) {
    let sum = 0;
    let weight = 0;
    if (target.weight) {
      sum += target.barycenter * target.weight;
      weight += target.weight;
    }
    if (source.weight) {
      sum += source.barycenter * source.weight;
      weight += source.weight;
    }
    target.vs = source.vs.concat(target.vs);
    target.barycenter = sum / weight;
    target.weight = weight;
    target.i = Math.min(source.i, target.i);
    source.merged = true;
  }
  return resolveConflicts_1;
}
var sort_1;
var hasRequiredSort;
function requireSort() {
  if (hasRequiredSort) return sort_1;
  hasRequiredSort = 1;
  let util2 = requireUtil$1();
  sort_1 = sort;
  function sort(entries, biasRight) {
    let parts = util2.partition(entries, (entry) => {
      return Object.hasOwn(entry, "barycenter");
    });
    let sortable = parts.lhs, unsortable = parts.rhs.sort((a, b) => b.i - a.i), vs = [], sum = 0, weight = 0, vsIndex = 0;
    sortable.sort(compareWithBias(!!biasRight));
    vsIndex = consumeUnsortable(vs, unsortable, vsIndex);
    sortable.forEach((entry) => {
      vsIndex += entry.vs.length;
      vs.push(entry.vs);
      sum += entry.barycenter * entry.weight;
      weight += entry.weight;
      vsIndex = consumeUnsortable(vs, unsortable, vsIndex);
    });
    let result = { vs: vs.flat(true) };
    if (weight) {
      result.barycenter = sum / weight;
      result.weight = weight;
    }
    return result;
  }
  function consumeUnsortable(vs, unsortable, index) {
    let last;
    while (unsortable.length && (last = unsortable[unsortable.length - 1]).i <= index) {
      unsortable.pop();
      vs.push(last.vs);
      index++;
    }
    return index;
  }
  function compareWithBias(bias2) {
    return (entryV, entryW) => {
      if (entryV.barycenter < entryW.barycenter) {
        return -1;
      } else if (entryV.barycenter > entryW.barycenter) {
        return 1;
      }
      return !bias2 ? entryV.i - entryW.i : entryW.i - entryV.i;
    };
  }
  return sort_1;
}
var sortSubgraph_1;
var hasRequiredSortSubgraph;
function requireSortSubgraph() {
  if (hasRequiredSortSubgraph) return sortSubgraph_1;
  hasRequiredSortSubgraph = 1;
  let barycenter = requireBarycenter();
  let resolveConflicts = requireResolveConflicts();
  let sort = requireSort();
  sortSubgraph_1 = sortSubgraph;
  function sortSubgraph(g, v, cg, biasRight) {
    let movable = g.children(v);
    let node = g.node(v);
    let bl = node ? node.borderLeft : void 0;
    let br = node ? node.borderRight : void 0;
    let subgraphs = {};
    if (bl) {
      movable = movable.filter((w) => w !== bl && w !== br);
    }
    let barycenters = barycenter(g, movable);
    barycenters.forEach((entry) => {
      if (g.children(entry.v).length) {
        let subgraphResult = sortSubgraph(g, entry.v, cg, biasRight);
        subgraphs[entry.v] = subgraphResult;
        if (Object.hasOwn(subgraphResult, "barycenter")) {
          mergeBarycenters(entry, subgraphResult);
        }
      }
    });
    let entries = resolveConflicts(barycenters, cg);
    expandSubgraphs(entries, subgraphs);
    let result = sort(entries, biasRight);
    if (bl) {
      result.vs = [bl, result.vs, br].flat(true);
      if (g.predecessors(bl).length) {
        let blPred = g.node(g.predecessors(bl)[0]), brPred = g.node(g.predecessors(br)[0]);
        if (!Object.hasOwn(result, "barycenter")) {
          result.barycenter = 0;
          result.weight = 0;
        }
        result.barycenter = (result.barycenter * result.weight + blPred.order + brPred.order) / (result.weight + 2);
        result.weight += 2;
      }
    }
    return result;
  }
  function expandSubgraphs(entries, subgraphs) {
    entries.forEach((entry) => {
      entry.vs = entry.vs.flatMap((v) => {
        if (subgraphs[v]) {
          return subgraphs[v].vs;
        }
        return v;
      });
    });
  }
  function mergeBarycenters(target, other) {
    if (target.barycenter !== void 0) {
      target.barycenter = (target.barycenter * target.weight + other.barycenter * other.weight) / (target.weight + other.weight);
      target.weight += other.weight;
    } else {
      target.barycenter = other.barycenter;
      target.weight = other.weight;
    }
  }
  return sortSubgraph_1;
}
var buildLayerGraph_1;
var hasRequiredBuildLayerGraph;
function requireBuildLayerGraph() {
  if (hasRequiredBuildLayerGraph) return buildLayerGraph_1;
  hasRequiredBuildLayerGraph = 1;
  let Graph = requireGraphlib().Graph;
  let util2 = requireUtil$1();
  buildLayerGraph_1 = buildLayerGraph;
  function buildLayerGraph(g, rank, relationship) {
    let root = createRootNode(g), result = new Graph({ compound: true }).setGraph({ root }).setDefaultNodeLabel((v) => g.node(v));
    g.nodes().forEach((v) => {
      let node = g.node(v), parent = g.parent(v);
      if (node.rank === rank || node.minRank <= rank && rank <= node.maxRank) {
        result.setNode(v);
        result.setParent(v, parent || root);
        g[relationship](v).forEach((e2) => {
          let u = e2.v === v ? e2.w : e2.v, edge = result.edge(u, v), weight = edge !== void 0 ? edge.weight : 0;
          result.setEdge(u, v, { weight: g.edge(e2).weight + weight });
        });
        if (Object.hasOwn(node, "minRank")) {
          result.setNode(v, {
            borderLeft: node.borderLeft[rank],
            borderRight: node.borderRight[rank]
          });
        }
      }
    });
    return result;
  }
  function createRootNode(g) {
    var v;
    while (g.hasNode(v = util2.uniqueId("_root"))) ;
    return v;
  }
  return buildLayerGraph_1;
}
var addSubgraphConstraints_1;
var hasRequiredAddSubgraphConstraints;
function requireAddSubgraphConstraints() {
  if (hasRequiredAddSubgraphConstraints) return addSubgraphConstraints_1;
  hasRequiredAddSubgraphConstraints = 1;
  addSubgraphConstraints_1 = addSubgraphConstraints;
  function addSubgraphConstraints(g, cg, vs) {
    let prev = {}, rootPrev;
    vs.forEach((v) => {
      let child = g.parent(v), parent, prevChild;
      while (child) {
        parent = g.parent(child);
        if (parent) {
          prevChild = prev[parent];
          prev[parent] = child;
        } else {
          prevChild = rootPrev;
          rootPrev = child;
        }
        if (prevChild && prevChild !== child) {
          cg.setEdge(prevChild, child);
          return;
        }
        child = parent;
      }
    });
  }
  return addSubgraphConstraints_1;
}
var order_1;
var hasRequiredOrder;
function requireOrder() {
  if (hasRequiredOrder) return order_1;
  hasRequiredOrder = 1;
  let initOrder = requireInitOrder();
  let crossCount = requireCrossCount();
  let sortSubgraph = requireSortSubgraph();
  let buildLayerGraph = requireBuildLayerGraph();
  let addSubgraphConstraints = requireAddSubgraphConstraints();
  let Graph = requireGraphlib().Graph;
  let util2 = requireUtil$1();
  order_1 = order;
  function order(g, opts) {
    if (opts && typeof opts.customOrder === "function") {
      opts.customOrder(g, order);
      return;
    }
    let maxRank = util2.maxRank(g), downLayerGraphs = buildLayerGraphs(g, util2.range(1, maxRank + 1), "inEdges"), upLayerGraphs = buildLayerGraphs(g, util2.range(maxRank - 1, -1, -1), "outEdges");
    let layering = initOrder(g);
    assignOrder(g, layering);
    if (opts && opts.disableOptimalOrderHeuristic) {
      return;
    }
    let bestCC = Number.POSITIVE_INFINITY, best;
    for (let i = 0, lastBest = 0; lastBest < 4; ++i, ++lastBest) {
      sweepLayerGraphs(i % 2 ? downLayerGraphs : upLayerGraphs, i % 4 >= 2);
      layering = util2.buildLayerMatrix(g);
      let cc = crossCount(g, layering);
      if (cc < bestCC) {
        lastBest = 0;
        best = Object.assign({}, layering);
        bestCC = cc;
      }
    }
    assignOrder(g, best);
  }
  function buildLayerGraphs(g, ranks, relationship) {
    return ranks.map(function(rank) {
      return buildLayerGraph(g, rank, relationship);
    });
  }
  function sweepLayerGraphs(layerGraphs, biasRight) {
    let cg = new Graph();
    layerGraphs.forEach(function(lg) {
      let root = lg.graph().root;
      let sorted = sortSubgraph(lg, root, cg, biasRight);
      sorted.vs.forEach((v, i) => lg.node(v).order = i);
      addSubgraphConstraints(lg, cg, sorted.vs);
    });
  }
  function assignOrder(g, layering) {
    Object.values(layering).forEach((layer) => layer.forEach((v, i) => g.node(v).order = i));
  }
  return order_1;
}
var bk;
var hasRequiredBk;
function requireBk() {
  if (hasRequiredBk) return bk;
  hasRequiredBk = 1;
  let Graph = requireGraphlib().Graph;
  let util2 = requireUtil$1();
  bk = {
    positionX,
    findType1Conflicts,
    findType2Conflicts,
    addConflict,
    hasConflict,
    verticalAlignment,
    horizontalCompaction,
    alignCoordinates,
    findSmallestWidthAlignment,
    balance
  };
  function findType1Conflicts(g, layering) {
    let conflicts = {};
    function visitLayer(prevLayer, layer) {
      let k0 = 0, scanPos = 0, prevLayerLength = prevLayer.length, lastNode = layer[layer.length - 1];
      layer.forEach((v, i) => {
        let w = findOtherInnerSegmentNode(g, v), k1 = w ? g.node(w).order : prevLayerLength;
        if (w || v === lastNode) {
          layer.slice(scanPos, i + 1).forEach((scanNode) => {
            g.predecessors(scanNode).forEach((u) => {
              let uLabel = g.node(u), uPos = uLabel.order;
              if ((uPos < k0 || k1 < uPos) && !(uLabel.dummy && g.node(scanNode).dummy)) {
                addConflict(conflicts, u, scanNode);
              }
            });
          });
          scanPos = i + 1;
          k0 = k1;
        }
      });
      return layer;
    }
    layering.length && layering.reduce(visitLayer);
    return conflicts;
  }
  function findType2Conflicts(g, layering) {
    let conflicts = {};
    function scan(south, southPos, southEnd, prevNorthBorder, nextNorthBorder) {
      let v;
      util2.range(southPos, southEnd).forEach((i) => {
        v = south[i];
        if (g.node(v).dummy) {
          g.predecessors(v).forEach((u) => {
            let uNode = g.node(u);
            if (uNode.dummy && (uNode.order < prevNorthBorder || uNode.order > nextNorthBorder)) {
              addConflict(conflicts, u, v);
            }
          });
        }
      });
    }
    function visitLayer(north, south) {
      let prevNorthPos = -1, nextNorthPos, southPos = 0;
      south.forEach((v, southLookahead) => {
        if (g.node(v).dummy === "border") {
          let predecessors = g.predecessors(v);
          if (predecessors.length) {
            nextNorthPos = g.node(predecessors[0]).order;
            scan(south, southPos, southLookahead, prevNorthPos, nextNorthPos);
            southPos = southLookahead;
            prevNorthPos = nextNorthPos;
          }
        }
        scan(south, southPos, south.length, nextNorthPos, north.length);
      });
      return south;
    }
    layering.length && layering.reduce(visitLayer);
    return conflicts;
  }
  function findOtherInnerSegmentNode(g, v) {
    if (g.node(v).dummy) {
      return g.predecessors(v).find((u) => g.node(u).dummy);
    }
  }
  function addConflict(conflicts, v, w) {
    if (v > w) {
      let tmp = v;
      v = w;
      w = tmp;
    }
    let conflictsV = conflicts[v];
    if (!conflictsV) {
      conflicts[v] = conflictsV = {};
    }
    conflictsV[w] = true;
  }
  function hasConflict(conflicts, v, w) {
    if (v > w) {
      let tmp = v;
      v = w;
      w = tmp;
    }
    return !!conflicts[v] && Object.hasOwn(conflicts[v], w);
  }
  function verticalAlignment(g, layering, conflicts, neighborFn) {
    let root = {}, align = {}, pos = {};
    layering.forEach((layer) => {
      layer.forEach((v, order) => {
        root[v] = v;
        align[v] = v;
        pos[v] = order;
      });
    });
    layering.forEach((layer) => {
      let prevIdx = -1;
      layer.forEach((v) => {
        let ws = neighborFn(v);
        if (ws.length) {
          ws = ws.sort((a, b) => pos[a] - pos[b]);
          let mp = (ws.length - 1) / 2;
          for (let i = Math.floor(mp), il = Math.ceil(mp); i <= il; ++i) {
            let w = ws[i];
            if (align[v] === v && prevIdx < pos[w] && !hasConflict(conflicts, v, w)) {
              align[w] = v;
              align[v] = root[v] = root[w];
              prevIdx = pos[w];
            }
          }
        }
      });
    });
    return { root, align };
  }
  function horizontalCompaction(g, layering, root, align, reverseSep) {
    let xs = {}, blockG = buildBlockGraph(g, layering, root, reverseSep), borderType = reverseSep ? "borderLeft" : "borderRight";
    function iterate(setXsFunc, nextNodesFunc) {
      let stack = blockG.nodes();
      let elem = stack.pop();
      let visited = {};
      while (elem) {
        if (visited[elem]) {
          setXsFunc(elem);
        } else {
          visited[elem] = true;
          stack.push(elem);
          stack = stack.concat(nextNodesFunc(elem));
        }
        elem = stack.pop();
      }
    }
    function pass1(elem) {
      xs[elem] = blockG.inEdges(elem).reduce((acc, e2) => {
        return Math.max(acc, xs[e2.v] + blockG.edge(e2));
      }, 0);
    }
    function pass2(elem) {
      let min = blockG.outEdges(elem).reduce((acc, e2) => {
        return Math.min(acc, xs[e2.w] - blockG.edge(e2));
      }, Number.POSITIVE_INFINITY);
      let node = g.node(elem);
      if (min !== Number.POSITIVE_INFINITY && node.borderType !== borderType) {
        xs[elem] = Math.max(xs[elem], min);
      }
    }
    iterate(pass1, blockG.predecessors.bind(blockG));
    iterate(pass2, blockG.successors.bind(blockG));
    Object.keys(align).forEach((v) => xs[v] = xs[root[v]]);
    return xs;
  }
  function buildBlockGraph(g, layering, root, reverseSep) {
    let blockGraph = new Graph(), graphLabel = g.graph(), sepFn = sep(graphLabel.nodesep, graphLabel.edgesep, reverseSep);
    layering.forEach((layer) => {
      let u;
      layer.forEach((v) => {
        let vRoot = root[v];
        blockGraph.setNode(vRoot);
        if (u) {
          var uRoot = root[u], prevMax = blockGraph.edge(uRoot, vRoot);
          blockGraph.setEdge(uRoot, vRoot, Math.max(sepFn(g, v, u), prevMax || 0));
        }
        u = v;
      });
    });
    return blockGraph;
  }
  function findSmallestWidthAlignment(g, xss) {
    return Object.values(xss).reduce((currentMinAndXs, xs) => {
      let max = Number.NEGATIVE_INFINITY;
      let min = Number.POSITIVE_INFINITY;
      Object.entries(xs).forEach(([v, x]) => {
        let halfWidth = width(g, v) / 2;
        max = Math.max(x + halfWidth, max);
        min = Math.min(x - halfWidth, min);
      });
      const newMin = max - min;
      if (newMin < currentMinAndXs[0]) {
        currentMinAndXs = [newMin, xs];
      }
      return currentMinAndXs;
    }, [Number.POSITIVE_INFINITY, null])[1];
  }
  function alignCoordinates(xss, alignTo) {
    let alignToVals = Object.values(alignTo), alignToMin = util2.applyWithChunking(Math.min, alignToVals), alignToMax = util2.applyWithChunking(Math.max, alignToVals);
    ["u", "d"].forEach((vert) => {
      ["l", "r"].forEach((horiz) => {
        let alignment = vert + horiz, xs = xss[alignment];
        if (xs === alignTo) return;
        let xsVals = Object.values(xs);
        let delta = alignToMin - util2.applyWithChunking(Math.min, xsVals);
        if (horiz !== "l") {
          delta = alignToMax - util2.applyWithChunking(Math.max, xsVals);
        }
        if (delta) {
          xss[alignment] = util2.mapValues(xs, (x) => x + delta);
        }
      });
    });
  }
  function balance(xss, align) {
    return util2.mapValues(xss.ul, (num2, v) => {
      if (align) {
        return xss[align.toLowerCase()][v];
      } else {
        let xs = Object.values(xss).map((xs2) => xs2[v]).sort((a, b) => a - b);
        return (xs[1] + xs[2]) / 2;
      }
    });
  }
  function positionX(g) {
    let layering = util2.buildLayerMatrix(g);
    let conflicts = Object.assign(
      findType1Conflicts(g, layering),
      findType2Conflicts(g, layering)
    );
    let xss = {};
    let adjustedLayering;
    ["u", "d"].forEach((vert) => {
      adjustedLayering = vert === "u" ? layering : Object.values(layering).reverse();
      ["l", "r"].forEach((horiz) => {
        if (horiz === "r") {
          adjustedLayering = adjustedLayering.map((inner) => {
            return Object.values(inner).reverse();
          });
        }
        let neighborFn = (vert === "u" ? g.predecessors : g.successors).bind(g);
        let align = verticalAlignment(g, adjustedLayering, conflicts, neighborFn);
        let xs = horizontalCompaction(
          g,
          adjustedLayering,
          align.root,
          align.align,
          horiz === "r"
        );
        if (horiz === "r") {
          xs = util2.mapValues(xs, (x) => -x);
        }
        xss[vert + horiz] = xs;
      });
    });
    let smallestWidth = findSmallestWidthAlignment(g, xss);
    alignCoordinates(xss, smallestWidth);
    return balance(xss, g.graph().align);
  }
  function sep(nodeSep, edgeSep, reverseSep) {
    return (g, v, w) => {
      let vLabel = g.node(v);
      let wLabel = g.node(w);
      let sum = 0;
      let delta;
      sum += vLabel.width / 2;
      if (Object.hasOwn(vLabel, "labelpos")) {
        switch (vLabel.labelpos.toLowerCase()) {
          case "l":
            delta = -vLabel.width / 2;
            break;
          case "r":
            delta = vLabel.width / 2;
            break;
        }
      }
      if (delta) {
        sum += reverseSep ? delta : -delta;
      }
      delta = 0;
      sum += (vLabel.dummy ? edgeSep : nodeSep) / 2;
      sum += (wLabel.dummy ? edgeSep : nodeSep) / 2;
      sum += wLabel.width / 2;
      if (Object.hasOwn(wLabel, "labelpos")) {
        switch (wLabel.labelpos.toLowerCase()) {
          case "l":
            delta = wLabel.width / 2;
            break;
          case "r":
            delta = -wLabel.width / 2;
            break;
        }
      }
      if (delta) {
        sum += reverseSep ? delta : -delta;
      }
      delta = 0;
      return sum;
    };
  }
  function width(g, v) {
    return g.node(v).width;
  }
  return bk;
}
var position_1;
var hasRequiredPosition;
function requirePosition() {
  if (hasRequiredPosition) return position_1;
  hasRequiredPosition = 1;
  let util2 = requireUtil$1();
  let positionX = requireBk().positionX;
  position_1 = position;
  function position(g) {
    g = util2.asNonCompoundGraph(g);
    positionY(g);
    Object.entries(positionX(g)).forEach(([v, x]) => g.node(v).x = x);
  }
  function positionY(g) {
    let layering = util2.buildLayerMatrix(g);
    let rankSep = g.graph().ranksep;
    let prevY = 0;
    layering.forEach((layer) => {
      const maxHeight = layer.reduce((acc, v) => {
        const height = g.node(v).height;
        if (acc > height) {
          return acc;
        } else {
          return height;
        }
      }, 0);
      layer.forEach((v) => g.node(v).y = prevY + maxHeight / 2);
      prevY += maxHeight + rankSep;
    });
  }
  return position_1;
}
var layout_1;
var hasRequiredLayout;
function requireLayout() {
  if (hasRequiredLayout) return layout_1;
  hasRequiredLayout = 1;
  let acyclic2 = requireAcyclic();
  let normalize2 = requireNormalize();
  let rank = requireRank();
  let normalizeRanks = requireUtil$1().normalizeRanks;
  let parentDummyChains = requireParentDummyChains();
  let removeEmptyRanks = requireUtil$1().removeEmptyRanks;
  let nestingGraph2 = requireNestingGraph();
  let addBorderSegments = requireAddBorderSegments();
  let coordinateSystem2 = requireCoordinateSystem();
  let order = requireOrder();
  let position = requirePosition();
  let util2 = requireUtil$1();
  let Graph = requireGraphlib().Graph;
  layout_1 = layout;
  function layout(g, opts) {
    let time = opts && opts.debugTiming ? util2.time : util2.notime;
    time("layout", () => {
      let layoutGraph = time("  buildLayoutGraph", () => buildLayoutGraph(g));
      time("  runLayout", () => runLayout(layoutGraph, time, opts));
      time("  updateInputGraph", () => updateInputGraph(g, layoutGraph));
    });
  }
  function runLayout(g, time, opts) {
    time("    makeSpaceForEdgeLabels", () => makeSpaceForEdgeLabels(g));
    time("    removeSelfEdges", () => removeSelfEdges(g));
    time("    acyclic", () => acyclic2.run(g));
    time("    nestingGraph.run", () => nestingGraph2.run(g));
    time("    rank", () => rank(util2.asNonCompoundGraph(g)));
    time("    injectEdgeLabelProxies", () => injectEdgeLabelProxies(g));
    time("    removeEmptyRanks", () => removeEmptyRanks(g));
    time("    nestingGraph.cleanup", () => nestingGraph2.cleanup(g));
    time("    normalizeRanks", () => normalizeRanks(g));
    time("    assignRankMinMax", () => assignRankMinMax(g));
    time("    removeEdgeLabelProxies", () => removeEdgeLabelProxies(g));
    time("    normalize.run", () => normalize2.run(g));
    time("    parentDummyChains", () => parentDummyChains(g));
    time("    addBorderSegments", () => addBorderSegments(g));
    time("    order", () => order(g, opts));
    time("    insertSelfEdges", () => insertSelfEdges(g));
    time("    adjustCoordinateSystem", () => coordinateSystem2.adjust(g));
    time("    position", () => position(g));
    time("    positionSelfEdges", () => positionSelfEdges(g));
    time("    removeBorderNodes", () => removeBorderNodes(g));
    time("    normalize.undo", () => normalize2.undo(g));
    time("    fixupEdgeLabelCoords", () => fixupEdgeLabelCoords(g));
    time("    undoCoordinateSystem", () => coordinateSystem2.undo(g));
    time("    translateGraph", () => translateGraph(g));
    time("    assignNodeIntersects", () => assignNodeIntersects(g));
    time("    reversePoints", () => reversePointsForReversedEdges(g));
    time("    acyclic.undo", () => acyclic2.undo(g));
  }
  function updateInputGraph(inputGraph, layoutGraph) {
    inputGraph.nodes().forEach((v) => {
      let inputLabel = inputGraph.node(v);
      let layoutLabel = layoutGraph.node(v);
      if (inputLabel) {
        inputLabel.x = layoutLabel.x;
        inputLabel.y = layoutLabel.y;
        inputLabel.rank = layoutLabel.rank;
        if (layoutGraph.children(v).length) {
          inputLabel.width = layoutLabel.width;
          inputLabel.height = layoutLabel.height;
        }
      }
    });
    inputGraph.edges().forEach((e2) => {
      let inputLabel = inputGraph.edge(e2);
      let layoutLabel = layoutGraph.edge(e2);
      inputLabel.points = layoutLabel.points;
      if (Object.hasOwn(layoutLabel, "x")) {
        inputLabel.x = layoutLabel.x;
        inputLabel.y = layoutLabel.y;
      }
    });
    inputGraph.graph().width = layoutGraph.graph().width;
    inputGraph.graph().height = layoutGraph.graph().height;
  }
  let graphNumAttrs = ["nodesep", "edgesep", "ranksep", "marginx", "marginy"];
  let graphDefaults = { ranksep: 50, edgesep: 20, nodesep: 50, rankdir: "tb" };
  let graphAttrs = ["acyclicer", "ranker", "rankdir", "align"];
  let nodeNumAttrs = ["width", "height", "rank"];
  let nodeDefaults = { width: 0, height: 0 };
  let edgeNumAttrs = ["minlen", "weight", "width", "height", "labeloffset"];
  let edgeDefaults = {
    minlen: 1,
    weight: 1,
    width: 0,
    height: 0,
    labeloffset: 10,
    labelpos: "r"
  };
  let edgeAttrs = ["labelpos"];
  function buildLayoutGraph(inputGraph) {
    let g = new Graph({ multigraph: true, compound: true });
    let graph2 = canonicalize(inputGraph.graph());
    g.setGraph(Object.assign(
      {},
      graphDefaults,
      selectNumberAttrs(graph2, graphNumAttrs),
      util2.pick(graph2, graphAttrs)
    ));
    inputGraph.nodes().forEach((v) => {
      let node = canonicalize(inputGraph.node(v));
      const newNode = selectNumberAttrs(node, nodeNumAttrs);
      Object.keys(nodeDefaults).forEach((k2) => {
        if (newNode[k2] === void 0) {
          newNode[k2] = nodeDefaults[k2];
        }
      });
      g.setNode(v, newNode);
      g.setParent(v, inputGraph.parent(v));
    });
    inputGraph.edges().forEach((e2) => {
      let edge = canonicalize(inputGraph.edge(e2));
      g.setEdge(e2, Object.assign(
        {},
        edgeDefaults,
        selectNumberAttrs(edge, edgeNumAttrs),
        util2.pick(edge, edgeAttrs)
      ));
    });
    return g;
  }
  function makeSpaceForEdgeLabels(g) {
    let graph2 = g.graph();
    graph2.ranksep /= 2;
    g.edges().forEach((e2) => {
      let edge = g.edge(e2);
      edge.minlen *= 2;
      if (edge.labelpos.toLowerCase() !== "c") {
        if (graph2.rankdir === "TB" || graph2.rankdir === "BT") {
          edge.width += edge.labeloffset;
        } else {
          edge.height += edge.labeloffset;
        }
      }
    });
  }
  function injectEdgeLabelProxies(g) {
    g.edges().forEach((e2) => {
      let edge = g.edge(e2);
      if (edge.width && edge.height) {
        let v = g.node(e2.v);
        let w = g.node(e2.w);
        let label = { rank: (w.rank - v.rank) / 2 + v.rank, e: e2 };
        util2.addDummyNode(g, "edge-proxy", label, "_ep");
      }
    });
  }
  function assignRankMinMax(g) {
    let maxRank = 0;
    g.nodes().forEach((v) => {
      let node = g.node(v);
      if (node.borderTop) {
        node.minRank = g.node(node.borderTop).rank;
        node.maxRank = g.node(node.borderBottom).rank;
        maxRank = Math.max(maxRank, node.maxRank);
      }
    });
    g.graph().maxRank = maxRank;
  }
  function removeEdgeLabelProxies(g) {
    g.nodes().forEach((v) => {
      let node = g.node(v);
      if (node.dummy === "edge-proxy") {
        g.edge(node.e).labelRank = node.rank;
        g.removeNode(v);
      }
    });
  }
  function translateGraph(g) {
    let minX = Number.POSITIVE_INFINITY;
    let maxX = 0;
    let minY = Number.POSITIVE_INFINITY;
    let maxY = 0;
    let graphLabel = g.graph();
    let marginX = graphLabel.marginx || 0;
    let marginY = graphLabel.marginy || 0;
    function getExtremes(attrs) {
      let x = attrs.x;
      let y = attrs.y;
      let w = attrs.width;
      let h = attrs.height;
      minX = Math.min(minX, x - w / 2);
      maxX = Math.max(maxX, x + w / 2);
      minY = Math.min(minY, y - h / 2);
      maxY = Math.max(maxY, y + h / 2);
    }
    g.nodes().forEach((v) => getExtremes(g.node(v)));
    g.edges().forEach((e2) => {
      let edge = g.edge(e2);
      if (Object.hasOwn(edge, "x")) {
        getExtremes(edge);
      }
    });
    minX -= marginX;
    minY -= marginY;
    g.nodes().forEach((v) => {
      let node = g.node(v);
      node.x -= minX;
      node.y -= minY;
    });
    g.edges().forEach((e2) => {
      let edge = g.edge(e2);
      edge.points.forEach((p2) => {
        p2.x -= minX;
        p2.y -= minY;
      });
      if (Object.hasOwn(edge, "x")) {
        edge.x -= minX;
      }
      if (Object.hasOwn(edge, "y")) {
        edge.y -= minY;
      }
    });
    graphLabel.width = maxX - minX + marginX;
    graphLabel.height = maxY - minY + marginY;
  }
  function assignNodeIntersects(g) {
    g.edges().forEach((e2) => {
      let edge = g.edge(e2);
      let nodeV = g.node(e2.v);
      let nodeW = g.node(e2.w);
      let p1, p2;
      if (!edge.points) {
        edge.points = [];
        p1 = nodeW;
        p2 = nodeV;
      } else {
        p1 = edge.points[0];
        p2 = edge.points[edge.points.length - 1];
      }
      edge.points.unshift(util2.intersectRect(nodeV, p1));
      edge.points.push(util2.intersectRect(nodeW, p2));
    });
  }
  function fixupEdgeLabelCoords(g) {
    g.edges().forEach((e2) => {
      let edge = g.edge(e2);
      if (Object.hasOwn(edge, "x")) {
        if (edge.labelpos === "l" || edge.labelpos === "r") {
          edge.width -= edge.labeloffset;
        }
        switch (edge.labelpos) {
          case "l":
            edge.x -= edge.width / 2 + edge.labeloffset;
            break;
          case "r":
            edge.x += edge.width / 2 + edge.labeloffset;
            break;
        }
      }
    });
  }
  function reversePointsForReversedEdges(g) {
    g.edges().forEach((e2) => {
      let edge = g.edge(e2);
      if (edge.reversed) {
        edge.points.reverse();
      }
    });
  }
  function removeBorderNodes(g) {
    g.nodes().forEach((v) => {
      if (g.children(v).length) {
        let node = g.node(v);
        let t = g.node(node.borderTop);
        let b = g.node(node.borderBottom);
        let l = g.node(node.borderLeft[node.borderLeft.length - 1]);
        let r = g.node(node.borderRight[node.borderRight.length - 1]);
        node.width = Math.abs(r.x - l.x);
        node.height = Math.abs(b.y - t.y);
        node.x = l.x + node.width / 2;
        node.y = t.y + node.height / 2;
      }
    });
    g.nodes().forEach((v) => {
      if (g.node(v).dummy === "border") {
        g.removeNode(v);
      }
    });
  }
  function removeSelfEdges(g) {
    g.edges().forEach((e2) => {
      if (e2.v === e2.w) {
        var node = g.node(e2.v);
        if (!node.selfEdges) {
          node.selfEdges = [];
        }
        node.selfEdges.push({ e: e2, label: g.edge(e2) });
        g.removeEdge(e2);
      }
    });
  }
  function insertSelfEdges(g) {
    var layers = util2.buildLayerMatrix(g);
    layers.forEach((layer) => {
      var orderShift = 0;
      layer.forEach((v, i) => {
        var node = g.node(v);
        node.order = i + orderShift;
        (node.selfEdges || []).forEach((selfEdge) => {
          util2.addDummyNode(g, "selfedge", {
            width: selfEdge.label.width,
            height: selfEdge.label.height,
            rank: node.rank,
            order: i + ++orderShift,
            e: selfEdge.e,
            label: selfEdge.label
          }, "_se");
        });
        delete node.selfEdges;
      });
    });
  }
  function positionSelfEdges(g) {
    g.nodes().forEach((v) => {
      var node = g.node(v);
      if (node.dummy === "selfedge") {
        var selfNode = g.node(node.e.v);
        var x = selfNode.x + selfNode.width / 2;
        var y = selfNode.y;
        var dx = node.x - x;
        var dy = selfNode.height / 2;
        g.setEdge(node.e, node.label);
        g.removeNode(v);
        node.label.points = [
          { x: x + 2 * dx / 3, y: y - dy },
          { x: x + 5 * dx / 6, y: y - dy },
          { x: x + dx, y },
          { x: x + 5 * dx / 6, y: y + dy },
          { x: x + 2 * dx / 3, y: y + dy }
        ];
        node.label.x = node.x;
        node.label.y = node.y;
      }
    });
  }
  function selectNumberAttrs(obj, attrs) {
    return util2.mapValues(util2.pick(obj, attrs), Number);
  }
  function canonicalize(attrs) {
    var newAttrs = {};
    if (attrs) {
      Object.entries(attrs).forEach(([k2, v]) => {
        if (typeof k2 === "string") {
          k2 = k2.toLowerCase();
        }
        newAttrs[k2] = v;
      });
    }
    return newAttrs;
  }
  return layout_1;
}
var debug;
var hasRequiredDebug;
function requireDebug() {
  if (hasRequiredDebug) return debug;
  hasRequiredDebug = 1;
  let util2 = requireUtil$1();
  let Graph = requireGraphlib().Graph;
  debug = {
    debugOrdering
  };
  function debugOrdering(g) {
    let layerMatrix = util2.buildLayerMatrix(g);
    let h = new Graph({ compound: true, multigraph: true }).setGraph({});
    g.nodes().forEach((v) => {
      h.setNode(v, { label: v });
      h.setParent(v, "layer" + g.node(v).rank);
    });
    g.edges().forEach((e2) => h.setEdge(e2.v, e2.w, {}, e2.name));
    layerMatrix.forEach((layer, i) => {
      let layerV = "layer" + i;
      h.setNode(layerV, { rank: "same" });
      layer.reduce((u, v) => {
        h.setEdge(u, v, { style: "invis" });
        return v;
      });
    });
    return h;
  }
  return debug;
}
var version;
var hasRequiredVersion;
function requireVersion() {
  if (hasRequiredVersion) return version;
  hasRequiredVersion = 1;
  version = "1.1.5";
  return version;
}
var dagre;
var hasRequiredDagre;
function requireDagre() {
  if (hasRequiredDagre) return dagre;
  hasRequiredDagre = 1;
  dagre = {
    graphlib: requireGraphlib(),
    layout: requireLayout(),
    debug: requireDebug(),
    util: {
      time: requireUtil$1().time,
      notime: requireUtil$1().notime
    },
    version: requireVersion()
  };
  return dagre;
}
requireDagre();
export {
  Duration as D,
  PeriodType$1 as P,
  isStringDate as a,
  defaultLocale as b,
  cls as c,
  defaultWindow as d,
  truncateText as e,
  formatDateWithLocale as f,
  getStringWidth as g,
  DateToken$1 as h,
  interpolatePath as i,
  range$1 as r,
  toTitleCase as t,
  watch as w
};
