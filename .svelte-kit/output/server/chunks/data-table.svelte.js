import { w as push, G as spread_props, y as pop, O as props_id, F as spread_attributes, P as bind_props, Q as copy_payload, R as assign_payload, J as clsx, B as escape_html } from "./index2.js";
import { c as cn } from "./shadcn.utils.js";
import { b as createId, w as watch, d as box, m as mergeProps } from "./create-id.js";
import { I as Icon } from "./states.svelte.js";
import "clsx";
import { d as MenuCheckboxGroupContext, e as MenuCheckboxItemState } from "./index6.js";
import { n as noop } from "./scroll-lock.js";
import { C as Check } from "./check.js";
import { h as html } from "./html.js";
import { createTable } from "@tanstack/table-core";
function Minus($$payload, $$props) {
  push();
  /**
   * @license @lucide/svelte v0.539.0 - ISC
   *
   * ISC License
   *
   * Copyright (c) for portions of Lucide are held by <PERSON> 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   *
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   *
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   *
   */
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [["path", { "d": "M5 12h14" }]];
  Icon($$payload, spread_props([
    { name: "minus" },
    /**
     * @component @name Minus
     * @description Lucide SVG icon component, renders SVG Element with children.
     *
     * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSAxMmgxNCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/minus
     * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
     *
     * @param {Object} props - Lucide icons props and any valid SVG attribute
     * @returns {FunctionalComponent} Svelte component
     *
     */
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function Menu_checkbox_item($$payload, $$props) {
  push();
  const uid = props_id($$payload);
  let {
    child,
    children,
    ref = null,
    checked = false,
    id = createId(uid),
    onCheckedChange = noop,
    disabled = false,
    onSelect = noop,
    closeOnSelect = true,
    indeterminate = false,
    onIndeterminateChange = noop,
    value = "",
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const group = MenuCheckboxGroupContext.getOr(null);
  if (group && value) {
    if (group.opts.value.current.includes(value)) {
      checked = true;
    } else {
      checked = false;
    }
  }
  watch.pre(() => value, () => {
    if (group && value) {
      if (group.opts.value.current.includes(value)) {
        checked = true;
      } else {
        checked = false;
      }
    }
  });
  const checkboxItemState = MenuCheckboxItemState.create(
    {
      checked: box.with(() => checked, (v) => {
        checked = v;
        onCheckedChange(v);
      }),
      id: box.with(() => id),
      disabled: box.with(() => disabled),
      onSelect: box.with(() => handleSelect),
      ref: box.with(() => ref, (v) => ref = v),
      closeOnSelect: box.with(() => closeOnSelect),
      indeterminate: box.with(() => indeterminate, (v) => {
        indeterminate = v;
        onIndeterminateChange(v);
      }),
      value: box.with(() => value)
    },
    group
  );
  function handleSelect(e) {
    onSelect(e);
    if (e.defaultPrevented) return;
    checkboxItemState.toggleChecked();
  }
  const mergedProps = mergeProps(restProps, checkboxItemState.props);
  if (child) {
    $$payload.out.push("<!--[-->");
    child($$payload, { checked, indeterminate, props: mergedProps });
    $$payload.out.push(`<!---->`);
  } else {
    $$payload.out.push("<!--[!-->");
    $$payload.out.push(`<div${spread_attributes({ ...mergedProps }, null)}>`);
    children?.($$payload, { checked, indeterminate });
    $$payload.out.push(`<!----></div>`);
  }
  $$payload.out.push(`<!--]-->`);
  bind_props($$props, { ref, checked, indeterminate });
  pop();
}
function Dropdown_menu_checkbox_item($$payload, $$props) {
  push();
  let {
    ref = null,
    checked = false,
    indeterminate = false,
    class: className,
    children: childrenProp,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out.push(`<!---->`);
    {
      let children = function($$payload3, { checked: checked2, indeterminate: indeterminate2 }) {
        $$payload3.out.push(`<span class="pointer-events-none absolute left-2 flex size-3.5 items-center justify-center">`);
        if (indeterminate2) {
          $$payload3.out.push("<!--[-->");
          Minus($$payload3, { class: "size-4" });
        } else {
          $$payload3.out.push("<!--[!-->");
          Check($$payload3, { class: cn("size-4", !checked2 && "text-transparent") });
        }
        $$payload3.out.push(`<!--]--></span> `);
        childrenProp?.($$payload3);
        $$payload3.out.push(`<!---->`);
      };
      Menu_checkbox_item($$payload2, spread_props([
        {
          "data-slot": "dropdown-menu-checkbox-item",
          class: cn("focus:bg-accent focus:text-accent-foreground outline-hidden relative flex cursor-default select-none items-center gap-2 rounded-sm py-1.5 pl-8 pr-2 text-sm data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg:not([class*='size-'])]:size-4 [&_svg]:pointer-events-none [&_svg]:shrink-0", className)
        },
        restProps,
        {
          get ref() {
            return ref;
          },
          set ref($$value) {
            ref = $$value;
            $$settled = false;
          },
          get checked() {
            return checked;
          },
          set checked($$value) {
            checked = $$value;
            $$settled = false;
          },
          get indeterminate() {
            return indeterminate;
          },
          set indeterminate($$value) {
            indeterminate = $$value;
            $$settled = false;
          },
          children,
          $$slots: { default: true }
        }
      ]));
    }
    $$payload2.out.push(`<!---->`);
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref, checked, indeterminate });
  pop();
}
function Table($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    children,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  $$payload.out.push(`<div data-slot="table-container" class="relative w-full overflow-x-auto"><table${spread_attributes(
    {
      "data-slot": "table",
      class: clsx(cn("w-full caption-bottom text-sm", className)),
      ...restProps
    },
    null
  )}>`);
  children?.($$payload);
  $$payload.out.push(`<!----></table></div>`);
  bind_props($$props, { ref });
  pop();
}
function Table_body($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    children,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  $$payload.out.push(`<tbody${spread_attributes(
    {
      "data-slot": "table-body",
      class: clsx(cn("[&_tr:last-child]:border-0", className)),
      ...restProps
    },
    null
  )}>`);
  children?.($$payload);
  $$payload.out.push(`<!----></tbody>`);
  bind_props($$props, { ref });
  pop();
}
function Table_cell($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    children,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  $$payload.out.push(`<td${spread_attributes(
    {
      "data-slot": "table-cell",
      class: clsx(cn("whitespace-nowrap p-2 align-middle [&:has([role=checkbox])]:pr-0", className)),
      ...restProps
    },
    null
  )}>`);
  children?.($$payload);
  $$payload.out.push(`<!----></td>`);
  bind_props($$props, { ref });
  pop();
}
function Table_head($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    children,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  $$payload.out.push(`<th${spread_attributes(
    {
      "data-slot": "table-head",
      class: clsx(cn("text-foreground h-10 whitespace-nowrap px-2 text-left align-middle font-medium [&:has([role=checkbox])]:pr-0", className)),
      ...restProps
    },
    null
  )}>`);
  children?.($$payload);
  $$payload.out.push(`<!----></th>`);
  bind_props($$props, { ref });
  pop();
}
function Table_header($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    children,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  $$payload.out.push(`<thead${spread_attributes(
    {
      "data-slot": "table-header",
      class: clsx(cn("[&_tr]:border-b", className)),
      ...restProps
    },
    null
  )}>`);
  children?.($$payload);
  $$payload.out.push(`<!----></thead>`);
  bind_props($$props, { ref });
  pop();
}
function Table_row($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    children,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  $$payload.out.push(`<tr${spread_attributes(
    {
      "data-slot": "table-row",
      class: clsx(cn("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors", className)),
      ...restProps
    },
    null
  )}>`);
  children?.($$payload);
  $$payload.out.push(`<!----></tr>`);
  bind_props($$props, { ref });
  pop();
}
class RenderComponentConfig {
  component;
  props;
  constructor(component, props = {}) {
    this.component = component;
    this.props = props;
  }
}
class RenderSnippetConfig {
  snippet;
  params;
  constructor(snippet, params) {
    this.snippet = snippet;
    this.params = params;
  }
}
function renderComponent(component, props = {}) {
  return new RenderComponentConfig(component, props);
}
function Flex_render($$payload, $$props) {
  push();
  let { content, context } = $$props;
  if (typeof content === "string") {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`${escape_html(content)}`);
  } else {
    $$payload.out.push("<!--[!-->");
    if (content instanceof Function) {
      $$payload.out.push("<!--[-->");
      const result = content(context);
      if (result instanceof RenderComponentConfig) {
        $$payload.out.push("<!--[-->");
        const { component: Component, props } = result;
        $$payload.out.push(`<!---->`);
        Component($$payload, spread_props([props]));
        $$payload.out.push(`<!---->`);
      } else {
        $$payload.out.push("<!--[!-->");
        if (result instanceof RenderSnippetConfig) {
          $$payload.out.push("<!--[-->");
          const { snippet, params } = result;
          snippet($$payload, params);
          $$payload.out.push(`<!---->`);
        } else {
          $$payload.out.push("<!--[!-->");
          if (typeof result === "string" && result.includes("<")) {
            $$payload.out.push("<!--[-->");
            $$payload.out.push(`${html(result)}`);
          } else {
            $$payload.out.push("<!--[!-->");
            $$payload.out.push(`${escape_html(result)}`);
          }
          $$payload.out.push(`<!--]-->`);
        }
        $$payload.out.push(`<!--]-->`);
      }
      $$payload.out.push(`<!--]-->`);
    } else {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]-->`);
  }
  $$payload.out.push(`<!--]-->`);
  pop();
}
function createSvelteTable(options) {
  const resolvedOptions = mergeObjects(
    {
      state: {},
      onStateChange() {
      },
      renderFallbackValue: null,
      mergeOptions: (defaultOptions, options2) => {
        return mergeObjects(defaultOptions, options2);
      }
    },
    options
  );
  const table = createTable(resolvedOptions);
  let state = table.initialState;
  function updateOptions() {
    table.setOptions((prev) => {
      return mergeObjects(prev, options, {
        state: mergeObjects(state, options.state || {}),
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        onStateChange: (updater) => {
          if (updater instanceof Function) state = updater(state);
          else state = mergeObjects(state, updater);
          options.onStateChange?.(updater);
        }
      });
    });
  }
  updateOptions();
  return table;
}
function mergeObjects(...sources) {
  const target = {};
  for (let i = 0; i < sources.length; i++) {
    let source = sources[i];
    if (typeof source === "function") source = source();
    if (source) {
      const descriptors = Object.getOwnPropertyDescriptors(source);
      for (const key in descriptors) {
        if (key in target) continue;
        Object.defineProperty(target, key, {
          enumerable: true,
          get() {
            for (let i2 = sources.length - 1; i2 >= 0; i2--) {
              let s = sources[i2];
              if (typeof s === "function") s = s();
              const v = (s || {})[key];
              if (v !== void 0) return v;
            }
          }
        });
      }
    }
  }
  return target;
}
export {
  Dropdown_menu_checkbox_item as D,
  Flex_render as F,
  Minus as M,
  Table as T,
  Table_header as a,
  Table_row as b,
  createSvelteTable as c,
  Table_head as d,
  Table_body as e,
  Table_cell as f,
  renderComponent as r
};
