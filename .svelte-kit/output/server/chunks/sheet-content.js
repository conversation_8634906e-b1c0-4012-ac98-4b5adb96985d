import "clsx";
import "@sveltejs/kit/internal";
import "./exports.js";
import "./state.svelte.js";
import { C as CalendarViewTypes, a0 as Appointment, $ as AppointmentType, _ as CalendarBlock, a3 as WorkdaySetting, H as User, F as Organization, E as Team, M as MemberInvite, a2 as OrgBankDetail, a1 as OrgPaymentProvider, B as TeamMember, a as SheetTypes, ac as timeGreeting, d as toast, ad as getRandomColorName } from "./auth-client.js";
import superjson from "superjson";
import "@oslojs/crypto/sha2";
import "decimal.js";
import "linq-extensions";
import { remult } from "remult";
import { cloneDeep } from "lodash-es";
import { addDays } from "date-fns";
import cloneDeep$1 from "lodash-es/cloneDeep.js";
import { x as setContext, S as getContext, M as run, N as derived, w as push, O as props_id, P as bind_props, y as pop, F as spread_attributes, G as spread_props, Q as copy_payload, R as assign_payload } from "./index2.js";
import { c as createSubscriber } from "./states.svelte.js";
import { c as cn } from "./shadcn.utils.js";
import { d as box, c as createBitsAttrs, a as attachRef, w as watch, e as getDataOpenClosed, f as getAriaExpanded, b as createId, m as mergeProps } from "./create-id.js";
import { C as Context, O as OpenChangeComplete, S as SPACE, E as ENTER, P as Presence_layer, n as noop, F as Focus_scope, f as afterSleep, g as Escape_layer, h as Dismissible_layer, T as Text_selection_layer, j as Scroll_lock, d as Portal } from "./scroll-lock.js";
import { tv } from "tailwind-variants";
import { X } from "./x.js";
const setStoreContext = (name, storeObject) => {
  const storeKey = Symbol.for(name);
  setContext(storeKey, storeObject);
};
const useStore = (name) => {
  const storeKey = Symbol.for(name);
  return getContext(storeKey);
};
const defaultWindow = void 0;
function getActiveElement(document) {
  let activeElement = document.activeElement;
  while (activeElement?.shadowRoot) {
    const node = activeElement.shadowRoot.activeElement;
    if (node === activeElement)
      break;
    else
      activeElement = node;
  }
  return activeElement;
}
class ActiveElement {
  #document;
  #subscribe;
  constructor(options = {}) {
    const { window = defaultWindow, document = window?.document } = options;
    if (window === void 0) return;
    this.#document = document;
    this.#subscribe = createSubscriber();
  }
  get current() {
    this.#subscribe?.();
    if (!this.#document) return null;
    return getActiveElement(this.#document);
  }
}
new ActiveElement();
function getStorage(storageType, window) {
  switch (storageType) {
    case "local":
      return window.localStorage;
    case "session":
      return window.sessionStorage;
  }
}
function proxy(value, root, proxies, subscribe, update, serialize) {
  if (value === null || typeof value !== "object") {
    return value;
  }
  const proto = Object.getPrototypeOf(value);
  if (proto !== null && proto !== Object.prototype && !Array.isArray(value)) {
    return value;
  }
  let p = proxies.get(value);
  if (!p) {
    p = new Proxy(value, {
      get: (target, property) => {
        subscribe?.();
        return proxy(Reflect.get(target, property), root, proxies, subscribe, update, serialize);
      },
      set: (target, property, value2) => {
        update?.();
        Reflect.set(target, property, value2);
        serialize(root);
        return true;
      }
    });
    proxies.set(value, p);
  }
  return p;
}
class PersistedState {
  #current;
  #key;
  #serializer;
  #storage;
  #subscribe;
  #update;
  #proxies = /* @__PURE__ */ new WeakMap();
  constructor(key, initialValue, options = {}) {
    const {
      storage: storageType = "local",
      serializer = { serialize: JSON.stringify, deserialize: JSON.parse },
      syncTabs = true
    } = options;
    const window = "window" in options ? options.window : defaultWindow;
    this.#current = initialValue;
    this.#key = key;
    this.#serializer = serializer;
    if (window === void 0) return;
    const storage = getStorage(storageType, window);
    this.#storage = storage;
    const existingValue = storage.getItem(key);
    if (existingValue !== null) {
      this.#current = this.#deserialize(existingValue);
    } else {
      this.#serialize(initialValue);
    }
    if (syncTabs && storageType === "local") {
      this.#subscribe = createSubscriber();
    }
  }
  get current() {
    this.#subscribe?.();
    const storageItem = this.#storage?.getItem(this.#key);
    const root = storageItem ? this.#deserialize(storageItem) : this.#current;
    return proxy(root, root, this.#proxies, this.#subscribe?.bind(this), this.#update?.bind(this), this.#serialize.bind(this));
  }
  set current(newValue) {
    this.#serialize(newValue);
    this.#update?.();
  }
  #handleStorageEvent = (event) => {
    if (event.key !== this.#key || event.newValue === null) return;
    this.#current = this.#deserialize(event.newValue);
    this.#update?.();
  };
  #deserialize(value) {
    try {
      return this.#serializer.deserialize(value);
    } catch (error) {
      console.error(`Error when parsing "${value}" from persisted store "${this.#key}"`, error);
      return;
    }
  }
  #serialize(value) {
    try {
      if (value != void 0) {
        this.#storage?.setItem(this.#key, this.#serializer.serialize(value));
      }
    } catch (error) {
      console.error(`Error when writing value from persisted store "${this.#key}" to ${this.#storage}`, error);
    }
  }
}
const defaultStoreState = {
  appointments: [],
  currentView: CalendarViewTypes.Day,
  selectedAppointment: void 0,
  calendarBlocks: [],
  workdaySetting: void 0,
  appointmentTemplates: [],
  selectedAppointmentLocation: void 0,
  currentDayAppointments: []
};
class AppointmentStore extends PersistedState {
  appointmentUnsub;
  calendarBlockUnsub;
  workdaySettingUnsub;
  appointmentTypeUnsub;
  constructor() {
    super("appointments", cloneDeep(defaultStoreState), {
      storage: "session",
      syncTabs: false,
      serializer: { serialize: superjson.stringify, deserialize: superjson.parse }
    });
  }
  setupLiveQuery() {
    this.appointmentUnsub = remult.repo(Appointment).liveQuery({
      where: { organizationId: remult.user?.organizationId },
      orderBy: { createdAt: "desc" }
    }).subscribe((info) => {
      this.current.appointments = info.items;
    });
    this.appointmentTypeUnsub = remult.repo(AppointmentType).liveQuery({
      where: { organizationId: remult.user?.organizationId },
      orderBy: { appointmentDetail: "asc" }
    }).subscribe((info) => {
      this.current.appointmentTemplates = info.items;
    });
    this.calendarBlockUnsub = remult.repo(CalendarBlock).liveQuery({
      where: { organizationId: remult.user?.organizationId },
      orderBy: { startDate: "asc" }
    }).subscribe((info) => {
      this.current.calendarBlocks = info.items;
    });
    this.workdaySettingUnsub = remult.repo(WorkdaySetting).liveQuery({ where: { organizationId: remult.user?.organizationId } }).subscribe((info) => {
      this.current.workdaySetting = info.items.firstOrNull() ?? void 0;
    });
  }
  setSelectedAppointment(data) {
    this.current.selectedAppointment = data;
  }
  setCurrentView(view) {
    this.current.currentView = view;
  }
  get selectedAppointment() {
    return this.current.selectedAppointment;
  }
  get currentView() {
    return this.current.currentView;
  }
  get appointments() {
    return this.current.appointments;
  }
  get calendarBlocks() {
    return this.current.calendarBlocks;
  }
  get workdaySetting() {
    return this.current.workdaySetting;
  }
  get appointmentTemplates() {
    return this.current.appointmentTemplates;
  }
  get selectedAppointmentLocation() {
    return this.current.selectedAppointmentLocation;
  }
  get currentDayAppointments() {
    return this.current.appointments?.where((k) => new Date(k.appointmentDate).getDate() == /* @__PURE__ */ (/* @__PURE__ */ new Date()).getDate()).toArray() ?? [];
  }
}
const appointmentStore = new AppointmentStore();
const appointmentStoreKey = "appointments.store";
const setAppointmentStoreContext = () => {
  setStoreContext(appointmentStoreKey, appointmentStore);
};
const getAppointmentStore = () => {
  return useStore(appointmentStoreKey);
};
const defaultAuthState = {
  ...new User(),
  isAdmin: false,
  teamConfigIsSetup: false,
  isTeamLocationAdmin: false,
  isOrgAdmin: false,
  expires: addDays(/* @__PURE__ */ new Date(), 7),
  organizationId: "",
  teamId: ""
};
class AuthStore extends PersistedState {
  constructor() {
    super("auth", cloneDeep$1(defaultAuthState), {
      storage: "session",
      syncTabs: false,
      serializer: { serialize: superjson.stringify, deserialize: superjson.parse }
    });
  }
  updateStore(sessionData) {
    if (!sessionData) {
      this.clearStore();
      return;
    }
    this.current = { ...sessionData };
  }
  async clearStore() {
    await remult.initUser();
    this.current = cloneDeep$1(defaultAuthState);
  }
  async checkAuth() {
    await remult.initUser();
    this.current = remult.authenticated() ? remult.user : cloneDeep$1(defaultAuthState);
  }
  get fullName() {
    return `${this.current.givenName} ${this.current.familyName}`;
  }
  get user() {
    return this.current;
  }
  get isAdmin() {
    return this.current.isAdmin;
  }
  get isOrgAdmin() {
    return this.current.isOrgAdmin;
  }
  get isTeamLocationAdmin() {
    return this.current.isTeamLocationAdmin;
  }
  get teamConfigIsSetup() {
    return this.current.teamConfigIsSetup;
  }
  get showHomeModal() {
    return !this.current.id;
  }
  get isAuthenticated() {
    return !!this.current?.id;
  }
}
const authStoreKey = "authState.store";
const authStore = new AuthStore();
const setAuthStoreContext = () => {
  setStoreContext(authStoreKey, authStore);
};
const getAuthStore = () => {
  return useStore(authStoreKey);
};
const defaultOrgStoreState = {
  org: void 0,
  teams: [],
  users: [],
  staffMemberInvites: [],
  teamMembers: [],
  orgBankDetail: void 0,
  orgPaymentProvider: void 0
};
class OrgStore extends PersistedState {
  orgUnsub;
  teamsUnsub;
  usersUnsub;
  staffMemberInviteUnsub;
  orgBankDetailUnsub;
  orgPaymentProviderUnsub;
  teamMembersUnSub;
  constructor() {
    super("org", cloneDeep(defaultOrgStoreState), {
      storage: "session",
      syncTabs: false,
      serializer: { serialize: superjson.stringify, deserialize: superjson.parse }
    });
  }
  setupLiveQuery() {
    this.orgUnsub = remult.repo(Organization).liveQuery({ where: { id: remult.user?.organizationId } }).subscribe((info) => {
      run(() => {
        this.current.org = info.items.firstOrNull() ?? void 0;
      });
    });
    this.teamsUnsub = remult.repo(Team).liveQuery({
      where: { organizationId: remult.user?.organizationId },
      orderBy: { name: "asc" }
    }).subscribe((info) => {
      run(() => {
        this.current.teams = info.items;
      });
    });
    this.usersUnsub = remult.repo(User).liveQuery({
      where: { organizationId: remult.user?.organizationId },
      orderBy: { name: "asc" }
    }).subscribe((info) => {
      run(() => {
        this.current.users = info.items;
      });
    });
    this.staffMemberInviteUnsub = remult.repo(MemberInvite).liveQuery({
      where: { organizationId: remult.user?.organizationId },
      orderBy: { createdAt: "desc" }
    }).subscribe((info) => {
      run(() => {
        this.current.staffMemberInvites = info.items;
      });
    });
    this.orgBankDetailUnsub = remult.repo(OrgBankDetail).liveQuery({
      where: { organizationId: remult.user?.organizationId },
      orderBy: { bankName: "asc" }
    }).subscribe((info) => {
      run(() => {
        this.current.orgBankDetail = info.items.firstOrNull() ?? void 0;
      });
    });
    this.orgPaymentProviderUnsub = remult.repo(OrgPaymentProvider).liveQuery({ where: { organizationId: remult.user?.organizationId } }).subscribe((info) => {
      run(() => {
        this.current.orgPaymentProvider = info.items.firstOrNull() ?? void 0;
      });
    });
    this.teamMembersUnSub = remult.repo(TeamMember).liveQuery({
      where: { teamId: remult.user?.teamId },
      orderBy: { createdAt: "desc" }
    }).subscribe((info) => {
      run(() => {
        this.current.teamMembers = info.items;
      });
    });
  }
  cleanup() {
    this.orgUnsub?.();
    this.teamsUnsub?.();
    this.usersUnsub?.();
    this.staffMemberInviteUnsub?.();
    this.orgBankDetailUnsub?.();
    this.orgPaymentProviderUnsub?.();
    this.teamMembersUnSub?.();
  }
  get org() {
    return this.current.org;
  }
  get orgNameInitials() {
    return this.current.org?.name?.split(" ").map((wrd) => wrd.charAt(0).toUpperCase()).join("") ?? "NA";
  }
  get teams() {
    return this.current.teams ?? [];
  }
  get staffMembers() {
    return this.current.teamMembers.map((teamMember) => {
      const location = this.current.teams.firstOrNull((team) => team.id === teamMember.teamId);
      const profile = this.current.users.firstOrNull((u) => u.id === teamMember.userId);
      return { profile, location, memberId: teamMember.id };
    }).filter((member) => member.memberId);
  }
  get staffMemberInvites() {
    return this.current.staffMemberInvites;
  }
  get orgBankDetail() {
    return this.current.orgBankDetail;
  }
  get orgPaymentProvider() {
    return this.current.orgPaymentProvider;
  }
  get orgIsSetup() {
    return !!this.current.org?.name;
  }
}
const orgStore = new OrgStore();
const orgStoreKey = "org.store";
const setOrgStoreContext = () => {
  setStoreContext(orgStoreKey, orgStore);
};
const getOrgStore = () => {
  return useStore(orgStoreKey);
};
const defaultUtilsState = {
  appLogoName: "SpenDeed",
  appName: "SpenDeed",
  isFirstTime: false,
  isNativePlatform: false,
  leftPanelOpen: false,
  showMobileMenu: false,
  activeNavBar: 1,
  homeSheet: {
    authingUserEmail: void 0,
    authingUserId: void 0,
    passkeyEnabled: void 0
  },
  currentHomeSheet: SheetTypes.Marketing,
  currentAuthEmail: "",
  appLayout: { currentPageTitle: "Home", sidebarVisible: false },
  dynamicModalItem: void 0,
  dynamicSheetItem: void 0,
  isAuthRoute: false,
  areWeLive: false,
  appLogoColor: "bg-green-900",
  signupPayload: void 0
};
class UtilsStore extends PersistedState {
  constructor() {
    super("utils", cloneDeep$1(defaultUtilsState), {
      storage: "session",
      syncTabs: false,
      serializer: { serialize: superjson.stringify, deserialize: superjson.parse }
    });
  }
  get st() {
    return this.current;
  }
  get currentHomeSheet() {
    return this.current.currentHomeSheet;
  }
  get signupPayload() {
    return this.current.signupPayload;
  }
  setPageTitle(pageTitle) {
    this.current.appLayout.currentPageTitle = pageTitle;
  }
  clearDynamicModal() {
    this.current.dynamicModalItem = void 0;
  }
  openDynamicModal(dynamicFormObj) {
    this.current.dynamicModalItem = dynamicFormObj;
  }
  closeDynamicSheet() {
    this.current.dynamicSheetItem = void 0;
  }
  openDynamicSheet(dynamicFormObj) {
    this.current.dynamicSheetItem = dynamicFormObj;
  }
  greetings(displayName) {
    return `${timeGreeting()}  ${displayName ? displayName : "Anonymous"}!`;
  }
  toggleAppSideBar() {
    this.current.appLayout.sidebarVisible = !this.current.appLayout.sidebarVisible;
  }
  toggleMobileMenu() {
    this.current.showMobileMenu = !this.current.showMobileMenu;
  }
  toastError(message) {
    toast.error(message);
  }
  toastSuccess(message) {
    toast.success(message);
  }
  toastInfo(message) {
    toast.info(message);
  }
  toastWarning(message) {
    toast.warning(message);
  }
  setAppColor() {
    this.current.appLogoColor = getRandomColorName();
  }
  setHomeSheet(sheetType) {
    this.current.currentHomeSheet = sheetType;
  }
  setCurrentAuthEmail(email) {
    this.current.currentAuthEmail = email;
  }
  holdSignupPayload(payload) {
    this.current.signupPayload = payload;
  }
}
const utilsStore = new UtilsStore();
const utilsStoreKey = "utilsState.store";
const setUtilsStoreContext = () => {
  setStoreContext(utilsStoreKey, utilsStore);
};
const getUtilsStore = () => {
  return useStore(utilsStoreKey);
};
const dialogAttrs = createBitsAttrs({
  component: "dialog",
  parts: [
    "content",
    "trigger",
    "overlay",
    "title",
    "description",
    "close",
    "cancel",
    "action"
  ]
});
const DialogRootContext = new Context("Dialog.Root | AlertDialog.Root");
class DialogRootState {
  static create(opts) {
    return DialogRootContext.set(new DialogRootState(opts));
  }
  opts;
  triggerNode = null;
  contentNode = null;
  descriptionNode = null;
  contentId = void 0;
  titleId = void 0;
  triggerId = void 0;
  descriptionId = void 0;
  cancelNode = null;
  constructor(opts) {
    this.opts = opts;
    this.handleOpen = this.handleOpen.bind(this);
    this.handleClose = this.handleClose.bind(this);
    new OpenChangeComplete({
      ref: box.with(() => this.contentNode),
      open: this.opts.open,
      enabled: true,
      onComplete: () => {
        this.opts.onOpenChangeComplete.current(this.opts.open.current);
      }
    });
  }
  handleOpen() {
    if (this.opts.open.current) return;
    this.opts.open.current = true;
  }
  handleClose() {
    if (!this.opts.open.current) return;
    this.opts.open.current = false;
  }
  getBitsAttr = (part) => {
    return dialogAttrs.getAttr(part, this.opts.variant.current);
  };
  #sharedProps = derived(() => ({ "data-state": getDataOpenClosed(this.opts.open.current) }));
  get sharedProps() {
    return this.#sharedProps();
  }
  set sharedProps($$value) {
    return this.#sharedProps($$value);
  }
}
class DialogTriggerState {
  static create(opts) {
    return new DialogTriggerState(opts, DialogRootContext.get());
  }
  opts;
  root;
  attachment;
  constructor(opts, root) {
    this.opts = opts;
    this.root = root;
    this.attachment = attachRef(this.opts.ref, (v) => {
      this.root.triggerNode = v;
      this.root.triggerId = v?.id;
    });
    this.onclick = this.onclick.bind(this);
    this.onkeydown = this.onkeydown.bind(this);
  }
  onclick(e) {
    if (this.opts.disabled.current) return;
    if (e.button > 0) return;
    this.root.handleOpen();
  }
  onkeydown(e) {
    if (this.opts.disabled.current) return;
    if (e.key === SPACE || e.key === ENTER) {
      e.preventDefault();
      this.root.handleOpen();
    }
  }
  #props = derived(() => ({
    id: this.opts.id.current,
    "aria-haspopup": "dialog",
    "aria-expanded": getAriaExpanded(this.root.opts.open.current),
    "aria-controls": this.root.contentId,
    [this.root.getBitsAttr("trigger")]: "",
    onkeydown: this.onkeydown,
    onclick: this.onclick,
    disabled: this.opts.disabled.current ? true : void 0,
    ...this.root.sharedProps,
    ...this.attachment
  }));
  get props() {
    return this.#props();
  }
  set props($$value) {
    return this.#props($$value);
  }
}
class DialogCloseState {
  static create(opts) {
    return new DialogCloseState(opts, DialogRootContext.get());
  }
  opts;
  root;
  attachment;
  constructor(opts, root) {
    this.opts = opts;
    this.root = root;
    this.attachment = attachRef(this.opts.ref);
    this.onclick = this.onclick.bind(this);
    this.onkeydown = this.onkeydown.bind(this);
  }
  onclick(e) {
    if (this.opts.disabled.current) return;
    if (e.button > 0) return;
    this.root.handleClose();
  }
  onkeydown(e) {
    if (this.opts.disabled.current) return;
    if (e.key === SPACE || e.key === ENTER) {
      e.preventDefault();
      this.root.handleClose();
    }
  }
  #props = derived(() => ({
    id: this.opts.id.current,
    [this.root.getBitsAttr(this.opts.variant.current)]: "",
    onclick: this.onclick,
    onkeydown: this.onkeydown,
    disabled: this.opts.disabled.current ? true : void 0,
    tabindex: 0,
    ...this.root.sharedProps,
    ...this.attachment
  }));
  get props() {
    return this.#props();
  }
  set props($$value) {
    return this.#props($$value);
  }
}
class DialogTitleState {
  static create(opts) {
    return new DialogTitleState(opts, DialogRootContext.get());
  }
  opts;
  root;
  attachment;
  constructor(opts, root) {
    this.opts = opts;
    this.root = root;
    this.root.titleId = this.opts.id.current;
    this.attachment = attachRef(this.opts.ref);
    watch.pre(() => this.opts.id.current, (id) => {
      this.root.titleId = id;
    });
  }
  #props = derived(() => ({
    id: this.opts.id.current,
    role: "heading",
    "aria-level": this.opts.level.current,
    [this.root.getBitsAttr("title")]: "",
    ...this.root.sharedProps,
    ...this.attachment
  }));
  get props() {
    return this.#props();
  }
  set props($$value) {
    return this.#props($$value);
  }
}
class DialogDescriptionState {
  static create(opts) {
    return new DialogDescriptionState(opts, DialogRootContext.get());
  }
  opts;
  root;
  attachment;
  constructor(opts, root) {
    this.opts = opts;
    this.root = root;
    this.root.descriptionId = this.opts.id.current;
    this.attachment = attachRef(this.opts.ref, (v) => {
      this.root.descriptionNode = v;
    });
    watch.pre(() => this.opts.id.current, (id) => {
      this.root.descriptionId = id;
    });
  }
  #props = derived(() => ({
    id: this.opts.id.current,
    [this.root.getBitsAttr("description")]: "",
    ...this.root.sharedProps,
    ...this.attachment
  }));
  get props() {
    return this.#props();
  }
  set props($$value) {
    return this.#props($$value);
  }
}
class DialogContentState {
  static create(opts) {
    return new DialogContentState(opts, DialogRootContext.get());
  }
  opts;
  root;
  attachment;
  constructor(opts, root) {
    this.opts = opts;
    this.root = root;
    this.attachment = attachRef(this.opts.ref, (v) => {
      this.root.contentNode = v;
      this.root.contentId = v?.id;
    });
  }
  #snippetProps = derived(() => ({ open: this.root.opts.open.current }));
  get snippetProps() {
    return this.#snippetProps();
  }
  set snippetProps($$value) {
    return this.#snippetProps($$value);
  }
  #props = derived(() => ({
    id: this.opts.id.current,
    role: this.root.opts.variant.current === "alert-dialog" ? "alertdialog" : "dialog",
    "aria-modal": "true",
    "aria-describedby": this.root.descriptionId,
    "aria-labelledby": this.root.titleId,
    [this.root.getBitsAttr("content")]: "",
    style: {
      pointerEvents: "auto",
      outline: this.root.opts.variant.current === "alert-dialog" ? "none" : void 0
    },
    tabindex: this.root.opts.variant.current === "alert-dialog" ? -1 : void 0,
    ...this.root.sharedProps,
    ...this.attachment
  }));
  get props() {
    return this.#props();
  }
  set props($$value) {
    return this.#props($$value);
  }
}
class DialogOverlayState {
  static create(opts) {
    return new DialogOverlayState(opts, DialogRootContext.get());
  }
  opts;
  root;
  attachment;
  constructor(opts, root) {
    this.opts = opts;
    this.root = root;
    this.attachment = attachRef(this.opts.ref);
  }
  #snippetProps = derived(() => ({ open: this.root.opts.open.current }));
  get snippetProps() {
    return this.#snippetProps();
  }
  set snippetProps($$value) {
    return this.#snippetProps($$value);
  }
  #props = derived(() => ({
    id: this.opts.id.current,
    [this.root.getBitsAttr("overlay")]: "",
    style: { pointerEvents: "auto" },
    ...this.root.sharedProps,
    ...this.attachment
  }));
  get props() {
    return this.#props();
  }
  set props($$value) {
    return this.#props($$value);
  }
}
function shouldEnableFocusTrap({ forceMount, present, open }) {
  if (forceMount)
    return open;
  return present && open;
}
function Dialog_overlay($$payload, $$props) {
  push();
  const uid = props_id($$payload);
  let {
    id = createId(uid),
    forceMount = false,
    child,
    children,
    ref = null,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const overlayState = DialogOverlayState.create({
    id: box.with(() => id),
    ref: box.with(() => ref, (v) => ref = v)
  });
  const mergedProps = mergeProps(restProps, overlayState.props);
  {
    let presence = function($$payload2) {
      if (child) {
        $$payload2.out.push("<!--[-->");
        child($$payload2, { props: mergeProps(mergedProps), ...overlayState.snippetProps });
        $$payload2.out.push(`<!---->`);
      } else {
        $$payload2.out.push("<!--[!-->");
        $$payload2.out.push(`<div${spread_attributes({ ...mergeProps(mergedProps) }, null)}>`);
        children?.($$payload2, overlayState.snippetProps);
        $$payload2.out.push(`<!----></div>`);
      }
      $$payload2.out.push(`<!--]-->`);
    };
    Presence_layer($$payload, {
      open: overlayState.root.opts.open.current || forceMount,
      ref: overlayState.opts.ref,
      presence
    });
  }
  bind_props($$props, { ref });
  pop();
}
function Dialog_close($$payload, $$props) {
  push();
  const uid = props_id($$payload);
  let {
    children,
    child,
    id = createId(uid),
    ref = null,
    disabled = false,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const closeState = DialogCloseState.create({
    variant: box.with(() => "close"),
    id: box.with(() => id),
    ref: box.with(() => ref, (v) => ref = v),
    disabled: box.with(() => Boolean(disabled))
  });
  const mergedProps = mergeProps(restProps, closeState.props);
  if (child) {
    $$payload.out.push("<!--[-->");
    child($$payload, { props: mergedProps });
    $$payload.out.push(`<!---->`);
  } else {
    $$payload.out.push("<!--[!-->");
    $$payload.out.push(`<button${spread_attributes({ ...mergedProps }, null)}>`);
    children?.($$payload);
    $$payload.out.push(`<!----></button>`);
  }
  $$payload.out.push(`<!--]-->`);
  bind_props($$props, { ref });
  pop();
}
function Dialog_content($$payload, $$props) {
  push();
  const uid = props_id($$payload);
  let {
    id = createId(uid),
    children,
    child,
    ref = null,
    forceMount = false,
    onCloseAutoFocus = noop,
    onOpenAutoFocus = noop,
    onEscapeKeydown = noop,
    onInteractOutside = noop,
    trapFocus = true,
    preventScroll = true,
    restoreScrollDelay = null,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const contentState = DialogContentState.create({
    id: box.with(() => id),
    ref: box.with(() => ref, (v) => ref = v)
  });
  const mergedProps = mergeProps(restProps, contentState.props);
  {
    let presence = function($$payload2) {
      {
        let focusScope = function($$payload3, { props: focusScopeProps }) {
          Escape_layer($$payload3, spread_props([
            mergedProps,
            {
              enabled: contentState.root.opts.open.current,
              ref: contentState.opts.ref,
              onEscapeKeydown: (e) => {
                onEscapeKeydown(e);
                if (e.defaultPrevented) return;
                contentState.root.handleClose();
              },
              children: ($$payload4) => {
                Dismissible_layer($$payload4, spread_props([
                  mergedProps,
                  {
                    ref: contentState.opts.ref,
                    enabled: contentState.root.opts.open.current,
                    onInteractOutside: (e) => {
                      onInteractOutside(e);
                      if (e.defaultPrevented) return;
                      contentState.root.handleClose();
                    },
                    children: ($$payload5) => {
                      Text_selection_layer($$payload5, spread_props([
                        mergedProps,
                        {
                          ref: contentState.opts.ref,
                          enabled: contentState.root.opts.open.current,
                          children: ($$payload6) => {
                            if (child) {
                              $$payload6.out.push("<!--[-->");
                              if (contentState.root.opts.open.current) {
                                $$payload6.out.push("<!--[-->");
                                Scroll_lock($$payload6, { preventScroll, restoreScrollDelay });
                              } else {
                                $$payload6.out.push("<!--[!-->");
                              }
                              $$payload6.out.push(`<!--]--> `);
                              child($$payload6, {
                                props: mergeProps(mergedProps, focusScopeProps),
                                ...contentState.snippetProps
                              });
                              $$payload6.out.push(`<!---->`);
                            } else {
                              $$payload6.out.push("<!--[!-->");
                              Scroll_lock($$payload6, { preventScroll });
                              $$payload6.out.push(`<!----> <div${spread_attributes({ ...mergeProps(mergedProps, focusScopeProps) }, null)}>`);
                              children?.($$payload6);
                              $$payload6.out.push(`<!----></div>`);
                            }
                            $$payload6.out.push(`<!--]-->`);
                          },
                          $$slots: { default: true }
                        }
                      ]));
                    },
                    $$slots: { default: true }
                  }
                ]));
              },
              $$slots: { default: true }
            }
          ]));
        };
        Focus_scope($$payload2, {
          ref: contentState.opts.ref,
          loop: true,
          trapFocus,
          enabled: shouldEnableFocusTrap({
            forceMount,
            present: contentState.root.opts.open.current,
            open: contentState.root.opts.open.current
          }),
          onOpenAutoFocus,
          onCloseAutoFocus: (e) => {
            onCloseAutoFocus(e);
            if (e.defaultPrevented) return;
            afterSleep(1, () => contentState.root.triggerNode?.focus());
          },
          focusScope
        });
      }
    };
    Presence_layer($$payload, spread_props([
      mergedProps,
      {
        forceMount,
        open: contentState.root.opts.open.current || forceMount,
        ref: contentState.opts.ref,
        presence,
        $$slots: { presence: true }
      }
    ]));
  }
  bind_props($$props, { ref });
  pop();
}
function Sheet_overlay($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out.push(`<!---->`);
    Dialog_overlay($$payload2, spread_props([
      {
        "data-slot": "sheet-overlay",
        class: cn("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50", className)
      },
      restProps,
      {
        get ref() {
          return ref;
        },
        set ref($$value) {
          ref = $$value;
          $$settled = false;
        }
      }
    ]));
    $$payload2.out.push(`<!---->`);
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref });
  pop();
}
const sheetVariants = tv({
  base: "bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500",
  variants: {
    side: {
      top: "data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b",
      bottom: "data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",
      left: "data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm",
      right: "data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm"
    }
  },
  defaultVariants: { side: "right" }
});
function Sheet_content($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    side = "right",
    showCloseButton,
    portalProps,
    children,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out.push(`<!---->`);
    Portal($$payload2, spread_props([
      portalProps,
      {
        children: ($$payload3) => {
          Sheet_overlay($$payload3, {});
          $$payload3.out.push(`<!----> <!---->`);
          Dialog_content($$payload3, spread_props([
            {
              "data-slot": "sheet-content",
              class: cn(sheetVariants({ side }), className)
            },
            restProps,
            {
              get ref() {
                return ref;
              },
              set ref($$value) {
                ref = $$value;
                $$settled = false;
              },
              children: ($$payload4) => {
                children?.($$payload4);
                $$payload4.out.push(`<!----> `);
                if (showCloseButton) {
                  $$payload4.out.push("<!--[-->");
                  $$payload4.out.push(`<!---->`);
                  Dialog_close($$payload4, {
                    class: "ring-offset-background focus-visible:ring-ring absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-hidden disabled:pointer-events-none",
                    children: ($$payload5) => {
                      X($$payload5, { class: "size-4" });
                      $$payload5.out.push(`<!----> <span class="sr-only">Close</span>`);
                    },
                    $$slots: { default: true }
                  });
                  $$payload4.out.push(`<!---->`);
                } else {
                  $$payload4.out.push("<!--[!-->");
                }
                $$payload4.out.push(`<!--]-->`);
              },
              $$slots: { default: true }
            }
          ]));
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      }
    ]));
    $$payload2.out.push(`<!---->`);
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref });
  pop();
}
export {
  DialogTriggerState as D,
  Sheet_content as S,
  setAuthStoreContext as a,
  setOrgStoreContext as b,
  setAppointmentStoreContext as c,
  getAuthStore as d,
  getOrgStore as e,
  getAppointmentStore as f,
  getUtilsStore as g,
  Dialog_overlay as h,
  Dialog_content as i,
  Dialog_close as j,
  DialogTitleState as k,
  DialogDescriptionState as l,
  DialogRootState as m,
  setUtilsStoreContext as s
};
