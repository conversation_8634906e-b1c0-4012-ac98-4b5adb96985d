import "clsx";
import { d as box } from "./create-id.js";
import "./states.svelte.js";
import { n as noop } from "./scroll-lock.js";
const defaultOptions = { afterMs: 1e4, onChange: noop };
function boxAutoReset(defaultValue, options) {
  const { afterMs, onChange, getWindow } = { ...defaultOptions, ...options };
  let timeout = null;
  let value = defaultValue;
  function resetAfter() {
    return getWindow().setTimeout(
      () => {
        value = defaultValue;
        onChange?.(defaultValue);
      },
      afterMs
    );
  }
  return box.with(() => value, (v) => {
    value = v;
    onChange?.(v);
    if (timeout) getWindow().clearTimeout(timeout);
    timeout = resetAfter();
  });
}
export {
  boxAutoReset as b
};
