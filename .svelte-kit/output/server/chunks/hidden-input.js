import { Q as copy_payload, R as assign_payload, G as spread_props } from "./index2.js";
import { I as Input } from "./input.js";
function Hidden_input($$payload, $$props) {
  let { value, props } = $$props;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    Input($$payload2, spread_props([
      { type: "hidden" },
      props,
      {
        get value() {
          return value;
        },
        set value($$value) {
          value = $$value;
          $$settled = false;
        }
      }
    ]));
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
}
export {
  Hidden_input as H
};
