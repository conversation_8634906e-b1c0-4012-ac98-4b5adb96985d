import { a as getRequestEvent, g as get_event_state, E as EVENT_STATE, w as with_event, b as create_remote_cache_key, d as stringify_remote_arg, s as stringify } from "./chunks/event-state.js";
import { error, json } from "@sveltejs/kit";
import { p as parse } from "./chunks/stringify.js";
import { D as DEV } from "./chunks/false.js";
import { b as base, c as app_dir, p as prerendering } from "./chunks/environment.js";
function create_validator(validate_or_fn, maybe_fn) {
  if (!maybe_fn) {
    return (arg) => {
      if (arg !== void 0) {
        error(400, "Bad Request");
      }
    };
  }
  if (validate_or_fn === "unchecked") {
    return (arg) => arg;
  }
  if ("~standard" in validate_or_fn) {
    return async (arg) => {
      const event = getRequestEvent();
      const state = get_event_state(event);
      const validate = validate_or_fn["~standard"].validate;
      const result = await validate(arg);
      if (result.issues) {
        error(
          400,
          await state.handleValidationError({
            ...result,
            event
          })
        );
      }
      return result.value;
    };
  }
  throw new Error(
    'Invalid validator passed to remote function. Expected "unchecked" or a Standard Schema (https://standardschema.dev)'
  );
}
function get_response(id, arg, event, get_result) {
  const state = get_event_state(event);
  const cache_key = create_remote_cache_key(id, stringify_remote_arg(arg, state.transport));
  return (state.remote_data ??= {})[cache_key] ??= get_result();
}
function check_experimental(feature) {
  {
    throw new Error(
      `Cannot use \`${feature}\` from \`$app/server\` without the experimental flag set to true. Please set kit.experimental.remoteFunctions to \`true\` in your config.`
    );
  }
}
function parse_remote_response(data, transport) {
  const revivers = {};
  for (const key in transport) {
    revivers[key] = transport[key].decode;
  }
  return parse(data, revivers);
}
async function run_remote_function(event, allow_cookies, arg, validate, fn) {
  const cleansed = {
    ...event,
    // @ts-expect-error this isn't part of the public `RequestEvent` type
    [EVENT_STATE]: event[EVENT_STATE],
    setHeaders: () => {
      throw new Error("setHeaders is not allowed in remote functions");
    },
    cookies: {
      ...event.cookies,
      set: (name, value, opts) => {
        if (!allow_cookies) {
          throw new Error("Cannot set cookies in `query` or `prerender` functions");
        }
        if (opts.path && !opts.path.startsWith("/")) {
          throw new Error("Cookies set in remote functions must have an absolute path");
        }
        return event.cookies.set(name, value, opts);
      },
      delete: (name, opts) => {
        if (!allow_cookies) {
          throw new Error("Cannot delete cookies in `query` or `prerender` functions");
        }
        if (opts.path && !opts.path.startsWith("/")) {
          throw new Error("Cookies deleted in remote functions must have an absolute path");
        }
        return event.cookies.delete(name, opts);
      }
    },
    route: { id: null },
    url: new URL(event.url.origin)
  };
  const validated = await with_event(cleansed, () => validate(arg));
  return with_event(cleansed, () => fn(validated));
}
// @__NO_SIDE_EFFECTS__
function command(validate_or_fn, maybe_fn) {
  check_experimental("command");
  const fn = maybe_fn ?? validate_or_fn;
  const validate = create_validator(validate_or_fn, maybe_fn);
  const __ = { type: "command", id: "", name: "" };
  const wrapper = (arg) => {
    const event = getRequestEvent();
    if (!event.isRemoteRequest) {
      throw new Error(
        `Cannot call a command (\`${__.name}(${maybe_fn ? "..." : ""})\`) during server-side rendering`
      );
    }
    get_event_state(event).refreshes ??= {};
    const promise = Promise.resolve(run_remote_function(event, true, arg, validate, fn));
    promise.updates = () => {
      throw new Error(`Cannot call '${__.name}(...).updates(...)' on the server`);
    };
    return (
      /** @type {ReturnType<RemoteCommand<Input, Output>>} */
      promise
    );
  };
  Object.defineProperty(wrapper, "__", { value: __ });
  return wrapper;
}
// @__NO_SIDE_EFFECTS__
function form(fn) {
  check_experimental("form");
  function create_instance(key) {
    const instance = {};
    instance.method = "POST";
    instance.onsubmit = () => {
    };
    Object.defineProperty(instance, "enhance", {
      value: () => {
        return { action: instance.action, method: instance.method, onsubmit: instance.onsubmit };
      }
    });
    const button_props = {
      type: "submit",
      onclick: () => {
      }
    };
    Object.defineProperty(button_props, "enhance", {
      value: () => {
        return { type: "submit", formaction: instance.buttonProps.formaction, onclick: () => {
        } };
      }
    });
    Object.defineProperty(instance, "buttonProps", {
      value: button_props
    });
    const __ = {
      type: "form",
      name: "",
      id: "",
      /** @param {FormData} form_data */
      fn: async (form_data) => {
        const event = getRequestEvent();
        const state = get_event_state(event);
        state.refreshes ??= {};
        const result = await run_remote_function(event, true, form_data, (d) => d, fn);
        if (!event.isRemoteRequest) {
          (state.remote_data ??= {})[__.id] = result;
        }
        return result;
      }
    };
    Object.defineProperty(instance, "__", { value: __ });
    Object.defineProperty(instance, "action", {
      get: () => `?/remote=${__.id}`,
      enumerable: true
    });
    Object.defineProperty(button_props, "formaction", {
      get: () => `?/remote=${__.id}`,
      enumerable: true
    });
    Object.defineProperty(instance, "result", {
      get() {
        try {
          const { remote_data } = get_event_state(getRequestEvent());
          return remote_data?.[__.id];
        } catch {
          return void 0;
        }
      }
    });
    if (key == void 0) {
      Object.defineProperty(instance, "for", {
        /** @type {RemoteForm<any>['for']} */
        value: (key2) => {
          const state = get_event_state(getRequestEvent());
          let instance2 = (state.form_instances ??= /* @__PURE__ */ new Map()).get(key2);
          if (!instance2) {
            instance2 = create_instance(key2);
            instance2.__.id = `${__.id}/${encodeURIComponent(JSON.stringify(key2))}`;
            instance2.__.name = __.name;
            state.form_instances.set(key2, instance2);
          }
          return instance2;
        }
      });
    }
    return instance;
  }
  return create_instance();
}
// @__NO_SIDE_EFFECTS__
function prerender(validate_or_fn, fn_or_options, maybe_options) {
  check_experimental("prerender");
  const maybe_fn = typeof fn_or_options === "function" ? fn_or_options : void 0;
  const options = maybe_options ?? (maybe_fn ? void 0 : fn_or_options);
  const fn = maybe_fn ?? validate_or_fn;
  const validate = create_validator(validate_or_fn, maybe_fn);
  const __ = {
    type: "prerender",
    id: "",
    name: "",
    has_arg: !!maybe_fn,
    inputs: options?.inputs,
    dynamic: options?.dynamic
  };
  const wrapper = (arg) => {
    const promise = (async () => {
      const event = getRequestEvent();
      const state = get_event_state(event);
      const payload = stringify_remote_arg(arg, state.transport);
      const id = __.id;
      const url = `${base}/${app_dir}/remote/${id}${payload ? `/${payload}` : ""}`;
      if (!state.prerendering && !DEV && !event.isRemoteRequest) {
        try {
          return await get_response(id, arg, event, async () => {
            const response = await fetch(new URL(url, event.url.origin).href);
            if (!response.ok) {
              throw new Error("Prerendered response not found");
            }
            const prerendered = await response.json();
            if (prerendered.type === "error") {
              error(prerendered.status, prerendered.error);
            }
            (state.remote_data ??= {})[create_remote_cache_key(id, payload)] = prerendered.result;
            return parse_remote_response(prerendered.result, state.transport);
          });
        } catch {
        }
      }
      if (state.prerendering?.remote_responses.has(url)) {
        return (
          /** @type {Promise<any>} */
          state.prerendering.remote_responses.get(url)
        );
      }
      const promise2 = get_response(
        id,
        arg,
        event,
        () => run_remote_function(event, false, arg, validate, fn)
      );
      if (state.prerendering) {
        state.prerendering.remote_responses.set(url, promise2);
      }
      const result = await promise2;
      if (state.prerendering) {
        const body = { type: "result", result: stringify(result, state.transport) };
        state.prerendering.dependencies.set(url, {
          body: JSON.stringify(body),
          response: json(body)
        });
      }
      return result;
    })();
    promise.catch(() => {
    });
    return (
      /** @type {RemoteResource<Output>} */
      promise
    );
  };
  Object.defineProperty(wrapper, "__", { value: __ });
  return wrapper;
}
// @__NO_SIDE_EFFECTS__
function query(validate_or_fn, maybe_fn) {
  check_experimental("query");
  const fn = maybe_fn ?? validate_or_fn;
  const validate = create_validator(validate_or_fn, maybe_fn);
  const __ = { type: "query", id: "", name: "" };
  const wrapper = (arg) => {
    if (prerendering) {
      throw new Error(
        `Cannot call query '${__.name}' while prerendering, as prerendered pages need static data. Use 'prerender' from $app/server instead`
      );
    }
    const event = getRequestEvent();
    const promise = get_response(
      __.id,
      arg,
      event,
      () => run_remote_function(event, false, arg, validate, fn)
    );
    promise.catch(() => {
    });
    promise.refresh = async () => {
      const event2 = getRequestEvent();
      const state = get_event_state(event2);
      const refreshes = state?.refreshes;
      if (!refreshes) {
        throw new Error(
          `Cannot call refresh on query '${__.name}' because it is not executed in the context of a command/form remote function`
        );
      }
      const cache_key = create_remote_cache_key(__.id, stringify_remote_arg(arg, state.transport));
      refreshes[cache_key] = await /** @type {Promise<any>} */
      promise;
    };
    promise.withOverride = () => {
      throw new Error(`Cannot call '${__.name}.withOverride()' on the server`);
    };
    return (
      /** @type {RemoteQuery<Output>} */
      promise
    );
  };
  Object.defineProperty(wrapper, "__", { value: __ });
  return wrapper;
}
export {
  command,
  form,
  prerender,
  query
};
