import { B as escape_html, y as pop, w as push } from "../../chunks/index2.js";
import "clsx";
import { g as goto } from "../../chunks/client.js";
import "../../chunks/client2.js";
import { B as <PERSON><PERSON> } from "../../chunks/button.js";
function _error($$payload, $$props) {
  push();
  let status = 0;
  let error = null;
  $$payload.out.push(`<div class="absolute top-1/2 left-1/2 mb-16 -translate-x-1/2 -translate-y-1/2 items-center justify-center text-center"><span class="from-foreground bg-gradient-to-b to-transparent bg-clip-text text-[10rem] leading-none font-extrabold text-transparent">${escape_html(status)}</span> <h2 class="font-heading my-2 text-2xl font-bold">Something's missing</h2> <p>Sorry, the page you are looking for doesn't exist or has been moved.</p> <small>${escape_html(error?.message)}</small> <div class="mt-8 flex justify-center gap-2">`);
  Button($$payload, {
    onclick: () => history.back(),
    variant: "default",
    size: "lg",
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->Go back`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!----> `);
  Button($$payload, {
    onclick: () => goto(),
    variant: "ghost",
    size: "lg",
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->Back to Home`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!----></div></div>`);
  pop();
}
export {
  _error as default
};
