import { A as head, y as pop, w as push } from "../../../../../chunks/index2.js";
import "@sveltejs/kit/internal";
import "../../../../../chunks/exports.js";
import "clsx";
import "../../../../../chunks/state.svelte.js";
import { ae as useSession } from "../../../../../chunks/auth-client.js";
import "superjson";
import "@oslojs/crypto/sha2";
import "decimal.js";
import "linq-extensions";
import "remult";
/* empty css                                                        */
function _page($$payload, $$props) {
  push();
  useSession();
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>Accept Invitation - SpenDeed</title>`;
  });
  $$payload.out.push(`<div class="invitation-page svelte-desgno"><div class="container svelte-desgno">`);
  {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="loading svelte-desgno"><div class="spinner svelte-desgno"></div> <p class="svelte-desgno">Loading invitation...</p></div>`);
  }
  $$payload.out.push(`<!--]--></div></div>`);
  pop();
}
export {
  _page as default
};
