import "clsx";
import { A as head, E as ensure_array_like, B as escape_html, G as spread_props, y as pop, w as push } from "../../../../chunks/index2.js";
import "../../../../chunks/client2.js";
import "../../../../chunks/states.svelte.js";
import "linq-extensions";
import "@sveltejs/kit/internal";
import "../../../../chunks/exports.js";
import "../../../../chunks/state.svelte.js";
import "../../../../chunks/nprogress.js";
import { C as Card_shell } from "../../../../chunks/card_shell.js";
import { B as Button } from "../../../../chunks/button.js";
import { C as Card, a as Card_content } from "../../../../chunks/card-content.js";
import { C as Card_header, a as Card_title, b as Card_description } from "../../../../chunks/card-title.js";
import { U as Users } from "../../../../chunks/users.js";
import { A as AreaChart, T as Target } from "../../../../chunks/AreaChart.js";
import { T as Trending_up } from "../../../../chunks/trending-up.js";
import { D as Dollar_sign } from "../../../../chunks/dollar-sign.js";
import { C as Chart_container, a as Chart_tooltip } from "../../../../chunks/chart-tooltip.js";
import "../../../../chunks/index4.js";
import { curveNatural } from "d3-shape";
import { scaleUtc } from "d3-scale";
import { c as createSvelteTable, D as Dropdown_menu_checkbox_item, T as Table, a as Table_header, b as Table_row, d as Table_head, F as Flex_render, e as Table_body, f as Table_cell } from "../../../../chunks/data-table.svelte.js";
import { I as Input } from "../../../../chunks/input.js";
import { R as Root, D as Dropdown_menu_trigger, a as Dropdown_menu_content } from "../../../../chunks/index6.js";
import { getFilteredRowModel, getSortedRowModel, getPaginationRowModel, getCoreRowModel } from "@tanstack/table-core";
import { U as User_plus } from "../../../../chunks/user-plus.js";
import { P as Plus } from "../../../../chunks/plus.js";
function Members_data_table($$payload, $$props) {
  push();
  let { members } = $$props;
  const getTierBadgeVariant = (tier) => {
    switch (tier) {
      case "Premium":
        return "default";
      case "Pro":
        return "secondary";
      case "Basic":
        return "outline";
      default:
        return "outline";
    }
  };
  const getStatusBadgeVariant = (status) => {
    switch (status) {
      case "Active":
        return "default";
      case "Inactive":
        return "secondary";
      case "Suspended":
        return "destructive";
      default:
        return "outline";
    }
  };
  const columns = [
    {
      accessorKey: "name",
      header: "Member",
      cell: ({ row }) => {
        const member = row.original;
        return `
					<div class="space-y-1">
						<div class="font-medium">${member.name}</div>
						<div class="text-sm text-muted-foreground">${member.email}</div>
					</div>
				`;
      }
    },
    {
      accessorKey: "tier",
      header: "Tier",
      cell: ({ row }) => {
        const tier = row.getValue("tier");
        const variant = getTierBadgeVariant(tier);
        return `<span class="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium bg-${variant === "default" ? "primary" : variant === "secondary" ? "secondary" : "muted"} text-${variant === "default" ? "primary-foreground" : variant === "secondary" ? "secondary-foreground" : "muted-foreground"}">${tier}</span>`;
      }
    },
    {
      accessorKey: "joinDate",
      header: "Join Date",
      cell: ({ row }) => {
        const date = new Date(row.getValue("joinDate"));
        return date.toLocaleDateString();
      }
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.getValue("status");
        const variant = getStatusBadgeVariant(status);
        return `<span class="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium bg-${variant === "default" ? "primary" : variant === "secondary" ? "secondary" : variant === "destructive" ? "destructive" : "muted"} text-${variant === "default" ? "primary-foreground" : variant === "secondary" ? "secondary-foreground" : variant === "destructive" ? "destructive-foreground" : "muted-foreground"}">${status}</span>`;
      }
    },
    {
      accessorKey: "totalSpent",
      header: "Total Spent",
      cell: ({ row }) => {
        const amount = parseFloat(row.getValue("totalSpent"));
        const formatted = new Intl.NumberFormat("en-US", { style: "currency", currency: "USD" }).format(amount);
        return `<div class="text-right font-medium">${formatted}</div>`;
      }
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const member = row.original;
        return `
					<div class="flex items-center justify-end gap-2">
						<button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-8 w-8 p-0" onclick="handleViewMember(${member.id})">
							<svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path></svg>
						</button>
						<button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-8 w-8 p-0" onclick="handleEditMember(${member.id})">
							<svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path></svg>
						</button>
						<button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-8 w-8 p-0 text-destructive hover:text-destructive" onclick="handleDeleteMember(${member.id})">
							<svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path></svg>
						</button>
					</div>
				`;
      }
    }
  ];
  let pagination = { pageIndex: 0, pageSize: 10 };
  let sorting = [];
  let columnFilters = [];
  let columnVisibility = {};
  const table = createSvelteTable({
    get data() {
      return members;
    },
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onPaginationChange: (updater) => {
      if (typeof updater === "function") {
        pagination = updater(pagination);
      } else {
        pagination = updater;
      }
    },
    onSortingChange: (updater) => {
      if (typeof updater === "function") {
        sorting = updater(sorting);
      } else {
        sorting = updater;
      }
    },
    onColumnFiltersChange: (updater) => {
      if (typeof updater === "function") {
        columnFilters = updater(columnFilters);
      } else {
        columnFilters = updater;
      }
    },
    onColumnVisibilityChange: (updater) => {
      if (typeof updater === "function") {
        columnVisibility = updater(columnVisibility);
      } else {
        columnVisibility = updater;
      }
    },
    state: {
      get pagination() {
        return pagination;
      },
      get sorting() {
        return sorting;
      },
      get columnFilters() {
        return columnFilters;
      },
      get columnVisibility() {
        return columnVisibility;
      }
    }
  });
  head($$payload, ($$payload2) => {
    $$payload2.out.push(`<script>
		window.handleViewMember = (id) => {
			const event = new CustomEvent('viewMember', { detail: { id } })
			document.dispatchEvent(event)
		}
		window.handleEditMember = (id) => {
			const event = new CustomEvent('editMember', { detail: { id } })
			document.dispatchEvent(event)
		}
		window.handleDeleteMember = (id) => {
			const event = new CustomEvent('deleteMember', { detail: { id } })
			document.dispatchEvent(event)
		}
	<\/script><!---->`);
  });
  $$payload.out.push(`<!---->`);
  Card($$payload, {
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->`);
      Card_header($$payload2, {
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Card_title($$payload3, {
            class: "flex items-center gap-2",
            children: ($$payload4) => {
              User_plus($$payload4, { class: "h-5 w-5" });
              $$payload4.out.push(`<!----> Recent Members`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> <!---->`);
          Card_description($$payload3, {
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Latest member registrations and activity`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!----> <!---->`);
      Card_content($$payload2, {
        children: ($$payload3) => {
          $$payload3.out.push(`<div class="space-y-4"><div class="flex items-center justify-between">`);
          Input($$payload3, {
            placeholder: "Filter members...",
            value: table.getColumn("name")?.getFilterValue(),
            onchange: (e) => table.getColumn("name")?.setFilterValue(e.currentTarget.value),
            oninput: (e) => table.getColumn("name")?.setFilterValue(e.currentTarget.value),
            class: "max-w-sm"
          });
          $$payload3.out.push(`<!----> <!---->`);
          Root($$payload3, {
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->`);
              {
                let child = function($$payload5, { props }) {
                  Button($$payload5, spread_props([
                    props,
                    {
                      variant: "outline",
                      class: "ml-auto",
                      children: ($$payload6) => {
                        $$payload6.out.push(`<!---->Columns`);
                      },
                      $$slots: { default: true }
                    }
                  ]));
                };
                Dropdown_menu_trigger($$payload4, { child, $$slots: { child: true } });
              }
              $$payload4.out.push(`<!----> <!---->`);
              Dropdown_menu_content($$payload4, {
                align: "end",
                children: ($$payload5) => {
                  const each_array = ensure_array_like(table.getAllColumns().filter((col) => col.getCanHide()));
                  $$payload5.out.push(`<!--[-->`);
                  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
                    let column = each_array[$$index];
                    $$payload5.out.push(`<!---->`);
                    Dropdown_menu_checkbox_item($$payload5, {
                      class: "capitalize",
                      checked: column.getIsVisible(),
                      onCheckedChange: (checked) => column.toggleVisibility(!!checked),
                      children: ($$payload6) => {
                        $$payload6.out.push(`<!---->${escape_html(column.id)}`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out.push(`<!---->`);
                  }
                  $$payload5.out.push(`<!--]-->`);
                },
                $$slots: { default: true }
              });
              $$payload4.out.push(`<!---->`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----></div> <div class="rounded-md border"><!---->`);
          Table($$payload3, {
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->`);
              Table_header($$payload4, {
                children: ($$payload5) => {
                  const each_array_1 = ensure_array_like(table.getHeaderGroups());
                  $$payload5.out.push(`<!--[-->`);
                  for (let $$index_2 = 0, $$length = each_array_1.length; $$index_2 < $$length; $$index_2++) {
                    let headerGroup = each_array_1[$$index_2];
                    $$payload5.out.push(`<!---->`);
                    Table_row($$payload5, {
                      children: ($$payload6) => {
                        const each_array_2 = ensure_array_like(headerGroup.headers);
                        $$payload6.out.push(`<!--[-->`);
                        for (let $$index_1 = 0, $$length2 = each_array_2.length; $$index_1 < $$length2; $$index_1++) {
                          let header = each_array_2[$$index_1];
                          $$payload6.out.push(`<!---->`);
                          Table_head($$payload6, {
                            colspan: header.colSpan,
                            children: ($$payload7) => {
                              if (!header.isPlaceholder) {
                                $$payload7.out.push("<!--[-->");
                                Flex_render($$payload7, {
                                  content: header.column.columnDef.header,
                                  context: header.getContext()
                                });
                              } else {
                                $$payload7.out.push("<!--[!-->");
                              }
                              $$payload7.out.push(`<!--]-->`);
                            },
                            $$slots: { default: true }
                          });
                          $$payload6.out.push(`<!---->`);
                        }
                        $$payload6.out.push(`<!--]-->`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out.push(`<!---->`);
                  }
                  $$payload5.out.push(`<!--]-->`);
                },
                $$slots: { default: true }
              });
              $$payload4.out.push(`<!----> <!---->`);
              Table_body($$payload4, {
                children: ($$payload5) => {
                  const each_array_3 = ensure_array_like(table.getRowModel().rows);
                  if (each_array_3.length !== 0) {
                    $$payload5.out.push("<!--[-->");
                    for (let $$index_4 = 0, $$length = each_array_3.length; $$index_4 < $$length; $$index_4++) {
                      let row = each_array_3[$$index_4];
                      $$payload5.out.push(`<!---->`);
                      Table_row($$payload5, {
                        "data-state": row.getIsSelected() && "selected",
                        children: ($$payload6) => {
                          const each_array_4 = ensure_array_like(row.getVisibleCells());
                          $$payload6.out.push(`<!--[-->`);
                          for (let $$index_3 = 0, $$length2 = each_array_4.length; $$index_3 < $$length2; $$index_3++) {
                            let cell = each_array_4[$$index_3];
                            $$payload6.out.push(`<!---->`);
                            Table_cell($$payload6, {
                              children: ($$payload7) => {
                                Flex_render($$payload7, {
                                  content: cell.column.columnDef.cell,
                                  context: cell.getContext()
                                });
                              },
                              $$slots: { default: true }
                            });
                            $$payload6.out.push(`<!---->`);
                          }
                          $$payload6.out.push(`<!--]-->`);
                        },
                        $$slots: { default: true }
                      });
                      $$payload5.out.push(`<!---->`);
                    }
                  } else {
                    $$payload5.out.push("<!--[!-->");
                    $$payload5.out.push(`<!---->`);
                    Table_row($$payload5, {
                      children: ($$payload6) => {
                        $$payload6.out.push(`<!---->`);
                        Table_cell($$payload6, {
                          colspan: columns.length,
                          class: "h-24 text-center",
                          children: ($$payload7) => {
                            $$payload7.out.push(`<!---->No results.`);
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out.push(`<!---->`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out.push(`<!---->`);
                  }
                  $$payload5.out.push(`<!--]-->`);
                },
                $$slots: { default: true }
              });
              $$payload4.out.push(`<!---->`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----></div> <div class="flex items-center justify-end space-x-2 py-4">`);
          Button($$payload3, {
            variant: "outline",
            size: "sm",
            onclick: () => table.previousPage(),
            disabled: !table.getCanPreviousPage(),
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Previous`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> `);
          Button($$payload3, {
            variant: "outline",
            size: "sm",
            onclick: () => table.nextPage(),
            disabled: !table.getCanNextPage(),
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Next`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----></div></div>`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!---->`);
  pop();
}
function Membership_growth_chart($$payload, $$props) {
  push();
  let { data } = $$props;
  const chartConfig = {
    members: { label: "Total Members", color: "var(--chart-1)" },
    newJoiners: { label: "New Joiners", color: "var(--chart-2)" }
  };
  $$payload.out.push(`<!---->`);
  Card($$payload, {
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->`);
      Card_header($$payload2, {
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Card_title($$payload3, {
            class: "flex items-center gap-2",
            children: ($$payload4) => {
              Trending_up($$payload4, { class: "h-5 w-5" });
              $$payload4.out.push(`<!----> Membership Growth`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> <!---->`);
          Card_description($$payload3, {
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Member acquisition trends over the last 6 months`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!----> <!---->`);
      Card_content($$payload2, {
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Chart_container($$payload3, {
            config: chartConfig,
            class: "h-[300px] w-full overflow-hidden",
            children: ($$payload4) => {
              {
                let tooltip = function($$payload5) {
                  $$payload5.out.push(`<!---->`);
                  Chart_tooltip($$payload5, {
                    indicator: "dot",
                    labelFormatter: (v) => {
                      return v.toLocaleDateString("en-US", { month: "long", year: "numeric" });
                    }
                  });
                  $$payload5.out.push(`<!---->`);
                };
                AreaChart($$payload4, {
                  data,
                  x: "date",
                  xScale: scaleUtc(),
                  padding: { left: 20, right: 20, top: 20, bottom: 40 },
                  yPadding: [0, 25],
                  series: [
                    {
                      key: "newJoiners",
                      label: "New Joiners",
                      color: "var(--color-newJoiners)"
                    },
                    {
                      key: "members",
                      label: "Total Members",
                      color: "var(--color-members)"
                    }
                  ],
                  seriesLayout: "stack",
                  props: {
                    area: {
                      curve: curveNatural,
                      "fill-opacity": 0.4,
                      line: { class: "stroke-2" },
                      motion: "tween"
                    },
                    xAxis: {
                      format: (v) => v.toLocaleDateString("en-US", { month: "short" })
                    }
                  },
                  tooltip,
                  $$slots: { tooltip: true }
                });
              }
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!---->`);
  pop();
}
function Membership_stats($$payload, $$props) {
  push();
  let { stats } = $$props;
  $$payload.out.push(`<div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4"><!---->`);
  Card($$payload, {
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->`);
      Card_header($$payload2, {
        class: "flex flex-row items-center justify-between space-y-0 pb-2",
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Card_title($$payload3, {
            class: "text-sm font-medium",
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Total Members`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> `);
          Users($$payload3, { class: "text-muted-foreground h-4 w-4" });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!----> <!---->`);
      Card_content($$payload2, {
        children: ($$payload3) => {
          $$payload3.out.push(`<div class="text-2xl font-bold">${escape_html(stats.totalMembers.toLocaleString())}</div> <p class="text-muted-foreground text-xs">+${escape_html(stats.growthRate)}% from last month</p>`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!----> <!---->`);
  Card($$payload, {
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->`);
      Card_header($$payload2, {
        class: "flex flex-row items-center justify-between space-y-0 pb-2",
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Card_title($$payload3, {
            class: "text-sm font-medium",
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Active Members`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> `);
          Target($$payload3, { class: "text-muted-foreground h-4 w-4" });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!----> <!---->`);
      Card_content($$payload2, {
        children: ($$payload3) => {
          $$payload3.out.push(`<div class="text-2xl font-bold">${escape_html(stats.activeMembers.toLocaleString())}</div> <p class="text-muted-foreground text-xs">${escape_html((stats.activeMembers / stats.totalMembers * 100).toFixed(1))}% of total</p>`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!----> <!---->`);
  Card($$payload, {
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->`);
      Card_header($$payload2, {
        class: "flex flex-row items-center justify-between space-y-0 pb-2",
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Card_title($$payload3, {
            class: "text-sm font-medium",
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->New This Month`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> `);
          Trending_up($$payload3, { class: "text-muted-foreground h-4 w-4" });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!----> <!---->`);
      Card_content($$payload2, {
        children: ($$payload3) => {
          $$payload3.out.push(`<div class="text-2xl font-bold">${escape_html(stats.newThisMonth.toLocaleString())}</div> <p class="text-muted-foreground text-xs">+${escape_html(stats.growthRate)}% from last month</p>`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!----> <!---->`);
  Card($$payload, {
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->`);
      Card_header($$payload2, {
        class: "flex flex-row items-center justify-between space-y-0 pb-2",
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Card_title($$payload3, {
            class: "text-sm font-medium",
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Monthly Revenue`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> `);
          Dollar_sign($$payload3, { class: "text-muted-foreground h-4 w-4" });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!----> <!---->`);
      Card_content($$payload2, {
        children: ($$payload3) => {
          $$payload3.out.push(`<div class="text-2xl font-bold">$${escape_html(stats.monthlyRevenue.toLocaleString())}</div> <p class="text-muted-foreground text-xs">+${escape_html(stats.growthRate)}% from last month</p>`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!----></div>`);
  pop();
}
function _page($$payload, $$props) {
  push();
  const membershipStats = {
    totalMembers: 2847,
    activeMembers: 2654,
    newThisMonth: 156,
    monthlyRevenue: 45680,
    growthRate: 12.5
  };
  const membershipGrowthData = [
    { date: /* @__PURE__ */ new Date("2024-07-01"), members: 2156, newJoiners: 145 },
    { date: /* @__PURE__ */ new Date("2024-08-01"), members: 2298, newJoiners: 142 },
    { date: /* @__PURE__ */ new Date("2024-09-01"), members: 2445, newJoiners: 147 },
    { date: /* @__PURE__ */ new Date("2024-10-01"), members: 2612, newJoiners: 167 },
    { date: /* @__PURE__ */ new Date("2024-11-01"), members: 2734, newJoiners: 122 },
    { date: /* @__PURE__ */ new Date("2024-12-01"), members: 2847, newJoiners: 113 }
  ];
  const recentMembers = [
    {
      id: 1,
      name: "Sarah Johnson",
      email: "<EMAIL>",
      tier: "Gold",
      joinDate: "2024-01-15",
      status: "Active",
      totalSpent: 1250
    },
    {
      id: 2,
      name: "Michael Chen",
      email: "<EMAIL>",
      tier: "Silver",
      joinDate: "2024-01-14",
      status: "Active",
      totalSpent: 890.5
    },
    {
      id: 3,
      name: "Emma Davis",
      email: "<EMAIL>",
      tier: "Bronze",
      joinDate: "2024-01-13",
      status: "Pending",
      totalSpent: 245.75
    },
    {
      id: 4,
      name: "James Wilson",
      email: "<EMAIL>",
      tier: "Gold",
      joinDate: "2024-01-12",
      status: "Active",
      totalSpent: 2100.25
    }
  ];
  const handleCreateMember = () => {
  };
  {
    let rightSlot = function($$payload2) {
      Button($$payload2, {
        onclick: handleCreateMember,
        class: "gap-2",
        children: ($$payload3) => {
          Plus($$payload3, { class: "h-4 w-4" });
          $$payload3.out.push(`<!----> Add Member`);
        },
        $$slots: { default: true }
      });
    };
    Card_shell($$payload, {
      heading: "Memberships Dashboard",
      subHeading: "Manage exclusive customer membership programs and track engagement",
      rightSlot,
      children: ($$payload2) => {
        $$payload2.out.push(`<div class="space-y-6">`);
        Membership_stats($$payload2, { stats: membershipStats });
        $$payload2.out.push(`<!----> `);
        Membership_growth_chart($$payload2, { data: membershipGrowthData });
        $$payload2.out.push(`<!----> `);
        Members_data_table($$payload2, {
          members: recentMembers
        });
        $$payload2.out.push(`<!----></div>`);
      }
    });
  }
  pop();
}
export {
  _page as default
};
