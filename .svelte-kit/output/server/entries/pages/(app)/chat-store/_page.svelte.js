import { B as escape_html, E as ensure_array_like, y as pop, w as push } from "../../../../chunks/index2.js";
import { C as Card, a as Card_content } from "../../../../chunks/card-content.js";
import { C as Card_header, a as Card_title, b as Card_description } from "../../../../chunks/card-title.js";
import "clsx";
function _page($$payload, $$props) {
  push();
  let stats = { activeChats: 0, completedSales: 0, averageRating: 0 };
  let recommendations = [{ id: 1, name: "Product 1", confidence: 0.95, price: 100 }];
  let description = `${stats.activeChats} active chats, ${stats.completedSales} sales completed`;
  $$payload.out.push(`<div class="grid gap-4">`);
  Card($$payload, {
    children: ($$payload2) => {
      Card_header($$payload2, {
        children: ($$payload3) => {
          Card_title($$payload3, {
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->AI Chat Store`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> `);
          Card_description($$payload3, {
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->${escape_html(description)}`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!----> `);
      Card_content($$payload2, {
        children: ($$payload3) => {
          $$payload3.out.push(`<div class="grid grid-cols-3 gap-4"><div class="space-y-2"><h3 class="text-xl font-semibold">Active Chats</h3> <p class="text-3xl font-bold">${escape_html(stats.activeChats)}</p></div> <div class="space-y-2"><h3 class="text-xl font-semibold">Completed Sales</h3> <p class="text-3xl font-bold">${escape_html(stats.completedSales)}</p></div> <div class="space-y-2"><h3 class="text-xl font-semibold">Average Rating</h3> <p class="text-3xl font-bold">${escape_html(stats.averageRating)}</p></div></div>`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!----> `);
  Card($$payload, {
    children: ($$payload2) => {
      Card_header($$payload2, {
        children: ($$payload3) => {
          Card_title($$payload3, {
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->AI Recommendations`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> `);
          Card_description($$payload3, {
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Top product recommendations by AI`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!----> `);
      Card_content($$payload2, {
        children: ($$payload3) => {
          const each_array = ensure_array_like(recommendations);
          $$payload3.out.push(`<div class="space-y-4"><!--[-->`);
          for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
            let rec = each_array[$$index];
            $$payload3.out.push(`<div class="flex items-center justify-between border-b pb-2"><div><p class="font-semibold">${escape_html(rec.name)}</p> <p class="text-muted-foreground text-sm">Confidence: ${escape_html((rec.confidence ?? 1 * 100).toFixed(1))}%</p></div> <p class="text-lg font-bold">$${escape_html(rec.price)}</p></div>`);
          }
          $$payload3.out.push(`<!--]--></div>`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!----></div>`);
  pop();
}
export {
  _page as default
};
