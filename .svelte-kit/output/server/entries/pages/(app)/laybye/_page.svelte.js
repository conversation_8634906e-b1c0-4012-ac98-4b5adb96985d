import "clsx";
import { G as spread_props, y as pop, w as push, E as ensure_array_like, I as attr_class, B as escape_html, D as stringify, K as attr_style } from "../../../../chunks/index2.js";
import "../../../../chunks/client2.js";
import { I as Icon } from "../../../../chunks/states.svelte.js";
import "linq-extensions";
import "@sveltejs/kit/internal";
import "../../../../chunks/exports.js";
import "../../../../chunks/state.svelte.js";
import "../../../../chunks/nprogress.js";
import { C as Card_shell } from "../../../../chunks/card_shell.js";
import { B as Button } from "../../../../chunks/button.js";
import { C as Card, a as Card_content } from "../../../../chunks/card-content.js";
import { C as Card_header, a as Card_title, b as Card_description } from "../../../../chunks/card-title.js";
import { D as Dollar_sign } from "../../../../chunks/dollar-sign.js";
import { C as Clock } from "../../../../chunks/clock.js";
import { T as Trending_up } from "../../../../chunks/trending-up.js";
import { T as Target, A as AreaChart } from "../../../../chunks/AreaChart.js";
import { C as Chart_container, a as Chart_tooltip } from "../../../../chunks/chart-tooltip.js";
import "../../../../chunks/index4.js";
import { curveNatural } from "d3-shape";
import { scaleUtc } from "d3-scale";
import { B as BarChart, P as Progress } from "../../../../chunks/progress.js";
import { c as createSvelteTable, D as Dropdown_menu_checkbox_item, T as Table, a as Table_header, b as Table_row, d as Table_head, F as Flex_render, e as Table_body, f as Table_cell, r as renderComponent } from "../../../../chunks/data-table.svelte.js";
import { I as Input } from "../../../../chunks/input.js";
import { R as Root, D as Dropdown_menu_trigger, a as Dropdown_menu_content } from "../../../../chunks/index6.js";
import { E as Eye } from "../../../../chunks/eye.js";
import { X } from "../../../../chunks/x.js";
import { B as Badge } from "../../../../chunks/badge.js";
import { getFilteredRowModel, getSortedRowModel, getPaginationRowModel, getCoreRowModel } from "@tanstack/table-core";
import { T as Triangle_alert } from "../../../../chunks/triangle-alert.js";
import { C as Calendar } from "../../../../chunks/calendar.js";
import { M as Mail } from "../../../../chunks/mail.js";
import { P as Plus } from "../../../../chunks/plus.js";
function Chart_pie($$payload, $$props) {
  push();
  /**
   * @license @lucide/svelte v0.539.0 - ISC
   *
   * ISC License
   *
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   *
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   *
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   *
   */
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    [
      "path",
      {
        "d": "M21 12c.552 0 1.005-.449.95-.998a10 10 0 0 0-8.953-8.951c-.55-.055-.998.398-.998.95v8a1 1 0 0 0 1 1z"
      }
    ],
    ["path", { "d": "M21.21 15.89A10 10 0 1 1 8 2.83" }]
  ];
  Icon($$payload, spread_props([
    { name: "chart-pie" },
    /**
     * @component @name ChartPie
     * @description Lucide SVG icon component, renders SVG Element with children.
     *
     * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgMTJjLjU1MiAwIDEuMDA1LS40NDkuOTUtLjk5OGExMCAxMCAwIDAgMC04Ljk1My04Ljk1MWMtLjU1LS4wNTUtLjk5OC4zOTgtLjk5OC45NXY4YTEgMSAwIDAgMCAxIDF6IiAvPgogIDxwYXRoIGQ9Ik0yMS4yMSAxNS44OUExMCAxMCAwIDEgMSA4IDIuODMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/chart-pie
     * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
     *
     * @param {Object} props - Lucide icons props and any valid SVG attribute
     * @returns {FunctionalComponent} Svelte component
     *
     */
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function Circle_alert($$payload, $$props) {
  push();
  /**
   * @license @lucide/svelte v0.539.0 - ISC
   *
   * ISC License
   *
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   *
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   *
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   *
   */
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    ["circle", { "cx": "12", "cy": "12", "r": "10" }],
    ["line", { "x1": "12", "x2": "12", "y1": "8", "y2": "12" }],
    [
      "line",
      { "x1": "12", "x2": "12.01", "y1": "16", "y2": "16" }
    ]
  ];
  Icon($$payload, spread_props([
    { name: "circle-alert" },
    /**
     * @component @name CircleAlert
     * @description Lucide SVG icon component, renders SVG Element with children.
     *
     * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8bGluZSB4MT0iMTIiIHgyPSIxMiIgeTE9IjgiIHkyPSIxMiIgLz4KICA8bGluZSB4MT0iMTIiIHgyPSIxMi4wMSIgeTE9IjE2IiB5Mj0iMTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/circle-alert
     * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
     *
     * @param {Object} props - Lucide icons props and any valid SVG attribute
     * @returns {FunctionalComponent} Svelte component
     *
     */
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function Circle_check_big($$payload, $$props) {
  push();
  /**
   * @license @lucide/svelte v0.539.0 - ISC
   *
   * ISC License
   *
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   *
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   *
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   *
   */
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    ["path", { "d": "M21.801 10A10 10 0 1 1 17 3.335" }],
    ["path", { "d": "m9 11 3 3L22 4" }]
  ];
  Icon($$payload, spread_props([
    { name: "circle-check-big" },
    /**
     * @component @name CircleCheckBig
     * @description Lucide SVG icon component, renders SVG Element with children.
     *
     * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEuODAxIDEwQTEwIDEwIDAgMSAxIDE3IDMuMzM1IiAvPgogIDxwYXRoIGQ9Im05IDExIDMgM0wyMiA0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/circle-check-big
     * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
     *
     * @param {Object} props - Lucide icons props and any valid SVG attribute
     * @returns {FunctionalComponent} Svelte component
     *
     */
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function Send($$payload, $$props) {
  push();
  /**
   * @license @lucide/svelte v0.539.0 - ISC
   *
   * ISC License
   *
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   *
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   *
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   *
   */
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    [
      "path",
      {
        "d": "M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z"
      }
    ],
    ["path", { "d": "m21.854 2.147-10.94 10.939" }]
  ];
  Icon($$payload, spread_props([
    { name: "send" },
    /**
     * @component @name Send
     * @description Lucide SVG icon component, renders SVG Element with children.
     *
     * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTQuNTM2IDIxLjY4NmEuNS41IDAgMCAwIC45MzctLjAyNGw2LjUtMTlhLjQ5Ni40OTYgMCAwIDAtLjYzNS0uNjM1bC0xOSA2LjVhLjUuNSAwIDAgMC0uMDI0LjkzN2w3LjkzIDMuMThhMiAyIDAgMCAxIDEuMTEyIDEuMTF6IiAvPgogIDxwYXRoIGQ9Im0yMS44NTQgMi4xNDctMTAuOTQgMTAuOTM5IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/send
     * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
     *
     * @param {Object} props - Lucide icons props and any valid SVG attribute
     * @returns {FunctionalComponent} Svelte component
     *
     */
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function Shopping_cart($$payload, $$props) {
  push();
  /**
   * @license @lucide/svelte v0.539.0 - ISC
   *
   * ISC License
   *
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   *
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   *
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   *
   */
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    ["circle", { "cx": "8", "cy": "21", "r": "1" }],
    ["circle", { "cx": "19", "cy": "21", "r": "1" }],
    [
      "path",
      {
        "d": "M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12"
      }
    ]
  ];
  Icon($$payload, spread_props([
    { name: "shopping-cart" },
    /**
     * @component @name ShoppingCart
     * @description Lucide SVG icon component, renders SVG Element with children.
     *
     * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSI4IiBjeT0iMjEiIHI9IjEiIC8+CiAgPGNpcmNsZSBjeD0iMTkiIGN5PSIyMSIgcj0iMSIgLz4KICA8cGF0aCBkPSJNMi4wNSAyLjA1aDJsMi42NiAxMi40MmEyIDIgMCAwIDAgMiAxLjU4aDkuNzhhMiAyIDAgMCAwIDEuOTUtMS41N2wxLjY1LTcuNDNINS4xMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/shopping-cart
     * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
     *
     * @param {Object} props - Lucide icons props and any valid SVG attribute
     * @returns {FunctionalComponent} Svelte component
     *
     */
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function Square_pen($$payload, $$props) {
  push();
  /**
   * @license @lucide/svelte v0.539.0 - ISC
   *
   * ISC License
   *
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   *
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   *
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   *
   */
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    [
      "path",
      {
        "d": "M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"
      }
    ],
    [
      "path",
      {
        "d": "M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z"
      }
    ]
  ];
  Icon($$payload, spread_props([
    { name: "square-pen" },
    /**
     * @component @name SquarePen
     * @description Lucide SVG icon component, renders SVG Element with children.
     *
     * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgM0g1YTIgMiAwIDAgMC0yIDJ2MTRhMiAyIDAgMCAwIDIgMmgxNGEyIDIgMCAwIDAgMi0ydi03IiAvPgogIDxwYXRoIGQ9Ik0xOC4zNzUgMi42MjVhMSAxIDAgMCAxIDMgM2wtOS4wMTMgOS4wMTRhMiAyIDAgMCAxLS44NTMuNTA1bC0yLjg3My44NGEuNS41IDAgMCAxLS42Mi0uNjJsLjg0LTIuODczYTIgMiAwIDAgMSAuNTA2LS44NTJ6IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/square-pen
     * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
     *
     * @param {Object} props - Lucide icons props and any valid SVG attribute
     * @returns {FunctionalComponent} Svelte component
     *
     */
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function Expiring_laybyes($$payload, $$props) {
  push();
  let { laybyes, onRemind, onExtend, onCancel, onView } = $$props;
  const getUrgencyVariant = (daysLeft) => {
    if (daysLeft <= 3) return "destructive";
    if (daysLeft <= 7) return "secondary";
    return "outline";
  };
  const getUrgencyText = (daysLeft) => {
    if (daysLeft < 0) return "OVERDUE";
    if (daysLeft === 0) return "Due Today";
    if (daysLeft === 1) return "1 day left";
    return `${daysLeft} days left`;
  };
  $$payload.out.push(`<!---->`);
  Card($$payload, {
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->`);
      Card_header($$payload2, {
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Card_title($$payload3, {
            class: "flex items-center gap-2",
            children: ($$payload4) => {
              Triangle_alert($$payload4, { class: "h-5 w-5 text-orange-500" });
              $$payload4.out.push(`<!----> Expiring Laybyes`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> <!---->`);
          Card_description($$payload3, {
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Laybyes requiring immediate attention`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!----> <!---->`);
      Card_content($$payload2, {
        children: ($$payload3) => {
          if (laybyes.length === 0) {
            $$payload3.out.push("<!--[-->");
            $$payload3.out.push(`<div class="text-center py-8 text-muted-foreground">`);
            Clock($$payload3, { class: "mx-auto h-12 w-12 mb-4 opacity-50" });
            $$payload3.out.push(`<!----> <p class="text-lg font-medium">No expiring laybyes</p> <p class="text-sm">All laybyes are on track with their payment schedules</p></div>`);
          } else {
            $$payload3.out.push("<!--[!-->");
            const each_array = ensure_array_like(laybyes);
            $$payload3.out.push(`<div class="space-y-4"><!--[-->`);
            for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
              let laybye = each_array[$$index];
              $$payload3.out.push(`<div${attr_class(`flex items-center justify-between space-x-4 rounded-lg border p-4 ${stringify(laybye.daysUntilExpiry <= 3 ? "border-destructive/50 bg-destructive/5" : laybye.daysUntilExpiry <= 7 ? "border-orange-200 bg-orange-50" : "")}`)}><div class="space-y-2 flex-1"><div class="flex items-center justify-between"><div><p class="font-medium">${escape_html(laybye.customerName)}</p> <p class="text-sm text-muted-foreground">${escape_html(laybye.customerEmail)}</p></div> `);
              Badge($$payload3, {
                variant: getUrgencyVariant(laybye.daysUntilExpiry),
                children: ($$payload4) => {
                  $$payload4.out.push(`<!---->${escape_html(getUrgencyText(laybye.daysUntilExpiry))}`);
                },
                $$slots: { default: true }
              });
              $$payload3.out.push(`<!----></div> <div class="text-sm text-muted-foreground"><div class="flex items-center gap-4"><span>Items: ${escape_html(laybye.items.slice(0, 2).join(", "))}${escape_html(laybye.items.length > 2 ? ` +${laybye.items.length - 2} more` : "")}</span></div> <div class="flex items-center gap-4 mt-1"><span>Remaining: <span class="font-medium text-orange-600">$${escape_html(laybye.remainingAmount.toFixed(2))}</span></span> <span class="flex items-center gap-1">`);
              Calendar($$payload3, { class: "h-3 w-3" });
              $$payload3.out.push(`<!----> Next: ${escape_html(new Date(laybye.nextPaymentDate).toLocaleDateString())}</span></div></div></div> <div class="flex items-center gap-2">`);
              Button($$payload3, {
                size: "sm",
                variant: "outline",
                onclick: () => onView?.(laybye.id),
                class: "h-8 px-3",
                children: ($$payload4) => {
                  $$payload4.out.push(`<!---->View`);
                },
                $$slots: { default: true }
              });
              $$payload3.out.push(`<!----> `);
              Button($$payload3, {
                size: "sm",
                variant: "outline",
                onclick: () => onRemind?.(laybye.id),
                class: "h-8 px-3",
                children: ($$payload4) => {
                  Mail($$payload4, { class: "h-3 w-3 mr-1" });
                  $$payload4.out.push(`<!----> Remind`);
                },
                $$slots: { default: true }
              });
              $$payload3.out.push(`<!----> `);
              Button($$payload3, {
                size: "sm",
                variant: "outline",
                onclick: () => onExtend?.(laybye.id),
                class: "h-8 px-3",
                children: ($$payload4) => {
                  Plus($$payload4, { class: "h-3 w-3 mr-1" });
                  $$payload4.out.push(`<!----> Extend`);
                },
                $$slots: { default: true }
              });
              $$payload3.out.push(`<!----> `);
              Button($$payload3, {
                size: "sm",
                variant: "destructive",
                onclick: () => onCancel?.(laybye.id),
                class: "h-8 px-3",
                children: ($$payload4) => {
                  $$payload4.out.push(`<!---->Cancel`);
                },
                $$slots: { default: true }
              });
              $$payload3.out.push(`<!----></div></div>`);
            }
            $$payload3.out.push(`<!--]--></div>`);
          }
          $$payload3.out.push(`<!--]-->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!---->`);
  pop();
}
function Laybye_actions($$payload, $$props) {
  push();
  let { laybyeId, onView, onEdit, onRemind, onCancel } = $$props;
  $$payload.out.push(`<div class="flex items-center justify-end gap-1">`);
  Button($$payload, {
    variant: "ghost",
    size: "sm",
    class: "h-8 w-8 p-0",
    onclick: () => onView?.(laybyeId),
    title: "View Details",
    children: ($$payload2) => {
      Eye($$payload2, { class: "h-4 w-4" });
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!----> `);
  Button($$payload, {
    variant: "ghost",
    size: "sm",
    class: "h-8 w-8 p-0",
    onclick: () => onEdit?.(laybyeId),
    title: "Edit",
    children: ($$payload2) => {
      Square_pen($$payload2, { class: "h-4 w-4" });
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!----> `);
  Button($$payload, {
    variant: "ghost",
    size: "sm",
    class: "h-8 w-8 p-0",
    onclick: () => onRemind?.(laybyeId),
    title: "Remind Customer",
    children: ($$payload2) => {
      Send($$payload2, { class: "h-4 w-4" });
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!----> `);
  Button($$payload, {
    variant: "ghost",
    size: "sm",
    class: "text-destructive hover:text-destructive h-8 w-8 p-0",
    onclick: () => onCancel?.(laybyeId),
    title: "Cancel",
    children: ($$payload2) => {
      X($$payload2, { class: "h-4 w-4" });
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!----></div>`);
  pop();
}
function Laybye_stats($$payload, $$props) {
  push();
  let { stats } = $$props;
  $$payload.out.push(`<div class="grid gap-4 md:grid-cols-2 lg:grid-cols-5"><!---->`);
  Card($$payload, {
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->`);
      Card_header($$payload2, {
        class: "flex flex-row items-center justify-between space-y-0 pb-2",
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Card_title($$payload3, {
            class: "text-sm font-medium",
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Active Laybyes`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> `);
          Shopping_cart($$payload3, { class: "text-muted-foreground h-4 w-4" });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!----> <!---->`);
      Card_content($$payload2, {
        children: ($$payload3) => {
          $$payload3.out.push(`<div class="text-2xl font-bold">${escape_html(stats.totalActiveLaybyes.toLocaleString())}</div> <p class="text-muted-foreground text-xs">+${escape_html(stats.growthRate)}% from last month</p>`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!----> <!---->`);
  Card($$payload, {
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->`);
      Card_header($$payload2, {
        class: "flex flex-row items-center justify-between space-y-0 pb-2",
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Card_title($$payload3, {
            class: "text-sm font-medium",
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Total Value`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> `);
          Dollar_sign($$payload3, { class: "text-muted-foreground h-4 w-4" });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!----> <!---->`);
      Card_content($$payload2, {
        children: ($$payload3) => {
          $$payload3.out.push(`<div class="text-2xl font-bold">$${escape_html(stats.totalLaybyeValue.toLocaleString())}</div> <p class="text-muted-foreground text-xs">Across all active laybyes</p>`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!----> <!---->`);
  Card($$payload, {
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->`);
      Card_header($$payload2, {
        class: "flex flex-row items-center justify-between space-y-0 pb-2",
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Card_title($$payload3, {
            class: "text-sm font-medium",
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Expiring This Month`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> `);
          Clock($$payload3, { class: "text-muted-foreground h-4 w-4" });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!----> <!---->`);
      Card_content($$payload2, {
        children: ($$payload3) => {
          $$payload3.out.push(`<div class="text-2xl font-bold">${escape_html(stats.laybyesExpiringThisMonth)}</div> <p class="text-muted-foreground text-xs">Require immediate attention</p>`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!----> <!---->`);
  Card($$payload, {
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->`);
      Card_header($$payload2, {
        class: "flex flex-row items-center justify-between space-y-0 pb-2",
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Card_title($$payload3, {
            class: "text-sm font-medium",
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Average Amount`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> `);
          Trending_up($$payload3, { class: "text-muted-foreground h-4 w-4" });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!----> <!---->`);
      Card_content($$payload2, {
        children: ($$payload3) => {
          $$payload3.out.push(`<div class="text-2xl font-bold">$${escape_html(stats.averageLaybyeAmount.toLocaleString())}</div> <p class="text-muted-foreground text-xs">Per laybye transaction</p>`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!----> <!---->`);
  Card($$payload, {
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->`);
      Card_header($$payload2, {
        class: "flex flex-row items-center justify-between space-y-0 pb-2",
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Card_title($$payload3, {
            class: "text-sm font-medium",
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Completion Rate`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> `);
          Target($$payload3, { class: "text-muted-foreground h-4 w-4" });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!----> <!---->`);
      Card_content($$payload2, {
        children: ($$payload3) => {
          $$payload3.out.push(`<div class="text-2xl font-bold">${escape_html(stats.completionRate)}%</div> <p class="text-muted-foreground text-xs">Successfully completed</p>`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!----></div>`);
  pop();
}
function Laybye_status_badge($$payload, $$props) {
  let { status } = $$props;
  const getStatusBadgeVariant = (status2) => {
    switch (status2) {
      case "Active":
        return "default";
      case "Completed":
        return "secondary";
      case "Expired":
        return "destructive";
      case "Cancelled":
        return "outline";
      default:
        return "outline";
    }
  };
  const variant = getStatusBadgeVariant(status);
  Badge($$payload, {
    variant,
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->${escape_html(status)}`);
    },
    $$slots: { default: true }
  });
}
function Laybye_status_chart($$payload, $$props) {
  let { data } = $$props;
  const chartConfig = {
    Active: { label: "Active", color: "var(--chart-1)" },
    Completed: { label: "Completed", color: "var(--chart-2)" },
    Expired: { label: "Expired", color: "var(--chart-3)" },
    Cancelled: { label: "Cancelled", color: "var(--chart-4)" }
  };
  const getStatusColor = (status) => {
    switch (status) {
      case "Active":
        return "var(--chart-1)";
      case "Completed":
        return "var(--chart-2)";
      case "Expired":
        return "var(--chart-3)";
      case "Cancelled":
        return "var(--chart-4)";
      default:
        return "var(--chart-5)";
    }
  };
  $$payload.out.push(`<!---->`);
  Card($$payload, {
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->`);
      Card_header($$payload2, {
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Card_title($$payload3, {
            class: "flex items-center gap-2",
            children: ($$payload4) => {
              Chart_pie($$payload4, { class: "h-5 w-5" });
              $$payload4.out.push(`<!----> Status Distribution`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> <!---->`);
          Card_description($$payload3, {
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Current laybye status breakdown`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!----> <!---->`);
      Card_content($$payload2, {
        children: ($$payload3) => {
          const each_array = ensure_array_like(data);
          $$payload3.out.push(`<div class="space-y-4"><div class="grid grid-cols-2 gap-4"><!--[-->`);
          for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
            let item = each_array[$$index];
            $$payload3.out.push(`<div class="flex items-center justify-between"><div class="flex items-center gap-2"><div class="h-3 w-3 rounded-full"${attr_style(`background-color: ${stringify(getStatusColor(item.status))}`)}></div> <span class="text-sm font-medium">${escape_html(item.status)}</span></div> <div class="text-right"><div class="text-sm font-bold">${escape_html(item.count)}</div> <div class="text-muted-foreground text-xs">${escape_html(item.percentage)}%</div></div></div>`);
          }
          $$payload3.out.push(`<!--]--></div> <!---->`);
          Chart_container($$payload3, {
            config: chartConfig,
            class: "h-[200px]",
            children: ($$payload4) => {
              {
                let tooltip = function($$payload5) {
                  $$payload5.out.push(`<!---->`);
                  Chart_tooltip($$payload5, {
                    indicator: "dashed",
                    labelFormatter: (label) => `${label} Laybyes`
                  });
                  $$payload5.out.push(`<!---->`);
                };
                BarChart($$payload4, {
                  data,
                  x: "status",
                  y: "count",
                  yPadding: [0, 25],
                  props: { bars: { "fill-opacity": 0.8, motion: "tween" } },
                  tooltip,
                  $$slots: { tooltip: true }
                });
              }
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----></div>`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!---->`);
}
function Laybye_trends_chart($$payload, $$props) {
  push();
  let { data } = $$props;
  const chartConfig = {
    laybyes: { label: "Laybyes Created", color: "var(--chart-1)" },
    value: { label: "Total Value", color: "var(--chart-2)" }
  };
  $$payload.out.push(`<!---->`);
  Card($$payload, {
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->`);
      Card_header($$payload2, {
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Card_title($$payload3, {
            class: "flex items-center gap-2",
            children: ($$payload4) => {
              Trending_up($$payload4, { class: "h-5 w-5" });
              $$payload4.out.push(`<!----> Laybye Trends`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> <!---->`);
          Card_description($$payload3, {
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Monthly laybye creation and value trends over the last 6 months`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!----> <!---->`);
      Card_content($$payload2, {
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Chart_container($$payload3, {
            config: chartConfig,
            class: "h-[300px] w-full overflow-hidden",
            children: ($$payload4) => {
              {
                let tooltip = function($$payload5) {
                  $$payload5.out.push(`<!---->`);
                  Chart_tooltip($$payload5, {
                    indicator: "dot",
                    labelFormatter: (v) => {
                      return v.toLocaleDateString("en-US", { month: "long", year: "numeric" });
                    }
                  });
                  $$payload5.out.push(`<!---->`);
                };
                AreaChart($$payload4, {
                  data,
                  x: "date",
                  xScale: scaleUtc(),
                  padding: { left: 20, right: 20, top: 20, bottom: 40 },
                  yPadding: [0, 25],
                  series: [
                    {
                      key: "laybyes",
                      label: "Laybyes Created",
                      color: "var(--color-laybyes)"
                    },
                    {
                      key: "value",
                      label: "Total Value ($)",
                      color: "var(--color-value)"
                    }
                  ],
                  seriesLayout: "stack",
                  props: {
                    area: {
                      curve: curveNatural,
                      "fill-opacity": 0.4,
                      line: { class: "stroke-2" },
                      motion: "tween"
                    },
                    xAxis: {
                      format: (v) => v.toLocaleDateString("en-US", { month: "short" })
                    }
                  },
                  tooltip,
                  $$slots: { tooltip: true }
                });
              }
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!---->`);
  pop();
}
function Payment_schedule($$payload, $$props) {
  push();
  let { schedules } = $$props;
  const getPaymentStatusIcon = (status) => {
    switch (status) {
      case "paid":
        return Circle_check_big;
      case "overdue":
        return Circle_alert;
      default:
        return Clock;
    }
  };
  const getPaymentStatusVariant = (status) => {
    switch (status) {
      case "paid":
        return "default";
      case "overdue":
        return "destructive";
      default:
        return "secondary";
    }
  };
  const getPaymentStatusColor = (status) => {
    switch (status) {
      case "paid":
        return "text-green-600";
      case "overdue":
        return "text-red-600";
      default:
        return "text-orange-600";
    }
  };
  $$payload.out.push(`<!---->`);
  Card($$payload, {
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->`);
      Card_header($$payload2, {
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Card_title($$payload3, {
            class: "flex items-center gap-2",
            children: ($$payload4) => {
              Calendar($$payload4, { class: "h-5 w-5" });
              $$payload4.out.push(`<!----> Payment Schedules`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> <!---->`);
          Card_description($$payload3, {
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Upcoming payment installments and progress tracking`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!----> <!---->`);
      Card_content($$payload2, {
        children: ($$payload3) => {
          const each_array = ensure_array_like(schedules);
          $$payload3.out.push(`<div class="space-y-6"><!--[-->`);
          for (let $$index_1 = 0, $$length = each_array.length; $$index_1 < $$length; $$index_1++) {
            let schedule = each_array[$$index_1];
            const each_array_1 = ensure_array_like(schedule.payments);
            $$payload3.out.push(`<div class="space-y-3 p-4 rounded-lg border"><div class="flex items-center justify-between"><div><h4 class="font-medium">${escape_html(schedule.customerName)}</h4> <p class="text-sm text-muted-foreground">Total: $${escape_html(schedule.totalAmount.toFixed(2))}</p></div> <div class="text-right"><div class="text-sm font-medium">${escape_html(schedule.overallProgress)}% Complete</div> `);
            Progress($$payload3, { value: schedule.overallProgress, class: "w-24 h-2 mt-1" });
            $$payload3.out.push(`<!----></div></div> <div class="grid gap-2"><!--[-->`);
            for (let $$index = 0, $$length2 = each_array_1.length; $$index < $$length2; $$index++) {
              let payment = each_array_1[$$index];
              const StatusIcon = getPaymentStatusIcon(payment.status);
              $$payload3.out.push(`<div class="flex items-center justify-between p-3 rounded-md bg-muted/30"><div class="flex items-center gap-3"><!---->`);
              StatusIcon($$payload3, {
                class: `h-4 w-4 ${stringify(getPaymentStatusColor(payment.status))}`
              });
              $$payload3.out.push(`<!----> <div><div class="text-sm font-medium">Installment ${escape_html(payment.installmentNumber)}</div> <div class="text-xs text-muted-foreground">Due: ${escape_html(new Date(payment.dueDate).toLocaleDateString())} `);
              if (payment.paidDate) {
                $$payload3.out.push("<!--[-->");
                $$payload3.out.push(`• Paid: ${escape_html(new Date(payment.paidDate).toLocaleDateString())}`);
              } else {
                $$payload3.out.push("<!--[!-->");
              }
              $$payload3.out.push(`<!--]--></div></div></div> <div class="flex items-center gap-2"><span class="text-sm font-medium">$${escape_html(payment.amount.toFixed(2))}</span> `);
              Badge($$payload3, {
                variant: getPaymentStatusVariant(payment.status),
                class: "text-xs",
                children: ($$payload4) => {
                  $$payload4.out.push(`<!---->${escape_html(payment.status.charAt(0).toUpperCase() + payment.status.slice(1))}`);
                },
                $$slots: { default: true }
              });
              $$payload3.out.push(`<!----></div></div>`);
            }
            $$payload3.out.push(`<!--]--></div></div>`);
          }
          $$payload3.out.push(`<!--]--> `);
          if (schedules.length === 0) {
            $$payload3.out.push("<!--[-->");
            $$payload3.out.push(`<div class="text-center py-8 text-muted-foreground">`);
            Calendar($$payload3, { class: "mx-auto h-12 w-12 mb-4 opacity-50" });
            $$payload3.out.push(`<!----> <p class="text-lg font-medium">No payment schedules</p> <p class="text-sm">Payment schedules will appear here when laybyes are created</p></div>`);
          } else {
            $$payload3.out.push("<!--[!-->");
          }
          $$payload3.out.push(`<!--]--></div>`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!---->`);
  pop();
}
function Recent_laybyes_table($$payload, $$props) {
  push();
  let { laybyes, onView, onEdit, onCancel, onRemind } = $$props;
  const columns = [
    {
      accessorKey: "customerName",
      header: "Customer",
      cell: ({ row }) => {
        const laybye = row.original;
        return `
					<div class="space-y-1">
						<div class="font-medium">${laybye.customerName}</div>
						<div class="text-sm text-muted-foreground">${laybye.customerEmail}</div>
					</div>
				`;
      }
    },
    {
      accessorKey: "items",
      header: "Items",
      cell: ({ row }) => {
        const items = row.getValue("items");
        const displayItems = items.slice(0, 2).join(", ");
        const moreCount = items.length - 2;
        return `
					<div class="space-y-1">
						<div class="font-medium">${displayItems}</div>
						${moreCount > 0 ? `<div class="text-xs text-muted-foreground">+${moreCount} more items</div>` : ""}
					</div>
				`;
      }
    },
    {
      accessorKey: "totalAmount",
      header: "Total Amount",
      cell: ({ row }) => {
        const amount = row.getValue("totalAmount");
        return `<span class="font-medium">$${amount.toFixed(2)}</span>`;
      }
    },
    {
      accessorKey: "paidAmount",
      header: "Paid / Remaining",
      cell: ({ row }) => {
        const laybye = row.original;
        const paidPercentage = laybye.paidAmount / laybye.totalAmount * 100;
        return `
					<div class="space-y-1">
						<div class="text-sm">
							<span class="font-medium text-green-600">$${laybye.paidAmount.toFixed(2)}</span>
							<span class="text-muted-foreground"> / </span>
							<span class="font-medium text-orange-600">$${laybye.remainingAmount.toFixed(2)}</span>
						</div>
						<div class="w-full bg-secondary rounded-full h-2">
							<div class="bg-primary h-2 rounded-full" style="width: ${paidPercentage}%"></div>
						</div>
					</div>
				`;
      }
    },
    {
      accessorKey: "nextPaymentDate",
      header: "Next Payment",
      cell: ({ row }) => {
        const date = new Date(row.getValue("nextPaymentDate"));
        const isOverdue = date < /* @__PURE__ */ new Date();
        return `
					<div class="text-sm ${isOverdue ? "text-destructive font-medium" : ""}">
						${date.toLocaleDateString()}
						${isOverdue ? '<div class="text-xs">OVERDUE</div>' : ""}
					</div>
				`;
      }
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.getValue("status");
        return renderComponent(Laybye_status_badge, { status });
      }
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const laybye = row.original;
        return renderComponent(Laybye_actions, { laybyeId: laybye.id, onView, onEdit, onRemind, onCancel });
      }
    }
  ];
  let pagination = { pageIndex: 0, pageSize: 10 };
  let sorting = [];
  let columnFilters = [];
  let columnVisibility = {};
  const table = createSvelteTable({
    get data() {
      return laybyes;
    },
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onPaginationChange: (updater) => {
      if (typeof updater === "function") {
        pagination = updater(pagination);
      } else {
        pagination = updater;
      }
    },
    onSortingChange: (updater) => {
      if (typeof updater === "function") {
        sorting = updater(sorting);
      } else {
        sorting = updater;
      }
    },
    onColumnFiltersChange: (updater) => {
      if (typeof updater === "function") {
        columnFilters = updater(columnFilters);
      } else {
        columnFilters = updater;
      }
    },
    onColumnVisibilityChange: (updater) => {
      if (typeof updater === "function") {
        columnVisibility = updater(columnVisibility);
      } else {
        columnVisibility = updater;
      }
    },
    state: {
      get pagination() {
        return pagination;
      },
      get sorting() {
        return sorting;
      },
      get columnFilters() {
        return columnFilters;
      },
      get columnVisibility() {
        return columnVisibility;
      }
    }
  });
  $$payload.out.push(`<!---->`);
  Card($$payload, {
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->`);
      Card_header($$payload2, {
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Card_title($$payload3, {
            class: "flex items-center gap-2",
            children: ($$payload4) => {
              Shopping_cart($$payload4, { class: "h-5 w-5" });
              $$payload4.out.push(`<!----> Recent Laybyes`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> <!---->`);
          Card_description($$payload3, {
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Latest laybye transactions and payment status`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!----> <!---->`);
      Card_content($$payload2, {
        children: ($$payload3) => {
          $$payload3.out.push(`<div class="space-y-4"><div class="flex items-center justify-between">`);
          Input($$payload3, {
            placeholder: "Filter by customer...",
            value: table.getColumn("customerName")?.getFilterValue(),
            onchange: (e) => table.getColumn("customerName")?.setFilterValue(e.currentTarget.value),
            oninput: (e) => table.getColumn("customerName")?.setFilterValue(e.currentTarget.value),
            class: "max-w-sm"
          });
          $$payload3.out.push(`<!----> <!---->`);
          Root($$payload3, {
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->`);
              {
                let child = function($$payload5, { props }) {
                  Button($$payload5, spread_props([
                    props,
                    {
                      variant: "outline",
                      class: "ml-auto",
                      children: ($$payload6) => {
                        $$payload6.out.push(`<!---->Columns`);
                      },
                      $$slots: { default: true }
                    }
                  ]));
                };
                Dropdown_menu_trigger($$payload4, { child, $$slots: { child: true } });
              }
              $$payload4.out.push(`<!----> <!---->`);
              Dropdown_menu_content($$payload4, {
                align: "end",
                children: ($$payload5) => {
                  const each_array = ensure_array_like(table.getAllColumns().filter((col) => col.getCanHide()));
                  $$payload5.out.push(`<!--[-->`);
                  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
                    let column = each_array[$$index];
                    $$payload5.out.push(`<!---->`);
                    Dropdown_menu_checkbox_item($$payload5, {
                      class: "capitalize",
                      checked: column.getIsVisible(),
                      onCheckedChange: (checked) => column.toggleVisibility(!!checked),
                      children: ($$payload6) => {
                        $$payload6.out.push(`<!---->${escape_html(column.id)}`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out.push(`<!---->`);
                  }
                  $$payload5.out.push(`<!--]-->`);
                },
                $$slots: { default: true }
              });
              $$payload4.out.push(`<!---->`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----></div> <div class="rounded-md border"><!---->`);
          Table($$payload3, {
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->`);
              Table_header($$payload4, {
                children: ($$payload5) => {
                  const each_array_1 = ensure_array_like(table.getHeaderGroups());
                  $$payload5.out.push(`<!--[-->`);
                  for (let $$index_2 = 0, $$length = each_array_1.length; $$index_2 < $$length; $$index_2++) {
                    let headerGroup = each_array_1[$$index_2];
                    $$payload5.out.push(`<!---->`);
                    Table_row($$payload5, {
                      children: ($$payload6) => {
                        const each_array_2 = ensure_array_like(headerGroup.headers);
                        $$payload6.out.push(`<!--[-->`);
                        for (let $$index_1 = 0, $$length2 = each_array_2.length; $$index_1 < $$length2; $$index_1++) {
                          let header = each_array_2[$$index_1];
                          $$payload6.out.push(`<!---->`);
                          Table_head($$payload6, {
                            colspan: header.colSpan,
                            children: ($$payload7) => {
                              if (!header.isPlaceholder) {
                                $$payload7.out.push("<!--[-->");
                                Flex_render($$payload7, {
                                  content: header.column.columnDef.header,
                                  context: header.getContext()
                                });
                              } else {
                                $$payload7.out.push("<!--[!-->");
                              }
                              $$payload7.out.push(`<!--]-->`);
                            },
                            $$slots: { default: true }
                          });
                          $$payload6.out.push(`<!---->`);
                        }
                        $$payload6.out.push(`<!--]-->`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out.push(`<!---->`);
                  }
                  $$payload5.out.push(`<!--]-->`);
                },
                $$slots: { default: true }
              });
              $$payload4.out.push(`<!----> <!---->`);
              Table_body($$payload4, {
                children: ($$payload5) => {
                  const each_array_3 = ensure_array_like(table.getRowModel().rows);
                  if (each_array_3.length !== 0) {
                    $$payload5.out.push("<!--[-->");
                    for (let $$index_4 = 0, $$length = each_array_3.length; $$index_4 < $$length; $$index_4++) {
                      let row = each_array_3[$$index_4];
                      $$payload5.out.push(`<!---->`);
                      Table_row($$payload5, {
                        "data-state": row.getIsSelected() && "selected",
                        children: ($$payload6) => {
                          const each_array_4 = ensure_array_like(row.getVisibleCells());
                          $$payload6.out.push(`<!--[-->`);
                          for (let $$index_3 = 0, $$length2 = each_array_4.length; $$index_3 < $$length2; $$index_3++) {
                            let cell = each_array_4[$$index_3];
                            $$payload6.out.push(`<!---->`);
                            Table_cell($$payload6, {
                              children: ($$payload7) => {
                                Flex_render($$payload7, {
                                  content: cell.column.columnDef.cell,
                                  context: cell.getContext()
                                });
                              },
                              $$slots: { default: true }
                            });
                            $$payload6.out.push(`<!---->`);
                          }
                          $$payload6.out.push(`<!--]-->`);
                        },
                        $$slots: { default: true }
                      });
                      $$payload5.out.push(`<!---->`);
                    }
                  } else {
                    $$payload5.out.push("<!--[!-->");
                    $$payload5.out.push(`<!---->`);
                    Table_row($$payload5, {
                      children: ($$payload6) => {
                        $$payload6.out.push(`<!---->`);
                        Table_cell($$payload6, {
                          colspan: columns.length,
                          class: "h-24 text-center",
                          children: ($$payload7) => {
                            $$payload7.out.push(`<!---->No results.`);
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out.push(`<!---->`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out.push(`<!---->`);
                  }
                  $$payload5.out.push(`<!--]-->`);
                },
                $$slots: { default: true }
              });
              $$payload4.out.push(`<!---->`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----></div> <div class="flex items-center justify-end space-x-2 py-4">`);
          Button($$payload3, {
            variant: "outline",
            size: "sm",
            onclick: () => table.previousPage(),
            disabled: !table.getCanPreviousPage(),
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Previous`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> `);
          Button($$payload3, {
            variant: "outline",
            size: "sm",
            onclick: () => table.nextPage(),
            disabled: !table.getCanNextPage(),
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Next`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----></div></div>`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!---->`);
  pop();
}
function _page($$payload, $$props) {
  push();
  const laybyeStats = {
    totalActiveLaybyes: 89,
    totalLaybyeValue: 45680,
    laybyesExpiringThisMonth: 12,
    averageLaybyeAmount: 513,
    completionRate: 78.5,
    growthRate: 15.2
  };
  const laybyeTrendsData = [
    { date: /* @__PURE__ */ new Date("2024-07-01"), laybyes: 23, value: 11500 },
    { date: /* @__PURE__ */ new Date("2024-08-01"), laybyes: 28, value: 14200 },
    { date: /* @__PURE__ */ new Date("2024-09-01"), laybyes: 31, value: 15800 },
    { date: /* @__PURE__ */ new Date("2024-10-01"), laybyes: 26, value: 13300 },
    { date: /* @__PURE__ */ new Date("2024-11-01"), laybyes: 34, value: 17400 },
    { date: /* @__PURE__ */ new Date("2024-12-01"), laybyes: 29, value: 14900 }
  ];
  const laybyeStatusData = [
    { status: "Active", count: 89, percentage: 57.1 },
    { status: "Completed", count: 45, percentage: 28.8 },
    { status: "Expired", count: 12, percentage: 7.7 },
    { status: "Cancelled", count: 10, percentage: 6.4 }
  ];
  const recentLaybyesData = [
    {
      id: 1,
      customerName: "Sarah Johnson",
      customerEmail: "<EMAIL>",
      items: ["iPhone 15 Pro", "AirPods Pro", "MagSafe Charger"],
      totalAmount: 1299.99,
      paidAmount: 433.33,
      remainingAmount: 866.66,
      nextPaymentDate: "2024-02-15",
      status: "Active",
      createdDate: "2024-01-15"
    },
    {
      id: 2,
      customerName: "Michael Chen",
      customerEmail: "<EMAIL>",
      items: ["MacBook Air M3", "Magic Mouse"],
      totalAmount: 1399,
      paidAmount: 466.33,
      remainingAmount: 932.67,
      nextPaymentDate: "2024-02-10",
      status: "Active",
      createdDate: "2024-01-10"
    },
    {
      id: 3,
      customerName: "Emma Davis",
      customerEmail: "<EMAIL>",
      items: ['iPad Pro 12.9"', "Apple Pencil", "Smart Keyboard"],
      totalAmount: 1549,
      paidAmount: 1549,
      remainingAmount: 0,
      nextPaymentDate: "2024-01-20",
      status: "Completed",
      createdDate: "2023-12-20"
    },
    {
      id: 4,
      customerName: "James Wilson",
      customerEmail: "<EMAIL>",
      items: ["Apple Watch Ultra 2", "Sport Loop Band"],
      totalAmount: 849,
      paidAmount: 283,
      remainingAmount: 566,
      nextPaymentDate: "2024-01-25",
      status: "Expired",
      createdDate: "2023-12-25"
    }
  ];
  const expiringLaybyesData = [
    {
      id: 1,
      customerName: "Sarah Johnson",
      customerEmail: "<EMAIL>",
      items: ["iPhone 15 Pro", "AirPods Pro"],
      totalAmount: 1299.99,
      remainingAmount: 866.66,
      nextPaymentDate: "2024-02-15",
      daysUntilExpiry: 3,
      status: "Active"
    },
    {
      id: 2,
      customerName: "Michael Chen",
      customerEmail: "<EMAIL>",
      items: ["MacBook Air M3"],
      totalAmount: 1399,
      remainingAmount: 932.67,
      nextPaymentDate: "2024-02-10",
      daysUntilExpiry: -2,
      status: "Overdue"
    },
    {
      id: 5,
      customerName: "Lisa Brown",
      customerEmail: "<EMAIL>",
      items: ["AirPods Max", "Lightning Cable"],
      totalAmount: 579,
      remainingAmount: 386,
      nextPaymentDate: "2024-02-20",
      daysUntilExpiry: 8,
      status: "Active"
    }
  ];
  const paymentScheduleData = [
    {
      id: 1,
      customerName: "Sarah Johnson",
      totalAmount: 1299.99,
      overallProgress: 33,
      payments: [
        {
          installmentNumber: 1,
          amount: 433.33,
          dueDate: "2024-01-15",
          paidDate: "2024-01-15",
          status: "paid"
        },
        {
          installmentNumber: 2,
          amount: 433.33,
          dueDate: "2024-02-15",
          status: "pending"
        },
        {
          installmentNumber: 3,
          amount: 433.33,
          dueDate: "2024-03-15",
          status: "pending"
        }
      ]
    },
    {
      id: 2,
      customerName: "Michael Chen",
      totalAmount: 1399,
      overallProgress: 33,
      payments: [
        {
          installmentNumber: 1,
          amount: 466.33,
          dueDate: "2024-01-10",
          paidDate: "2024-01-10",
          status: "paid"
        },
        {
          installmentNumber: 2,
          amount: 466.33,
          dueDate: "2024-02-10",
          status: "overdue"
        },
        {
          installmentNumber: 3,
          amount: 466.34,
          dueDate: "2024-03-10",
          status: "pending"
        }
      ]
    }
  ];
  const handleCreateLaybye = () => {
  };
  const handleViewLaybye = (id) => {
  };
  const handleEditLaybye = (id) => {
  };
  const handleCancelLaybye = (id) => {
  };
  const handleRemindCustomer = (id) => {
  };
  const handleExtendLaybye = (id) => {
  };
  {
    let rightSlot = function($$payload2) {
      Button($$payload2, {
        onclick: handleCreateLaybye,
        class: "gap-2",
        children: ($$payload3) => {
          Plus($$payload3, { class: "h-4 w-4" });
          $$payload3.out.push(`<!----> Create Laybye`);
        },
        $$slots: { default: true }
      });
    };
    Card_shell($$payload, {
      heading: "Laybye Dashboard",
      subHeading: "Manage customer layaway purchases and payment schedules",
      rightSlot,
      children: ($$payload2) => {
        $$payload2.out.push(`<div class="space-y-6">`);
        Laybye_stats($$payload2, { stats: laybyeStats });
        $$payload2.out.push(`<!----> <div class="grid gap-6 lg:grid-cols-2">`);
        Laybye_trends_chart($$payload2, { data: laybyeTrendsData });
        $$payload2.out.push(`<!----> `);
        Laybye_status_chart($$payload2, { data: laybyeStatusData });
        $$payload2.out.push(`<!----></div> `);
        Expiring_laybyes($$payload2, {
          laybyes: expiringLaybyesData,
          onRemind: handleRemindCustomer,
          onExtend: handleExtendLaybye,
          onCancel: handleCancelLaybye,
          onView: handleViewLaybye
        });
        $$payload2.out.push(`<!----> <div class="grid gap-6 lg:grid-cols-2">`);
        Recent_laybyes_table($$payload2, {
          laybyes: recentLaybyesData,
          onView: handleViewLaybye,
          onEdit: handleEditLaybye,
          onCancel: handleCancelLaybye,
          onRemind: handleRemindCustomer
        });
        $$payload2.out.push(`<!----> `);
        Payment_schedule($$payload2, { schedules: paymentScheduleData });
        $$payload2.out.push(`<!----></div></div>`);
      }
    });
  }
  pop();
}
export {
  _page as default
};
