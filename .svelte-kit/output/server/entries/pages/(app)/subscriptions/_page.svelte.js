import "clsx";
import { G as spread_props, y as pop, w as push, Q as copy_payload, R as assign_payload, P as bind_props, E as ensure_array_like, B as escape_html } from "../../../../chunks/index2.js";
import "../../../../chunks/client2.js";
import { I as Icon } from "../../../../chunks/states.svelte.js";
import "linq-extensions";
import "@sveltejs/kit/internal";
import "../../../../chunks/exports.js";
import "../../../../chunks/state.svelte.js";
import "../../../../chunks/nprogress.js";
import { C as Card_shell } from "../../../../chunks/card_shell.js";
import { B as Button } from "../../../../chunks/button.js";
import { C as Card, a as Card_content } from "../../../../chunks/card-content.js";
import { C as Card_header, a as Card_title, b as Card_description } from "../../../../chunks/card-title.js";
import { T as Trending_up } from "../../../../chunks/trending-up.js";
import { D as Dollar_sign } from "../../../../chunks/dollar-sign.js";
import { T as Triangle_alert } from "../../../../chunks/triangle-alert.js";
import { l as SeriesState, n as chartDataArray, c as accessor, s as setTooltipMetaContext, o as Chart, p as defaultChartPadding, L as Layer, q as asAny, G as Grid, t as ChartClipPath, u as ChartAnnotations, S as Spline, R as Rule, A as Axis, P as Points, v as Labels, H as Highlight, w as Legend, D as DefaultTooltip, y as createLegendProps, z as findRelatedData, E as layerClass, C as Chart_container, a as Chart_tooltip } from "../../../../chunks/chart-tooltip.js";
import { c as cls } from "../../../../chunks/index4.js";
import { curveNatural } from "d3-shape";
import { scaleTime, scaleLinear, scaleUtc } from "d3-scale";
import { B as Badge } from "../../../../chunks/badge.js";
import { C as Clock } from "../../../../chunks/clock.js";
import { C as Calendar } from "../../../../chunks/calendar.js";
import { C as Credit_card } from "../../../../chunks/credit-card.js";
import { P as Plus } from "../../../../chunks/plus.js";
function Refresh_cw($$payload, $$props) {
  push();
  /**
   * @license @lucide/svelte v0.539.0 - ISC
   *
   * ISC License
   *
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   *
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   *
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   *
   */
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    [
      "path",
      { "d": "M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8" }
    ],
    ["path", { "d": "M21 3v5h-5" }],
    [
      "path",
      { "d": "M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16" }
    ],
    ["path", { "d": "M8 16H3v5" }]
  ];
  Icon($$payload, spread_props([
    { name: "refresh-cw" },
    /**
     * @component @name RefreshCw
     * @description Lucide SVG icon component, renders SVG Element with children.
     *
     * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyAxMmE5IDkgMCAwIDEgOS05IDkuNzUgOS43NSAwIDAgMSA2Ljc0IDIuNzRMMjEgOCIgLz4KICA8cGF0aCBkPSJNMjEgM3Y1aC01IiAvPgogIDxwYXRoIGQ9Ik0yMSAxMmE5IDkgMCAwIDEtOSA5IDkuNzUgOS43NSAwIDAgMS02Ljc0LTIuNzRMMyAxNiIgLz4KICA8cGF0aCBkPSJNOCAxNkgzdjUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/refresh-cw
     * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
     *
     * @param {Object} props - Lucide icons props and any valid SVG attribute
     * @returns {FunctionalComponent} Svelte component
     *
     */
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function LineChart($$payload, $$props) {
  push();
  let {
    data = [],
    x: xProp,
    y: yProp,
    xDomain,
    radial = false,
    series: seriesProp,
    seriesLayout = "overlap",
    axis = true,
    brush = false,
    grid = true,
    labels = false,
    legend = false,
    points = false,
    rule = true,
    onTooltipClick = () => {
    },
    onPointClick,
    props = {},
    renderContext = "svg",
    profile = false,
    debug = false,
    xScale: xScaleProp,
    tooltip = true,
    children: childrenProp,
    aboveContext,
    belowContext,
    belowMarks,
    aboveMarks,
    marks,
    spline,
    highlight = true,
    annotations = [],
    context = void 0,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const series = seriesProp === void 0 ? [
    { key: "default", value: yProp, color: "var(--color-primary)" }
  ] : seriesProp;
  const seriesState = new SeriesState(() => series);
  const chartData = seriesState.allSeriesData.length ? seriesState.allSeriesData : chartDataArray(data);
  const xScale = xScaleProp ?? (accessor(xProp)(chartData[0]) instanceof Date ? scaleTime() : scaleLinear());
  function getSplineProps(s, i) {
    const splineProps = {
      data: s.data,
      y: s.value ?? (s.data ? void 0 : s.key),
      stroke: s.color,
      ...props.spline,
      ...s.props,
      class: cls(
        layerClass("line-chart-line"),
        "transition-opacity",
        // Checking `visibleSeries.length > 1` fixes re-animated tweened areas on hover
        seriesState.visibleSeries.length > 1 && seriesState.highlightKey.current && seriesState.highlightKey.current !== s.key && "opacity-10",
        props.spline?.class,
        s.props?.class
      )
    };
    return splineProps;
  }
  function getPointsProps(s, i) {
    const pointsProps = {
      data: s.data,
      y: s.value ?? (s.data ? void 0 : s.key),
      fill: s.color,
      ...props.points,
      ...typeof points === "object" ? points : null,
      class: cls("stroke-surface-200 transition-opacity", seriesState.highlightKey.current && seriesState.highlightKey.current !== s.key && "opacity-10", props.points?.class, typeof points === "object" && points.class)
    };
    return pointsProps;
  }
  function getLabelsProps(s, i) {
    const labelsProps = {
      data: s.data,
      y: s.value ?? (s.data ? void 0 : s.key),
      ...props.labels,
      ...typeof labels === "object" ? labels : null,
      class: cls("stroke-surface-200 transition-opacity", seriesState.highlightKey.current && seriesState.highlightKey.current !== s.key && "opacity-10", props.labels?.class, typeof labels === "object" && labels.class)
    };
    return labelsProps;
  }
  const highlightPointsProps = typeof props.highlight?.points === "object" ? props.highlight.points : null;
  function getHighlightProps(s, i) {
    if (!context || !context.tooltip.data) return {};
    const seriesTooltipData = s.data && context.tooltip.data ? findRelatedData(s.data, context.tooltip.data, context.x) : null;
    return {
      data: seriesTooltipData,
      y: s.value ?? (s.data ? void 0 : s.key),
      lines: i === 0,
      onPointClick: onPointClick ? (e, detail) => onPointClick(e, { ...detail, series: s }) : void 0,
      onPointEnter: () => seriesState.highlightKey.current = s.key,
      onPointLeave: () => seriesState.highlightKey.current = null,
      ...props.highlight,
      points: props.highlight?.points == false ? false : {
        ...highlightPointsProps,
        fill: s.color,
        class: cls("transition-opacity", seriesState.highlightKey.current && seriesState.highlightKey.current !== s.key && "opacity-10", highlightPointsProps?.class)
      }
    };
  }
  function getLegendProps() {
    return createLegendProps({
      seriesState,
      props: {
        ...props.legend,
        ...typeof legend === "object" ? legend : null
      }
    });
  }
  function getGridProps() {
    return {
      x: radial,
      y: true,
      ...typeof grid === "object" ? grid : null,
      ...props.grid
    };
  }
  function getAxisProps(axisDirection) {
    if (axisDirection === "y") {
      return {
        placement: radial ? "radius" : "left",
        ...typeof axis === "object" ? axis : null,
        ...props.yAxis
      };
    }
    return {
      placement: radial ? "angle" : "bottom",
      ...typeof axis === "object" ? axis : null,
      ...props.xAxis
    };
  }
  function getRuleProps() {
    return {
      x: 0,
      y: 0,
      ...typeof rule === "object" ? rule : null,
      ...props.rule
    };
  }
  const brushProps = { ...typeof brush === "object" ? brush : null, ...props.brush };
  if (profile) {
    console.time("LineChart render");
  }
  setTooltipMetaContext({
    type: "line",
    get visibleSeries() {
      return seriesState.visibleSeries;
    }
  });
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    {
      let children = function($$payload3, { context: context2 }) {
        const snippetProps = {
          context: context2,
          series,
          visibleSeries: seriesState.visibleSeries,
          getLabelsProps,
          getPointsProps,
          getSplineProps,
          getHighlightProps,
          getLegendProps,
          getGridProps,
          getAxisProps,
          getRuleProps,
          highlightKey: seriesState.highlightKey.current,
          setHighlightKey: seriesState.highlightKey.set
        };
        if (childrenProp) {
          $$payload3.out.push("<!--[-->");
          childrenProp($$payload3, snippetProps);
          $$payload3.out.push(`<!---->`);
        } else {
          $$payload3.out.push("<!--[!-->");
          belowContext?.($$payload3, snippetProps);
          $$payload3.out.push(`<!----> `);
          Layer($$payload3, spread_props([
            { type: renderContext },
            asAny(renderContext === "canvas" ? props.canvas : props.svg),
            {
              center: radial,
              debug,
              children: ($$payload4) => {
                if (typeof grid === "function") {
                  $$payload4.out.push("<!--[-->");
                  grid($$payload4, snippetProps);
                  $$payload4.out.push(`<!---->`);
                } else {
                  $$payload4.out.push("<!--[!-->");
                  if (grid) {
                    $$payload4.out.push("<!--[-->");
                    Grid($$payload4, spread_props([getGridProps()]));
                  } else {
                    $$payload4.out.push("<!--[!-->");
                  }
                  $$payload4.out.push(`<!--]-->`);
                }
                $$payload4.out.push(`<!--]--> `);
                ChartClipPath($$payload4, {
                  disabled: !brush,
                  children: ($$payload5) => {
                    ChartAnnotations($$payload5, {
                      annotations,
                      layer: "below",
                      highlightKey: seriesState.highlightKey.current,
                      visibleSeries: seriesState.visibleSeries
                    });
                    $$payload5.out.push(`<!----> `);
                    belowMarks?.($$payload5, snippetProps);
                    $$payload5.out.push(`<!----> `);
                    if (marks) {
                      $$payload5.out.push("<!--[-->");
                      marks($$payload5, snippetProps);
                      $$payload5.out.push(`<!---->`);
                    } else {
                      $$payload5.out.push("<!--[!-->");
                      const each_array = ensure_array_like(seriesState.visibleSeries);
                      $$payload5.out.push(`<!--[-->`);
                      for (let i = 0, $$length = each_array.length; i < $$length; i++) {
                        let s = each_array[i];
                        if (typeof spline === "function") {
                          $$payload5.out.push("<!--[-->");
                          spline($$payload5, { ...snippetProps, props: getSplineProps(s), seriesIndex: i });
                          $$payload5.out.push(`<!---->`);
                        } else {
                          $$payload5.out.push("<!--[!-->");
                          Spline($$payload5, spread_props([getSplineProps(s)]));
                        }
                        $$payload5.out.push(`<!--]-->`);
                      }
                      $$payload5.out.push(`<!--]-->`);
                    }
                    $$payload5.out.push(`<!--]--> `);
                    aboveMarks?.($$payload5, snippetProps);
                    $$payload5.out.push(`<!---->`);
                  },
                  $$slots: { default: true }
                });
                $$payload4.out.push(`<!----> `);
                if (typeof axis === "function") {
                  $$payload4.out.push("<!--[-->");
                  axis($$payload4, snippetProps);
                  $$payload4.out.push(`<!----> `);
                  if (typeof rule === "function") {
                    $$payload4.out.push("<!--[-->");
                    rule($$payload4, snippetProps);
                    $$payload4.out.push(`<!---->`);
                  } else {
                    $$payload4.out.push("<!--[!-->");
                    if (rule) {
                      $$payload4.out.push("<!--[-->");
                      Rule($$payload4, spread_props([getRuleProps()]));
                    } else {
                      $$payload4.out.push("<!--[!-->");
                    }
                    $$payload4.out.push(`<!--]-->`);
                  }
                  $$payload4.out.push(`<!--]-->`);
                } else {
                  $$payload4.out.push("<!--[!-->");
                  if (axis) {
                    $$payload4.out.push("<!--[-->");
                    if (axis !== "x") {
                      $$payload4.out.push("<!--[-->");
                      Axis($$payload4, spread_props([getAxisProps("y")]));
                    } else {
                      $$payload4.out.push("<!--[!-->");
                    }
                    $$payload4.out.push(`<!--]--> `);
                    if (axis !== "y") {
                      $$payload4.out.push("<!--[-->");
                      Axis($$payload4, spread_props([getAxisProps("x")]));
                    } else {
                      $$payload4.out.push("<!--[!-->");
                    }
                    $$payload4.out.push(`<!--]--> `);
                    if (typeof rule === "function") {
                      $$payload4.out.push("<!--[-->");
                      rule($$payload4, snippetProps);
                      $$payload4.out.push(`<!---->`);
                    } else {
                      $$payload4.out.push("<!--[!-->");
                      if (rule) {
                        $$payload4.out.push("<!--[-->");
                        Rule($$payload4, spread_props([getRuleProps()]));
                      } else {
                        $$payload4.out.push("<!--[!-->");
                      }
                      $$payload4.out.push(`<!--]-->`);
                    }
                    $$payload4.out.push(`<!--]-->`);
                  } else {
                    $$payload4.out.push("<!--[!-->");
                  }
                  $$payload4.out.push(`<!--]-->`);
                }
                $$payload4.out.push(`<!--]--> `);
                ChartClipPath($$payload4, {
                  disabled: !brush,
                  full: true,
                  children: ($$payload5) => {
                    if (typeof points === "function") {
                      $$payload5.out.push("<!--[-->");
                      points($$payload5, snippetProps);
                      $$payload5.out.push(`<!---->`);
                    } else {
                      $$payload5.out.push("<!--[!-->");
                      if (points) {
                        $$payload5.out.push("<!--[-->");
                        const each_array_1 = ensure_array_like(seriesState.visibleSeries);
                        $$payload5.out.push(`<!--[-->`);
                        for (let i = 0, $$length = each_array_1.length; i < $$length; i++) {
                          let s = each_array_1[i];
                          Points($$payload5, spread_props([getPointsProps(s)]));
                        }
                        $$payload5.out.push(`<!--]-->`);
                      } else {
                        $$payload5.out.push("<!--[!-->");
                      }
                      $$payload5.out.push(`<!--]-->`);
                    }
                    $$payload5.out.push(`<!--]--> `);
                    if (typeof labels === "function") {
                      $$payload5.out.push("<!--[-->");
                      labels($$payload5, snippetProps);
                      $$payload5.out.push(`<!---->`);
                    } else {
                      $$payload5.out.push("<!--[!-->");
                      if (labels) {
                        $$payload5.out.push("<!--[-->");
                        const each_array_2 = ensure_array_like(seriesState.visibleSeries);
                        $$payload5.out.push(`<!--[-->`);
                        for (let i = 0, $$length = each_array_2.length; i < $$length; i++) {
                          let s = each_array_2[i];
                          Labels($$payload5, spread_props([getLabelsProps(s)]));
                        }
                        $$payload5.out.push(`<!--]-->`);
                      } else {
                        $$payload5.out.push("<!--[!-->");
                      }
                      $$payload5.out.push(`<!--]-->`);
                    }
                    $$payload5.out.push(`<!--]--> `);
                    if (typeof highlight === "function") {
                      $$payload5.out.push("<!--[-->");
                      highlight($$payload5, snippetProps);
                      $$payload5.out.push(`<!---->`);
                    } else {
                      $$payload5.out.push("<!--[!-->");
                      if (highlight) {
                        $$payload5.out.push("<!--[-->");
                        const each_array_3 = ensure_array_like(seriesState.visibleSeries);
                        $$payload5.out.push(`<!--[-->`);
                        for (let i = 0, $$length = each_array_3.length; i < $$length; i++) {
                          let s = each_array_3[i];
                          Highlight($$payload5, spread_props([getHighlightProps(s, i)]));
                        }
                        $$payload5.out.push(`<!--]-->`);
                      } else {
                        $$payload5.out.push("<!--[!-->");
                      }
                      $$payload5.out.push(`<!--]-->`);
                    }
                    $$payload5.out.push(`<!--]--> `);
                    ChartAnnotations($$payload5, {
                      annotations,
                      layer: "above",
                      highlightKey: seriesState.highlightKey.current,
                      visibleSeries: seriesState.visibleSeries
                    });
                    $$payload5.out.push(`<!---->`);
                  },
                  $$slots: { default: true }
                });
                $$payload4.out.push(`<!---->`);
              },
              $$slots: { default: true }
            }
          ]));
          $$payload3.out.push(`<!----> `);
          aboveContext?.($$payload3, snippetProps);
          $$payload3.out.push(`<!----> `);
          if (typeof legend === "function") {
            $$payload3.out.push("<!--[-->");
            legend($$payload3, snippetProps);
            $$payload3.out.push(`<!---->`);
          } else {
            $$payload3.out.push("<!--[!-->");
            if (legend) {
              $$payload3.out.push("<!--[-->");
              Legend($$payload3, spread_props([getLegendProps()]));
            } else {
              $$payload3.out.push("<!--[!-->");
            }
            $$payload3.out.push(`<!--]-->`);
          }
          $$payload3.out.push(`<!--]--> `);
          if (typeof tooltip === "function") {
            $$payload3.out.push("<!--[-->");
            tooltip($$payload3, snippetProps);
            $$payload3.out.push(`<!---->`);
          } else {
            $$payload3.out.push("<!--[!-->");
            if (tooltip) {
              $$payload3.out.push("<!--[-->");
              DefaultTooltip($$payload3, { tooltipProps: props.tooltip, seriesState, canHaveTotal: true });
            } else {
              $$payload3.out.push("<!--[!-->");
            }
            $$payload3.out.push(`<!--]-->`);
          }
          $$payload3.out.push(`<!--]-->`);
        }
        $$payload3.out.push(`<!--]-->`);
      };
      Chart($$payload2, spread_props([
        {
          data: chartData,
          x: xProp,
          xDomain,
          xScale,
          y: yProp ?? series.map((s) => s.value ?? s.key),
          yBaseline: 0,
          yNice: true,
          radial,
          padding: radial ? void 0 : defaultChartPadding(axis, legend)
        },
        restProps,
        {
          tooltip: tooltip === false ? false : {
            mode: "bisect-x",
            onclick: onTooltipClick,
            debug,
            ...props.tooltip?.context
          },
          brush: brush && (brush === true || brush.mode == void 0 || brush.mode === "integrated") ? {
            axis: "x",
            resetOnEnd: true,
            xDomain,
            ...brushProps,
            onBrushEnd: (e) => {
              xDomain = e.xDomain;
              brushProps.onBrushEnd?.(e);
            }
          } : false,
          get context() {
            return context;
          },
          set context($$value) {
            context = $$value;
            $$settled = false;
          },
          children,
          $$slots: { default: true }
        }
      ]));
    }
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { context });
  pop();
}
function Expiring_subscriptions($$payload, $$props) {
  push();
  let { subscriptions, onRenew, onContact } = $$props;
  const getDaysLeftVariant = (daysLeft) => {
    if (daysLeft <= 3) return "destructive";
    if (daysLeft <= 7) return "secondary";
    return "outline";
  };
  $$payload.out.push(`<!---->`);
  Card($$payload, {
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->`);
      Card_header($$payload2, {
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Card_title($$payload3, {
            class: "flex items-center gap-2",
            children: ($$payload4) => {
              Clock($$payload4, { class: "h-5 w-5" });
              $$payload4.out.push(`<!----> Expiring Subscriptions`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> <!---->`);
          Card_description($$payload3, {
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Subscriptions expiring in the next 30 days`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!----> <!---->`);
      Card_content($$payload2, {
        children: ($$payload3) => {
          const each_array = ensure_array_like(subscriptions);
          $$payload3.out.push(`<div class="space-y-4"><!--[-->`);
          for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
            let subscription = each_array[$$index];
            $$payload3.out.push(`<div class="flex items-center justify-between space-x-4 rounded-lg border p-4"><div class="space-y-1"><p class="text-sm font-medium leading-none">${escape_html(subscription.customerName)}</p> <p class="text-sm text-muted-foreground">${escape_html(subscription.plan)}</p> <div class="flex items-center gap-2 text-sm text-muted-foreground">`);
            Calendar($$payload3, { class: "h-4 w-4" });
            $$payload3.out.push(`<!----> Expires: ${escape_html(new Date(subscription.expiryDate).toLocaleDateString())}</div></div> <div class="flex items-center gap-2">`);
            Badge($$payload3, {
              variant: getDaysLeftVariant(subscription.daysLeft),
              children: ($$payload4) => {
                $$payload4.out.push(`<!---->${escape_html(subscription.daysLeft)} days left`);
              },
              $$slots: { default: true }
            });
            $$payload3.out.push(`<!----> <div class="text-right"><p class="text-sm font-medium">$${escape_html(subscription.amount)}</p> <div class="flex gap-1">`);
            Button($$payload3, {
              size: "sm",
              variant: "outline",
              onclick: () => onContact?.(subscription.id),
              class: "h-7 px-2",
              children: ($$payload4) => {
                $$payload4.out.push(`<!---->Contact`);
              },
              $$slots: { default: true }
            });
            $$payload3.out.push(`<!----> `);
            Button($$payload3, {
              size: "sm",
              onclick: () => onRenew?.(subscription.id),
              class: "h-7 px-2",
              children: ($$payload4) => {
                Credit_card($$payload4, { class: "h-3 w-3 mr-1" });
                $$payload4.out.push(`<!----> Renew`);
              },
              $$slots: { default: true }
            });
            $$payload3.out.push(`<!----></div></div></div></div>`);
          }
          $$payload3.out.push(`<!--]--></div>`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!---->`);
  pop();
}
function Revenue_analytics_chart($$payload, $$props) {
  push();
  let { data } = $$props;
  const chartConfig = {
    revenue: { label: "Revenue", color: "var(--chart-1)" },
    subscriptions: { label: "Subscriptions", color: "var(--chart-2)" }
  };
  $$payload.out.push(`<!---->`);
  Card($$payload, {
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->`);
      Card_header($$payload2, {
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Card_title($$payload3, {
            class: "flex items-center gap-2",
            children: ($$payload4) => {
              Trending_up($$payload4, { class: "h-5 w-5" });
              $$payload4.out.push(`<!----> Revenue Analytics`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> <!---->`);
          Card_description($$payload3, {
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Subscription revenue trends over the last 6 months`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!----> <!---->`);
      Card_content($$payload2, {
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Chart_container($$payload3, {
            config: chartConfig,
            class: "h-[300px]",
            children: ($$payload4) => {
              {
                let tooltip = function($$payload5) {
                  $$payload5.out.push(`<!---->`);
                  Chart_tooltip($$payload5, {
                    indicator: "dot",
                    labelFormatter: (v) => {
                      return v.toLocaleDateString("en-US", { month: "long", year: "numeric" });
                    }
                  });
                  $$payload5.out.push(`<!---->`);
                };
                LineChart($$payload4, {
                  data,
                  x: "date",
                  xScale: scaleUtc(),
                  y: "revenue",
                  yPadding: [0, 25],
                  props: {
                    spline: {
                      curve: curveNatural,
                      stroke: "var(--color-revenue)",
                      "stroke-width": 2
                    },
                    xAxis: {
                      format: (v) => v.toLocaleDateString("en-US", { month: "short" })
                    },
                    yAxis: { format: (v) => `$${(v / 1e3).toFixed(0)}k` }
                  },
                  points: { r: 4, fill: "var(--color-revenue)" },
                  tooltip,
                  $$slots: { tooltip: true }
                });
              }
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!---->`);
  pop();
}
function Subscription_stats($$payload, $$props) {
  push();
  let { stats } = $$props;
  $$payload.out.push(`<div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4"><!---->`);
  Card($$payload, {
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->`);
      Card_header($$payload2, {
        class: "flex flex-row items-center justify-between space-y-0 pb-2",
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Card_title($$payload3, {
            class: "text-sm font-medium",
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Total Subscriptions`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> `);
          Refresh_cw($$payload3, { class: "h-4 w-4 text-muted-foreground" });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!----> <!---->`);
      Card_content($$payload2, {
        children: ($$payload3) => {
          $$payload3.out.push(`<div class="text-2xl font-bold">${escape_html(stats.totalSubscriptions.toLocaleString())}</div> <p class="text-xs text-muted-foreground">+${escape_html(stats.growthRate)}% from last month</p>`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!----> <!---->`);
  Card($$payload, {
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->`);
      Card_header($$payload2, {
        class: "flex flex-row items-center justify-between space-y-0 pb-2",
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Card_title($$payload3, {
            class: "text-sm font-medium",
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Active Subscriptions`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> `);
          Trending_up($$payload3, { class: "h-4 w-4 text-muted-foreground" });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!----> <!---->`);
      Card_content($$payload2, {
        children: ($$payload3) => {
          $$payload3.out.push(`<div class="text-2xl font-bold">${escape_html(stats.activeSubscriptions.toLocaleString())}</div> <p class="text-xs text-muted-foreground">${escape_html((stats.activeSubscriptions / stats.totalSubscriptions * 100).toFixed(1))}% active rate</p>`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!----> <!---->`);
  Card($$payload, {
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->`);
      Card_header($$payload2, {
        class: "flex flex-row items-center justify-between space-y-0 pb-2",
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Card_title($$payload3, {
            class: "text-sm font-medium",
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Monthly Revenue`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> `);
          Dollar_sign($$payload3, { class: "h-4 w-4 text-muted-foreground" });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!----> <!---->`);
      Card_content($$payload2, {
        children: ($$payload3) => {
          $$payload3.out.push(`<div class="text-2xl font-bold">$${escape_html(stats.monthlyRevenue.toLocaleString())}</div> <p class="text-xs text-muted-foreground">+${escape_html(stats.growthRate)}% from last month</p>`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!----> <!---->`);
  Card($$payload, {
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->`);
      Card_header($$payload2, {
        class: "flex flex-row items-center justify-between space-y-0 pb-2",
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Card_title($$payload3, {
            class: "text-sm font-medium",
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Churn Rate`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> `);
          Triangle_alert($$payload3, { class: "h-4 w-4 text-muted-foreground" });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!----> <!---->`);
      Card_content($$payload2, {
        children: ($$payload3) => {
          $$payload3.out.push(`<div class="text-2xl font-bold">${escape_html(stats.churnRate)}%</div> <p class="text-xs text-muted-foreground">Monthly churn rate</p>`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!----></div>`);
  pop();
}
function _page($$payload, $$props) {
  push();
  const subscriptionStats = {
    totalSubscriptions: 3456,
    activeSubscriptions: 3234,
    monthlyRevenue: 89450,
    churnRate: 2.8,
    growthRate: 15.2
  };
  const revenueData = [
    {
      date: /* @__PURE__ */ new Date("2024-07-01"),
      revenue: 67890,
      subscriptions: 1654
    },
    {
      date: /* @__PURE__ */ new Date("2024-08-01"),
      revenue: 72340,
      subscriptions: 1723
    },
    {
      date: /* @__PURE__ */ new Date("2024-09-01"),
      revenue: 78920,
      subscriptions: 1789
    },
    {
      date: /* @__PURE__ */ new Date("2024-10-01"),
      revenue: 84560,
      subscriptions: 1834
    },
    {
      date: /* @__PURE__ */ new Date("2024-11-01"),
      revenue: 87230,
      subscriptions: 1847
    },
    {
      date: /* @__PURE__ */ new Date("2024-12-01"),
      revenue: 89450,
      subscriptions: 1847
    }
  ];
  const expiringSubscriptionsData = [
    {
      id: 1,
      customerName: "Small Business LLC",
      plan: "Basic Plan",
      expiryDate: "2024-01-20",
      daysLeft: 5,
      amount: 9.99
    },
    {
      id: 2,
      customerName: "Design Studio",
      plan: "Pro Plan",
      expiryDate: "2024-01-22",
      daysLeft: 7,
      amount: 29.99
    },
    {
      id: 3,
      customerName: "Marketing Agency",
      plan: "Enterprise Plan",
      expiryDate: "2024-01-25",
      daysLeft: 10,
      amount: 99.99
    }
  ];
  const handleCreatePlan = () => {
  };
  const handleRenewSubscription = (id) => {
  };
  const handleContactCustomer = (id) => {
  };
  {
    let rightSlot = function($$payload2) {
      Button($$payload2, {
        onclick: handleCreatePlan,
        class: "gap-2",
        children: ($$payload3) => {
          Plus($$payload3, { class: "h-4 w-4" });
          $$payload3.out.push(`<!----> Create Plan`);
        },
        $$slots: { default: true }
      });
    };
    Card_shell($$payload, {
      heading: "Subscriptions Dashboard",
      subHeading: "Manage subscription plans and track recurring revenue metrics",
      rightSlot,
      children: ($$payload2) => {
        $$payload2.out.push(`<div class="space-y-6">`);
        Subscription_stats($$payload2, { stats: subscriptionStats });
        $$payload2.out.push(`<!----> <div class="grid gap-6 lg:grid-cols-2">`);
        Revenue_analytics_chart($$payload2, { data: revenueData });
        $$payload2.out.push(`<!----> `);
        Expiring_subscriptions($$payload2, {
          subscriptions: expiringSubscriptionsData,
          onRenew: handleRenewSubscription,
          onContact: handleContactCustomer
        });
        $$payload2.out.push(`<!----></div></div>`);
      }
    });
  }
  pop();
}
export {
  _page as default
};
