import "clsx";
import { w as push, Q as copy_payload, R as assign_payload, y as pop, G as spread_props, V as store_mutate, T as store_get, E as ensure_array_like, B as escape_html, z as attr, U as unsubscribe_stores } from "../../../../chunks/index2.js";
import { f as getAppointmentStore, g as getUtilsStore, e as getOrgStore, d as getAuthStore } from "../../../../chunks/sheet-content.js";
import "@sveltejs/kit/internal";
import "../../../../chunks/exports.js";
import "../../../../chunks/state.svelte.js";
import { A as AppointmentController, n as newAppointmentFormSchema } from "../../../../chunks/auth-client.js";
import "superjson";
import { subDays } from "date-fns";
import "@oslojs/crypto/sha2";
import "decimal.js";
import "linq-extensions";
import "remult";
import { b as buttonVariants, B as Button } from "../../../../chunks/button.js";
import "../../../../chunks/badge.js";
import "../../../../chunks/states.svelte.js";
import { F as Form_description, C as Card_footer, D as Dynamic_modal } from "../../../../chunks/form-description.js";
import { C as Card, a as Card_content } from "../../../../chunks/card-content.js";
import { c as cn } from "../../../../chunks/shadcn.utils.js";
import { u as useId, t as tick } from "../../../../chunks/scroll-lock.js";
import { g as getFlashModule } from "../../../../chunks/dialog.js";
import { s as superForm } from "../../../../chunks/string.js";
import { a as valibotClient, d as defaults } from "../../../../chunks/valibot.js";
import "@sveltejs/kit";
import { F as Form_alerts, a as Form_field, C as Control, b as Form_field_errors } from "../../../../chunks/index5.js";
import { I as Input } from "../../../../chunks/input.js";
import { C as Command, a as Command_input, b as Command_empty, c as Command_group, d as Command_item } from "../../../../chunks/command-input.js";
import { R as Root, P as Popover_content, a as Popover_trigger } from "../../../../chunks/index7.js";
import { F as Form_label, a as Form_button, L as Loader_circle } from "../../../../chunks/form-button.js";
import { C as Check } from "../../../../chunks/check.js";
import { C as Chevrons_up_down } from "../../../../chunks/chevrons-up-down.js";
import { L as Label } from "../../../../chunks/label.js";
function Current_appointment_form($$payload, $$props) {
  push();
  var $$store_subs;
  let aStore = getAppointmentStore();
  let utilsStore = getUtilsStore();
  let orgStore = getOrgStore();
  let authStore = getAuthStore();
  const form = superForm(defaults(aStore.selectedAppointment, valibotClient(newAppointmentFormSchema)), {
    dataType: "json",
    SPA: true,
    clearOnSubmit: "errors-and-message",
    validators: valibotClient(newAppointmentFormSchema),
    async onUpdate({ form: form2 }) {
      try {
        if (!form2.valid) return;
        const { data: formData2 } = form2;
        const appointmentType = aStore.appointmentTemplates.firstOrNull((k) => k.id === formData2.appointmentTypeId);
        const appointmentLocation = orgStore.teams?.firstOrNull((k) => k.id === formData2.locationId);
        const params = {
          id: aStore.selectedAppointment?.id,
          customerName: formData2.customerName,
          customerEmail: formData2.customerEmail,
          customerPhoneNumber: formData2.customerPhoneNumber,
          appointmentTypeId: appointmentType?.id,
          appointmentDate: formData2.appointmentDate,
          startTime: formData2.startTime,
          endTime: formData2.endTime,
          organizationId: orgStore.org?.id,
          teamId: appointmentLocation?.id,
          updatedBy: authStore.user?.id,
          updatedAt: /* @__PURE__ */ new Date()
        };
        const updateResp = await AppointmentController.update(aStore.selectedAppointment?.id, params);
        if (!updateResp.id) throw new Error("Failed to update appointment");
        form2.message = { status: "success", text: "Appointment updated successfully!" };
      } catch (err) {
        console.error(err);
        form2.message = { status: "error", text: "Appointment update failed!" };
      }
    },
    onResult: ({ result }) => {
      if (result.type === "success") {
        utilsStore.toastSuccess("Bank details updated successfully!");
        utilsStore.clearDynamicModal();
      }
    },
    ...getFlashModule()
  });
  const {
    form: formData,
    enhance,
    submitting,
    allErrors,
    message,
    validateForm
  } = form;
  let open = false;
  let locationOpen = false;
  const triggerId = useId();
  const locationTriggerId = useId();
  const closeAndFocusTrigger = (triggerId2) => {
    open = false;
    tick().then(() => {
      document.getElementById(triggerId2)?.focus();
    });
  };
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out.push(`<form method="POST">`);
    Form_alerts($$payload2, { message });
    $$payload2.out.push(`<!----> <!---->`);
    Card($$payload2, {
      class: "rounded-none",
      children: ($$payload3) => {
        $$payload3.out.push(`<!---->`);
        Card_content($$payload3, {
          children: ($$payload4) => {
            $$payload4.out.push(`<div class="grid grid-cols-1 gap-4 space-y-4 md:grid-cols-3"><!---->`);
            Form_field($$payload4, {
              form,
              name: "customerName",
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->`);
                {
                  let children = function($$payload6, { props }) {
                    $$payload6.out.push(`<!---->`);
                    Form_label($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out.push(`<!---->full Name`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out.push(`<!----> `);
                    Input($$payload6, spread_props([
                      props,
                      {
                        placeholder: "Customer Name",
                        get value() {
                          return store_get($$store_subs ??= {}, "$formData", formData).customerName;
                        },
                        set value($$value) {
                          store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).customerName = $$value);
                          $$settled = false;
                        }
                      }
                    ]));
                    $$payload6.out.push(`<!---->`);
                  };
                  Control($$payload5, { children });
                }
                $$payload5.out.push(`<!----> <!---->`);
                Form_field_errors($$payload5, {});
                $$payload5.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!----> <!---->`);
            Form_field($$payload4, {
              form,
              name: "customerEmail",
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->`);
                {
                  let children = function($$payload6, { props }) {
                    $$payload6.out.push(`<!---->`);
                    Form_label($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out.push(`<!---->Email`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out.push(`<!----> `);
                    Input($$payload6, spread_props([
                      props,
                      {
                        placeholder: "Customer Email",
                        get value() {
                          return store_get($$store_subs ??= {}, "$formData", formData).customerEmail;
                        },
                        set value($$value) {
                          store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).customerEmail = $$value);
                          $$settled = false;
                        }
                      }
                    ]));
                    $$payload6.out.push(`<!---->`);
                  };
                  Control($$payload5, { children });
                }
                $$payload5.out.push(`<!----> <!---->`);
                Form_field_errors($$payload5, {});
                $$payload5.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!----> <!---->`);
            Form_field($$payload4, {
              form,
              name: "customerPhoneNumber",
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->`);
                {
                  let children = function($$payload6, { props }) {
                    $$payload6.out.push(`<!---->`);
                    Form_label($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out.push(`<!---->Phone Number`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out.push(`<!----> `);
                    Input($$payload6, spread_props([
                      props,
                      {
                        placeholder: "Phone Number",
                        get value() {
                          return store_get($$store_subs ??= {}, "$formData", formData).customerPhoneNumber;
                        },
                        set value($$value) {
                          store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).customerPhoneNumber = $$value);
                          $$settled = false;
                        }
                      }
                    ]));
                    $$payload6.out.push(`<!---->`);
                  };
                  Control($$payload5, { children });
                }
                $$payload5.out.push(`<!----> <!---->`);
                Form_field_errors($$payload5, {});
                $$payload5.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!----> <!---->`);
            Form_field($$payload4, {
              form,
              name: "appointmentTypeId",
              class: "flex flex-col",
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->`);
                Root($$payload5, {
                  get open() {
                    return open;
                  },
                  set open($$value) {
                    open = $$value;
                    $$settled = false;
                  },
                  children: ($$payload6) => {
                    $$payload6.out.push(`<!---->`);
                    {
                      let children = function($$payload7, { props }) {
                        $$payload7.out.push(`<!---->`);
                        Form_label($$payload7, {
                          children: ($$payload8) => {
                            $$payload8.out.push(`<!---->Appointment Type`);
                          },
                          $$slots: { default: true }
                        });
                        $$payload7.out.push(`<!----> <!---->`);
                        Popover_trigger($$payload7, spread_props([
                          {
                            class: cn(buttonVariants({ variant: "outline" }), "w-[200px] justify-between", !store_get($$store_subs ??= {}, "$formData", formData).appointmentTypeId && "text-muted-foreground"),
                            role: "combobox"
                          },
                          props,
                          {
                            children: ($$payload8) => {
                              $$payload8.out.push(`<!---->${escape_html(aStore.appointmentTemplates.find((f) => f.id === store_get($$store_subs ??= {}, "$formData", formData).appointmentTypeId)?.appointmentDetail ?? "Select Appointment Type")} `);
                              Chevrons_up_down($$payload8, { class: "opacity-50" });
                              $$payload8.out.push(`<!---->`);
                            },
                            $$slots: { default: true }
                          }
                        ]));
                        $$payload7.out.push(`<!----> <input hidden${attr("value", store_get($$store_subs ??= {}, "$formData", formData).appointmentTypeId)}${attr("name", props.name)}/>`);
                      };
                      Control($$payload6, { id: triggerId, children });
                    }
                    $$payload6.out.push(`<!----> <!---->`);
                    Popover_content($$payload6, {
                      class: "w-[200px] p-0",
                      children: ($$payload7) => {
                        $$payload7.out.push(`<!---->`);
                        Command($$payload7, {
                          children: ($$payload8) => {
                            $$payload8.out.push(`<!---->`);
                            Command_input($$payload8, {
                              autofocus: true,
                              placeholder: "Select appointment Type...",
                              class: "h-9"
                            });
                            $$payload8.out.push(`<!----> <!---->`);
                            Command_empty($$payload8, {
                              children: ($$payload9) => {
                                $$payload9.out.push(`<!---->No Appointment type found.`);
                              },
                              $$slots: { default: true }
                            });
                            $$payload8.out.push(`<!----> <!---->`);
                            Command_group($$payload8, {
                              children: ($$payload9) => {
                                const each_array = ensure_array_like(aStore.appointmentTemplates ?? []);
                                $$payload9.out.push(`<!--[-->`);
                                for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
                                  let appointmentType = each_array[$$index];
                                  $$payload9.out.push(`<!---->`);
                                  Command_item($$payload9, {
                                    value: appointmentType.id,
                                    onSelect: () => {
                                      store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).appointmentTypeId = appointmentType.id);
                                      closeAndFocusTrigger(triggerId);
                                    },
                                    children: ($$payload10) => {
                                      $$payload10.out.push(`<!---->${escape_html(appointmentType.appointmentDetail)} `);
                                      Check($$payload10, {
                                        class: cn("ml-auto", appointmentType.id !== store_get($$store_subs ??= {}, "$formData", formData).appointmentTypeId && "text-transparent")
                                      });
                                      $$payload10.out.push(`<!---->`);
                                    },
                                    $$slots: { default: true }
                                  });
                                  $$payload9.out.push(`<!---->`);
                                }
                                $$payload9.out.push(`<!--]-->`);
                              },
                              $$slots: { default: true }
                            });
                            $$payload8.out.push(`<!---->`);
                          },
                          $$slots: { default: true }
                        });
                        $$payload7.out.push(`<!---->`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out.push(`<!---->`);
                  },
                  $$slots: { default: true }
                });
                $$payload5.out.push(`<!----> <!---->`);
                Form_description($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out.push(`<!---->This is the service the appointment is for.`);
                  },
                  $$slots: { default: true }
                });
                $$payload5.out.push(`<!----> <!---->`);
                Form_field_errors($$payload5, {});
                $$payload5.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!----> <!---->`);
            Form_field($$payload4, {
              form,
              name: "appointmentDate",
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->`);
                {
                  let children = function($$payload6, { props }) {
                    $$payload6.out.push(`<!---->`);
                    Form_label($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out.push(`<!---->Appointment Date`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out.push(`<!----> `);
                    Input($$payload6, spread_props([
                      { type: "datetime-local" },
                      props,
                      {
                        placeholder: "Appointment Date",
                        get value() {
                          return store_get($$store_subs ??= {}, "$formData", formData).appointmentDate;
                        },
                        set value($$value) {
                          store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).appointmentDate = $$value);
                          $$settled = false;
                        }
                      }
                    ]));
                    $$payload6.out.push(`<!---->`);
                  };
                  Control($$payload5, { children });
                }
                $$payload5.out.push(`<!----> <!---->`);
                Form_field_errors($$payload5, {});
                $$payload5.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!----> <!---->`);
            Form_field($$payload4, {
              form,
              name: "startTime",
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->`);
                {
                  let children = function($$payload6, { props }) {
                    $$payload6.out.push(`<!---->`);
                    Form_label($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out.push(`<!---->Start Time`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out.push(`<!----> `);
                    Input($$payload6, spread_props([
                      props,
                      {
                        placeholder: "Start Time",
                        get value() {
                          return store_get($$store_subs ??= {}, "$formData", formData).startTime;
                        },
                        set value($$value) {
                          store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).startTime = $$value);
                          $$settled = false;
                        }
                      }
                    ]));
                    $$payload6.out.push(`<!---->`);
                  };
                  Control($$payload5, { children });
                }
                $$payload5.out.push(`<!----> <!---->`);
                Form_field_errors($$payload5, {});
                $$payload5.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!----> <!---->`);
            Form_field($$payload4, {
              form,
              name: "endTime",
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->`);
                {
                  let children = function($$payload6, { props }) {
                    $$payload6.out.push(`<!---->`);
                    Form_label($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out.push(`<!---->End Time`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out.push(`<!----> `);
                    Input($$payload6, spread_props([
                      props,
                      {
                        placeholder: "End Time",
                        get value() {
                          return store_get($$store_subs ??= {}, "$formData", formData).endTime;
                        },
                        set value($$value) {
                          store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).endTime = $$value);
                          $$settled = false;
                        }
                      }
                    ]));
                    $$payload6.out.push(`<!---->`);
                  };
                  Control($$payload5, { children });
                }
                $$payload5.out.push(`<!----> <!---->`);
                Form_field_errors($$payload5, {});
                $$payload5.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!----> <!---->`);
            Form_field($$payload4, {
              form,
              name: "locationId",
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->`);
                {
                  let children = function($$payload6, { props }) {
                    $$payload6.out.push(`<!---->`);
                    Form_label($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out.push(`<!---->Business Location`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out.push(`<!----> `);
                    Input($$payload6, spread_props([
                      props,
                      {
                        placeholder: "Branch Location",
                        get value() {
                          return store_get($$store_subs ??= {}, "$formData", formData).locationId;
                        },
                        set value($$value) {
                          store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).locationId = $$value);
                          $$settled = false;
                        }
                      }
                    ]));
                    $$payload6.out.push(`<!---->`);
                  };
                  Control($$payload5, { children });
                }
                $$payload5.out.push(`<!----> <!---->`);
                Form_field_errors($$payload5, {});
                $$payload5.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!----> <!---->`);
            Form_field($$payload4, {
              form,
              name: "locationId",
              class: "flex flex-col",
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->`);
                Root($$payload5, {
                  get open() {
                    return locationOpen;
                  },
                  set open($$value) {
                    locationOpen = $$value;
                    $$settled = false;
                  },
                  children: ($$payload6) => {
                    $$payload6.out.push(`<!---->`);
                    {
                      let children = function($$payload7, { props }) {
                        $$payload7.out.push(`<!---->`);
                        Form_label($$payload7, {
                          children: ($$payload8) => {
                            $$payload8.out.push(`<!---->Branch/Location`);
                          },
                          $$slots: { default: true }
                        });
                        $$payload7.out.push(`<!----> <!---->`);
                        Popover_trigger($$payload7, spread_props([
                          {
                            class: cn(buttonVariants({ variant: "outline" }), "w-[200px] justify-between", !store_get($$store_subs ??= {}, "$formData", formData).locationId && "text-muted-foreground"),
                            role: "combobox"
                          },
                          props,
                          {
                            children: ($$payload8) => {
                              $$payload8.out.push(`<!---->${escape_html(orgStore.teams?.firstOrNull((f) => f.id === store_get($$store_subs ??= {}, "$formData", formData).locationId)?.name ?? "Select branch Location")} `);
                              Chevrons_up_down($$payload8, { class: "opacity-50" });
                              $$payload8.out.push(`<!---->`);
                            },
                            $$slots: { default: true }
                          }
                        ]));
                        $$payload7.out.push(`<!----> <input hidden${attr("value", store_get($$store_subs ??= {}, "$formData", formData).locationId)}${attr("name", props.name)}/>`);
                      };
                      Control($$payload6, { id: triggerId, children });
                    }
                    $$payload6.out.push(`<!----> <!---->`);
                    Popover_content($$payload6, {
                      class: "w-[200px] p-0",
                      children: ($$payload7) => {
                        $$payload7.out.push(`<!---->`);
                        Command($$payload7, {
                          children: ($$payload8) => {
                            $$payload8.out.push(`<!---->`);
                            Command_input($$payload8, {
                              autofocus: true,
                              placeholder: "Search locations...",
                              class: "h-9"
                            });
                            $$payload8.out.push(`<!----> <!---->`);
                            Command_empty($$payload8, {
                              children: ($$payload9) => {
                                $$payload9.out.push(`<!---->No location data found.`);
                              },
                              $$slots: { default: true }
                            });
                            $$payload8.out.push(`<!----> <!---->`);
                            Command_group($$payload8, {
                              children: ($$payload9) => {
                                const each_array_1 = ensure_array_like(orgStore.teams ?? []);
                                $$payload9.out.push(`<!--[-->`);
                                for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
                                  let location = each_array_1[$$index_1];
                                  $$payload9.out.push(`<!---->`);
                                  Command_item($$payload9, {
                                    value: location.id,
                                    onSelect: () => {
                                      store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).locationId = location.id);
                                      closeAndFocusTrigger(locationTriggerId);
                                    },
                                    children: ($$payload10) => {
                                      $$payload10.out.push(`<!---->${escape_html(location.name)} `);
                                      Check($$payload10, {
                                        class: cn("ml-auto", location.id !== store_get($$store_subs ??= {}, "$formData", formData).locationId && "text-transparent")
                                      });
                                      $$payload10.out.push(`<!---->`);
                                    },
                                    $$slots: { default: true }
                                  });
                                  $$payload9.out.push(`<!---->`);
                                }
                                $$payload9.out.push(`<!--]-->`);
                              },
                              $$slots: { default: true }
                            });
                            $$payload8.out.push(`<!---->`);
                          },
                          $$slots: { default: true }
                        });
                        $$payload7.out.push(`<!---->`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out.push(`<!---->`);
                  },
                  $$slots: { default: true }
                });
                $$payload5.out.push(`<!----> <!---->`);
                Form_field_errors($$payload5, {});
                $$payload5.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!----></div>`);
          },
          $$slots: { default: true }
        });
        $$payload3.out.push(`<!----> <!---->`);
        Card_footer($$payload3, {
          class: "flex justify-between",
          children: ($$payload4) => {
            Button($$payload4, {
              disabled: store_get($$store_subs ??= {}, "$submitting", submitting),
              variant: "outline",
              class: "mt-4 w-1/3",
              onclick: () => utilsStore.clearDynamicModal(),
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->Cancel`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!----> <!---->`);
            Form_button($$payload4, {
              disabled: store_get($$store_subs ??= {}, "$allErrors", allErrors).length > 0 || store_get($$store_subs ??= {}, "$submitting", submitting),
              class: "mt-4 w-4/10",
              children: ($$payload5) => {
                if (store_get($$store_subs ??= {}, "$submitting", submitting)) {
                  $$payload5.out.push("<!--[-->");
                  Loader_circle($$payload5, { class: "mr-2 size-4 animate-spin" });
                } else {
                  $$payload5.out.push("<!--[!-->");
                }
                $$payload5.out.push(`<!--]--> ${escape_html(store_get($$store_subs ??= {}, "$submitting", submitting) ? "Please wait..." : "Update Appointment")}`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!---->`);
          },
          $$slots: { default: true }
        });
        $$payload3.out.push(`<!---->`);
      },
      $$slots: { default: true }
    });
    $$payload2.out.push(`<!----></form>`);
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
function Current_appointment_detail($$payload, $$props) {
  push();
  let aStore = getAppointmentStore();
  let uStore = getUtilsStore();
  const closeDynamicModal = () => {
    aStore.setSelectedAppointment(void 0);
    uStore.clearDynamicModal();
  };
  const startEditAppointment = () => {
    const newAppointmentModal = {
      canClose: false,
      title: "Edit Appointment",
      componentName: "EditAppointment",
      size: "lg",
      color: "default",
      description: "Modify appointment detail",
      componentProps: {}
    };
    uStore.openDynamicModal(newAppointmentModal);
  };
  $$payload.out.push(`<!---->`);
  Card($$payload, {
    class: "rounded-none",
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->`);
      Card_content($$payload2, {
        children: ($$payload3) => {
          $$payload3.out.push(`<div class="grid grid-cols-1 gap-4 space-y-4 md:grid-cols-3"><div class="space-y-1">`);
          Label($$payload3, {
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Location`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> <p class="text-muted-foreground text-sm">${escape_html(aStore.selectedAppointmentLocation?.name)}</p></div> <div class="space-y-1">`);
          Label($$payload3, {
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Customer Name`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> <p class="text-muted-foreground text-sm">${escape_html(aStore.selectedAppointment?.customerName)}</p></div> <div class="space-y-1">`);
          Label($$payload3, {
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Customer Email`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> <p class="text-muted-foreground text-sm">${escape_html(aStore.selectedAppointment?.customerEmail)}</p></div> <div class="space-y-1">`);
          Label($$payload3, {
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Appointment Day`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> <p class="text-muted-foreground text-sm">${escape_html(aStore.selectedAppointment?.appointmentDate)}</p></div> <div class="space-y-1">`);
          Label($$payload3, {
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Appointment Status`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> <p class="text-muted-foreground text-sm">${escape_html(aStore.selectedAppointment?.appointmentStatus)}</p></div> <div class="space-y-1">`);
          Label($$payload3, {
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Start Time`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> <p class="text-muted-foreground text-sm">${escape_html(aStore.selectedAppointment?.startTime)}</p></div> <div class="space-y-1">`);
          Label($$payload3, {
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->End Time`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> <p class="text-muted-foreground text-sm">${escape_html(aStore.selectedAppointment?.endTime)}</p></div> <div class="space-y-1">`);
          Label($$payload3, {
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Notes`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> <p class="text-muted-foreground text-sm">${escape_html(aStore.selectedAppointment?.appointmentNotes)}</p></div></div>`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!----> <!---->`);
      Card_footer($$payload2, {
        children: ($$payload3) => {
          $$payload3.out.push(`<div class="flex justify-between">`);
          Button($$payload3, {
            variant: "outline",
            size: "icon",
            onclick: startEditAppointment,
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Edit`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> `);
          Button($$payload3, {
            variant: "outline",
            size: "icon",
            onclick: closeDynamicModal,
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Close`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----></div>`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!---->`);
  pop();
}
function _layout($$payload, $$props) {
  push();
  let { children } = $$props;
  let currentDate = /* @__PURE__ */ new Date();
  subDays(currentDate, 1);
  let componentsCollection = [
    {
      componentName: "ViewAppointment",
      component: Current_appointment_detail,
      dialogClass: "md:min-w-[620px]"
    },
    {
      componentName: "EditAppointment",
      component: Current_appointment_form,
      dialogClass: "md:min-w-[620px]"
    }
  ];
  children?.($$payload);
  $$payload.out.push(`<!----> `);
  Dynamic_modal($$payload, { componentsCollection });
  $$payload.out.push(`<!---->`);
  pop();
}
export {
  _layout as default
};
