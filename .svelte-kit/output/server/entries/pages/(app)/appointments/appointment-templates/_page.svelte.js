import "clsx";
import { w as push, G as spread_props, y as pop, F as spread_attributes, J as clsx, P as bind_props, Q as copy_payload, R as assign_payload, E as ensure_array_like, B as escape_html, V as store_mutate, T as store_get, U as unsubscribe_stores } from "../../../../../chunks/index2.js";
import "../../../../../chunks/client2.js";
import { I as Icon } from "../../../../../chunks/states.svelte.js";
import "linq-extensions";
import "@sveltejs/kit/internal";
import "../../../../../chunks/exports.js";
import "../../../../../chunks/state.svelte.js";
import "../../../../../chunks/nprogress.js";
import { C as Card_shell } from "../../../../../chunks/card_shell.js";
import { I as Input } from "../../../../../chunks/input.js";
import { B as But<PERSON>, b as buttonVariants } from "../../../../../chunks/button.js";
import { S as Switch } from "../../../../../chunks/switch.js";
import { R as Root$3, D as Dropdown_menu_trigger, a as Dropdown_menu_content } from "../../../../../chunks/index6.js";
import { C as Card } from "../../../../../chunks/card-content.js";
import { F as Form_alerts, a as Form_field, C as Control, b as Form_field_errors } from "../../../../../chunks/index5.js";
import { g as getFlashModule } from "../../../../../chunks/dialog.js";
import { s as superForm } from "../../../../../chunks/string.js";
import "@sveltejs/kit";
import { a as valibotClient, d as defaults } from "../../../../../chunks/valibot.js";
import { w as AppointmentTypeController, x as createAppointmentTypeFormSchema, y as appointmentRepeatTypes, z as deleteAppointmentTypeFormSchema } from "../../../../../chunks/auth-client.js";
import "superjson";
import "@oslojs/crypto/sha2";
import "decimal.js";
import "remult";
import { H as Hidden_input } from "../../../../../chunks/hidden-input.js";
import { f as getAppointmentStore } from "../../../../../chunks/sheet-content.js";
import { R as Root$1, D as Dialog_content, a as Dialog_header, b as Dialog_title, c as Dialog_description } from "../../../../../chunks/index9.js";
import { L as Label } from "../../../../../chunks/label.js";
import { R as Root$2, S as Select_trigger, a as Select_content, b as Select_item } from "../../../../../chunks/index8.js";
import { t as tick } from "../../../../../chunks/scroll-lock.js";
import { C as Command, a as Command_input, b as Command_empty, c as Command_group, d as Command_item } from "../../../../../chunks/command-input.js";
import { c as cn } from "../../../../../chunks/shadcn.utils.js";
import { C as Command_list } from "../../../../../chunks/command-list.js";
import { R as Root, a as Popover_trigger, P as Popover_content } from "../../../../../chunks/index7.js";
import { C as Check } from "../../../../../chunks/check.js";
import { C as Chevrons_up_down } from "../../../../../chunks/chevrons-up-down.js";
import { uuidv7 } from "@kripod/uuidv7";
import { E as Ellipsis } from "../../../../../chunks/ellipsis.js";
import { a as Dropdown_menu_group, b as Dropdown_menu_item, D as Dropdown_menu_separator } from "../../../../../chunks/dropdown-menu-separator.js";
import { C as Calendar } from "../../../../../chunks/calendar.js";
function Delete($$payload, $$props) {
  push();
  /**
   * @license @lucide/svelte v0.539.0 - ISC
   *
   * ISC License
   *
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   *
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   *
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   *
   */
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    [
      "path",
      {
        "d": "M10 5a2 2 0 0 0-1.344.519l-6.328 5.74a1 1 0 0 0 0 1.481l6.328 5.741A2 2 0 0 0 10 19h10a2 2 0 0 0 2-2V7a2 2 0 0 0-2-2z"
      }
    ],
    ["path", { "d": "m12 9 6 6" }],
    ["path", { "d": "m18 9-6 6" }]
  ];
  Icon($$payload, spread_props([
    { name: "delete" },
    /**
     * @component @name Delete
     * @description Lucide SVG icon component, renders SVG Element with children.
     *
     * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgNWEyIDIgMCAwIDAtMS4zNDQuNTE5bC02LjMyOCA1Ljc0YTEgMSAwIDAgMCAwIDEuNDgxbDYuMzI4IDUuNzQxQTIgMiAwIDAgMCAxMCAxOWgxMGEyIDIgMCAwIDAgMi0yVjdhMiAyIDAgMCAwLTItMnoiIC8+CiAgPHBhdGggZD0ibTEyIDkgNiA2IiAvPgogIDxwYXRoIGQ9Im0xOCA5LTYgNiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/delete
     * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
     *
     * @param {Object} props - Lucide icons props and any valid SVG attribute
     * @returns {FunctionalComponent} Svelte component
     *
     */
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function Link_2($$payload, $$props) {
  push();
  /**
   * @license @lucide/svelte v0.539.0 - ISC
   *
   * ISC License
   *
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   *
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   *
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   *
   */
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    ["path", { "d": "M9 17H7A5 5 0 0 1 7 7h2" }],
    ["path", { "d": "M15 7h2a5 5 0 1 1 0 10h-2" }],
    ["line", { "x1": "8", "x2": "16", "y1": "12", "y2": "12" }]
  ];
  Icon($$payload, spread_props([
    { name: "link-2" },
    /**
     * @component @name Link2
     * @description Lucide SVG icon component, renders SVG Element with children.
     *
     * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNOSAxN0g3QTUgNSAwIDAgMSA3IDdoMiIgLz4KICA8cGF0aCBkPSJNMTUgN2gyYTUgNSAwIDEgMSAwIDEwaC0yIiAvPgogIDxsaW5lIHgxPSI4IiB4Mj0iMTYiIHkxPSIxMiIgeTI9IjEyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/link-2
     * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
     *
     * @param {Object} props - Lucide icons props and any valid SVG attribute
     * @returns {FunctionalComponent} Svelte component
     *
     */
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
const TIMEZONES = [
  {
    id: "UTC",
    name: "UTC"
  },
  {
    id: "America/New_York",
    name: "America/New_York"
  },
  {
    id: "America/Chicago",
    name: "America/Chicago"
  },
  {
    id: "America/Denver",
    name: "America/Denver"
  },
  {
    id: "America/Los_Angeles",
    name: "America/Los_Angeles"
  },
  {
    id: "Europe/London",
    name: "Europe/London"
  },
  {
    id: "Europe/Paris",
    name: "Europe/Paris"
  },
  {
    id: "Europe/Berlin",
    name: "Europe/Berlin"
  },
  {
    id: "Europe/Moscow",
    name: "Europe/Moscow"
  },
  {
    id: "Asia/Dubai",
    name: "Asia/Dubai"
  },
  {
    id: "Asia/Singapore",
    name: "Asia/Singapore"
  },
  {
    id: "Asia/Tokyo",
    name: "Asia/Tokyo"
  },
  {
    id: "Australia/Sydney",
    name: "Australia/Sydney"
  },
  {
    id: "Pacific/Auckland",
    name: "Pacific/Auckland"
  },
  {
    id: "Africa/Lagos",
    name: "Africa/Lagos (Abuja)"
  },
  {
    id: "Africa/Johannesburg",
    name: "Africa/Johannesburg (Pretoria)"
  },
  {
    id: "Africa/Nairobi",
    name: "Africa/Nairobi"
  }
];
function Dialog_footer($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    children,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  $$payload.out.push(`<div${spread_attributes(
    {
      "data-slot": "dialog-footer",
      class: clsx(cn("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end", className)),
      ...restProps
    },
    null
  )}>`);
  children?.($$payload);
  $$payload.out.push(`<!----></div>`);
  bind_props($$props, { ref });
  pop();
}
function Combo_input($$payload, $$props) {
  push();
  let { comboData, comboValue, comboLabel } = $$props;
  let open = false;
  let triggerRef = null;
  const selectedValue = comboData.find((f) => f.value === comboValue)?.label ?? comboLabel;
  const closeAndFocusTrigger = () => {
    open = false;
    tick().then(() => {
      triggerRef?.focus();
    });
  };
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out.push(`<!---->`);
    Root($$payload2, {
      get open() {
        return open;
      },
      set open($$value) {
        open = $$value;
        $$settled = false;
      },
      children: ($$payload3) => {
        $$payload3.out.push(`<!---->`);
        {
          let child = function($$payload4, { props }) {
            Button($$payload4, spread_props([
              { variant: "outline", class: "w-[200px] justify-between" },
              props,
              {
                role: "combobox",
                "aria-expanded": open,
                children: ($$payload5) => {
                  $$payload5.out.push(`<!---->${escape_html(selectedValue || { comboLabel })} `);
                  Chevrons_up_down($$payload5, { class: "opacity-50" });
                  $$payload5.out.push(`<!---->`);
                },
                $$slots: { default: true }
              }
            ]));
          };
          Popover_trigger($$payload3, {
            get ref() {
              return triggerRef;
            },
            set ref($$value) {
              triggerRef = $$value;
              $$settled = false;
            },
            child,
            $$slots: { child: true }
          });
        }
        $$payload3.out.push(`<!----> <!---->`);
        Popover_content($$payload3, {
          class: "w-[200px] p-0",
          children: ($$payload4) => {
            $$payload4.out.push(`<!---->`);
            Command($$payload4, {
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->`);
                Command_input($$payload5, { placeholder: "Search framework...", class: "h-9" });
                $$payload5.out.push(`<!----> <!---->`);
                Command_list($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out.push(`<!---->`);
                    Command_empty($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out.push(`<!---->No framework found.`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out.push(`<!----> <!---->`);
                    Command_group($$payload6, {
                      children: ($$payload7) => {
                        const each_array = ensure_array_like(comboData);
                        $$payload7.out.push(`<!--[-->`);
                        for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
                          let comboItem = each_array[$$index];
                          $$payload7.out.push(`<!---->`);
                          Command_item($$payload7, {
                            value: comboItem.value,
                            onSelect: () => {
                              comboValue = comboItem.value;
                              closeAndFocusTrigger();
                            },
                            children: ($$payload8) => {
                              Check($$payload8, {
                                class: cn(comboValue !== comboItem.value && "text-transparent")
                              });
                              $$payload8.out.push(`<!----> ${escape_html(comboItem.label)}`);
                            },
                            $$slots: { default: true }
                          });
                          $$payload7.out.push(`<!---->`);
                        }
                        $$payload7.out.push(`<!--]-->`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out.push(`<!---->`);
                  },
                  $$slots: { default: true }
                });
                $$payload5.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!---->`);
          },
          $$slots: { default: true }
        });
        $$payload3.out.push(`<!---->`);
      },
      $$slots: { default: true }
    });
    $$payload2.out.push(`<!---->`);
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  pop();
}
function Create_appointment_type($$payload, $$props) {
  push();
  var $$store_subs;
  let { createAppointmentTypeForm, showDialog } = $$props;
  const form = superForm(createAppointmentTypeForm, {
    validators: valibotClient(createAppointmentTypeFormSchema),
    multipleSubmits: "prevent",
    SPA: true,
    clearOnSubmit: "errors-and-message",
    async onUpdate({ form: form2 }) {
      try {
        if (!form2.valid) return;
        const timeZone = TIMEZONES.find((tz) => tz.id === form2.data.timeZoneId);
        const appType = {
          id: uuidv7(),
          appointmentDetail: form2.data.name,
          durationInMinutes: form2.data.durationInMinutes,
          isActive: form2.data.isActive,
          isRepeatable: form2.data.isRepeatEnabled,
          repeatType: form2.data.repeatInterval,
          requiresUpfrontPayment: form2.data.requiresUpfrontPayment,
          upfrontPaymentAmount: form2.data.requiresUpfrontPayment ? form2.data.upfrontPaymentAmount?.toString() : "0",
          timezoneId: timeZone?.id
        };
        const resp = await AppointmentTypeController.insert(appType);
        if (!resp.id) throw new Error("Failed to create appointment type");
        form2.message = {
          status: "success",
          message: "Appointment type created successfully"
        };
      } catch (error) {
        console.error(error);
        form2.message = {
          status: "error",
          message: "Failed to create appointment type"
        };
      }
    }
  });
  const { form: formData, enhance, message, allErrors, submitting } = form;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out.push(`<!---->`);
    Root$1($$payload2, {
      get open() {
        return showDialog;
      },
      set open($$value) {
        showDialog = $$value;
        $$settled = false;
      },
      children: ($$payload3) => {
        $$payload3.out.push(`<!---->`);
        Dialog_content($$payload3, {
          class: "sm:max-w-[425px]",
          showCloseButton: false,
          children: ($$payload4) => {
            $$payload4.out.push(`<!---->`);
            Dialog_header($$payload4, {
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->`);
                Dialog_title($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out.push(`<!---->Create Appointment Type`);
                  },
                  $$slots: { default: true }
                });
                $$payload5.out.push(`<!----> <!---->`);
                Dialog_description($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out.push(`<!---->Create a new event type for bookings. Click save when you're done.`);
                  },
                  $$slots: { default: true }
                });
                $$payload5.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!----> <form method="POST" class="grid gap-4 py-4">`);
            Form_alerts($$payload4, { message });
            $$payload4.out.push(`<!----> <!---->`);
            Form_field($$payload4, {
              form,
              name: "name",
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->`);
                {
                  let children = function($$payload6, { props }) {
                    Label($$payload6, {
                      for: "name",
                      children: ($$payload7) => {
                        $$payload7.out.push(`<!---->Name`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out.push(`<!----> `);
                    Input($$payload6, spread_props([
                      props,
                      {
                        get value() {
                          return store_get($$store_subs ??= {}, "$formData", formData).name;
                        },
                        set value($$value) {
                          store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).name = $$value);
                          $$settled = false;
                        }
                      }
                    ]));
                    $$payload6.out.push(`<!---->`);
                  };
                  Control($$payload5, { children });
                }
                $$payload5.out.push(`<!----> <!---->`);
                Form_field_errors($$payload5, {});
                $$payload5.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!----> <!---->`);
            Form_field($$payload4, {
              form,
              name: "durationInMinutes",
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->`);
                {
                  let children = function($$payload6, { props }) {
                    Label($$payload6, {
                      for: "duration",
                      children: ($$payload7) => {
                        $$payload7.out.push(`<!---->Duration (minutes)`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out.push(`<!----> `);
                    Input($$payload6, spread_props([
                      { type: "number" },
                      props,
                      {
                        get value() {
                          return store_get($$store_subs ??= {}, "$formData", formData).durationInMinutes;
                        },
                        set value($$value) {
                          store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).durationInMinutes = $$value);
                          $$settled = false;
                        }
                      }
                    ]));
                    $$payload6.out.push(`<!---->`);
                  };
                  Control($$payload5, { children });
                }
                $$payload5.out.push(`<!----> <!---->`);
                Form_field_errors($$payload5, {});
                $$payload5.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!----> <!---->`);
            Form_field($$payload4, {
              form,
              name: "isActive",
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->`);
                {
                  let children = function($$payload6, { props }) {
                    $$payload6.out.push(`<div class="flex items-center gap-2">`);
                    Label($$payload6, {
                      for: "isActive",
                      children: ($$payload7) => {
                        $$payload7.out.push(`<!---->Active`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out.push(`<!----> `);
                    Switch($$payload6, spread_props([
                      props,
                      {
                        get checked() {
                          return store_get($$store_subs ??= {}, "$formData", formData).isActive;
                        },
                        set checked($$value) {
                          store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).isActive = $$value);
                          $$settled = false;
                        }
                      }
                    ]));
                    $$payload6.out.push(`<!----></div>`);
                  };
                  Control($$payload5, { children });
                }
                $$payload5.out.push(`<!----> <!---->`);
                Form_field_errors($$payload5, {});
                $$payload5.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!----> <!---->`);
            Form_field($$payload4, {
              form,
              name: "isRepeatEnabled",
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->`);
                {
                  let children = function($$payload6, { props }) {
                    $$payload6.out.push(`<div class="flex items-center gap-2">`);
                    Label($$payload6, {
                      for: "repeat",
                      children: ($$payload7) => {
                        $$payload7.out.push(`<!---->Enable Repeat`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out.push(`<!----> `);
                    Switch($$payload6, spread_props([
                      props,
                      {
                        get checked() {
                          return store_get($$store_subs ??= {}, "$formData", formData).isRepeatEnabled;
                        },
                        set checked($$value) {
                          store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).isRepeatEnabled = $$value);
                          $$settled = false;
                        }
                      }
                    ]));
                    $$payload6.out.push(`<!----></div>`);
                  };
                  Control($$payload5, { children });
                }
                $$payload5.out.push(`<!----> <!---->`);
                Form_field_errors($$payload5, {});
                $$payload5.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!----> `);
            if (store_get($$store_subs ??= {}, "$formData", formData).isRepeatEnabled) {
              $$payload4.out.push("<!--[-->");
              $$payload4.out.push(`<!---->`);
              Form_field($$payload4, {
                form,
                name: "repeatInterval",
                children: ($$payload5) => {
                  $$payload5.out.push(`<!---->`);
                  {
                    let children = function($$payload6, { props }) {
                      Label($$payload6, {
                        for: "repeatInterval",
                        children: ($$payload7) => {
                          $$payload7.out.push(`<!---->Repeat Interval`);
                        },
                        $$slots: { default: true }
                      });
                      $$payload6.out.push(`<!----> <!---->`);
                      Root$2($$payload6, {
                        type: "single",
                        get value() {
                          return store_get($$store_subs ??= {}, "$formData", formData).repeatInterval;
                        },
                        set value($$value) {
                          store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).repeatInterval = $$value);
                          $$settled = false;
                        },
                        children: ($$payload7) => {
                          $$payload7.out.push(`<!---->`);
                          Select_trigger($$payload7, spread_props([
                            { class: "w-full" },
                            props,
                            {
                              children: ($$payload8) => {
                                $$payload8.out.push(`<!---->${escape_html(store_get($$store_subs ??= {}, "$formData", formData).repeatInterval ? store_get($$store_subs ??= {}, "$formData", formData).repeatInterval : "Select a repeat interval")}`);
                              },
                              $$slots: { default: true }
                            }
                          ]));
                          $$payload7.out.push(`<!----> <!---->`);
                          Select_content($$payload7, {
                            children: ($$payload8) => {
                              const each_array = ensure_array_like(Object.values(appointmentRepeatTypes));
                              $$payload8.out.push(`<!--[-->`);
                              for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
                                let type = each_array[$$index];
                                $$payload8.out.push(`<!---->`);
                                Select_item($$payload8, {
                                  value: type,
                                  children: ($$payload9) => {
                                    $$payload9.out.push(`<!---->${escape_html(type)}`);
                                  },
                                  $$slots: { default: true }
                                });
                                $$payload8.out.push(`<!---->`);
                              }
                              $$payload8.out.push(`<!--]-->`);
                            },
                            $$slots: { default: true }
                          });
                          $$payload7.out.push(`<!---->`);
                        },
                        $$slots: { default: true }
                      });
                      $$payload6.out.push(`<!---->`);
                    };
                    Control($$payload5, { children });
                  }
                  $$payload5.out.push(`<!----> <!---->`);
                  Form_field_errors($$payload5, {});
                  $$payload5.out.push(`<!---->`);
                },
                $$slots: { default: true }
              });
              $$payload4.out.push(`<!---->`);
            } else {
              $$payload4.out.push("<!--[!-->");
            }
            $$payload4.out.push(`<!--]--> <!---->`);
            Form_field($$payload4, {
              form,
              name: "requiresUpfrontPayment",
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->`);
                {
                  let children = function($$payload6, { props }) {
                    $$payload6.out.push(`<div class="flex items-center gap-2">`);
                    Label($$payload6, {
                      for: "requiresUpfrontPayment",
                      children: ($$payload7) => {
                        $$payload7.out.push(`<!---->Upfront Payment`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out.push(`<!----> `);
                    Switch($$payload6, spread_props([
                      props,
                      {
                        get checked() {
                          return store_get($$store_subs ??= {}, "$formData", formData).requiresUpfrontPayment;
                        },
                        set checked($$value) {
                          store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).requiresUpfrontPayment = $$value);
                          $$settled = false;
                        }
                      }
                    ]));
                    $$payload6.out.push(`<!----></div>`);
                  };
                  Control($$payload5, { children });
                }
                $$payload5.out.push(`<!----> <!---->`);
                Form_field_errors($$payload5, {});
                $$payload5.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!----> `);
            if (store_get($$store_subs ??= {}, "$formData", formData).requiresUpfrontPayment) {
              $$payload4.out.push("<!--[-->");
              $$payload4.out.push(`<!---->`);
              Form_field($$payload4, {
                form,
                name: "upfrontPaymentAmount",
                children: ($$payload5) => {
                  $$payload5.out.push(`<!---->`);
                  {
                    let children = function($$payload6, { props }) {
                      Label($$payload6, {
                        for: "upfrontPaymentAmount",
                        children: ($$payload7) => {
                          $$payload7.out.push(`<!---->Upfront Payment Amount`);
                        },
                        $$slots: { default: true }
                      });
                      $$payload6.out.push(`<!----> `);
                      Input($$payload6, spread_props([
                        { type: "number" },
                        props,
                        {
                          get value() {
                            return store_get($$store_subs ??= {}, "$formData", formData).upfrontPaymentAmount;
                          },
                          set value($$value) {
                            store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).upfrontPaymentAmount = $$value);
                            $$settled = false;
                          }
                        }
                      ]));
                      $$payload6.out.push(`<!---->`);
                    };
                    Control($$payload5, { children });
                  }
                  $$payload5.out.push(`<!----> <!---->`);
                  Form_field_errors($$payload5, {});
                  $$payload5.out.push(`<!---->`);
                },
                $$slots: { default: true }
              });
              $$payload4.out.push(`<!---->`);
            } else {
              $$payload4.out.push("<!--[!-->");
            }
            $$payload4.out.push(`<!--]--> <!---->`);
            Form_field($$payload4, {
              form,
              name: "timeZoneId",
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->`);
                {
                  let children = function($$payload6, { props }) {
                    Label($$payload6, {
                      for: "timeZoneId",
                      children: ($$payload7) => {
                        $$payload7.out.push(`<!---->Timezone`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out.push(`<!----> `);
                    Combo_input($$payload6, {
                      comboLabel: "Select a timezone",
                      comboData: TIMEZONES,
                      comboValue: store_get($$store_subs ??= {}, "$formData", formData).timeZoneId
                    });
                    $$payload6.out.push(`<!---->`);
                  };
                  Control($$payload5, { children });
                }
                $$payload5.out.push(`<!----> <!---->`);
                Form_field_errors($$payload5, {});
                $$payload5.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!----> <!---->`);
            Dialog_footer($$payload4, {
              children: ($$payload5) => {
                Button($$payload5, {
                  variant: "outline",
                  disabled: store_get($$store_subs ??= {}, "$submitting", submitting),
                  onclick: () => showDialog = false,
                  children: ($$payload6) => {
                    $$payload6.out.push(`<!---->Cancel`);
                  },
                  $$slots: { default: true }
                });
                $$payload5.out.push(`<!----> `);
                Button($$payload5, {
                  type: "submit",
                  disabled: store_get($$store_subs ??= {}, "$submitting", submitting) || store_get($$store_subs ??= {}, "$allErrors", allErrors).length > 0,
                  children: ($$payload6) => {
                    $$payload6.out.push(`<!---->Create`);
                  },
                  $$slots: { default: true }
                });
                $$payload5.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!----></form>`);
          },
          $$slots: { default: true }
        });
        $$payload3.out.push(`<!---->`);
      },
      $$slots: { default: true }
    });
    $$payload2.out.push(`<!---->`);
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
function Edit_appointment_type($$payload, $$props) {
  push();
  var $$store_subs;
  let { selectedType, showEditDialog } = $$props;
  const form = superForm(defaults(selectedType, valibotClient(createAppointmentTypeFormSchema)), {
    validators: valibotClient(createAppointmentTypeFormSchema),
    SPA: true,
    clearOnSubmit: "errors-and-message",
    multipleSubmits: "prevent",
    async onUpdate({ form: form2 }) {
      try {
        if (!form2.valid) {
          form2.message = { status: "error", text: "validation failed" };
          return;
        }
        const selectedTimeZone = TIMEZONES.find((k) => k.id === form2.data.timeZoneId);
        const appointmentTypeData = {
          appointmentDetail: form2.data.name,
          durationInMinutes: form2.data.durationInMinutes,
          isActive: form2.data.isActive,
          isRepeatable: form2.data.isRepeatEnabled,
          repeatType: form2.data.repeatInterval,
          requiresUpfrontPayment: form2.data.requiresUpfrontPayment,
          upfrontPaymentAmount: form2.data.requiresUpfrontPayment ? form2.data.upfrontPaymentAmount : 0,
          timezoneId: selectedTimeZone?.id
        };
        const updateResp = await AppointmentTypeController.update(selectedType.id, appointmentTypeData);
        if (!updateResp.id) throw new Error("Failed to update appointment type");
        form2.message = {
          status: "success",
          text: "Appointment type updated successfully"
        };
      } catch (error) {
        console.error(error);
        form2.message = { status: "error", text: "Failed to update appointment type" };
      }
    },
    ...getFlashModule()
  });
  const { form: formData, enhance, message } = form;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out.push(`<!---->`);
    Root$1($$payload2, {
      get open() {
        return showEditDialog;
      },
      set open($$value) {
        showEditDialog = $$value;
        $$settled = false;
      },
      children: ($$payload3) => {
        $$payload3.out.push(`<!---->`);
        Dialog_content($$payload3, {
          class: "sm:max-w-[425px]",
          showCloseButton: false,
          interactOutsideBehavior: "close",
          children: ($$payload4) => {
            $$payload4.out.push(`<!---->`);
            Dialog_header($$payload4, {
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->`);
                Dialog_title($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out.push(`<!---->Edit Event Type`);
                  },
                  $$slots: { default: true }
                });
                $$payload5.out.push(`<!----> <!---->`);
                Dialog_description($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out.push(`<!---->Make changes to the event type. Click save when you're done.`);
                  },
                  $$slots: { default: true }
                });
                $$payload5.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!----> <form method="POST">`);
            Form_alerts($$payload4, { message });
            $$payload4.out.push(`<!----> <!---->`);
            Form_field($$payload4, {
              form,
              name: "name",
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->`);
                {
                  let children = function($$payload6, { props }) {
                    Label($$payload6, {
                      for: "name",
                      children: ($$payload7) => {
                        $$payload7.out.push(`<!---->Name`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out.push(`<!----> `);
                    Input($$payload6, spread_props([
                      props,
                      {
                        get value() {
                          return store_get($$store_subs ??= {}, "$formData", formData).name;
                        },
                        set value($$value) {
                          store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).name = $$value);
                          $$settled = false;
                        }
                      }
                    ]));
                    $$payload6.out.push(`<!---->`);
                  };
                  Control($$payload5, { children });
                }
                $$payload5.out.push(`<!----> <!---->`);
                Form_field_errors($$payload5, {});
                $$payload5.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!----> <!---->`);
            Form_field($$payload4, {
              form,
              name: "durationInMinutes",
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->`);
                {
                  let children = function($$payload6, { props }) {
                    Label($$payload6, {
                      for: "duration",
                      children: ($$payload7) => {
                        $$payload7.out.push(`<!---->Duration (minutes)`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out.push(`<!----> `);
                    Input($$payload6, spread_props([
                      { type: "number" },
                      props,
                      {
                        get value() {
                          return store_get($$store_subs ??= {}, "$formData", formData).durationInMinutes;
                        },
                        set value($$value) {
                          store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).durationInMinutes = $$value);
                          $$settled = false;
                        }
                      }
                    ]));
                    $$payload6.out.push(`<!---->`);
                  };
                  Control($$payload5, { children });
                }
                $$payload5.out.push(`<!----> <!---->`);
                Form_field_errors($$payload5, {});
                $$payload5.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!----> <!---->`);
            Form_field($$payload4, {
              form,
              name: "isActive",
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->`);
                {
                  let children = function($$payload6, { props }) {
                    $$payload6.out.push(`<div class="flex items-center gap-2">`);
                    Label($$payload6, {
                      for: "active",
                      children: ($$payload7) => {
                        $$payload7.out.push(`<!---->Active`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out.push(`<!----> `);
                    Switch($$payload6, spread_props([
                      props,
                      {
                        get checked() {
                          return store_get($$store_subs ??= {}, "$formData", formData).isActive;
                        },
                        set checked($$value) {
                          store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).isActive = $$value);
                          $$settled = false;
                        }
                      }
                    ]));
                    $$payload6.out.push(`<!----></div>`);
                  };
                  Control($$payload5, { children });
                }
                $$payload5.out.push(`<!----> <!---->`);
                Form_field_errors($$payload5, {});
                $$payload5.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!----> <!---->`);
            Form_field($$payload4, {
              form,
              name: "isRepeatEnabled",
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->`);
                {
                  let children = function($$payload6, { props }) {
                    $$payload6.out.push(`<div class="flex items-center gap-2">`);
                    Label($$payload6, {
                      for: "repeat",
                      children: ($$payload7) => {
                        $$payload7.out.push(`<!---->Enable Repeat`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out.push(`<!----> `);
                    Switch($$payload6, spread_props([
                      props,
                      {
                        get checked() {
                          return store_get($$store_subs ??= {}, "$formData", formData).isRepeatEnabled;
                        },
                        set checked($$value) {
                          store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).isRepeatEnabled = $$value);
                          $$settled = false;
                        }
                      }
                    ]));
                    $$payload6.out.push(`<!----></div>`);
                  };
                  Control($$payload5, { children });
                }
                $$payload5.out.push(`<!----> <!---->`);
                Form_field_errors($$payload5, {});
                $$payload5.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!----> `);
            if (store_get($$store_subs ??= {}, "$formData", formData).isRepeatEnabled) {
              $$payload4.out.push("<!--[-->");
              $$payload4.out.push(`<!---->`);
              Form_field($$payload4, {
                form,
                name: "repeatInterval",
                children: ($$payload5) => {
                  $$payload5.out.push(`<!---->`);
                  {
                    let children = function($$payload6, { props }) {
                      Label($$payload6, {
                        for: "repeatInterval",
                        children: ($$payload7) => {
                          $$payload7.out.push(`<!---->Repeat Interval`);
                        },
                        $$slots: { default: true }
                      });
                      $$payload6.out.push(`<!----> <!---->`);
                      Root$2($$payload6, {
                        type: "single",
                        get value() {
                          return store_get($$store_subs ??= {}, "$formData", formData).repeatInterval;
                        },
                        set value($$value) {
                          store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).repeatInterval = $$value);
                          $$settled = false;
                        },
                        children: ($$payload7) => {
                          $$payload7.out.push(`<!---->`);
                          Select_trigger($$payload7, spread_props([
                            { class: "w-full" },
                            props,
                            {
                              children: ($$payload8) => {
                                $$payload8.out.push(`<!---->${escape_html(store_get($$store_subs ??= {}, "$formData", formData).repeatInterval ? store_get($$store_subs ??= {}, "$formData", formData).repeatInterval : "Select a repeat interval")}`);
                              },
                              $$slots: { default: true }
                            }
                          ]));
                          $$payload7.out.push(`<!----> <!---->`);
                          Select_content($$payload7, {
                            children: ($$payload8) => {
                              const each_array = ensure_array_like(Object.values(appointmentRepeatTypes));
                              $$payload8.out.push(`<!--[-->`);
                              for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
                                let type = each_array[$$index];
                                $$payload8.out.push(`<!---->`);
                                Select_item($$payload8, {
                                  value: type,
                                  children: ($$payload9) => {
                                    $$payload9.out.push(`<!---->${escape_html(type)}`);
                                  },
                                  $$slots: { default: true }
                                });
                                $$payload8.out.push(`<!---->`);
                              }
                              $$payload8.out.push(`<!--]-->`);
                            },
                            $$slots: { default: true }
                          });
                          $$payload7.out.push(`<!---->`);
                        },
                        $$slots: { default: true }
                      });
                      $$payload6.out.push(`<!---->`);
                    };
                    Control($$payload5, { children });
                  }
                  $$payload5.out.push(`<!----> <!---->`);
                  Form_field_errors($$payload5, {});
                  $$payload5.out.push(`<!---->`);
                },
                $$slots: { default: true }
              });
              $$payload4.out.push(`<!---->`);
            } else {
              $$payload4.out.push("<!--[!-->");
            }
            $$payload4.out.push(`<!--]--> <!---->`);
            Dialog_footer($$payload4, {
              children: ($$payload5) => {
                Button($$payload5, {
                  variant: "outline",
                  onclick: () => showEditDialog = false,
                  children: ($$payload6) => {
                    $$payload6.out.push(`<!---->Cancel`);
                  },
                  $$slots: { default: true }
                });
                $$payload5.out.push(`<!----> `);
                Button($$payload5, {
                  type: "submit",
                  children: ($$payload6) => {
                    $$payload6.out.push(`<!---->Save changes`);
                  },
                  $$slots: { default: true }
                });
                $$payload5.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!----></form>`);
          },
          $$slots: { default: true }
        });
        $$payload3.out.push(`<!---->`);
      },
      $$slots: { default: true }
    });
    $$payload2.out.push(`<!---->`);
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
function Appointment_type_list($$payload, $$props) {
  push();
  var $$store_subs;
  let { deleteAppointmentTypeForm, createAppointmentTypeForm } = $$props;
  let showCreateDialog = false;
  let showEditDialog = false;
  let selectedType = null;
  let searchQuery = "";
  const aStore = getAppointmentStore();
  const deleteForm = superForm(deleteAppointmentTypeForm, {
    validators: valibotClient(deleteAppointmentTypeFormSchema),
    SPA: true,
    clearOnSubmit: "errors-and-message",
    multipleSubmits: "prevent",
    async onUpdate({ form }) {
      try {
        if (!form.valid) return;
        const { data: formData } = form;
        const appointmentType = aStore.appointmentTemplates?.find((at) => at.id === formData.id);
        if (!appointmentType) throw new Error("Appointment type not found");
        await AppointmentTypeController.deleteById(formData.id);
        form.message = {
          status: "success",
          message: "Appointment type deleted successfully"
        };
      } catch (error) {
        form.message = {
          status: "error",
          message: `Failed to delete appointment type: ${error.message}`
        };
      }
    },
    ...getFlashModule()
  });
  const { enhance, message, submitting } = deleteForm;
  const filteredAppointmentTypes = (() => {
    return aStore.appointmentTemplates?.filter((type) => {
      const query = searchQuery.toLowerCase();
      return type.appointmentDetail?.toLowerCase().includes(query);
    }) ?? [];
  })();
  const handleTypeClick = (type) => {
    selectedType = { ...type };
    showEditDialog = true;
  };
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out.push(`<div class="container mx-auto space-y-6 p-4"><div class="flex items-center justify-between"><h2 class="text-2xl font-semibold">Event Types</h2> <div class="flex items-center gap-4">`);
    Input($$payload2, {
      type: "search",
      placeholder: "Search...",
      class: "w-[150px] lg:w-[250px]",
      get value() {
        return searchQuery;
      },
      set value($$value) {
        searchQuery = $$value;
        $$settled = false;
      }
    });
    $$payload2.out.push(`<!----> `);
    Button($$payload2, {
      onclick: () => showCreateDialog = true,
      children: ($$payload3) => {
        $$payload3.out.push(`<!---->Create Appointment Type`);
      },
      $$slots: { default: true }
    });
    $$payload2.out.push(`<!----></div></div> `);
    if (filteredAppointmentTypes.length > 0) {
      $$payload2.out.push("<!--[-->");
      const each_array = ensure_array_like(filteredAppointmentTypes);
      Form_alerts($$payload2, { message });
      $$payload2.out.push(`<!----> <div class="grid gap-4"><!--[-->`);
      for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
        let type = each_array[$$index];
        Card($$payload2, {
          class: "hover:bg-muted/50 cursor-pointer p-4",
          onclick: () => handleTypeClick(type),
          children: ($$payload3) => {
            $$payload3.out.push(`<div class="flex items-center justify-between"><div class="flex items-center gap-4"><div><h3 class="font-medium">${escape_html(type.appointmentDetail)}</h3> <div class="text-muted-foreground flex items-center gap-1 text-sm">${escape_html(type.durationInMinutes)} <span class="px-1">•</span> ${escape_html(type.isRepeatable ? "Repeats" : "Does not repeat")}</div> <div class="text-muted-foreground flex items-center gap-1 text-sm">${escape_html(type.isActive ? "Active" : "Inactive")}</div></div></div> <div class="flex items-center gap-2">`);
            Switch($$payload3, { checked: type.isActive ?? false, disabled: true });
            $$payload3.out.push(`<!----> <!---->`);
            Root$3($$payload3, {
              children: ($$payload4) => {
                $$payload4.out.push(`<!---->`);
                Dropdown_menu_trigger($$payload4, {
                  class: buttonVariants({ variant: "outline" }),
                  children: ($$payload5) => {
                    Ellipsis($$payload5, { class: "size-4" });
                  },
                  $$slots: { default: true }
                });
                $$payload4.out.push(`<!----> <!---->`);
                Dropdown_menu_content($$payload4, {
                  children: ($$payload5) => {
                    $$payload5.out.push(`<!---->`);
                    Dropdown_menu_group($$payload5, {
                      children: ($$payload6) => {
                        $$payload6.out.push(`<!---->`);
                        Dropdown_menu_item($$payload6, {
                          children: ($$payload7) => {
                            Link_2($$payload7, { class: "mr-2 size-4" });
                            $$payload7.out.push(`<!----> Copy Link`);
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out.push(`<!----> <!---->`);
                        Dropdown_menu_item($$payload6, {
                          children: ($$payload7) => {
                            Calendar($$payload7, { class: "mr-2 size-4" });
                            $$payload7.out.push(`<!----> View Bookings`);
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out.push(`<!---->`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out.push(`<!----> <!---->`);
                    Dropdown_menu_separator($$payload5, {});
                    $$payload5.out.push(`<!----> <!---->`);
                    Dropdown_menu_item($$payload5, {
                      class: "text-destructive focus:bg-destructive focus:text-destructive-foreground",
                      children: ($$payload6) => {
                        $$payload6.out.push(`<form method="POST"><!---->`);
                        Form_field($$payload6, {
                          form: deleteForm,
                          name: "id",
                          children: ($$payload7) => {
                            $$payload7.out.push(`<!---->`);
                            {
                              let children = function($$payload8, { props }) {
                                Hidden_input($$payload8, { props, value: type.id });
                              };
                              Control($$payload7, { children });
                            }
                            $$payload7.out.push(`<!---->`);
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out.push(`<!----> `);
                        Button($$payload6, {
                          variant: "destructive",
                          size: "icon",
                          type: "submit",
                          disabled: store_get($$store_subs ??= {}, "$submitting", submitting) || !type.id,
                          children: ($$payload7) => {
                            Delete($$payload7, { class: "size-4 pr-1" });
                            $$payload7.out.push(`<!----> Delete`);
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out.push(`<!----></form>`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out.push(`<!---->`);
                  },
                  $$slots: { default: true }
                });
                $$payload4.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload3.out.push(`<!----></div></div>`);
          },
          $$slots: { default: true }
        });
      }
      $$payload2.out.push(`<!--]--></div>`);
    } else {
      $$payload2.out.push("<!--[!-->");
      Card($$payload2, {
        class: "flex flex-col items-center justify-center p-8",
        children: ($$payload3) => {
          $$payload3.out.push(`<div class="flex flex-col items-center justify-center gap-2"><h3 class="text-lg font-medium">No event types found</h3> <p class="text-muted-foreground text-sm">${escape_html(searchQuery ? "Try adjusting your search terms or clear the search" : "Create your first event type to start accepting bookings")}</p> `);
          Button($$payload3, {
            onclick: () => showCreateDialog = true,
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Create Appointment Type`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----></div>`);
        },
        $$slots: { default: true }
      });
    }
    $$payload2.out.push(`<!--]--></div> `);
    Create_appointment_type($$payload2, { createAppointmentTypeForm, showDialog: showCreateDialog });
    $$payload2.out.push(`<!----> `);
    Edit_appointment_type($$payload2, { selectedType, showEditDialog });
    $$payload2.out.push(`<!---->`);
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
function _page($$payload, $$props) {
  push();
  const createAppointmentTypeForm = defaults({}, valibotClient(createAppointmentTypeFormSchema));
  const deleteAppointmentTypeForm = defaults({}, valibotClient(deleteAppointmentTypeFormSchema));
  Card_shell($$payload, {
    heading: "Appointment Types",
    subHeading: "Create and manage appointment types across all locations",
    children: ($$payload2) => {
      Appointment_type_list($$payload2, { createAppointmentTypeForm, deleteAppointmentTypeForm });
    }
  });
  pop();
}
export {
  _page as default
};
