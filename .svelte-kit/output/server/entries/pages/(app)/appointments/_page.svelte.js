import "clsx";
import { G as spread_props, y as pop, w as push, N as derived, O as props_id, F as spread_attributes, P as bind_props, Q as copy_payload, R as assign_payload, I as attr_class, J as clsx, B as escape_html, E as ensure_array_like, K as attr_style, D as stringify } from "../../../../chunks/index2.js";
import "linq-extensions";
import { f as getAppointmentStore, e as getOrgStore, g as getUtilsStore } from "../../../../chunks/sheet-content.js";
import "@sveltejs/kit/internal";
import "../../../../chunks/exports.js";
import "../../../../chunks/state.svelte.js";
import { u as formatTimeWithAmPm, v as appointmentStatuses, C as CalendarViewTypes } from "../../../../chunks/auth-client.js";
import "superjson";
import "@oslojs/crypto/sha2";
import "decimal.js";
import "remult";
import { c as cn } from "../../../../chunks/shadcn.utils.js";
import { a as attachRef, d as box, w as watch, c as createBitsAttrs, m as mergeProps, b as createId } from "../../../../chunks/create-id.js";
import { I as Icon } from "../../../../chunks/states.svelte.js";
import { C as Context, D as DOMContext, u as useId, e as StateMachine, P as Presence_layer } from "../../../../chunks/scroll-lock.js";
import { i as isFunction } from "../../../../chunks/is.js";
import { today, getLocalTimeZone } from "@internationalized/date";
import { C as Calendar_1 } from "../../../../chunks/calendar-prev-button.js";
import { B as Button } from "../../../../chunks/button.js";
import { B as Badge } from "../../../../chunks/badge.js";
import { C as Chevron_left, a as Chevron_right } from "../../../../chunks/chevron-right.js";
import { L as List } from "../../../../chunks/list.js";
import { P as Plus } from "../../../../chunks/plus.js";
import "../../../../chunks/string.js";
import "@sveltejs/kit";
import "../../../../chunks/client2.js";
import "../../../../chunks/nprogress.js";
import { C as Card_shell } from "../../../../chunks/card_shell.js";
function Columns_2($$payload, $$props) {
  push();
  /**
   * @license @lucide/svelte v0.539.0 - ISC
   *
   * ISC License
   *
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   *
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   *
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   *
   */
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    [
      "rect",
      { "width": "18", "height": "18", "x": "3", "y": "3", "rx": "2" }
    ],
    ["path", { "d": "M12 3v18" }]
  ];
  Icon($$payload, spread_props([
    { name: "columns-2" },
    /**
     * @component @name Columns2
     * @description Lucide SVG icon component, renders SVG Element with children.
     *
     * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHg9IjMiIHk9IjMiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Ik0xMiAzdjE4IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/columns-2
     * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
     *
     * @param {Object} props - Lucide icons props and any valid SVG attribute
     * @returns {FunctionalComponent} Svelte component
     *
     */
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function Grid_3x3($$payload, $$props) {
  push();
  /**
   * @license @lucide/svelte v0.539.0 - ISC
   *
   * ISC License
   *
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   *
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   *
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   *
   */
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    [
      "rect",
      { "width": "18", "height": "18", "x": "3", "y": "3", "rx": "2" }
    ],
    ["path", { "d": "M3 9h18" }],
    ["path", { "d": "M3 15h18" }],
    ["path", { "d": "M9 3v18" }],
    ["path", { "d": "M15 3v18" }]
  ];
  Icon($$payload, spread_props([
    { name: "grid-3x3" },
    /**
     * @component @name Grid3x3
     * @description Lucide SVG icon component, renders SVG Element with children.
     *
     * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHg9IjMiIHk9IjMiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Ik0zIDloMTgiIC8+CiAgPHBhdGggZD0iTTMgMTVoMTgiIC8+CiAgPHBhdGggZD0iTTkgM3YxOCIgLz4KICA8cGF0aCBkPSJNMTUgM3YxOCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/grid-3x3
     * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
     *
     * @param {Object} props - Lucide icons props and any valid SVG attribute
     * @returns {FunctionalComponent} Svelte component
     *
     */
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function extract(value, defaultValue) {
  if (isFunction(value)) {
    const getter = value;
    const gotten = getter();
    if (gotten === void 0) return defaultValue;
    return gotten;
  }
  if (value === void 0) return defaultValue;
  return value;
}
function useDebounce(callback, wait) {
  let context = null;
  const wait$ = extract(wait, 250);
  function debounced(...args) {
    if (context) {
      if (context.timeout) {
        clearTimeout(context.timeout);
      }
    } else {
      let resolve;
      let reject;
      const promise = new Promise((res, rej) => {
        resolve = res;
        reject = rej;
      });
      context = { timeout: null, runner: null, promise, resolve, reject };
    }
    context.runner = async () => {
      if (!context) return;
      const ctx = context;
      context = null;
      try {
        ctx.resolve(await callback.apply(this, args));
      } catch (error) {
        ctx.reject(error);
      }
    };
    context.timeout = setTimeout(context.runner, wait$);
    return context.promise;
  }
  debounced.cancel = async () => {
    if (!context || context.timeout === null) {
      await new Promise((resolve) => setTimeout(resolve, 0));
      if (!context || context.timeout === null) return;
    }
    clearTimeout(context.timeout);
    context.reject("Cancelled");
    context = null;
  };
  debounced.runScheduledNow = async () => {
    if (!context || !context.timeout) {
      await new Promise((resolve) => setTimeout(resolve, 0));
      if (!context || !context.timeout) return;
    }
    clearTimeout(context.timeout);
    context.timeout = null;
    await context.runner?.();
  };
  Object.defineProperty(debounced, "pending", {
    enumerable: true,
    get() {
      return !!context?.timeout;
    }
  });
  return debounced;
}
class IsMounted {
  #isMounted = false;
  constructor() {
  }
  get current() {
    return this.#isMounted;
  }
}
class SvelteResizeObserver {
  #node;
  #onResize;
  constructor(node, onResize) {
    this.#node = node;
    this.#onResize = onResize;
    this.handler = this.handler.bind(this);
  }
  handler() {
    let rAF = 0;
    const _node = this.#node();
    if (!_node) return;
    const resizeObserver = new ResizeObserver(() => {
      cancelAnimationFrame(rAF);
      rAF = window.requestAnimationFrame(this.#onResize);
    });
    resizeObserver.observe(_node);
    return () => {
      window.cancelAnimationFrame(rAF);
      resizeObserver.unobserve(_node);
    };
  }
}
function clamp(n, min, max) {
  return Math.min(max, Math.max(min, n));
}
const scrollAreaAttrs = createBitsAttrs({
  component: "scroll-area",
  parts: ["root", "viewport", "corner", "thumb", "scrollbar"]
});
const ScrollAreaRootContext = new Context("ScrollArea.Root");
const ScrollAreaScrollbarContext = new Context("ScrollArea.Scrollbar");
const ScrollAreaScrollbarVisibleContext = new Context("ScrollArea.ScrollbarVisible");
const ScrollAreaScrollbarAxisContext = new Context("ScrollArea.ScrollbarAxis");
const ScrollAreaScrollbarSharedContext = new Context("ScrollArea.ScrollbarShared");
class ScrollAreaRootState {
  static create(opts) {
    return ScrollAreaRootContext.set(new ScrollAreaRootState(opts));
  }
  opts;
  attachment;
  scrollAreaNode = null;
  viewportNode = null;
  contentNode = null;
  scrollbarXNode = null;
  scrollbarYNode = null;
  cornerWidth = 0;
  cornerHeight = 0;
  scrollbarXEnabled = false;
  scrollbarYEnabled = false;
  domContext;
  constructor(opts) {
    this.opts = opts;
    this.attachment = attachRef(opts.ref, (v) => this.scrollAreaNode = v);
    this.domContext = new DOMContext(opts.ref);
  }
  #props = derived(() => ({
    id: this.opts.id.current,
    dir: this.opts.dir.current,
    style: {
      position: "relative",
      "--bits-scroll-area-corner-height": `${this.cornerHeight}px`,
      "--bits-scroll-area-corner-width": `${this.cornerWidth}px`
    },
    [scrollAreaAttrs.root]: "",
    ...this.attachment
  }));
  get props() {
    return this.#props();
  }
  set props($$value) {
    return this.#props($$value);
  }
}
class ScrollAreaViewportState {
  static create(opts) {
    return new ScrollAreaViewportState(opts, ScrollAreaRootContext.get());
  }
  opts;
  root;
  attachment;
  #contentId = box(useId());
  #contentRef = box(null);
  contentAttachment = attachRef(this.#contentRef, (v) => this.root.contentNode = v);
  constructor(opts, root) {
    this.opts = opts;
    this.root = root;
    this.attachment = attachRef(opts.ref, (v) => this.root.viewportNode = v);
  }
  #props = derived(() => ({
    id: this.opts.id.current,
    style: {
      overflowX: this.root.scrollbarXEnabled ? "scroll" : "hidden",
      overflowY: this.root.scrollbarYEnabled ? "scroll" : "hidden"
    },
    [scrollAreaAttrs.viewport]: "",
    ...this.attachment
  }));
  get props() {
    return this.#props();
  }
  set props($$value) {
    return this.#props($$value);
  }
  #contentProps = derived(() => ({
    id: this.#contentId.current,
    "data-scroll-area-content": "",
    style: {
      minWidth: this.root.scrollbarXEnabled ? "fit-content" : void 0
    },
    ...this.contentAttachment
  }));
  get contentProps() {
    return this.#contentProps();
  }
  set contentProps($$value) {
    return this.#contentProps($$value);
  }
}
class ScrollAreaScrollbarState {
  static create(opts) {
    return ScrollAreaScrollbarContext.set(new ScrollAreaScrollbarState(opts, ScrollAreaRootContext.get()));
  }
  opts;
  root;
  #isHorizontal = derived(() => this.opts.orientation.current === "horizontal");
  get isHorizontal() {
    return this.#isHorizontal();
  }
  set isHorizontal($$value) {
    return this.#isHorizontal($$value);
  }
  hasThumb = false;
  constructor(opts, root) {
    this.opts = opts;
    this.root = root;
    watch(() => this.isHorizontal, (isHorizontal) => {
      if (isHorizontal) {
        this.root.scrollbarXEnabled = true;
        return () => {
          this.root.scrollbarXEnabled = false;
        };
      } else {
        this.root.scrollbarYEnabled = true;
        return () => {
          this.root.scrollbarYEnabled = false;
        };
      }
    });
  }
}
class ScrollAreaScrollbarHoverState {
  static create() {
    return new ScrollAreaScrollbarHoverState(ScrollAreaScrollbarContext.get());
  }
  scrollbar;
  root;
  isVisible = false;
  constructor(scrollbar) {
    this.scrollbar = scrollbar;
    this.root = scrollbar.root;
  }
  #props = derived(() => ({ "data-state": this.isVisible ? "visible" : "hidden" }));
  get props() {
    return this.#props();
  }
  set props($$value) {
    return this.#props($$value);
  }
}
class ScrollAreaScrollbarScrollState {
  static create() {
    return new ScrollAreaScrollbarScrollState(ScrollAreaScrollbarContext.get());
  }
  scrollbar;
  root;
  machine = new StateMachine("hidden", {
    hidden: { SCROLL: "scrolling" },
    scrolling: { SCROLL_END: "idle", POINTER_ENTER: "interacting" },
    interacting: { SCROLL: "interacting", POINTER_LEAVE: "idle" },
    idle: {
      HIDE: "hidden",
      SCROLL: "scrolling",
      POINTER_ENTER: "interacting"
    }
  });
  #isHidden = derived(() => this.machine.state.current === "hidden");
  get isHidden() {
    return this.#isHidden();
  }
  set isHidden($$value) {
    return this.#isHidden($$value);
  }
  constructor(scrollbar) {
    this.scrollbar = scrollbar;
    this.root = scrollbar.root;
    useDebounce(() => this.machine.dispatch("SCROLL_END"), 100);
    this.onpointerenter = this.onpointerenter.bind(this);
    this.onpointerleave = this.onpointerleave.bind(this);
  }
  onpointerenter(_) {
    this.machine.dispatch("POINTER_ENTER");
  }
  onpointerleave(_) {
    this.machine.dispatch("POINTER_LEAVE");
  }
  #props = derived(() => ({
    "data-state": this.machine.state.current === "hidden" ? "hidden" : "visible",
    onpointerenter: this.onpointerenter,
    onpointerleave: this.onpointerleave
  }));
  get props() {
    return this.#props();
  }
  set props($$value) {
    return this.#props($$value);
  }
}
class ScrollAreaScrollbarAutoState {
  static create() {
    return new ScrollAreaScrollbarAutoState(ScrollAreaScrollbarContext.get());
  }
  scrollbar;
  root;
  isVisible = false;
  constructor(scrollbar) {
    this.scrollbar = scrollbar;
    this.root = scrollbar.root;
    const handleResize = useDebounce(
      () => {
        const viewportNode = this.root.viewportNode;
        if (!viewportNode) return;
        const isOverflowX = viewportNode.offsetWidth < viewportNode.scrollWidth;
        const isOverflowY = viewportNode.offsetHeight < viewportNode.scrollHeight;
        this.isVisible = this.scrollbar.isHorizontal ? isOverflowX : isOverflowY;
      },
      10
    );
    new SvelteResizeObserver(() => this.root.viewportNode, handleResize);
    new SvelteResizeObserver(() => this.root.contentNode, handleResize);
  }
  #props = derived(() => ({ "data-state": this.isVisible ? "visible" : "hidden" }));
  get props() {
    return this.#props();
  }
  set props($$value) {
    return this.#props($$value);
  }
}
class ScrollAreaScrollbarVisibleState {
  static create() {
    return ScrollAreaScrollbarVisibleContext.set(new ScrollAreaScrollbarVisibleState(ScrollAreaScrollbarContext.get()));
  }
  scrollbar;
  root;
  thumbNode = null;
  pointerOffset = 0;
  sizes = {
    content: 0,
    viewport: 0,
    scrollbar: { size: 0, paddingStart: 0, paddingEnd: 0 }
  };
  #thumbRatio = derived(() => getThumbRatio(this.sizes.viewport, this.sizes.content));
  get thumbRatio() {
    return this.#thumbRatio();
  }
  set thumbRatio($$value) {
    return this.#thumbRatio($$value);
  }
  #hasThumb = derived(() => Boolean(this.thumbRatio > 0 && this.thumbRatio < 1));
  get hasThumb() {
    return this.#hasThumb();
  }
  set hasThumb($$value) {
    return this.#hasThumb($$value);
  }
  prevTransformStyle = "";
  constructor(scrollbar) {
    this.scrollbar = scrollbar;
    this.root = scrollbar.root;
  }
  setSizes(sizes) {
    this.sizes = sizes;
  }
  getScrollPosition(pointerPos, dir) {
    return getScrollPositionFromPointer({
      pointerPos,
      pointerOffset: this.pointerOffset,
      sizes: this.sizes,
      dir
    });
  }
  onThumbPointerUp() {
    this.pointerOffset = 0;
  }
  onThumbPointerDown(pointerPos) {
    this.pointerOffset = pointerPos;
  }
  xOnThumbPositionChange() {
    if (!(this.root.viewportNode && this.thumbNode)) return;
    const scrollPos = this.root.viewportNode.scrollLeft;
    const offset = getThumbOffsetFromScroll({
      scrollPos,
      sizes: this.sizes,
      dir: this.root.opts.dir.current
    });
    const transformStyle = `translate3d(${offset}px, 0, 0)`;
    this.thumbNode.style.transform = transformStyle;
    this.prevTransformStyle = transformStyle;
  }
  xOnWheelScroll(scrollPos) {
    if (!this.root.viewportNode) return;
    this.root.viewportNode.scrollLeft = scrollPos;
  }
  xOnDragScroll(pointerPos) {
    if (!this.root.viewportNode) return;
    this.root.viewportNode.scrollLeft = this.getScrollPosition(pointerPos, this.root.opts.dir.current);
  }
  yOnThumbPositionChange() {
    if (!(this.root.viewportNode && this.thumbNode)) return;
    const scrollPos = this.root.viewportNode.scrollTop;
    const offset = getThumbOffsetFromScroll({ scrollPos, sizes: this.sizes });
    const transformStyle = `translate3d(0, ${offset}px, 0)`;
    this.thumbNode.style.transform = transformStyle;
    this.prevTransformStyle = transformStyle;
  }
  yOnWheelScroll(scrollPos) {
    if (!this.root.viewportNode) return;
    this.root.viewportNode.scrollTop = scrollPos;
  }
  yOnDragScroll(pointerPos) {
    if (!this.root.viewportNode) return;
    this.root.viewportNode.scrollTop = this.getScrollPosition(pointerPos, this.root.opts.dir.current);
  }
}
class ScrollAreaScrollbarXState {
  static create(opts) {
    return ScrollAreaScrollbarAxisContext.set(new ScrollAreaScrollbarXState(opts, ScrollAreaScrollbarVisibleContext.get()));
  }
  opts;
  scrollbarVis;
  root;
  scrollbar;
  attachment;
  computedStyle;
  constructor(opts, scrollbarVis) {
    this.opts = opts;
    this.scrollbarVis = scrollbarVis;
    this.root = scrollbarVis.root;
    this.scrollbar = scrollbarVis.scrollbar;
    this.attachment = attachRef(this.scrollbar.opts.ref, (v) => this.root.scrollbarXNode = v);
  }
  onThumbPointerDown = (pointerPos) => {
    this.scrollbarVis.onThumbPointerDown(pointerPos.x);
  };
  onDragScroll = (pointerPos) => {
    this.scrollbarVis.xOnDragScroll(pointerPos.x);
  };
  onThumbPointerUp = () => {
    this.scrollbarVis.onThumbPointerUp();
  };
  onThumbPositionChange = () => {
    this.scrollbarVis.xOnThumbPositionChange();
  };
  onWheelScroll = (e, maxScrollPos) => {
    if (!this.root.viewportNode) return;
    const scrollPos = this.root.viewportNode.scrollLeft + e.deltaX;
    this.scrollbarVis.xOnWheelScroll(scrollPos);
    if (isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos)) {
      e.preventDefault();
    }
  };
  onResize = () => {
    if (!(this.scrollbar.opts.ref.current && this.root.viewportNode && this.computedStyle)) return;
    this.scrollbarVis.setSizes({
      content: this.root.viewportNode.scrollWidth,
      viewport: this.root.viewportNode.offsetWidth,
      scrollbar: {
        size: this.scrollbar.opts.ref.current.clientWidth,
        paddingStart: toInt(this.computedStyle.paddingLeft),
        paddingEnd: toInt(this.computedStyle.paddingRight)
      }
    });
  };
  #thumbSize = derived(() => {
    return getThumbSize(this.scrollbarVis.sizes);
  });
  get thumbSize() {
    return this.#thumbSize();
  }
  set thumbSize($$value) {
    return this.#thumbSize($$value);
  }
  #props = derived(() => ({
    id: this.scrollbar.opts.id.current,
    "data-orientation": "horizontal",
    style: {
      bottom: 0,
      left: this.root.opts.dir.current === "rtl" ? "var(--bits-scroll-area-corner-width)" : 0,
      right: this.root.opts.dir.current === "ltr" ? "var(--bits-scroll-area-corner-width)" : 0,
      "--bits-scroll-area-thumb-width": `${this.thumbSize}px`
    },
    ...this.attachment
  }));
  get props() {
    return this.#props();
  }
  set props($$value) {
    return this.#props($$value);
  }
}
class ScrollAreaScrollbarYState {
  static create(opts) {
    return ScrollAreaScrollbarAxisContext.set(new ScrollAreaScrollbarYState(opts, ScrollAreaScrollbarVisibleContext.get()));
  }
  opts;
  scrollbarVis;
  root;
  scrollbar;
  attachment;
  computedStyle;
  constructor(opts, scrollbarVis) {
    this.opts = opts;
    this.scrollbarVis = scrollbarVis;
    this.root = scrollbarVis.root;
    this.scrollbar = scrollbarVis.scrollbar;
    this.attachment = attachRef(this.scrollbar.opts.ref, (v) => this.root.scrollbarYNode = v);
    this.onThumbPointerDown = this.onThumbPointerDown.bind(this);
    this.onDragScroll = this.onDragScroll.bind(this);
    this.onThumbPointerUp = this.onThumbPointerUp.bind(this);
    this.onThumbPositionChange = this.onThumbPositionChange.bind(this);
    this.onWheelScroll = this.onWheelScroll.bind(this);
    this.onResize = this.onResize.bind(this);
  }
  onThumbPointerDown(pointerPos) {
    this.scrollbarVis.onThumbPointerDown(pointerPos.y);
  }
  onDragScroll(pointerPos) {
    this.scrollbarVis.yOnDragScroll(pointerPos.y);
  }
  onThumbPointerUp() {
    this.scrollbarVis.onThumbPointerUp();
  }
  onThumbPositionChange() {
    this.scrollbarVis.yOnThumbPositionChange();
  }
  onWheelScroll(e, maxScrollPos) {
    if (!this.root.viewportNode) return;
    const scrollPos = this.root.viewportNode.scrollTop + e.deltaY;
    this.scrollbarVis.yOnWheelScroll(scrollPos);
    if (isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos)) {
      e.preventDefault();
    }
  }
  onResize() {
    if (!(this.scrollbar.opts.ref.current && this.root.viewportNode && this.computedStyle)) return;
    this.scrollbarVis.setSizes({
      content: this.root.viewportNode.scrollHeight,
      viewport: this.root.viewportNode.offsetHeight,
      scrollbar: {
        size: this.scrollbar.opts.ref.current.clientHeight,
        paddingStart: toInt(this.computedStyle.paddingTop),
        paddingEnd: toInt(this.computedStyle.paddingBottom)
      }
    });
  }
  #thumbSize = derived(() => {
    return getThumbSize(this.scrollbarVis.sizes);
  });
  get thumbSize() {
    return this.#thumbSize();
  }
  set thumbSize($$value) {
    return this.#thumbSize($$value);
  }
  #props = derived(() => ({
    id: this.scrollbar.opts.id.current,
    "data-orientation": "vertical",
    style: {
      top: 0,
      right: this.root.opts.dir.current === "ltr" ? 0 : void 0,
      left: this.root.opts.dir.current === "rtl" ? 0 : void 0,
      bottom: "var(--bits-scroll-area-corner-height)",
      "--bits-scroll-area-thumb-height": `${this.thumbSize}px`
    },
    ...this.attachment
  }));
  get props() {
    return this.#props();
  }
  set props($$value) {
    return this.#props($$value);
  }
}
class ScrollAreaScrollbarSharedState {
  static create() {
    return ScrollAreaScrollbarSharedContext.set(new ScrollAreaScrollbarSharedState(ScrollAreaScrollbarAxisContext.get()));
  }
  scrollbarState;
  root;
  scrollbarVis;
  scrollbar;
  rect = null;
  prevWebkitUserSelect = "";
  handleResize;
  handleThumbPositionChange;
  handleWheelScroll;
  handleThumbPointerDown;
  handleThumbPointerUp;
  #maxScrollPos = derived(() => this.scrollbarVis.sizes.content - this.scrollbarVis.sizes.viewport);
  get maxScrollPos() {
    return this.#maxScrollPos();
  }
  set maxScrollPos($$value) {
    return this.#maxScrollPos($$value);
  }
  constructor(scrollbarState) {
    this.scrollbarState = scrollbarState;
    this.root = scrollbarState.root;
    this.scrollbarVis = scrollbarState.scrollbarVis;
    this.scrollbar = scrollbarState.scrollbarVis.scrollbar;
    this.handleResize = useDebounce(() => this.scrollbarState.onResize(), 10);
    this.handleThumbPositionChange = this.scrollbarState.onThumbPositionChange;
    this.handleWheelScroll = this.scrollbarState.onWheelScroll;
    this.handleThumbPointerDown = this.scrollbarState.onThumbPointerDown;
    this.handleThumbPointerUp = this.scrollbarState.onThumbPointerUp;
    new SvelteResizeObserver(() => this.scrollbar.opts.ref.current, this.handleResize);
    new SvelteResizeObserver(() => this.root.contentNode, this.handleResize);
    this.onpointerdown = this.onpointerdown.bind(this);
    this.onpointermove = this.onpointermove.bind(this);
    this.onpointerup = this.onpointerup.bind(this);
  }
  handleDragScroll(e) {
    if (!this.rect) return;
    const x = e.clientX - this.rect.left;
    const y = e.clientY - this.rect.top;
    this.scrollbarState.onDragScroll({ x, y });
  }
  onpointerdown(e) {
    if (e.button !== 0) return;
    const target = e.target;
    target.setPointerCapture(e.pointerId);
    this.rect = this.scrollbar.opts.ref.current?.getBoundingClientRect() ?? null;
    this.prevWebkitUserSelect = this.root.domContext.getDocument().body.style.webkitUserSelect;
    this.root.domContext.getDocument().body.style.webkitUserSelect = "none";
    if (this.root.viewportNode) this.root.viewportNode.style.scrollBehavior = "auto";
    this.handleDragScroll(e);
  }
  onpointermove(e) {
    this.handleDragScroll(e);
  }
  onpointerup(e) {
    const target = e.target;
    if (target.hasPointerCapture(e.pointerId)) {
      target.releasePointerCapture(e.pointerId);
    }
    this.root.domContext.getDocument().body.style.webkitUserSelect = this.prevWebkitUserSelect;
    if (this.root.viewportNode) this.root.viewportNode.style.scrollBehavior = "";
    this.rect = null;
  }
  #props = derived(() => mergeProps({
    ...this.scrollbarState.props,
    style: { position: "absolute", ...this.scrollbarState.props.style },
    [scrollAreaAttrs.scrollbar]: "",
    onpointerdown: this.onpointerdown,
    onpointermove: this.onpointermove,
    onpointerup: this.onpointerup
  }));
  get props() {
    return this.#props();
  }
  set props($$value) {
    return this.#props($$value);
  }
}
class ScrollAreaThumbImplState {
  static create(opts) {
    return new ScrollAreaThumbImplState(opts, ScrollAreaScrollbarSharedContext.get());
  }
  opts;
  scrollbarState;
  attachment;
  #root;
  #removeUnlinkedScrollListener;
  #debounceScrollEnd = useDebounce(
    () => {
      if (this.#removeUnlinkedScrollListener) {
        this.#removeUnlinkedScrollListener();
        this.#removeUnlinkedScrollListener = void 0;
      }
    },
    100
  );
  constructor(opts, scrollbarState) {
    this.opts = opts;
    this.scrollbarState = scrollbarState;
    this.#root = scrollbarState.root;
    this.attachment = attachRef(this.opts.ref, (v) => this.scrollbarState.scrollbarVis.thumbNode = v);
    this.onpointerdowncapture = this.onpointerdowncapture.bind(this);
    this.onpointerup = this.onpointerup.bind(this);
  }
  onpointerdowncapture(e) {
    const thumb = e.target;
    if (!thumb) return;
    const thumbRect = thumb.getBoundingClientRect();
    const x = e.clientX - thumbRect.left;
    const y = e.clientY - thumbRect.top;
    this.scrollbarState.handleThumbPointerDown({ x, y });
  }
  onpointerup(_) {
    this.scrollbarState.handleThumbPointerUp();
  }
  #props = derived(() => ({
    id: this.opts.id.current,
    "data-state": this.scrollbarState.scrollbarVis.hasThumb ? "visible" : "hidden",
    style: {
      width: "var(--bits-scroll-area-thumb-width)",
      height: "var(--bits-scroll-area-thumb-height)",
      transform: this.scrollbarState.scrollbarVis.prevTransformStyle
    },
    onpointerdowncapture: this.onpointerdowncapture,
    onpointerup: this.onpointerup,
    [scrollAreaAttrs.thumb]: "",
    ...this.attachment
  }));
  get props() {
    return this.#props();
  }
  set props($$value) {
    return this.#props($$value);
  }
}
class ScrollAreaCornerImplState {
  static create(opts) {
    return new ScrollAreaCornerImplState(opts, ScrollAreaRootContext.get());
  }
  opts;
  root;
  attachment;
  #width = 0;
  #height = 0;
  #hasSize = derived(() => Boolean(this.#width && this.#height));
  get hasSize() {
    return this.#hasSize();
  }
  set hasSize($$value) {
    return this.#hasSize($$value);
  }
  constructor(opts, root) {
    this.opts = opts;
    this.root = root;
    this.attachment = attachRef(this.opts.ref);
    new SvelteResizeObserver(() => this.root.scrollbarXNode, () => {
      const height = this.root.scrollbarXNode?.offsetHeight || 0;
      this.root.cornerHeight = height;
      this.#height = height;
    });
    new SvelteResizeObserver(() => this.root.scrollbarYNode, () => {
      const width = this.root.scrollbarYNode?.offsetWidth || 0;
      this.root.cornerWidth = width;
      this.#width = width;
    });
  }
  #props = derived(() => ({
    id: this.opts.id.current,
    style: {
      width: this.#width,
      height: this.#height,
      position: "absolute",
      right: this.root.opts.dir.current === "ltr" ? 0 : void 0,
      left: this.root.opts.dir.current === "rtl" ? 0 : void 0,
      bottom: 0
    },
    [scrollAreaAttrs.corner]: "",
    ...this.attachment
  }));
  get props() {
    return this.#props();
  }
  set props($$value) {
    return this.#props($$value);
  }
}
function toInt(value) {
  return value ? Number.parseInt(value, 10) : 0;
}
function getThumbRatio(viewportSize, contentSize) {
  const ratio = viewportSize / contentSize;
  return Number.isNaN(ratio) ? 0 : ratio;
}
function getThumbSize(sizes) {
  const ratio = getThumbRatio(sizes.viewport, sizes.content);
  const scrollbarPadding = sizes.scrollbar.paddingStart + sizes.scrollbar.paddingEnd;
  const thumbSize = (sizes.scrollbar.size - scrollbarPadding) * ratio;
  return Math.max(thumbSize, 18);
}
function getScrollPositionFromPointer({ pointerPos, pointerOffset, sizes, dir = "ltr" }) {
  const thumbSizePx = getThumbSize(sizes);
  const thumbCenter = thumbSizePx / 2;
  const offset = pointerOffset || thumbCenter;
  const thumbOffsetFromEnd = thumbSizePx - offset;
  const minPointerPos = sizes.scrollbar.paddingStart + offset;
  const maxPointerPos = sizes.scrollbar.size - sizes.scrollbar.paddingEnd - thumbOffsetFromEnd;
  const maxScrollPos = sizes.content - sizes.viewport;
  const scrollRange = dir === "ltr" ? [0, maxScrollPos] : [maxScrollPos * -1, 0];
  const interpolate = linearScale([minPointerPos, maxPointerPos], scrollRange);
  return interpolate(pointerPos);
}
function getThumbOffsetFromScroll({ scrollPos, sizes, dir = "ltr" }) {
  const thumbSizePx = getThumbSize(sizes);
  const scrollbarPadding = sizes.scrollbar.paddingStart + sizes.scrollbar.paddingEnd;
  const scrollbar = sizes.scrollbar.size - scrollbarPadding;
  const maxScrollPos = sizes.content - sizes.viewport;
  const maxThumbPos = scrollbar - thumbSizePx;
  const scrollClampRange = dir === "ltr" ? [0, maxScrollPos] : [maxScrollPos * -1, 0];
  const scrollWithoutMomentum = clamp(scrollPos, scrollClampRange[0], scrollClampRange[1]);
  const interpolate = linearScale([0, maxScrollPos], [0, maxThumbPos]);
  return interpolate(scrollWithoutMomentum);
}
function linearScale(input, output) {
  return (value) => {
    if (input[0] === input[1] || output[0] === output[1]) return output[0];
    const ratio = (output[1] - output[0]) / (input[1] - input[0]);
    return output[0] + ratio * (value - input[0]);
  };
}
function isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos) {
  return scrollPos > 0 && scrollPos < maxScrollPos;
}
function Scroll_area$1($$payload, $$props) {
  push();
  const uid = props_id($$payload);
  let {
    ref = null,
    id = createId(uid),
    type = "hover",
    dir = "ltr",
    scrollHideDelay = 600,
    children,
    child,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const rootState = ScrollAreaRootState.create({
    type: box.with(() => type),
    dir: box.with(() => dir),
    scrollHideDelay: box.with(() => scrollHideDelay),
    id: box.with(() => id),
    ref: box.with(() => ref, (v) => ref = v)
  });
  const mergedProps = mergeProps(restProps, rootState.props);
  if (child) {
    $$payload.out.push("<!--[-->");
    child($$payload, { props: mergedProps });
    $$payload.out.push(`<!---->`);
  } else {
    $$payload.out.push("<!--[!-->");
    $$payload.out.push(`<div${spread_attributes({ ...mergedProps }, null)}>`);
    children?.($$payload);
    $$payload.out.push(`<!----></div>`);
  }
  $$payload.out.push(`<!--]-->`);
  bind_props($$props, { ref });
  pop();
}
function Scroll_area_viewport($$payload, $$props) {
  push();
  const uid = props_id($$payload);
  let {
    ref = null,
    id = createId(uid),
    children,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const viewportState = ScrollAreaViewportState.create({
    id: box.with(() => id),
    ref: box.with(() => ref, (v) => ref = v)
  });
  const mergedProps = mergeProps(restProps, viewportState.props);
  const mergedContentProps = mergeProps({}, viewportState.contentProps);
  $$payload.out.push(`<div${spread_attributes({ ...mergedProps }, null)}><div${spread_attributes({ ...mergedContentProps }, null)}>`);
  children?.($$payload);
  $$payload.out.push(`<!----></div></div>`);
  bind_props($$props, { ref });
  pop();
}
function Scroll_area_scrollbar_shared($$payload, $$props) {
  push();
  let { child, children, $$slots, $$events, ...restProps } = $$props;
  const scrollbarSharedState = ScrollAreaScrollbarSharedState.create();
  const mergedProps = mergeProps(restProps, scrollbarSharedState.props);
  if (child) {
    $$payload.out.push("<!--[-->");
    child($$payload, { props: mergedProps });
    $$payload.out.push(`<!---->`);
  } else {
    $$payload.out.push("<!--[!-->");
    $$payload.out.push(`<div${spread_attributes({ ...mergedProps }, null)}>`);
    children?.($$payload);
    $$payload.out.push(`<!----></div>`);
  }
  $$payload.out.push(`<!--]-->`);
  pop();
}
function Scroll_area_scrollbar_x($$payload, $$props) {
  push();
  let { $$slots, $$events, ...restProps } = $$props;
  const isMounted = new IsMounted();
  const scrollbarXState = ScrollAreaScrollbarXState.create({ mounted: box.with(() => isMounted.current) });
  const mergedProps = mergeProps(restProps, scrollbarXState.props);
  Scroll_area_scrollbar_shared($$payload, spread_props([mergedProps]));
  pop();
}
function Scroll_area_scrollbar_y($$payload, $$props) {
  push();
  let { $$slots, $$events, ...restProps } = $$props;
  const isMounted = new IsMounted();
  const scrollbarYState = ScrollAreaScrollbarYState.create({ mounted: box.with(() => isMounted.current) });
  const mergedProps = mergeProps(restProps, scrollbarYState.props);
  Scroll_area_scrollbar_shared($$payload, spread_props([mergedProps]));
  pop();
}
function Scroll_area_scrollbar_visible($$payload, $$props) {
  push();
  let { $$slots, $$events, ...restProps } = $$props;
  const scrollbarVisibleState = ScrollAreaScrollbarVisibleState.create();
  if (scrollbarVisibleState.scrollbar.opts.orientation.current === "horizontal") {
    $$payload.out.push("<!--[-->");
    Scroll_area_scrollbar_x($$payload, spread_props([restProps]));
  } else {
    $$payload.out.push("<!--[!-->");
    Scroll_area_scrollbar_y($$payload, spread_props([restProps]));
  }
  $$payload.out.push(`<!--]-->`);
  pop();
}
function Scroll_area_scrollbar_auto($$payload, $$props) {
  push();
  let { forceMount = false, $$slots, $$events, ...restProps } = $$props;
  const scrollbarAutoState = ScrollAreaScrollbarAutoState.create();
  const mergedProps = mergeProps(restProps, scrollbarAutoState.props);
  {
    let presence = function($$payload2) {
      Scroll_area_scrollbar_visible($$payload2, spread_props([mergedProps]));
    };
    Presence_layer($$payload, {
      open: forceMount || scrollbarAutoState.isVisible,
      ref: scrollbarAutoState.scrollbar.opts.ref,
      presence
    });
  }
  pop();
}
function Scroll_area_scrollbar_scroll($$payload, $$props) {
  push();
  let { forceMount = false, $$slots, $$events, ...restProps } = $$props;
  const scrollbarScrollState = ScrollAreaScrollbarScrollState.create();
  const mergedProps = mergeProps(restProps, scrollbarScrollState.props);
  {
    let presence = function($$payload2) {
      Scroll_area_scrollbar_visible($$payload2, spread_props([mergedProps]));
    };
    Presence_layer($$payload, spread_props([
      mergedProps,
      {
        open: forceMount || !scrollbarScrollState.isHidden,
        ref: scrollbarScrollState.scrollbar.opts.ref,
        presence,
        $$slots: { presence: true }
      }
    ]));
  }
  pop();
}
function Scroll_area_scrollbar_hover($$payload, $$props) {
  push();
  let { forceMount = false, $$slots, $$events, ...restProps } = $$props;
  const scrollbarHoverState = ScrollAreaScrollbarHoverState.create();
  const scrollbarAutoState = ScrollAreaScrollbarAutoState.create();
  const mergedProps = mergeProps(restProps, scrollbarHoverState.props, scrollbarAutoState.props, {
    "data-state": scrollbarHoverState.isVisible ? "visible" : "hidden"
  });
  const open = forceMount || scrollbarHoverState.isVisible && scrollbarAutoState.isVisible;
  {
    let presence = function($$payload2) {
      Scroll_area_scrollbar_visible($$payload2, spread_props([mergedProps]));
    };
    Presence_layer($$payload, {
      open,
      ref: scrollbarAutoState.scrollbar.opts.ref,
      presence
    });
  }
  pop();
}
function Scroll_area_scrollbar$1($$payload, $$props) {
  push();
  const uid = props_id($$payload);
  let {
    ref = null,
    id = createId(uid),
    orientation,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const scrollbarState = ScrollAreaScrollbarState.create({
    orientation: box.with(() => orientation),
    id: box.with(() => id),
    ref: box.with(() => ref, (v) => ref = v)
  });
  const type = scrollbarState.root.opts.type.current;
  if (type === "hover") {
    $$payload.out.push("<!--[-->");
    Scroll_area_scrollbar_hover($$payload, spread_props([restProps, { id }]));
  } else {
    $$payload.out.push("<!--[!-->");
    if (type === "scroll") {
      $$payload.out.push("<!--[-->");
      Scroll_area_scrollbar_scroll($$payload, spread_props([restProps, { id }]));
    } else {
      $$payload.out.push("<!--[!-->");
      if (type === "auto") {
        $$payload.out.push("<!--[-->");
        Scroll_area_scrollbar_auto($$payload, spread_props([restProps, { id }]));
      } else {
        $$payload.out.push("<!--[!-->");
        if (type === "always") {
          $$payload.out.push("<!--[-->");
          Scroll_area_scrollbar_visible($$payload, spread_props([restProps, { id }]));
        } else {
          $$payload.out.push("<!--[!-->");
        }
        $$payload.out.push(`<!--]-->`);
      }
      $$payload.out.push(`<!--]-->`);
    }
    $$payload.out.push(`<!--]-->`);
  }
  $$payload.out.push(`<!--]-->`);
  bind_props($$props, { ref });
  pop();
}
function Scroll_area_thumb_impl($$payload, $$props) {
  push();
  let {
    ref = null,
    id,
    child,
    children,
    present,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const isMounted = new IsMounted();
  const thumbState = ScrollAreaThumbImplState.create({
    id: box.with(() => id),
    ref: box.with(() => ref, (v) => ref = v),
    mounted: box.with(() => isMounted.current)
  });
  const mergedProps = mergeProps(restProps, thumbState.props, { style: { hidden: !present } });
  if (child) {
    $$payload.out.push("<!--[-->");
    child($$payload, { props: mergedProps });
    $$payload.out.push(`<!---->`);
  } else {
    $$payload.out.push("<!--[!-->");
    $$payload.out.push(`<div${spread_attributes({ ...mergedProps }, null)}>`);
    children?.($$payload);
    $$payload.out.push(`<!----></div>`);
  }
  $$payload.out.push(`<!--]-->`);
  bind_props($$props, { ref });
  pop();
}
function Scroll_area_thumb($$payload, $$props) {
  push();
  const uid = props_id($$payload);
  let {
    id = createId(uid),
    ref = null,
    forceMount = false,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const scrollbarState = ScrollAreaScrollbarVisibleContext.get();
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    {
      let presence = function($$payload3, { present }) {
        Scroll_area_thumb_impl($$payload3, spread_props([
          restProps,
          {
            id,
            present,
            get ref() {
              return ref;
            },
            set ref($$value) {
              ref = $$value;
              $$settled = false;
            }
          }
        ]));
      };
      Presence_layer($$payload2, {
        open: forceMount || scrollbarState.hasThumb,
        ref: scrollbarState.scrollbar.opts.ref,
        presence
      });
    }
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref });
  pop();
}
function Scroll_area_corner_impl($$payload, $$props) {
  push();
  let {
    ref = null,
    id,
    children,
    child,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const cornerState = ScrollAreaCornerImplState.create({
    id: box.with(() => id),
    ref: box.with(() => ref, (v) => ref = v)
  });
  const mergedProps = mergeProps(restProps, cornerState.props);
  if (child) {
    $$payload.out.push("<!--[-->");
    child($$payload, { props: mergedProps });
    $$payload.out.push(`<!---->`);
  } else {
    $$payload.out.push("<!--[!-->");
    $$payload.out.push(`<div${spread_attributes({ ...mergedProps }, null)}>`);
    children?.($$payload);
    $$payload.out.push(`<!----></div>`);
  }
  $$payload.out.push(`<!--]-->`);
  bind_props($$props, { ref });
  pop();
}
function Scroll_area_corner($$payload, $$props) {
  push();
  const uid = props_id($$payload);
  let {
    ref = null,
    id = createId(uid),
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const scrollAreaState = ScrollAreaRootContext.get();
  const hasBothScrollbarsVisible = Boolean(scrollAreaState.scrollbarXNode && scrollAreaState.scrollbarYNode);
  const hasCorner = scrollAreaState.opts.type.current !== "scroll" && hasBothScrollbarsVisible;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    if (hasCorner) {
      $$payload2.out.push("<!--[-->");
      Scroll_area_corner_impl($$payload2, spread_props([
        restProps,
        {
          id,
          get ref() {
            return ref;
          },
          set ref($$value) {
            ref = $$value;
            $$settled = false;
          }
        }
      ]));
    } else {
      $$payload2.out.push("<!--[!-->");
    }
    $$payload2.out.push(`<!--]-->`);
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref });
  pop();
}
function Button_group($$payload, $$props) {
  push();
  let { class: className = "", orientation = "horizontal", children } = $$props;
  const isVertical = orientation === "vertical";
  $$payload.out.push(`<div${attr_class(clsx(
    // Track number of children for first/last calculations
    //	let childArray = $derived(Array.isArray(children) ? children : [children])
    //let childCount = $derived(childArray.length)
    cn("flex", { "flex-col": isVertical, "w-fit": isVertical }, className)
  ))}>`);
  children($$payload);
  $$payload.out.push(`<!----></div>`);
  pop();
}
function Scroll_area_scrollbar($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    orientation = "vertical",
    children,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out.push(`<!---->`);
    Scroll_area_scrollbar$1($$payload2, spread_props([
      {
        "data-slot": "scroll-area-scrollbar",
        orientation,
        class: cn("flex touch-none select-none p-px transition-colors", orientation === "vertical" && "h-full w-2.5 border-l border-l-transparent", orientation === "horizontal" && "h-2.5 flex-col border-t border-t-transparent", className)
      },
      restProps,
      {
        get ref() {
          return ref;
        },
        set ref($$value) {
          ref = $$value;
          $$settled = false;
        },
        children: ($$payload3) => {
          children?.($$payload3);
          $$payload3.out.push(`<!----> <!---->`);
          Scroll_area_thumb($$payload3, {
            "data-slot": "scroll-area-thumb",
            class: "bg-border relative flex-1 rounded-full"
          });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      }
    ]));
    $$payload2.out.push(`<!---->`);
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref });
  pop();
}
function Scroll_area($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    orientation = "vertical",
    scrollbarXClasses = "",
    scrollbarYClasses = "",
    children,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out.push(`<!---->`);
    Scroll_area$1($$payload2, spread_props([
      { "data-slot": "scroll-area", class: cn("relative", className) },
      restProps,
      {
        get ref() {
          return ref;
        },
        set ref($$value) {
          ref = $$value;
          $$settled = false;
        },
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Scroll_area_viewport($$payload3, {
            "data-slot": "scroll-area-viewport",
            class: "ring-ring/10 dark:ring-ring/20 dark:outline-ring/40 outline-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] focus-visible:outline-1 focus-visible:ring-4",
            children: ($$payload4) => {
              children?.($$payload4);
              $$payload4.out.push(`<!---->`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> `);
          if (orientation === "vertical" || orientation === "both") {
            $$payload3.out.push("<!--[-->");
            Scroll_area_scrollbar($$payload3, { orientation: "vertical", class: scrollbarYClasses });
          } else {
            $$payload3.out.push("<!--[!-->");
          }
          $$payload3.out.push(`<!--]--> `);
          if (orientation === "horizontal" || orientation === "both") {
            $$payload3.out.push("<!--[-->");
            Scroll_area_scrollbar($$payload3, { orientation: "horizontal", class: scrollbarXClasses });
          } else {
            $$payload3.out.push("<!--[!-->");
          }
          $$payload3.out.push(`<!--]--> <!---->`);
          Scroll_area_corner($$payload3, {});
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      }
    ]));
    $$payload2.out.push(`<!---->`);
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref });
  pop();
}
function Pick_calendar($$payload, $$props) {
  push();
  let value = today(getLocalTimeZone());
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    Calendar_1($$payload2, {
      type: "single",
      class: "rounded-md border",
      get value() {
        return value;
      },
      set value($$value) {
        value = $$value;
        $$settled = false;
      }
    });
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  pop();
}
function Calendar_day_body($$payload, $$props) {
  push();
  const appointmentColors = {
    Scheduled: "bg-indigo-50   rounded-d border px-2 py-1.5 text-xs focus-visible:outline-offset-2 border-indigo-200  text-indigo-700 dark:border-indigo-800 dark:bg-indigo-950 dark:text-indigo-300",
    Confirmed: "bg-emerald-50   rounded-d border px-2 py-1.5 text-xs focus-visible:outline-offset-2 border-emerald-200  text-emerald-700 dark:border-emerald-800 dark:bg-emerald-950 dark:text-emerald-300",
    Missed: "bg-amber-50   rounded-d border px-2 py-1.5 text-xs focus-visible:outline-offset-2 border-amber-200  text-amber-700 dark:border-amber-800 dark:bg-amber-950 dark:text-amber-300",
    Done: "bg-rose-50   rounded-d border px-2 py-1.5 text-xs focus-visible:outline-offset-2 border-rose-200  text-rose-700 dark:border-rose-800 dark:bg-rose-950 dark:text-rose-300",
    Cancelled: "bg-red-50   rounded-d border px-2 py-1.5 text-xs focus-visible:outline-offset-2 border-red-200  text-red-700 dark:border-red-800 dark:bg-red-950 dark:text-red-300"
  };
  let aStore = getAppointmentStore();
  let orgStore = getOrgStore();
  let currentDayOfWeek = (() => (/* @__PURE__ */ new Date()).toLocaleString("default", { weekday: "short" }))();
  let appointmentPositions = (() => {
    const startTime = aStore.workdaySetting?.startTime ?? "09:00";
    const [dayStartHour] = startTime.toString().split(":").map(Number);
    return aStore.currentDayAppointments?.map((appointment) => {
      const [startHour, startMinute] = appointment.startTime.toString().split(":").map(Number);
      const [endHour, endMinute] = appointment.endTime.toString().split(":").map(Number);
      const startOffset = (startHour - dayStartHour) * 96 + startMinute / 60 * 96;
      const duration = (endHour - startHour) * 96 + (endMinute - startMinute) / 60 * 96;
      const formattedStartTime = formatTimeWithAmPm(appointment.startTime.toString());
      const formattedEndTime = formatTimeWithAmPm(appointment.endTime.toString());
      const colorClass = appointmentColors[appointment.appointmentStatus];
      const template = aStore.appointmentTemplates.firstOrNull((k) => k.id === appointment.appointmentTypeId);
      const location = orgStore.teams.firstOrNull(({ id }) => id === appointment.teamId);
      return {
        ...appointment,
        appointmentTemplate: template,
        location,
        startTime: formattedStartTime,
        endTime: formattedEndTime,
        style: `top: ${startOffset}px; width: 99%; left:0;`,
        innerStyle: `height: ${duration - 6}px`,
        colorClass
      };
    });
  })();
  let currentDayOfMonth = (/* @__PURE__ */ new Date()).getDate().toString().padStart(2, "0");
  let currentTimeFormatted = (/* @__PURE__ */ new Date()).toLocaleString("en-US", { hour: "numeric", minute: "2-digit", hour12: true }).toLowerCase();
  let currentTimePosition = (() => {
    const now = /* @__PURE__ */ new Date();
    const startTime = aStore.workdaySetting?.startTime ?? "09:00";
    const [startHour] = startTime.toString().split(":").map(Number);
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();
    const hoursFromStart = currentHour - startHour;
    const minutePercentage = currentMinute / 60;
    const pixelsFromTop = (hoursFromStart + minutePercentage) * 96;
    return `top: ${pixelsFromTop}px`;
  })();
  let formattedTimeIntervals = (() => {
    const startTime = aStore.workdaySetting?.startTime ?? "09:00";
    const endTime = aStore.workdaySetting?.endTime ?? "17:00";
    const [startHour, startMinute] = startTime.toString().split(":").map(Number);
    const roundedStartHour = startMinute >= 30 ? startHour + 1 : startHour;
    const [endHour] = endTime.toString().split(":").map(Number);
    const timeSlots = [];
    for (let hour = roundedStartHour; hour <= endHour; hour++) {
      const date = /* @__PURE__ */ new Date();
      date.setHours(hour, 0, 0);
      timeSlots.push(date.toLocaleString("en-US", { hour: "numeric", minute: "2-digit", hour12: true }).toLowerCase());
    }
    return timeSlots;
  })();
  let showCurrentTime = (() => {
    const now = /* @__PURE__ */ new Date();
    const currentHour = now.getHours();
    const startTime = aStore.workdaySetting?.startTime ?? "09:00";
    const endTime = aStore.workdaySetting?.endTime ?? "17:00";
    const [startHour] = startTime.toString().split(":").map(Number);
    const [endHour] = endTime.toString().split(":").map(Number);
    return currentHour >= startHour && currentHour <= endHour;
  })();
  $$payload.out.push(`<div class="flex border-b lg:border-b-0"><div class="flex flex-1 flex-col"><div><div class="shadow-calendar dark:shadow-calendar-dark relative z-20 flex border-b"><div class="w-18"></div> <div class="flex-1 border-l py-2 text-center"><span class="text-muted-foreground text-xs font-medium">${escape_html(currentDayOfWeek)} <span class="text-t-secondary font-semibold">${escape_html(currentDayOfMonth)}</span></span></div></div></div> `);
  Scroll_area($$payload, {
    class: "!scrollbar-hide relative h-[608px]",
    orientation: "vertical",
    children: ($$payload2) => {
      const each_array = ensure_array_like(formattedTimeIntervals);
      const each_array_1 = ensure_array_like(formattedTimeIntervals);
      const each_array_2 = ensure_array_like(appointmentPositions);
      $$payload2.out.push(`<div class="flex"><div class="relative w-38"><!--[-->`);
      for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
        let timeSlot = each_array[$$index];
        $$payload2.out.push(`<div class="relative h-24 border-t"><div class="-top-3 right-2 flex h-6 items-center px-10"><span class="text-muted-foreground text-xs">${escape_html(timeSlot)}</span></div></div>`);
      }
      $$payload2.out.push(`<!--]--></div> <div class="relative flex-1 border-l">`);
      if (showCurrentTime) {
        $$payload2.out.push("<!--[-->");
        $$payload2.out.push(`<div class="border-primary-600 dark:border-primary-700 pointer-events-none absolute inset-x-0 z-50 border-t"${attr_style(currentTimePosition)}><div class="absolute -top-1.5 -left-1.5 size-3 rounded-full bg-indigo-600 dark:bg-indigo-700"></div> <div class="bg-bg-primary text-primary-600 dark:text-primary-700 absolute -left-18 flex w-16 -translate-y-1/2 justify-end pr-1 text-xs font-medium">${escape_html(currentTimeFormatted)}</div></div>`);
      } else {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]--> <div class="relative"><!--[-->`);
      for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
        each_array_1[$$index_1];
        $$payload2.out.push(`<div class="relative" style="height:96px;"><div class="absolute inset-x-0 top-0 border-b"></div> <div class="border-b-tertiary absolute inset-x-0 top-1/2 border-b border-dashed"></div></div>`);
      }
      $$payload2.out.push(`<!--]--> <!--[-->`);
      for (let $$index_2 = 0, $$length = each_array_2.length; $$index_2 < $$length; $$index_2++) {
        let appointment = each_array_2[$$index_2];
        $$payload2.out.push(`<div class="absolute p-1"${attr_style(appointment.style)}><div role="button" tabindex="0"${attr_class(`flex flex-col gap-0.5 truncate whitespace-nowrap select-none ${stringify(appointment.colorClass)}`)}${attr_style(appointment.innerStyle)}><div class="flex items-center justify-between"><span class="truncate font-semibold">${escape_html(appointment.appointmentTemplate?.appointmentDetail)}</span> `);
        if (appointment.appointmentStatus === appointmentStatuses.Confirmed) {
          $$payload2.out.push("<!--[-->");
          $$payload2.out.push(`<span class="bg-primary-200/50 rounded-d px-2 py-0.5 text-xs">Confirmed</span>`);
        } else {
          $$payload2.out.push("<!--[!-->");
        }
        $$payload2.out.push(`<!--]--></div> <div class="flex items-center justify-between"><span class="text-xs opacity-75">${escape_html(appointment.startTime)} - ${escape_html(appointment.endTime)}</span> <span class="text-xs opacity-75">${escape_html(appointment.customerName)}</span></div> <span class="text-xs opacity-75">${escape_html(appointment.location?.name)}</span></div></div>`);
      }
      $$payload2.out.push(`<!--]--></div></div></div>`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!----></div> <div class="hidden w-72 divide-y border-l lg:block">`);
  Pick_calendar($$payload);
  $$payload.out.push(`<!----></div></div>`);
  pop();
}
function Calendar_week_body($$payload) {
  $$payload.out.push(`<p>Week calendar</p>`);
}
function Calendar_month_body($$payload) {
  $$payload.out.push(`<p>Month calendar</p>`);
}
function Calendar_main($$payload, $$props) {
  push();
  let aStore = getAppointmentStore();
  $$payload.out.push(`<div>`);
  if (aStore.currentView === CalendarViewTypes.Month) {
    $$payload.out.push("<!--[-->");
    Calendar_month_body($$payload);
  } else {
    $$payload.out.push("<!--[!-->");
    if (aStore.currentView === CalendarViewTypes.Week) {
      $$payload.out.push("<!--[-->");
      Calendar_week_body($$payload);
    } else {
      $$payload.out.push("<!--[!-->");
      Calendar_day_body($$payload);
    }
    $$payload.out.push(`<!--]-->`);
  }
  $$payload.out.push(`<!--]--></div>`);
  pop();
}
function Calendar_header_date($$payload, $$props) {
  push();
  let currentMonth = (/* @__PURE__ */ new Date()).toLocaleString("default", { month: "short" });
  let currentDayOfMonth = (/* @__PURE__ */ new Date()).getDate();
  let currentMonthAndYear = (/* @__PURE__ */ new Date()).toLocaleString("default", { month: "long", year: "numeric" });
  let currentAppointmentsCount = (() => {
    const eventCount = Math.floor(Math.random() * 10);
    return `${eventCount} ${eventCount === 1 ? "Event" : "Events"}`;
  })();
  let currentNavigationDate = (() => {
    return (/* @__PURE__ */ new Date()).toLocaleString("default", { day: "2-digit", month: "long", year: "numeric" });
  })();
  const nextDate = () => {
  };
  const previousDate = () => {
  };
  $$payload.out.push(`<div class="flex-center flex gap-3"><button class="rounded-logo mt-2 flex size-14 flex-col items-start overflow-hidden border"><p class="flex h-6 w-full items-center justify-center bg-indigo-600 text-center text-xs font-semibold text-white">${escape_html(currentMonth)}</p> <p class="flex w-full items-center justify-center text-lg font-bold">${escape_html(currentDayOfMonth)}</p></button> <div class="space-y-0.5"><div class="flex items-center gap-2"><span class="text-lg font-semibold">${escape_html(currentMonthAndYear)}</span> `);
  Badge($$payload, {
    variant: "outline",
    class: "whitespace-nowrap",
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->${escape_html(currentAppointmentsCount)}`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!----></div> <div class="flex items-center gap-2">`);
  Button($$payload, {
    variant: "outline",
    size: "icon",
    class: "rounded-logo size-7",
    onclick: previousDate,
    children: ($$payload2) => {
      Chevron_left($$payload2, {});
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!----> <p class="text-muted-foreground text-sm">${escape_html(currentNavigationDate)}</p> `);
  Button($$payload, {
    variant: "outline",
    size: "sm",
    class: "rounded-d size-7",
    onclick: nextDate,
    children: ($$payload2) => {
      Chevron_right($$payload2, {});
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!----></div></div></div>`);
  pop();
}
function Calendar_header_menu($$payload, $$props) {
  push();
  let { showAddAppointment = true } = $$props;
  let appointmentStore = getAppointmentStore();
  $$payload.out.push(`<div class="flex items-center justify-between gap-3"><div class="inline-flex">`);
  Button_group($$payload, {
    children: ($$payload2) => {
      Button($$payload2, {
        variant: "outline",
        class: "rounded-r-none",
        onclick: () => appointmentStore.setCurrentView(CalendarViewTypes.Day),
        children: ($$payload3) => {
          List($$payload3, { class: "mr-2" });
          $$payload3.out.push(`<!----> <span class="hidden xl:block">Day</span>`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!----> `);
      Button($$payload2, {
        variant: "outline",
        class: "hidden rounded-l-none rounded-r-none border-l-0 lg:inline-flex",
        onclick: () => appointmentStore.setCurrentView(CalendarViewTypes.Week),
        children: ($$payload3) => {
          Columns_2($$payload3, { class: "xl:mr-2" });
          $$payload3.out.push(`<!----> <span class="hidden xl:block">Week</span>`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!----> `);
      Button($$payload2, {
        variant: "outline",
        class: "rounded-l-none border-l-0",
        onclick: () => appointmentStore.setCurrentView(CalendarViewTypes.Month),
        children: ($$payload3) => {
          Grid_3x3($$payload3, { class: "mr-2" });
          $$payload3.out.push(`<!----> <span class="hidden xl:block">Month</span>`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    }
  });
  $$payload.out.push(`<!----></div> `);
  if (showAddAppointment) {
    $$payload.out.push("<!--[-->");
    Button($$payload, {
      class: "transition-colors",
      children: ($$payload2) => {
        Plus($$payload2, { class: "mr-2 size-4" });
        $$payload2.out.push(`<!----> <span>Create Appointment</span>`);
      },
      $$slots: { default: true }
    });
    $$payload.out.push(`<!---->variant`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--></div>`);
  pop();
}
function Calendar_header($$payload, $$props) {
  let { showAddAppointment } = $$props;
  $$payload.out.push(`<div class="flex flex-col gap-4 border-b p-4 md:flex-row md:items-center md:justify-between">`);
  Calendar_header_date($$payload);
  $$payload.out.push(`<!----> `);
  Calendar_header_menu($$payload, { showAddAppointment });
  $$payload.out.push(`<!----></div>`);
}
function Calendar($$payload, $$props) {
  let { showAddAppointment } = $$props;
  $$payload.out.push(`<div class="h-fit w-full border lg:rounded-xl">`);
  Calendar_header($$payload, { showAddAppointment });
  $$payload.out.push(`<!----> `);
  Calendar_main($$payload);
  $$payload.out.push(`<!----></div>`);
}
function _page($$payload, $$props) {
  push();
  getUtilsStore();
  getAppointmentStore();
  Card_shell($$payload, {
    heading: "Appointment Dashboard",
    subHeading: "View and manage appointments across all locations",
    children: ($$payload2) => {
      Calendar($$payload2, { showAddAppointment: false });
    }
  });
  pop();
}
export {
  _page as default
};
