import "clsx";
import { G as spread_props, y as pop, w as push, E as ensure_array_like, I as attr_class, z as attr, J as clsx, K as attr_style, B as escape_html, F as spread_attributes, M as run, D as stringify, N as derived, O as props_id, P as bind_props, Q as copy_payload, R as assign_payload, S as getContext, A as head, T as store_get, U as unsubscribe_stores, V as store_mutate } from "../../../chunks/index2.js";
import "../../../chunks/client2.js";
import { t as toastState, c as cn, S as SonnerState, g as getCopyRight, a as SheetTypes, l as loginFormSchema, b as authClient, d as toast, s as signUpFormSchema, U as UserAccountTypes, R as RolesType, e as saveEmailLocally, f as signUpCodeFormSchema, h as setupPasskey, j as isUserInRole, k as getInitials, m as logOutFormSchema, T as TeamPlans } from "../../../chunks/auth-client.js";
import "../../../chunks/index4.js";
import { C as Context } from "../../../chunks/context.js";
import { I as Icon, e as derivedMode } from "../../../chunks/states.svelte.js";
import "linq-extensions";
import "@sveltejs/kit/internal";
import "../../../chunks/exports.js";
import "../../../chunks/state.svelte.js";
import "../../../chunks/nprogress.js";
import "remult";
import { g as getUtilsStore, d as getAuthStore, S as Sheet_content, e as getOrgStore } from "../../../chunks/sheet-content.js";
import { D as Dialog_title, a as Dialog_description, b as Dialog, S as Shopping_bag, g as getFlashModule, c as generateRandomPassword, m as menuData } from "../../../chunks/dialog.js";
import "superjson";
import { B as Button } from "../../../chunks/button.js";
import { t as toggleMode } from "../../../chunks/mode.js";
import { c as cn$1 } from "../../../chunks/shadcn.utils.js";
import "@oslojs/crypto/sha2";
import "decimal.js";
import { C as Credit_card } from "../../../chunks/credit-card.js";
import { G as Gift } from "../../../chunks/gift.js";
import { C as Clock } from "../../../chunks/clock.js";
import { M as Mail } from "../../../chunks/mail.js";
import { U as Users } from "../../../chunks/users.js";
import { I as Input } from "../../../chunks/input.js";
import { F as Form_alerts, a as Form_field, C as Control, b as Form_field_errors } from "../../../chunks/index5.js";
import { T as Tabs, a as Tabs_list, b as Tabs_trigger } from "../../../chunks/tabs-trigger.js";
import { s as superForm } from "../../../chunks/string.js";
import { v as valibot, d as defaults, a as valibotClient } from "../../../chunks/valibot.js";
import { fail } from "@sveltejs/kit";
import { H as Hidden_input } from "../../../chunks/hidden-input.js";
import { C as Chevron_left, a as Chevron_right } from "../../../chunks/chevron-right.js";
import { F as Form_label, a as Form_button, L as Loader_circle } from "../../../chunks/form-button.js";
import { image } from "gravatar-gen";
import { uuidv7 } from "uuidv7";
import { a as attachRef, w as watch, c as createBitsAttrs, b as createId, d as box, m as mergeProps, g as getDataDisabled, e as getDataOpenClosed, f as getAriaExpanded, h as getDisabled } from "../../../chunks/create-id.js";
import { C as Context$1, D as DOMContext, O as OpenChangeComplete, S as SPACE, E as ENTER, a as afterTick, n as noop, P as Presence_layer, b as Previous } from "../../../chunks/scroll-lock.js";
import { D as Dialog_trigger } from "../../../chunks/dialog-trigger.js";
import { s as setSidebar, P as Provider, S as SIDEBAR_COOKIE_NAME, a as SIDEBAR_COOKIE_MAX_AGE, b as SIDEBAR_WIDTH, c as SIDEBAR_WIDTH_ICON, u as useSidebar, d as SIDEBAR_WIDTH_MOBILE, e as Sidebar_menu_button } from "../../../chunks/sidebar-menu-button.js";
import { S as Separator } from "../../../chunks/separator.js";
import { R as Root$2, D as Dropdown_menu_trigger, a as Dropdown_menu_content } from "../../../chunks/index6.js";
import { g as goto } from "../../../chunks/client.js";
import { D as Dropdown_menu_separator, a as Dropdown_menu_group, b as Dropdown_menu_item } from "../../../chunks/dropdown-menu-separator.js";
import { C as Chevrons_up_down } from "../../../chunks/chevrons-up-down.js";
import "../../../chunks/badge.js";
/* empty css                                                  */
/* empty css                   */
function Arrow_right($$payload, $$props) {
  push();
  /**
   * @license @lucide/svelte v0.539.0 - ISC
   *
   * ISC License
   *
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   *
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   *
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   *
   */
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    ["path", { "d": "M5 12h14" }],
    ["path", { "d": "m12 5 7 7-7 7" }]
  ];
  Icon($$payload, spread_props([
    { name: "arrow-right" },
    /**
     * @component @name ArrowRight
     * @description Lucide SVG icon component, renders SVG Element with children.
     *
     * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSAxMmgxNCIgLz4KICA8cGF0aCBkPSJtMTIgNSA3IDctNyA3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/arrow-right
     * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
     *
     * @param {Object} props - Lucide icons props and any valid SVG attribute
     * @returns {FunctionalComponent} Svelte component
     *
     */
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function Badge_check($$payload, $$props) {
  push();
  /**
   * @license @lucide/svelte v0.539.0 - ISC
   *
   * ISC License
   *
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   *
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   *
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   *
   */
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    [
      "path",
      {
        "d": "M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z"
      }
    ],
    ["path", { "d": "m9 12 2 2 4-4" }]
  ];
  Icon($$payload, spread_props([
    { name: "badge-check" },
    /**
     * @component @name BadgeCheck
     * @description Lucide SVG icon component, renders SVG Element with children.
     *
     * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMy44NSA4LjYyYTQgNCAwIDAgMSA0Ljc4LTQuNzcgNCA0IDAgMCAxIDYuNzQgMCA0IDQgMCAwIDEgNC43OCA0Ljc4IDQgNCAwIDAgMSAwIDYuNzQgNCA0IDAgMCAxLTQuNzcgNC43OCA0IDQgMCAwIDEtNi43NSAwIDQgNCAwIDAgMS00Ljc4LTQuNzcgNCA0IDAgMCAxIDAtNi43NloiIC8+CiAgPHBhdGggZD0ibTkgMTIgMiAyIDQtNCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/badge-check
     * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
     *
     * @param {Object} props - Lucide icons props and any valid SVG attribute
     * @returns {FunctionalComponent} Svelte component
     *
     */
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function Bell($$payload, $$props) {
  push();
  /**
   * @license @lucide/svelte v0.539.0 - ISC
   *
   * ISC License
   *
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   *
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   *
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   *
   */
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    ["path", { "d": "M10.268 21a2 2 0 0 0 3.464 0" }],
    [
      "path",
      {
        "d": "M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326"
      }
    ]
  ];
  Icon($$payload, spread_props([
    { name: "bell" },
    /**
     * @component @name Bell
     * @description Lucide SVG icon component, renders SVG Element with children.
     *
     * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAuMjY4IDIxYTIgMiAwIDAgMCAzLjQ2NCAwIiAvPgogIDxwYXRoIGQ9Ik0zLjI2MiAxNS4zMjZBMSAxIDAgMCAwIDQgMTdoMTZhMSAxIDAgMCAwIC43NC0xLjY3M0MxOS40MSAxMy45NTYgMTggMTIuNDk5IDE4IDhBNiA2IDAgMCAwIDYgOGMwIDQuNDk5LTEuNDExIDUuOTU2LTIuNzM4IDcuMzI2IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/bell
     * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
     *
     * @param {Object} props - Lucide icons props and any valid SVG attribute
     * @returns {FunctionalComponent} Svelte component
     *
     */
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function Building($$payload, $$props) {
  push();
  /**
   * @license @lucide/svelte v0.539.0 - ISC
   *
   * ISC License
   *
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   *
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   *
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   *
   */
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    [
      "rect",
      {
        "width": "16",
        "height": "20",
        "x": "4",
        "y": "2",
        "rx": "2",
        "ry": "2"
      }
    ],
    ["path", { "d": "M9 22v-4h6v4" }],
    ["path", { "d": "M8 6h.01" }],
    ["path", { "d": "M16 6h.01" }],
    ["path", { "d": "M12 6h.01" }],
    ["path", { "d": "M12 10h.01" }],
    ["path", { "d": "M12 14h.01" }],
    ["path", { "d": "M16 10h.01" }],
    ["path", { "d": "M16 14h.01" }],
    ["path", { "d": "M8 10h.01" }],
    ["path", { "d": "M8 14h.01" }]
  ];
  Icon($$payload, spread_props([
    { name: "building" },
    /**
     * @component @name Building
     * @description Lucide SVG icon component, renders SVG Element with children.
     *
     * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTYiIGhlaWdodD0iMjAiIHg9IjQiIHk9IjIiIHJ4PSIyIiByeT0iMiIgLz4KICA8cGF0aCBkPSJNOSAyMnYtNGg2djQiIC8+CiAgPHBhdGggZD0iTTggNmguMDEiIC8+CiAgPHBhdGggZD0iTTE2IDZoLjAxIiAvPgogIDxwYXRoIGQ9Ik0xMiA2aC4wMSIgLz4KICA8cGF0aCBkPSJNMTIgMTBoLjAxIiAvPgogIDxwYXRoIGQ9Ik0xMiAxNGguMDEiIC8+CiAgPHBhdGggZD0iTTE2IDEwaC4wMSIgLz4KICA8cGF0aCBkPSJNMTYgMTRoLjAxIiAvPgogIDxwYXRoIGQ9Ik04IDEwaC4wMSIgLz4KICA8cGF0aCBkPSJNOCAxNGguMDEiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/building
     * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
     *
     * @param {Object} props - Lucide icons props and any valid SVG attribute
     * @returns {FunctionalComponent} Svelte component
     *
     */
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function Dot($$payload, $$props) {
  push();
  /**
   * @license @lucide/svelte v0.539.0 - ISC
   *
   * ISC License
   *
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   *
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   *
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   *
   */
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [["circle", { "cx": "12.1", "cy": "12.1", "r": "1" }]];
  Icon($$payload, spread_props([
    { name: "dot" },
    /**
     * @component @name Dot
     * @description Lucide SVG icon component, renders SVG Element with children.
     *
     * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMi4xIiBjeT0iMTIuMSIgcj0iMSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/dot
     * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
     *
     * @param {Object} props - Lucide icons props and any valid SVG attribute
     * @returns {FunctionalComponent} Svelte component
     *
     */
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function Key_round($$payload, $$props) {
  push();
  /**
   * @license @lucide/svelte v0.539.0 - ISC
   *
   * ISC License
   *
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   *
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   *
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   *
   */
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    [
      "path",
      {
        "d": "M2.586 17.414A2 2 0 0 0 2 18.828V21a1 1 0 0 0 1 1h3a1 1 0 0 0 1-1v-1a1 1 0 0 1 1-1h1a1 1 0 0 0 1-1v-1a1 1 0 0 1 1-1h.172a2 2 0 0 0 1.414-.586l.814-.814a6.5 6.5 0 1 0-4-4z"
      }
    ],
    [
      "circle",
      { "cx": "16.5", "cy": "7.5", "r": ".5", "fill": "currentColor" }
    ]
  ];
  Icon($$payload, spread_props([
    { name: "key-round" },
    /**
     * @component @name KeyRound
     * @description Lucide SVG icon component, renders SVG Element with children.
     *
     * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMi41ODYgMTcuNDE0QTIgMiAwIDAgMCAyIDE4LjgyOFYyMWExIDEgMCAwIDAgMSAxaDNhMSAxIDAgMCAwIDEtMXYtMWExIDEgMCAwIDEgMS0xaDFhMSAxIDAgMCAwIDEtMXYtMWExIDEgMCAwIDEgMS0xaC4xNzJhMiAyIDAgMCAwIDEuNDE0LS41ODZsLjgxNC0uODE0YTYuNSA2LjUgMCAxIDAtNC00eiIgLz4KICA8Y2lyY2xlIGN4PSIxNi41IiBjeT0iNy41IiByPSIuNSIgZmlsbD0iY3VycmVudENvbG9yIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/key-round
     * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
     *
     * @param {Object} props - Lucide icons props and any valid SVG attribute
     * @returns {FunctionalComponent} Svelte component
     *
     */
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function Log_out($$payload, $$props) {
  push();
  /**
   * @license @lucide/svelte v0.539.0 - ISC
   *
   * ISC License
   *
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   *
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   *
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   *
   */
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    ["path", { "d": "m16 17 5-5-5-5" }],
    ["path", { "d": "M21 12H9" }],
    ["path", { "d": "M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4" }]
  ];
  Icon($$payload, spread_props([
    { name: "log-out" },
    /**
     * @component @name LogOut
     * @description Lucide SVG icon component, renders SVG Element with children.
     *
     * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTYgMTcgNS01LTUtNSIgLz4KICA8cGF0aCBkPSJNMjEgMTJIOSIgLz4KICA8cGF0aCBkPSJNOSAyMUg1YTIgMiAwIDAgMS0yLTJWNWEyIDIgMCAwIDEgMi0yaDQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/log-out
     * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
     *
     * @param {Object} props - Lucide icons props and any valid SVG attribute
     * @returns {FunctionalComponent} Svelte component
     *
     */
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function Menu($$payload, $$props) {
  push();
  /**
   * @license @lucide/svelte v0.539.0 - ISC
   *
   * ISC License
   *
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   *
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   *
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   *
   */
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    ["path", { "d": "M4 12h16" }],
    ["path", { "d": "M4 18h16" }],
    ["path", { "d": "M4 6h16" }]
  ];
  Icon($$payload, spread_props([
    { name: "menu" },
    /**
     * @component @name Menu
     * @description Lucide SVG icon component, renders SVG Element with children.
     *
     * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNCAxMmgxNiIgLz4KICA8cGF0aCBkPSJNNCAxOGgxNiIgLz4KICA8cGF0aCBkPSJNNCA2aDE2IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/menu
     * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
     *
     * @param {Object} props - Lucide icons props and any valid SVG attribute
     * @returns {FunctionalComponent} Svelte component
     *
     */
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function Message_square($$payload, $$props) {
  push();
  /**
   * @license @lucide/svelte v0.539.0 - ISC
   *
   * ISC License
   *
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   *
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   *
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   *
   */
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    [
      "path",
      {
        "d": "M22 17a2 2 0 0 1-2 2H6.828a2 2 0 0 0-1.414.586l-2.202 2.202A.71.71 0 0 1 2 21.286V5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2z"
      }
    ]
  ];
  Icon($$payload, spread_props([
    { name: "message-square" },
    /**
     * @component @name MessageSquare
     * @description Lucide SVG icon component, renders SVG Element with children.
     *
     * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjIgMTdhMiAyIDAgMCAxLTIgMkg2LjgyOGEyIDIgMCAwIDAtMS40MTQuNTg2bC0yLjIwMiAyLjIwMkEuNzEuNzEgMCAwIDEgMiAyMS4yODZWNWEyIDIgMCAwIDEgMi0yaDE2YTIgMiAwIDAgMSAyIDJ6IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/message-square
     * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
     *
     * @param {Object} props - Lucide icons props and any valid SVG attribute
     * @returns {FunctionalComponent} Svelte component
     *
     */
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function Moon($$payload, $$props) {
  push();
  /**
   * @license @lucide/svelte v0.539.0 - ISC
   *
   * ISC License
   *
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   *
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   *
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   *
   */
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    [
      "path",
      {
        "d": "M20.985 12.486a9 9 0 1 1-9.473-9.472c.405-.022.617.46.402.803a6 6 0 0 0 8.268 8.268c.344-.215.825-.004.803.401"
      }
    ]
  ];
  Icon($$payload, spread_props([
    { name: "moon" },
    /**
     * @component @name Moon
     * @description Lucide SVG icon component, renders SVG Element with children.
     *
     * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjAuOTg1IDEyLjQ4NmE5IDkgMCAxIDEtOS40NzMtOS40NzJjLjQwNS0uMDIyLjYxNy40Ni40MDIuODAzYTYgNiAwIDAgMCA4LjI2OCA4LjI2OGMuMzQ0LS4yMTUuODI1LS4wMDQuODAzLjQwMSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/moon
     * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
     *
     * @param {Object} props - Lucide icons props and any valid SVG attribute
     * @returns {FunctionalComponent} Svelte component
     *
     */
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function Panel_left($$payload, $$props) {
  push();
  /**
   * @license @lucide/svelte v0.539.0 - ISC
   *
   * ISC License
   *
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   *
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   *
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   *
   */
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    [
      "rect",
      { "width": "18", "height": "18", "x": "3", "y": "3", "rx": "2" }
    ],
    ["path", { "d": "M9 3v18" }]
  ];
  Icon($$payload, spread_props([
    { name: "panel-left" },
    /**
     * @component @name PanelLeft
     * @description Lucide SVG icon component, renders SVG Element with children.
     *
     * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHg9IjMiIHk9IjMiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Ik05IDN2MTgiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/panel-left
     * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
     *
     * @param {Object} props - Lucide icons props and any valid SVG attribute
     * @returns {FunctionalComponent} Svelte component
     *
     */
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function Receipt_text($$payload, $$props) {
  push();
  /**
   * @license @lucide/svelte v0.539.0 - ISC
   *
   * ISC License
   *
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   *
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   *
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   *
   */
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    [
      "path",
      {
        "d": "M4 2v20l2-1 2 1 2-1 2 1 2-1 2 1 2-1 2 1V2l-2 1-2-1-2 1-2-1-2 1-2-1-2 1Z"
      }
    ],
    ["path", { "d": "M14 8H8" }],
    ["path", { "d": "M16 12H8" }],
    ["path", { "d": "M13 16H8" }]
  ];
  Icon($$payload, spread_props([
    { name: "receipt-text" },
    /**
     * @component @name ReceiptText
     * @description Lucide SVG icon component, renders SVG Element with children.
     *
     * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNCAydjIwbDItMSAyIDEgMi0xIDIgMSAyLTEgMiAxIDItMSAyIDFWMmwtMiAxLTItMS0yIDEtMi0xLTIgMS0yLTEtMiAxWiIgLz4KICA8cGF0aCBkPSJNMTQgOEg4IiAvPgogIDxwYXRoIGQ9Ik0xNiAxMkg4IiAvPgogIDxwYXRoIGQ9Ik0xMyAxNkg4IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/receipt-text
     * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
     *
     * @param {Object} props - Lucide icons props and any valid SVG attribute
     * @returns {FunctionalComponent} Svelte component
     *
     */
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function Sparkles($$payload, $$props) {
  push();
  /**
   * @license @lucide/svelte v0.539.0 - ISC
   *
   * ISC License
   *
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   *
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   *
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   *
   */
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    [
      "path",
      {
        "d": "M11.017 2.814a1 1 0 0 1 1.966 0l1.051 5.558a2 2 0 0 0 1.594 1.594l5.558 1.051a1 1 0 0 1 0 1.966l-5.558 1.051a2 2 0 0 0-1.594 1.594l-1.051 5.558a1 1 0 0 1-1.966 0l-1.051-5.558a2 2 0 0 0-1.594-1.594l-5.558-1.051a1 1 0 0 1 0-1.966l5.558-1.051a2 2 0 0 0 1.594-1.594z"
      }
    ],
    ["path", { "d": "M20 2v4" }],
    ["path", { "d": "M22 4h-4" }],
    ["circle", { "cx": "4", "cy": "20", "r": "2" }]
  ];
  Icon($$payload, spread_props([
    { name: "sparkles" },
    /**
     * @component @name Sparkles
     * @description Lucide SVG icon component, renders SVG Element with children.
     *
     * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTEuMDE3IDIuODE0YTEgMSAwIDAgMSAxLjk2NiAwbDEuMDUxIDUuNTU4YTIgMiAwIDAgMCAxLjU5NCAxLjU5NGw1LjU1OCAxLjA1MWExIDEgMCAwIDEgMCAxLjk2NmwtNS41NTggMS4wNTFhMiAyIDAgMCAwLTEuNTk0IDEuNTk0bC0xLjA1MSA1LjU1OGExIDEgMCAwIDEtMS45NjYgMGwtMS4wNTEtNS41NThhMiAyIDAgMCAwLTEuNTk0LTEuNTk0bC01LjU1OC0xLjA1MWExIDEgMCAwIDEgMC0xLjk2Nmw1LjU1OC0xLjA1MWEyIDIgMCAwIDAgMS41OTQtMS41OTR6IiAvPgogIDxwYXRoIGQ9Ik0yMCAydjQiIC8+CiAgPHBhdGggZD0iTTIyIDRoLTQiIC8+CiAgPGNpcmNsZSBjeD0iNCIgY3k9IjIwIiByPSIyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/sparkles
     * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
     *
     * @param {Object} props - Lucide icons props and any valid SVG attribute
     * @returns {FunctionalComponent} Svelte component
     *
     */
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function Sun($$payload, $$props) {
  push();
  /**
   * @license @lucide/svelte v0.539.0 - ISC
   *
   * ISC License
   *
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   *
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   *
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   *
   */
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    ["circle", { "cx": "12", "cy": "12", "r": "4" }],
    ["path", { "d": "M12 2v2" }],
    ["path", { "d": "M12 20v2" }],
    ["path", { "d": "m4.93 4.93 1.41 1.41" }],
    ["path", { "d": "m17.66 17.66 1.41 1.41" }],
    ["path", { "d": "M2 12h2" }],
    ["path", { "d": "M20 12h2" }],
    ["path", { "d": "m6.34 17.66-1.41 1.41" }],
    ["path", { "d": "m19.07 4.93-1.41 1.41" }]
  ];
  Icon($$payload, spread_props([
    { name: "sun" },
    /**
     * @component @name Sun
     * @description Lucide SVG icon component, renders SVG Element with children.
     *
     * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSI0IiAvPgogIDxwYXRoIGQ9Ik0xMiAydjIiIC8+CiAgPHBhdGggZD0iTTEyIDIwdjIiIC8+CiAgPHBhdGggZD0ibTQuOTMgNC45MyAxLjQxIDEuNDEiIC8+CiAgPHBhdGggZD0ibTE3LjY2IDE3LjY2IDEuNDEgMS40MSIgLz4KICA8cGF0aCBkPSJNMiAxMmgyIiAvPgogIDxwYXRoIGQ9Ik0yMCAxMmgyIiAvPgogIDxwYXRoIGQ9Im02LjM0IDE3LjY2LTEuNDEgMS40MSIgLz4KICA8cGF0aCBkPSJtMTkuMDcgNC45My0xLjQxIDEuNDEiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/sun
     * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
     *
     * @param {Object} props - Lucide icons props and any valid SVG attribute
     * @returns {FunctionalComponent} Svelte component
     *
     */
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
const PAGES = {
  "_ROOT": `/`,
  "appointments": `/appointments`,
  "appointments_appointment_templates": `/appointments/appointment-templates`,
  "appointments_calendar_blocks": `/appointments/calendar-blocks`,
  "campaigns": `/campaigns`,
  "chat_store": `/chat-store`,
  "invitation_id": (id, params) => {
    return `/invitation/${id}`;
  },
  "laybye": `/laybye`,
  "memberships": `/memberships`,
  "rewards": `/rewards`,
  "settings": `/settings`,
  "settings_invites": `/settings/invites`,
  "settings_locations": `/settings/locations`,
  "settings_payment_engine": `/settings/payment-engine`,
  "settings_staff": `/settings/staff`,
  "settings_staff_invites": `/settings/staff-invites`,
  "subscriptions": `/subscriptions`,
  "wallets": `/wallets`
};
const bars = Array(12).fill(0);
function Loader($$payload, $$props) {
  push();
  let { visible, class: className } = $$props;
  const each_array = ensure_array_like(bars);
  $$payload.out.push(`<div${attr_class(clsx(["sonner-loading-wrapper", className].filter(Boolean).join(" ")))}${attr("data-visible", visible)}><div class="sonner-spinner"><!--[-->`);
  for (let i = 0, $$length = each_array.length; i < $$length; i++) {
    each_array[i];
    $$payload.out.push(`<div class="sonner-loading-bar"></div>`);
  }
  $$payload.out.push(`<!--]--></div></div>`);
  pop();
}
const sonnerContext = new Context("<Toaster/>");
function isAction(action) {
  return action.label !== void 0;
}
const TOAST_LIFETIME$1 = 4e3;
const GAP$1 = 14;
const TIME_BEFORE_UNMOUNT = 200;
const DEFAULT_TOAST_CLASSES = {
  toast: "",
  title: "",
  description: "",
  loader: "",
  closeButton: "",
  cancelButton: "",
  actionButton: "",
  action: "",
  warning: "",
  error: "",
  success: "",
  default: "",
  info: "",
  loading: ""
};
function Toast($$payload, $$props) {
  push();
  let {
    toast: toast2,
    index,
    expanded,
    invert: invertFromToaster,
    position,
    visibleToasts,
    expandByDefault,
    closeButton: closeButtonFromToaster,
    interacting,
    cancelButtonStyle = "",
    actionButtonStyle = "",
    duration: durationFromToaster,
    descriptionClass = "",
    classes: classesProp,
    unstyled = false,
    loadingIcon,
    successIcon,
    errorIcon,
    warningIcon,
    closeIcon,
    infoIcon,
    defaultRichColors = false,
    swipeDirections: swipeDirectionsProp,
    closeButtonAriaLabel,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const defaultClasses = { ...DEFAULT_TOAST_CLASSES };
  let mounted = false;
  let removed = false;
  let swiping = false;
  let swipeOut = false;
  let isSwiped = false;
  let offsetBeforeRemove = 0;
  let initialHeight = 0;
  toast2.duration || durationFromToaster || TOAST_LIFETIME$1;
  let swipeOutDirection = null;
  const isFront = index === 0;
  const isVisible = index + 1 <= visibleToasts;
  const toastType = toast2.type;
  const dismissable = toast2.dismissable !== false;
  const toastClass = toast2.class || "";
  const toastDescriptionClass = toast2.descriptionClass || "";
  const heightIndex = toastState.heights.findIndex((height) => height.toastId === toast2.id) || 0;
  const closeButton = toast2.closeButton ?? closeButtonFromToaster;
  toast2.duration ?? durationFromToaster ?? TOAST_LIFETIME$1;
  const coords = position.split("-");
  const toastsHeightBefore = toastState.heights.reduce(
    (prev, curr, reducerIndex) => {
      if (reducerIndex >= heightIndex) return prev;
      return prev + curr.height;
    },
    0
  );
  const invert = toast2.invert || invertFromToaster;
  const disabled = toastType === "loading";
  const classes = { ...defaultClasses, ...classesProp };
  toast2.title;
  toast2.description;
  const offset = Math.round(heightIndex * GAP$1 + toastsHeightBefore);
  function deleteToast() {
    removed = true;
    offsetBeforeRemove = offset;
    toastState.removeHeight(toast2.id);
    setTimeout(
      () => {
        toastState.remove(toast2.id);
      },
      TIME_BEFORE_UNMOUNT
    );
  }
  toast2.promise && toastType === "loading" || toast2.duration === Number.POSITIVE_INFINITY;
  const icon = (() => {
    if (toast2.icon) return toast2.icon;
    if (toastType === "success") return successIcon;
    if (toastType === "error") return errorIcon;
    if (toastType === "warning") return warningIcon;
    if (toastType === "info") return infoIcon;
    if (toastType === "loading") return loadingIcon;
    return null;
  })();
  function LoadingIcon($$payload2) {
    if (loadingIcon) {
      $$payload2.out.push("<!--[-->");
      $$payload2.out.push(`<div${attr_class(clsx(cn(classes?.loader, toast2?.classes?.loader, "sonner-loader")))}${attr("data-visible", toastType === "loading")}>`);
      loadingIcon($$payload2);
      $$payload2.out.push(`<!----></div>`);
    } else {
      $$payload2.out.push("<!--[!-->");
      Loader($$payload2, {
        class: cn(classes?.loader, toast2.classes?.loader),
        visible: toastType === "loading"
      });
    }
    $$payload2.out.push(`<!--]-->`);
  }
  $$payload.out.push(`<li${attr("tabindex", 0)}${attr_class(clsx(cn(restProps.class, toastClass, classes?.toast, toast2?.classes?.toast, classes?.[toastType], toast2?.classes?.[toastType])))} data-sonner-toast=""${attr("data-rich-colors", toast2.richColors ?? defaultRichColors)}${attr("data-styled", !(toast2.component || toast2.unstyled || unstyled))}${attr("data-mounted", mounted)}${attr("data-promise", Boolean(toast2.promise))}${attr("data-swiped", isSwiped)}${attr("data-removed", removed)}${attr("data-visible", isVisible)}${attr("data-y-position", coords[0])}${attr("data-x-position", coords[1])}${attr("data-index", index)}${attr("data-front", isFront)}${attr("data-swiping", swiping)}${attr("data-dismissable", dismissable)}${attr("data-type", toastType)}${attr("data-invert", invert)}${attr("data-swipe-out", swipeOut)}${attr("data-swipe-direction", swipeOutDirection)}${attr("data-expanded", Boolean(expanded || expandByDefault && mounted))}${attr_style(`${restProps.style} ${toast2.style}`, {
    "--index": index,
    "--toasts-before": index,
    "--z-index": toastState.toasts.length - index,
    "--offset": `${removed ? offsetBeforeRemove : offset}px`,
    "--initial-height": expandByDefault ? "auto" : `${initialHeight}px`
  })}>`);
  if (closeButton && !toast2.component && toastType !== "loading" && closeIcon !== null) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<button${attr("aria-label", closeButtonAriaLabel)}${attr("data-disabled", disabled)} data-close-button=""${attr_class(clsx(cn(classes?.closeButton, toast2?.classes?.closeButton)))}>`);
    closeIcon?.($$payload);
    $$payload.out.push(`<!----></button>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--> `);
  if (toast2.component) {
    $$payload.out.push("<!--[-->");
    const Component = toast2.component;
    $$payload.out.push(`<!---->`);
    Component($$payload, spread_props([toast2.componentProps, { closeToast: deleteToast }]));
    $$payload.out.push(`<!---->`);
  } else {
    $$payload.out.push("<!--[!-->");
    if ((toastType || toast2.icon || toast2.promise) && toast2.icon !== null && (icon !== null || toast2.icon)) {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<div data-icon=""${attr_class(clsx(cn(classes?.icon, toast2?.classes?.icon)))}>`);
      if (toast2.promise || toastType === "loading") {
        $$payload.out.push("<!--[-->");
        if (toast2.icon) {
          $$payload.out.push("<!--[-->");
          $$payload.out.push(`<!---->`);
          toast2.icon($$payload, {});
          $$payload.out.push(`<!---->`);
        } else {
          $$payload.out.push("<!--[!-->");
          LoadingIcon($$payload);
        }
        $$payload.out.push(`<!--]-->`);
      } else {
        $$payload.out.push("<!--[!-->");
      }
      $$payload.out.push(`<!--]--> `);
      if (toast2.type !== "loading") {
        $$payload.out.push("<!--[-->");
        if (toast2.icon) {
          $$payload.out.push("<!--[-->");
          $$payload.out.push(`<!---->`);
          toast2.icon($$payload, {});
          $$payload.out.push(`<!---->`);
        } else {
          $$payload.out.push("<!--[!-->");
          if (toastType === "success") {
            $$payload.out.push("<!--[-->");
            successIcon?.($$payload);
            $$payload.out.push(`<!---->`);
          } else {
            $$payload.out.push("<!--[!-->");
            if (toastType === "error") {
              $$payload.out.push("<!--[-->");
              errorIcon?.($$payload);
              $$payload.out.push(`<!---->`);
            } else {
              $$payload.out.push("<!--[!-->");
              if (toastType === "warning") {
                $$payload.out.push("<!--[-->");
                warningIcon?.($$payload);
                $$payload.out.push(`<!---->`);
              } else {
                $$payload.out.push("<!--[!-->");
                if (toastType === "info") {
                  $$payload.out.push("<!--[-->");
                  infoIcon?.($$payload);
                  $$payload.out.push(`<!---->`);
                } else {
                  $$payload.out.push("<!--[!-->");
                }
                $$payload.out.push(`<!--]-->`);
              }
              $$payload.out.push(`<!--]-->`);
            }
            $$payload.out.push(`<!--]-->`);
          }
          $$payload.out.push(`<!--]-->`);
        }
        $$payload.out.push(`<!--]-->`);
      } else {
        $$payload.out.push("<!--[!-->");
      }
      $$payload.out.push(`<!--]--></div>`);
    } else {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]--> <div data-content=""><div data-title=""${attr_class(clsx(cn(classes?.title, toast2?.classes?.title)))}>`);
    if (toast2.title) {
      $$payload.out.push("<!--[-->");
      if (typeof toast2.title !== "string") {
        $$payload.out.push("<!--[-->");
        const Title = toast2.title;
        $$payload.out.push(`<!---->`);
        Title($$payload, spread_props([toast2.componentProps]));
        $$payload.out.push(`<!---->`);
      } else {
        $$payload.out.push("<!--[!-->");
        $$payload.out.push(`${escape_html(toast2.title)}`);
      }
      $$payload.out.push(`<!--]-->`);
    } else {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]--></div> `);
    if (toast2.description) {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<div data-description=""${attr_class(clsx(cn(descriptionClass, toastDescriptionClass, classes?.description, toast2.classes?.description)))}>`);
      if (typeof toast2.description !== "string") {
        $$payload.out.push("<!--[-->");
        const Description = toast2.description;
        $$payload.out.push(`<!---->`);
        Description($$payload, spread_props([toast2.componentProps]));
        $$payload.out.push(`<!---->`);
      } else {
        $$payload.out.push("<!--[!-->");
        $$payload.out.push(`${escape_html(toast2.description)}`);
      }
      $$payload.out.push(`<!--]--></div>`);
    } else {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]--></div> `);
    if (toast2.cancel) {
      $$payload.out.push("<!--[-->");
      if (typeof toast2.cancel === "function") {
        $$payload.out.push("<!--[-->");
        $$payload.out.push(`<!---->`);
        toast2.cancel($$payload, {});
        $$payload.out.push(`<!---->`);
      } else {
        $$payload.out.push("<!--[!-->");
        if (isAction(toast2.cancel)) {
          $$payload.out.push("<!--[-->");
          $$payload.out.push(`<button data-button="" data-cancel=""${attr_style(toast2.cancelButtonStyle ?? cancelButtonStyle)}${attr_class(clsx(cn(classes?.cancelButton, toast2?.classes?.cancelButton)))}>${escape_html(toast2.cancel.label)}</button>`);
        } else {
          $$payload.out.push("<!--[!-->");
        }
        $$payload.out.push(`<!--]-->`);
      }
      $$payload.out.push(`<!--]-->`);
    } else {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]--> `);
    if (toast2.action) {
      $$payload.out.push("<!--[-->");
      if (typeof toast2.action === "function") {
        $$payload.out.push("<!--[-->");
        $$payload.out.push(`<!---->`);
        toast2.action($$payload, {});
        $$payload.out.push(`<!---->`);
      } else {
        $$payload.out.push("<!--[!-->");
        if (isAction(toast2.action)) {
          $$payload.out.push("<!--[-->");
          $$payload.out.push(`<button data-button=""${attr_style(toast2.actionButtonStyle ?? actionButtonStyle)}${attr_class(clsx(cn(classes?.actionButton, toast2?.classes?.actionButton)))}>${escape_html(toast2.action.label)}</button>`);
        } else {
          $$payload.out.push("<!--[!-->");
        }
        $$payload.out.push(`<!--]-->`);
      }
      $$payload.out.push(`<!--]-->`);
    } else {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]-->`);
  }
  $$payload.out.push(`<!--]--></li>`);
  pop();
}
function SuccessIcon($$payload) {
  $$payload.out.push(`<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" height="20" width="20" data-sonner-success-icon=""><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd"></path></svg>`);
}
function ErrorIcon($$payload) {
  $$payload.out.push(`<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" height="20" width="20" data-sonner-error-icon=""><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"></path></svg>`);
}
function WarningIcon($$payload) {
  $$payload.out.push(`<svg viewBox="0 0 64 64" fill="currentColor" height="20" width="20" data-sonner-warning-icon="" xmlns="http://www.w3.org/2000/svg"><path d="M32.427,7.987c2.183,0.124 4,1.165 5.096,3.281l17.936,36.208c1.739,3.66 -0.954,8.585 -5.373,8.656l-36.119,0c-4.022,-0.064 -7.322,-4.631 -5.352,-8.696l18.271,-36.207c0.342,-0.65 0.498,-0.838 0.793,-1.179c1.186,-1.375 2.483,-2.111 4.748,-2.063Zm-0.295,3.997c-0.687,0.034 -1.316,0.419 -1.659,1.017c-6.312,11.979 -12.397,24.081 -18.301,36.267c-0.546,1.225 0.391,2.797 1.762,2.863c12.06,0.195 24.125,0.195 36.185,0c1.325,-0.064 2.321,-1.584 1.769,-2.85c-5.793,-12.184 -11.765,-24.286 -17.966,-36.267c-0.366,-0.651 -0.903,-1.042 -1.79,-1.03Z"></path><path d="M33.631,40.581l-3.348,0l-0.368,-16.449l4.1,0l-0.384,16.449Zm-3.828,5.03c0,-0.609 0.197,-1.113 0.592,-1.514c0.396,-0.4 0.935,-0.601 1.618,-0.601c0.684,0 1.223,0.201 1.618,0.601c0.395,0.401 0.593,0.905 0.593,1.514c0,0.587 -0.193,1.078 -0.577,1.473c-0.385,0.395 -0.929,0.593 -1.634,0.593c-0.705,0 -1.249,-0.198 -1.634,-0.593c-0.384,-0.395 -0.576,-0.886 -0.576,-1.473Z"></path></svg>`);
}
function InfoIcon($$payload) {
  $$payload.out.push(`<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" height="20" width="20" data-sonner-info-icon=""><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z" clip-rule="evenodd"></path></svg>`);
}
function CloseIcon($$payload) {
  $$payload.out.push(`<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-sonner-close-icon=""><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>`);
}
const VISIBLE_TOASTS_AMOUNT = 3;
const VIEWPORT_OFFSET = "24px";
const MOBILE_VIEWPORT_OFFSET = "16px";
const TOAST_LIFETIME = 4e3;
const TOAST_WIDTH = 356;
const GAP = 14;
const DARK = "dark";
const LIGHT = "light";
function getOffsetObject(defaultOffset, mobileOffset) {
  const styles = {};
  [defaultOffset, mobileOffset].forEach((offset, index) => {
    const isMobile = index === 1;
    const prefix = isMobile ? "--mobile-offset" : "--offset";
    const defaultValue = isMobile ? MOBILE_VIEWPORT_OFFSET : VIEWPORT_OFFSET;
    function assignAll(offset2) {
      ["top", "right", "bottom", "left"].forEach((key) => {
        styles[`${prefix}-${key}`] = typeof offset2 === "number" ? `${offset2}px` : offset2;
      });
    }
    if (typeof offset === "number" || typeof offset === "string") {
      assignAll(offset);
    } else if (typeof offset === "object") {
      ["top", "right", "bottom", "left"].forEach((key) => {
        const value = offset[key];
        if (value === void 0) {
          styles[`${prefix}-${key}`] = defaultValue;
        } else {
          styles[`${prefix}-${key}`] = typeof value === "number" ? `${value}px` : value;
        }
      });
    } else {
      assignAll(defaultValue);
    }
  });
  return styles;
}
function Toaster($$payload, $$props) {
  push();
  function getInitialTheme(t) {
    if (t !== "system") return t;
    if (typeof window !== "undefined") {
      if (window.matchMedia && window.matchMedia("(prefers-color-scheme: dark)").matches) {
        return DARK;
      }
      return LIGHT;
    }
    return LIGHT;
  }
  let {
    invert = false,
    position = "bottom-right",
    hotkey = ["altKey", "KeyT"],
    expand = false,
    closeButton = false,
    offset = VIEWPORT_OFFSET,
    mobileOffset = MOBILE_VIEWPORT_OFFSET,
    theme = "light",
    richColors = false,
    duration = TOAST_LIFETIME,
    visibleToasts = VISIBLE_TOASTS_AMOUNT,
    toastOptions = {},
    dir = "auto",
    gap = GAP,
    loadingIcon: loadingIconProp,
    successIcon: successIconProp,
    errorIcon: errorIconProp,
    warningIcon: warningIconProp,
    closeIcon: closeIconProp,
    infoIcon: infoIconProp,
    containerAriaLabel = "Notifications",
    class: className,
    closeButtonAriaLabel = "Close toast",
    onblur,
    onfocus,
    onmouseenter,
    onmousemove,
    onmouseleave,
    ondragend,
    onpointerdown,
    onpointerup,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  function getDocumentDirection() {
    if (dir !== "auto") return dir;
    if (typeof window === "undefined") return "ltr";
    if (typeof document === "undefined") return "ltr";
    const dirAttribute = document.documentElement.getAttribute("dir");
    if (dirAttribute === "auto" || !dirAttribute) {
      run(() => dir = window.getComputedStyle(document.documentElement).direction ?? "ltr");
      return dir;
    }
    run(() => dir = dirAttribute);
    return dirAttribute;
  }
  const possiblePositions = Array.from(new Set([
    position,
    ...toastState.toasts.filter((toast2) => toast2.position).map((toast2) => toast2.position)
  ].filter(Boolean)));
  let expanded = false;
  let interacting = false;
  let actualTheme = getInitialTheme(theme);
  const hotkeyLabel = hotkey.join("+").replace(/Key/g, "").replace(/Digit/g, "");
  sonnerContext.set(new SonnerState());
  $$payload.out.push(`<section${attr("aria-label", `${stringify(containerAriaLabel)} ${stringify(hotkeyLabel)}`)}${attr("tabindex", -1)} aria-live="polite" aria-relevant="additions text" aria-atomic="false" class="svelte-tppj9g">`);
  if (toastState.toasts.length > 0) {
    $$payload.out.push("<!--[-->");
    const each_array = ensure_array_like(possiblePositions);
    $$payload.out.push(`<!--[-->`);
    for (let index = 0, $$length = each_array.length; index < $$length; index++) {
      let position2 = each_array[index];
      const [y, x] = position2.split("-");
      const offsetObject = getOffsetObject(offset, mobileOffset);
      const each_array_1 = ensure_array_like(toastState.toasts.filter((toast2) => !toast2.position && index === 0 || toast2.position === position2));
      $$payload.out.push(`<ol${spread_attributes(
        {
          tabindex: -1,
          dir: getDocumentDirection(),
          class: clsx(className),
          "data-sonner-toaster": true,
          "data-sonner-theme": actualTheme,
          "data-y-position": y,
          "data-x-position": x,
          style: restProps.style,
          ...restProps
        },
        "svelte-tppj9g",
        void 0,
        {
          "--front-toast-height": `${toastState.heights[0]?.height}px`,
          "--width": `${TOAST_WIDTH}px`,
          "--gap": `${gap}px`,
          "--offset-top": offsetObject["--offset-top"],
          "--offset-right": offsetObject["--offset-right"],
          "--offset-bottom": offsetObject["--offset-bottom"],
          "--offset-left": offsetObject["--offset-left"],
          "--mobile-offset-top": offsetObject["--mobile-offset-top"],
          "--mobile-offset-right": offsetObject["--mobile-offset-right"],
          "--mobile-offset-bottom": offsetObject["--mobile-offset-bottom"],
          "--mobile-offset-left": offsetObject["--mobile-offset-left"]
        }
      )}><!--[-->`);
      for (let index2 = 0, $$length2 = each_array_1.length; index2 < $$length2; index2++) {
        let toast2 = each_array_1[index2];
        {
          let successIcon = function($$payload2) {
            if (successIconProp) {
              $$payload2.out.push("<!--[-->");
              successIconProp?.($$payload2);
              $$payload2.out.push(`<!---->`);
            } else {
              $$payload2.out.push("<!--[!-->");
              if (successIconProp !== null) {
                $$payload2.out.push("<!--[-->");
                SuccessIcon($$payload2);
              } else {
                $$payload2.out.push("<!--[!-->");
              }
              $$payload2.out.push(`<!--]-->`);
            }
            $$payload2.out.push(`<!--]-->`);
          }, errorIcon = function($$payload2) {
            if (errorIconProp) {
              $$payload2.out.push("<!--[-->");
              errorIconProp?.($$payload2);
              $$payload2.out.push(`<!---->`);
            } else {
              $$payload2.out.push("<!--[!-->");
              if (errorIconProp !== null) {
                $$payload2.out.push("<!--[-->");
                ErrorIcon($$payload2);
              } else {
                $$payload2.out.push("<!--[!-->");
              }
              $$payload2.out.push(`<!--]-->`);
            }
            $$payload2.out.push(`<!--]-->`);
          }, warningIcon = function($$payload2) {
            if (warningIconProp) {
              $$payload2.out.push("<!--[-->");
              warningIconProp?.($$payload2);
              $$payload2.out.push(`<!---->`);
            } else {
              $$payload2.out.push("<!--[!-->");
              if (warningIconProp !== null) {
                $$payload2.out.push("<!--[-->");
                WarningIcon($$payload2);
              } else {
                $$payload2.out.push("<!--[!-->");
              }
              $$payload2.out.push(`<!--]-->`);
            }
            $$payload2.out.push(`<!--]-->`);
          }, infoIcon = function($$payload2) {
            if (infoIconProp) {
              $$payload2.out.push("<!--[-->");
              infoIconProp?.($$payload2);
              $$payload2.out.push(`<!---->`);
            } else {
              $$payload2.out.push("<!--[!-->");
              if (infoIconProp !== null) {
                $$payload2.out.push("<!--[-->");
                InfoIcon($$payload2);
              } else {
                $$payload2.out.push("<!--[!-->");
              }
              $$payload2.out.push(`<!--]-->`);
            }
            $$payload2.out.push(`<!--]-->`);
          }, closeIcon = function($$payload2) {
            if (closeIconProp) {
              $$payload2.out.push("<!--[-->");
              closeIconProp?.($$payload2);
              $$payload2.out.push(`<!---->`);
            } else {
              $$payload2.out.push("<!--[!-->");
              if (closeIconProp !== null) {
                $$payload2.out.push("<!--[-->");
                CloseIcon($$payload2);
              } else {
                $$payload2.out.push("<!--[!-->");
              }
              $$payload2.out.push(`<!--]-->`);
            }
            $$payload2.out.push(`<!--]-->`);
          };
          Toast($$payload, {
            index: index2,
            toast: toast2,
            defaultRichColors: richColors,
            duration: toastOptions?.duration ?? duration,
            class: toastOptions?.class ?? "",
            descriptionClass: toastOptions?.descriptionClass || "",
            invert,
            visibleToasts,
            closeButton,
            interacting,
            position: position2,
            style: toastOptions?.style ?? "",
            classes: toastOptions.classes || {},
            unstyled: toastOptions.unstyled ?? false,
            cancelButtonStyle: toastOptions?.cancelButtonStyle ?? "",
            actionButtonStyle: toastOptions?.actionButtonStyle ?? "",
            closeButtonAriaLabel: toastOptions?.closeButtonAriaLabel ?? closeButtonAriaLabel,
            expandByDefault: expand,
            expanded,
            loadingIcon: loadingIconProp,
            successIcon,
            errorIcon,
            warningIcon,
            infoIcon,
            closeIcon,
            $$slots: {
              successIcon: true,
              errorIcon: true,
              warningIcon: true,
              infoIcon: true,
              closeIcon: true
            }
          });
        }
      }
      $$payload.out.push(`<!--]--></ol>`);
    }
    $$payload.out.push(`<!--]-->`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]--></section>`);
  pop();
}
function Light_switch($$payload) {
  Button($$payload, {
    onclick: toggleMode,
    variant: "ghost",
    size: "sm",
    class: "w-9 px-0",
    children: ($$payload2) => {
      Sun($$payload2, {
        class: "h-[1.2rem] w-[1.2rem] scale-100 rotate-0 transition-all dark:scale-0 dark:-rotate-90"
      });
      $$payload2.out.push(`<!----> `);
      Moon($$payload2, {
        class: "absolute h-[1.2rem] w-[1.2rem] scale-0 rotate-90 transition-all dark:scale-100 dark:rotate-0"
      });
      $$payload2.out.push(`<!----> <span class="sr-only">Toggle theme</span>`);
    },
    $$slots: { default: true }
  });
}
function Logo($$payload, $$props) {
  push();
  let { clsName = "flex items-center justify-center", onclick } = $$props;
  $$payload.out.push(`<button${attr_class(clsx(cn$1("cursor-pointer focus:outline-none", clsName)))} type="button">`);
  Receipt_text($$payload, { class: "text-primary size-6" });
  $$payload.out.push(`<!----> <span class="ml-0 truncate text-xl font-bold"><span class="text-primary">Spen</span> <span class="text-muted-foreground -ml-1">Deed</span></span></button>`);
  pop();
}
const avatarAttrs = createBitsAttrs({ component: "avatar", parts: ["root", "image", "fallback"] });
const AvatarRootContext = new Context$1("Avatar.Root");
class AvatarRootState {
  static create(opts) {
    return AvatarRootContext.set(new AvatarRootState(opts));
  }
  opts;
  domContext;
  attachment;
  constructor(opts) {
    this.opts = opts;
    this.domContext = new DOMContext(this.opts.ref);
    this.loadImage = this.loadImage.bind(this);
    this.attachment = attachRef(this.opts.ref);
  }
  loadImage(src, crossorigin, referrerPolicy) {
    if (this.opts.loadingStatus.current === "loaded") return;
    let imageTimerId;
    const image2 = new Image();
    image2.src = src;
    if (crossorigin !== void 0) image2.crossOrigin = crossorigin;
    if (referrerPolicy) image2.referrerPolicy = referrerPolicy;
    this.opts.loadingStatus.current = "loading";
    image2.onload = () => {
      imageTimerId = this.domContext.setTimeout(
        () => {
          this.opts.loadingStatus.current = "loaded";
        },
        this.opts.delayMs.current
      );
    };
    image2.onerror = () => {
      this.opts.loadingStatus.current = "error";
    };
    return () => {
      if (!imageTimerId) return;
      this.domContext.clearTimeout(imageTimerId);
    };
  }
  #props = derived(() => ({
    id: this.opts.id.current,
    [avatarAttrs.root]: "",
    "data-status": this.opts.loadingStatus.current,
    ...this.attachment
  }));
  get props() {
    return this.#props();
  }
  set props($$value) {
    return this.#props($$value);
  }
}
class AvatarImageState {
  static create(opts) {
    return new AvatarImageState(opts, AvatarRootContext.get());
  }
  opts;
  root;
  attachment;
  constructor(opts, root) {
    this.opts = opts;
    this.root = root;
    this.attachment = attachRef(this.opts.ref);
    watch.pre(
      [
        () => this.opts.src.current,
        () => this.opts.crossOrigin.current
      ],
      ([src, crossOrigin]) => {
        if (!src) {
          this.root.opts.loadingStatus.current = "error";
          return;
        }
        this.root.loadImage(src, crossOrigin, this.opts.referrerPolicy.current);
      }
    );
  }
  #props = derived(() => ({
    id: this.opts.id.current,
    style: {
      display: this.root.opts.loadingStatus.current === "loaded" ? "block" : "none"
    },
    "data-status": this.root.opts.loadingStatus.current,
    [avatarAttrs.image]: "",
    src: this.opts.src.current,
    crossorigin: this.opts.crossOrigin.current,
    referrerpolicy: this.opts.referrerPolicy.current,
    ...this.attachment
  }));
  get props() {
    return this.#props();
  }
  set props($$value) {
    return this.#props($$value);
  }
}
class AvatarFallbackState {
  static create(opts) {
    return new AvatarFallbackState(opts, AvatarRootContext.get());
  }
  opts;
  root;
  attachment;
  constructor(opts, root) {
    this.opts = opts;
    this.root = root;
    this.attachment = attachRef(this.opts.ref);
  }
  #style = derived(() => this.root.opts.loadingStatus.current === "loaded" ? { display: "none" } : void 0);
  get style() {
    return this.#style();
  }
  set style($$value) {
    return this.#style($$value);
  }
  #props = derived(() => ({
    style: this.style,
    "data-status": this.root.opts.loadingStatus.current,
    [avatarAttrs.fallback]: "",
    ...this.attachment
  }));
  get props() {
    return this.#props();
  }
  set props($$value) {
    return this.#props($$value);
  }
}
function Avatar$1($$payload, $$props) {
  push();
  const uid = props_id($$payload);
  let {
    delayMs = 0,
    loadingStatus = "loading",
    onLoadingStatusChange,
    child,
    children,
    id = createId(uid),
    ref = null,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const rootState = AvatarRootState.create({
    delayMs: box.with(() => delayMs),
    loadingStatus: box.with(() => loadingStatus, (v) => {
      if (loadingStatus !== v) {
        loadingStatus = v;
        onLoadingStatusChange?.(v);
      }
    }),
    id: box.with(() => id),
    ref: box.with(() => ref, (v) => ref = v)
  });
  const mergedProps = mergeProps(restProps, rootState.props);
  if (child) {
    $$payload.out.push("<!--[-->");
    child($$payload, { props: mergedProps });
    $$payload.out.push(`<!---->`);
  } else {
    $$payload.out.push("<!--[!-->");
    $$payload.out.push(`<div${spread_attributes({ ...mergedProps }, null)}>`);
    children?.($$payload);
    $$payload.out.push(`<!----></div>`);
  }
  $$payload.out.push(`<!--]-->`);
  bind_props($$props, { loadingStatus, ref });
  pop();
}
function Avatar_image$1($$payload, $$props) {
  push();
  const uid = props_id($$payload);
  let {
    src,
    child,
    id = createId(uid),
    ref = null,
    crossorigin = void 0,
    referrerpolicy = void 0,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const imageState = AvatarImageState.create({
    src: box.with(() => src),
    id: box.with(() => id),
    ref: box.with(() => ref, (v) => ref = v),
    crossOrigin: box.with(() => crossorigin),
    referrerPolicy: box.with(() => referrerpolicy)
  });
  const mergedProps = mergeProps(restProps, imageState.props);
  if (child) {
    $$payload.out.push("<!--[-->");
    child($$payload, { props: mergedProps });
    $$payload.out.push(`<!---->`);
  } else {
    $$payload.out.push("<!--[!-->");
    $$payload.out.push(`<img${spread_attributes({ ...mergedProps, src }, null)} onload="this.__e=event" onerror="this.__e=event"/>`);
  }
  $$payload.out.push(`<!--]-->`);
  bind_props($$props, { ref });
  pop();
}
function Avatar_fallback$1($$payload, $$props) {
  push();
  const uid = props_id($$payload);
  let {
    children,
    child,
    id = createId(uid),
    ref = null,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const fallbackState = AvatarFallbackState.create({
    id: box.with(() => id),
    ref: box.with(() => ref, (v) => ref = v)
  });
  const mergedProps = mergeProps(restProps, fallbackState.props);
  if (child) {
    $$payload.out.push("<!--[-->");
    child($$payload, { props: mergedProps });
    $$payload.out.push(`<!---->`);
  } else {
    $$payload.out.push("<!--[!-->");
    $$payload.out.push(`<span${spread_attributes({ ...mergedProps }, null)}>`);
    children?.($$payload);
    $$payload.out.push(`<!----></span>`);
  }
  $$payload.out.push(`<!--]-->`);
  bind_props($$props, { ref });
  pop();
}
const collapsibleAttrs = createBitsAttrs({
  component: "collapsible",
  parts: ["root", "content", "trigger"]
});
const CollapsibleRootContext = new Context$1("Collapsible.Root");
class CollapsibleRootState {
  static create(opts) {
    return CollapsibleRootContext.set(new CollapsibleRootState(opts));
  }
  opts;
  attachment;
  contentNode = null;
  contentId = void 0;
  constructor(opts) {
    this.opts = opts;
    this.toggleOpen = this.toggleOpen.bind(this);
    this.attachment = attachRef(this.opts.ref);
    new OpenChangeComplete({
      ref: box.with(() => this.contentNode),
      open: this.opts.open,
      onComplete: () => {
        this.opts.onOpenChangeComplete.current(this.opts.open.current);
      }
    });
  }
  toggleOpen() {
    this.opts.open.current = !this.opts.open.current;
  }
  #props = derived(() => ({
    id: this.opts.id.current,
    "data-state": getDataOpenClosed(this.opts.open.current),
    "data-disabled": getDataDisabled(this.opts.disabled.current),
    [collapsibleAttrs.root]: "",
    ...this.attachment
  }));
  get props() {
    return this.#props();
  }
  set props($$value) {
    return this.#props($$value);
  }
}
class CollapsibleContentState {
  static create(opts) {
    return new CollapsibleContentState(opts, CollapsibleRootContext.get());
  }
  opts;
  root;
  attachment;
  #present = derived(() => this.opts.forceMount.current || this.root.opts.open.current);
  get present() {
    return this.#present();
  }
  set present($$value) {
    return this.#present($$value);
  }
  #originalStyles;
  #isMountAnimationPrevented = false;
  #width = 0;
  #height = 0;
  constructor(opts, root) {
    this.opts = opts;
    this.root = root;
    this.#isMountAnimationPrevented = root.opts.open.current;
    this.root.contentId = this.opts.id.current;
    this.attachment = attachRef(this.opts.ref, (v) => this.root.contentNode = v);
    watch.pre(() => this.opts.id.current, (id) => {
      this.root.contentId = id;
    });
    watch([() => this.opts.ref.current, () => this.present], ([node]) => {
      if (!node) return;
      afterTick(() => {
        if (!this.opts.ref.current) return;
        this.#originalStyles = this.#originalStyles || {
          transitionDuration: node.style.transitionDuration,
          animationName: node.style.animationName
        };
        node.style.transitionDuration = "0s";
        node.style.animationName = "none";
        const rect = node.getBoundingClientRect();
        this.#height = rect.height;
        this.#width = rect.width;
        if (!this.#isMountAnimationPrevented) {
          const { animationName, transitionDuration } = this.#originalStyles;
          node.style.transitionDuration = transitionDuration;
          node.style.animationName = animationName;
        }
      });
    });
  }
  #snippetProps = derived(() => ({ open: this.root.opts.open.current }));
  get snippetProps() {
    return this.#snippetProps();
  }
  set snippetProps($$value) {
    return this.#snippetProps($$value);
  }
  #props = derived(() => ({
    id: this.opts.id.current,
    style: {
      "--bits-collapsible-content-height": this.#height ? `${this.#height}px` : void 0,
      "--bits-collapsible-content-width": this.#width ? `${this.#width}px` : void 0
    },
    "data-state": getDataOpenClosed(this.root.opts.open.current),
    "data-disabled": getDataDisabled(this.root.opts.disabled.current),
    [collapsibleAttrs.content]: "",
    ...this.attachment
  }));
  get props() {
    return this.#props();
  }
  set props($$value) {
    return this.#props($$value);
  }
}
class CollapsibleTriggerState {
  static create(opts) {
    return new CollapsibleTriggerState(opts, CollapsibleRootContext.get());
  }
  opts;
  root;
  attachment;
  #isDisabled = derived(() => this.opts.disabled.current || this.root.opts.disabled.current);
  constructor(opts, root) {
    this.opts = opts;
    this.root = root;
    this.attachment = attachRef(this.opts.ref);
    this.onclick = this.onclick.bind(this);
    this.onkeydown = this.onkeydown.bind(this);
  }
  onclick(e) {
    if (this.#isDisabled()) return;
    if (e.button !== 0) return e.preventDefault();
    this.root.toggleOpen();
  }
  onkeydown(e) {
    if (this.#isDisabled()) return;
    if (e.key === SPACE || e.key === ENTER) {
      e.preventDefault();
      this.root.toggleOpen();
    }
  }
  #props = derived(() => ({
    id: this.opts.id.current,
    type: "button",
    disabled: this.#isDisabled(),
    "aria-controls": this.root.contentId,
    "aria-expanded": getAriaExpanded(this.root.opts.open.current),
    "data-state": getDataOpenClosed(this.root.opts.open.current),
    "data-disabled": getDataDisabled(this.#isDisabled()),
    [collapsibleAttrs.trigger]: "",
    //
    onclick: this.onclick,
    onkeydown: this.onkeydown,
    ...this.attachment
  }));
  get props() {
    return this.#props();
  }
  set props($$value) {
    return this.#props($$value);
  }
}
function Collapsible($$payload, $$props) {
  push();
  const uid = props_id($$payload);
  let {
    children,
    child,
    id = createId(uid),
    ref = null,
    open = false,
    disabled = false,
    onOpenChange = noop,
    onOpenChangeComplete = noop,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const rootState = CollapsibleRootState.create({
    open: box.with(() => open, (v) => {
      open = v;
      onOpenChange(v);
    }),
    disabled: box.with(() => disabled),
    id: box.with(() => id),
    ref: box.with(() => ref, (v) => ref = v),
    onOpenChangeComplete: box.with(() => onOpenChangeComplete)
  });
  const mergedProps = mergeProps(restProps, rootState.props);
  if (child) {
    $$payload.out.push("<!--[-->");
    child($$payload, { props: mergedProps });
    $$payload.out.push(`<!---->`);
  } else {
    $$payload.out.push("<!--[!-->");
    $$payload.out.push(`<div${spread_attributes({ ...mergedProps }, null)}>`);
    children?.($$payload);
    $$payload.out.push(`<!----></div>`);
  }
  $$payload.out.push(`<!--]-->`);
  bind_props($$props, { ref, open });
  pop();
}
function Collapsible_content($$payload, $$props) {
  push();
  const uid = props_id($$payload);
  let {
    child,
    ref = null,
    forceMount = false,
    children,
    id = createId(uid),
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const contentState = CollapsibleContentState.create({
    id: box.with(() => id),
    forceMount: box.with(() => forceMount),
    ref: box.with(() => ref, (v) => ref = v)
  });
  {
    let presence = function($$payload2, { present }) {
      const mergedProps = mergeProps(restProps, contentState.props, { hidden: forceMount ? void 0 : !present });
      if (child) {
        $$payload2.out.push("<!--[-->");
        child($$payload2, { ...contentState.snippetProps, props: mergedProps });
        $$payload2.out.push(`<!---->`);
      } else {
        $$payload2.out.push("<!--[!-->");
        $$payload2.out.push(`<div${spread_attributes({ ...mergedProps }, null)}>`);
        children?.($$payload2);
        $$payload2.out.push(`<!----></div>`);
      }
      $$payload2.out.push(`<!--]-->`);
    };
    Presence_layer($$payload, {
      forceMount: true,
      open: contentState.present,
      ref: contentState.opts.ref,
      presence
    });
  }
  bind_props($$props, { ref });
  pop();
}
function Collapsible_trigger($$payload, $$props) {
  push();
  const uid = props_id($$payload);
  let {
    children,
    child,
    ref = null,
    id = createId(uid),
    disabled = false,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const triggerState = CollapsibleTriggerState.create({
    id: box.with(() => id),
    ref: box.with(() => ref, (v) => ref = v),
    disabled: box.with(() => disabled)
  });
  const mergedProps = mergeProps(restProps, triggerState.props);
  if (child) {
    $$payload.out.push("<!--[-->");
    child($$payload, { props: mergedProps });
    $$payload.out.push(`<!---->`);
  } else {
    $$payload.out.push("<!--[!-->");
    $$payload.out.push(`<button${spread_attributes({ ...mergedProps }, null)}>`);
    children?.($$payload);
    $$payload.out.push(`<!----></button>`);
  }
  $$payload.out.push(`<!--]-->`);
  bind_props($$props, { ref });
  pop();
}
const PWM_BADGE_SPACE_WIDTH_PX = 40;
const PWM_BADGE_SPACE_WIDTH = `${PWM_BADGE_SPACE_WIDTH_PX}px`;
function usePasswordManagerBadge({
  containerRef,
  inputRef,
  pushPasswordManagerStrategy,
  isFocused,
  domContext
}) {
  let hasPwmBadge = false;
  function willPushPwmBadge() {
    const strategy = pushPasswordManagerStrategy.current;
    if (strategy === "none") return false;
    const increaseWidthCase = strategy === "increase-width" && hasPwmBadge;
    return increaseWidthCase;
  }
  return {
    get hasPwmBadge() {
      return hasPwmBadge;
    },
    get willPushPwmBadge() {
      return willPushPwmBadge();
    },
    PWM_BADGE_SPACE_WIDTH
  };
}
const pinInputAttrs = createBitsAttrs({ component: "pin-input", parts: ["root", "cell"] });
const KEYS_TO_IGNORE = [
  "Backspace",
  "Delete",
  "ArrowLeft",
  "ArrowRight",
  "ArrowUp",
  "ArrowDown",
  "Home",
  "End",
  "Escape",
  "Enter",
  "Tab",
  "Shift",
  "Control",
  "Meta"
];
class PinInputRootState {
  static create(opts) {
    return new PinInputRootState(opts);
  }
  opts;
  attachment;
  #inputRef = box(null);
  #isHoveringInput = false;
  inputAttachment = attachRef(this.#inputRef);
  #isFocused = box(false);
  #mirrorSelectionStart = null;
  #mirrorSelectionEnd = null;
  #previousValue = new Previous(() => this.opts.value.current ?? "");
  #regexPattern = derived(() => {
    if (typeof this.opts.pattern.current === "string") {
      return new RegExp(this.opts.pattern.current);
    } else {
      return this.opts.pattern.current;
    }
  });
  #prevInputMetadata = { prev: [null, null, "none"], willSyntheticBlur: false };
  #pwmb;
  #initialLoad;
  domContext;
  constructor(opts) {
    this.opts = opts;
    this.attachment = attachRef(this.opts.ref);
    this.domContext = new DOMContext(opts.ref);
    this.#initialLoad = {
      value: this.opts.value,
      isIOS: typeof window !== "undefined" && window?.CSS?.supports("-webkit-touch-callout", "none")
    };
    this.#pwmb = usePasswordManagerBadge({
      containerRef: this.opts.ref,
      inputRef: this.#inputRef,
      isFocused: this.#isFocused,
      pushPasswordManagerStrategy: this.opts.pushPasswordManagerStrategy,
      domContext: this.domContext
    });
    watch([() => this.opts.value.current, () => this.#inputRef.current], () => {
      syncTimeouts(
        () => {
          const input = this.#inputRef.current;
          if (!input) return;
          input.dispatchEvent(new Event("input"));
          const start = input.selectionStart;
          const end = input.selectionEnd;
          const dir = input.selectionDirection ?? "none";
          if (start !== null && end !== null) {
            this.#mirrorSelectionStart = start;
            this.#mirrorSelectionEnd = end;
            this.#prevInputMetadata.prev = [start, end, dir];
          }
        },
        this.domContext
      );
    });
  }
  onkeydown = (e) => {
    const key = e.key;
    if (KEYS_TO_IGNORE.includes(key)) return;
    if (e.ctrlKey || e.metaKey) return;
    if (key && this.#regexPattern() && !this.#regexPattern().test(key)) {
      e.preventDefault();
    }
  };
  #rootStyles = derived(() => ({
    position: "relative",
    cursor: this.opts.disabled.current ? "default" : "text",
    userSelect: "none",
    WebkitUserSelect: "none",
    pointerEvents: "none"
  }));
  #rootProps = derived(() => ({
    id: this.opts.id.current,
    [pinInputAttrs.root]: "",
    style: this.#rootStyles(),
    ...this.attachment
  }));
  get rootProps() {
    return this.#rootProps();
  }
  set rootProps($$value) {
    return this.#rootProps($$value);
  }
  #inputWrapperProps = derived(() => ({
    style: { position: "absolute", inset: 0, pointerEvents: "none" }
  }));
  get inputWrapperProps() {
    return this.#inputWrapperProps();
  }
  set inputWrapperProps($$value) {
    return this.#inputWrapperProps($$value);
  }
  #inputStyle = derived(() => ({
    position: "absolute",
    inset: 0,
    width: this.#pwmb.willPushPwmBadge ? `calc(100% + ${this.#pwmb.PWM_BADGE_SPACE_WIDTH})` : "100%",
    clipPath: this.#pwmb.willPushPwmBadge ? `inset(0 ${this.#pwmb.PWM_BADGE_SPACE_WIDTH} 0 0)` : void 0,
    height: "100%",
    display: "flex",
    textAlign: this.opts.textAlign.current,
    opacity: "1",
    color: "transparent",
    pointerEvents: "all",
    background: "transparent",
    caretColor: "transparent",
    border: "0 solid transparent",
    outline: "0 solid transparent",
    boxShadow: "none",
    lineHeight: "1",
    letterSpacing: "-.5em",
    fontSize: "var(--bits-pin-input-root-height)",
    fontFamily: "monospace",
    fontVariantNumeric: "tabular-nums"
  }));
  #applyStyles() {
    const doc = this.domContext.getDocument();
    const styleEl = doc.createElement("style");
    styleEl.id = "pin-input-style";
    doc.head.appendChild(styleEl);
    if (styleEl.sheet) {
      const autoFillStyles = "background: transparent !important; color: transparent !important; border-color: transparent !important; opacity: 0 !important; box-shadow: none !important; -webkit-box-shadow: none !important; -webkit-text-fill-color: transparent !important;";
      safeInsertRule(styleEl.sheet, "[data-pin-input-input]::selection { background: transparent !important; color: transparent !important; }");
      safeInsertRule(styleEl.sheet, `[data-pin-input-input]:autofill { ${autoFillStyles} }`);
      safeInsertRule(styleEl.sheet, `[data-pin-input-input]:-webkit-autofill { ${autoFillStyles} }`);
      safeInsertRule(styleEl.sheet, `@supports (-webkit-touch-callout: none) { [data-pin-input-input] { letter-spacing: -.6em !important; font-weight: 100 !important; font-stretch: ultra-condensed; font-optical-sizing: none !important; left: -1px !important; right: 1px !important; } }`);
      safeInsertRule(styleEl.sheet, `[data-pin-input-input] + * { pointer-events: all !important; }`);
    }
  }
  #onDocumentSelectionChange = () => {
    const input = this.#inputRef.current;
    const container = this.opts.ref.current;
    if (!input || !container) return;
    if (this.domContext.getActiveElement() !== input) {
      this.#mirrorSelectionStart = null;
      this.#mirrorSelectionEnd = null;
      return;
    }
    const selStart = input.selectionStart;
    const selEnd = input.selectionEnd;
    const selDir = input.selectionDirection ?? "none";
    const maxLength = input.maxLength;
    const val = input.value;
    const prev = this.#prevInputMetadata.prev;
    let start = -1;
    let end = -1;
    let direction;
    if (val.length !== 0 && selStart !== null && selEnd !== null) {
      const isSingleCaret = selStart === selEnd;
      const isInsertMode = selStart === val.length && val.length < maxLength;
      if (isSingleCaret && !isInsertMode) {
        const c = selStart;
        if (c === 0) {
          start = 0;
          end = 1;
          direction = "forward";
        } else if (c === maxLength) {
          start = c - 1;
          end = c;
          direction = "backward";
        } else if (maxLength > 1 && val.length > 1) {
          let offset = 0;
          if (prev[0] !== null && prev[1] !== null) {
            direction = c < prev[0] ? "backward" : "forward";
            const wasPreviouslyInserting = prev[0] === prev[1] && prev[0] < maxLength;
            if (direction === "backward" && !wasPreviouslyInserting) {
              offset = -1;
            }
          }
          start = offset - c;
          end = offset + c + 1;
        }
      }
      if (start !== -1 && end !== -1 && start !== end) {
        this.#inputRef.current?.setSelectionRange(start, end, direction);
      }
    }
    const s = start !== -1 ? start : selStart;
    const e = end !== -1 ? end : selEnd;
    const dir = direction ?? selDir;
    this.#mirrorSelectionStart = s;
    this.#mirrorSelectionEnd = e;
    this.#prevInputMetadata.prev = [s, e, dir];
  };
  oninput = (e) => {
    const newValue = e.currentTarget.value.slice(0, this.opts.maxLength.current);
    if (newValue.length > 0 && this.#regexPattern() && !this.#regexPattern().test(newValue)) {
      e.preventDefault();
      return;
    }
    const maybeHasDeleted = typeof this.#previousValue.current === "string" && newValue.length < this.#previousValue.current.length;
    if (maybeHasDeleted) {
      this.domContext.getDocument().dispatchEvent(new Event("selectionchange"));
    }
    this.opts.value.current = newValue;
  };
  onfocus = (_) => {
    const input = this.#inputRef.current;
    if (input) {
      const start = Math.min(input.value.length, this.opts.maxLength.current - 1);
      const end = input.value.length;
      input.setSelectionRange(start, end);
      this.#mirrorSelectionStart = start;
      this.#mirrorSelectionEnd = end;
    }
    this.#isFocused.current = true;
  };
  onpaste = (e) => {
    const input = this.#inputRef.current;
    if (!input) return;
    const getNewValue = (finalContent) => {
      const start = input.selectionStart === null ? void 0 : input.selectionStart;
      const end = input.selectionEnd === null ? void 0 : input.selectionEnd;
      const isReplacing = start !== end;
      const initNewVal = this.opts.value.current;
      const newValueUncapped = isReplacing ? initNewVal.slice(0, start) + finalContent + initNewVal.slice(end) : initNewVal.slice(0, start) + finalContent + initNewVal.slice(start);
      return newValueUncapped.slice(0, this.opts.maxLength.current);
    };
    const isValueInvalid = (newValue2) => {
      return newValue2.length > 0 && this.#regexPattern() && !this.#regexPattern().test(newValue2);
    };
    if (!this.opts.pasteTransformer?.current && (!this.#initialLoad.isIOS || !e.clipboardData || !input)) {
      const newValue2 = getNewValue(e.clipboardData?.getData("text/plain"));
      if (isValueInvalid(newValue2)) {
        e.preventDefault();
      }
      return;
    }
    const _content = e.clipboardData?.getData("text/plain") ?? "";
    const content = this.opts.pasteTransformer?.current ? this.opts.pasteTransformer.current(_content) : _content;
    e.preventDefault();
    const newValue = getNewValue(content);
    if (isValueInvalid(newValue)) return;
    input.value = newValue;
    this.opts.value.current = newValue;
    const selStart = Math.min(newValue.length, this.opts.maxLength.current - 1);
    const selEnd = newValue.length;
    input.setSelectionRange(selStart, selEnd);
    this.#mirrorSelectionStart = selStart;
    this.#mirrorSelectionEnd = selEnd;
  };
  onmouseover = (_) => {
    this.#isHoveringInput = true;
  };
  onmouseleave = (_) => {
    this.#isHoveringInput = false;
  };
  onblur = (_) => {
    if (this.#prevInputMetadata.willSyntheticBlur) {
      this.#prevInputMetadata.willSyntheticBlur = false;
      return;
    }
    this.#isFocused.current = false;
  };
  #inputProps = derived(() => ({
    id: this.opts.inputId.current,
    style: this.#inputStyle(),
    autocomplete: this.opts.autocomplete.current || "one-time-code",
    "data-pin-input-input": "",
    "data-pin-input-input-mss": this.#mirrorSelectionStart,
    "data-pin-input-input-mse": this.#mirrorSelectionEnd,
    inputmode: this.opts.inputmode.current,
    pattern: this.#regexPattern()?.source,
    maxlength: this.opts.maxLength.current,
    value: this.opts.value.current,
    disabled: getDisabled(this.opts.disabled.current),
    //
    onpaste: this.onpaste,
    oninput: this.oninput,
    onkeydown: this.onkeydown,
    onmouseover: this.onmouseover,
    onmouseleave: this.onmouseleave,
    onfocus: this.onfocus,
    onblur: this.onblur,
    ...this.inputAttachment
  }));
  get inputProps() {
    return this.#inputProps();
  }
  set inputProps($$value) {
    return this.#inputProps($$value);
  }
  #cells = derived(() => Array.from({ length: this.opts.maxLength.current }).map((_, idx) => {
    const isActive = this.#isFocused.current && this.#mirrorSelectionStart !== null && this.#mirrorSelectionEnd !== null && (this.#mirrorSelectionStart === this.#mirrorSelectionEnd && idx === this.#mirrorSelectionStart || idx >= this.#mirrorSelectionStart && idx < this.#mirrorSelectionEnd);
    const char = this.opts.value.current[idx] !== void 0 ? this.opts.value.current[idx] : null;
    return { char, isActive, hasFakeCaret: isActive && char === null };
  }));
  #snippetProps = derived(() => ({
    cells: this.#cells(),
    isFocused: this.#isFocused.current,
    isHovering: this.#isHoveringInput
  }));
  get snippetProps() {
    return this.#snippetProps();
  }
  set snippetProps($$value) {
    return this.#snippetProps($$value);
  }
}
class PinInputCellState {
  static create(opts) {
    return new PinInputCellState(opts);
  }
  opts;
  attachment;
  constructor(opts) {
    this.opts = opts;
    this.attachment = attachRef(this.opts.ref);
  }
  #props = derived(() => ({
    id: this.opts.id.current,
    [pinInputAttrs.cell]: "",
    "data-active": this.opts.cell.current.isActive ? "" : void 0,
    "data-inactive": !this.opts.cell.current.isActive ? "" : void 0,
    ...this.attachment
  }));
  get props() {
    return this.#props();
  }
  set props($$value) {
    return this.#props($$value);
  }
}
function syncTimeouts(cb, domContext) {
  const t1 = domContext.setTimeout(cb, 0);
  const t2 = domContext.setTimeout(cb, 10);
  const t3 = domContext.setTimeout(cb, 50);
  return [t1, t2, t3];
}
function safeInsertRule(sheet, rule) {
  try {
    sheet.insertRule(rule);
  } catch {
    console.error("pin input could not insert CSS rule:", rule);
  }
}
function Pin_input($$payload, $$props) {
  push();
  const uid = props_id($$payload);
  let {
    id = createId(uid),
    inputId = `${createId(uid)}-input`,
    ref = null,
    maxlength = 6,
    textalign = "left",
    pattern,
    inputmode = "numeric",
    onComplete = noop,
    pushPasswordManagerStrategy = "increase-width",
    class: containerClass = "",
    children,
    autocomplete = "one-time-code",
    disabled = false,
    value = "",
    onValueChange = noop,
    pasteTransformer,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const rootState = PinInputRootState.create({
    id: box.with(() => id),
    ref: box.with(() => ref, (v) => ref = v),
    inputId: box.with(() => inputId),
    autocomplete: box.with(() => autocomplete),
    maxLength: box.with(() => maxlength),
    textAlign: box.with(() => textalign),
    disabled: box.with(() => disabled),
    inputmode: box.with(() => inputmode),
    pattern: box.with(() => pattern),
    onComplete: box.with(() => onComplete),
    value: box.with(() => value, (v) => {
      value = v;
      onValueChange(v);
    }),
    pushPasswordManagerStrategy: box.with(() => pushPasswordManagerStrategy),
    pasteTransformer: box.with(() => pasteTransformer)
  });
  const mergedInputProps = mergeProps(restProps, rootState.inputProps);
  const mergedRootProps = mergeProps(rootState.rootProps, { class: containerClass });
  const mergedInputWrapperProps = mergeProps(rootState.inputWrapperProps, {});
  $$payload.out.push(`<div${spread_attributes({ ...mergedRootProps }, null)}>`);
  children?.($$payload, rootState.snippetProps);
  $$payload.out.push(`<!----> <div${spread_attributes({ ...mergedInputWrapperProps }, null)}><input${spread_attributes({ ...mergedInputProps }, null)}/></div></div>`);
  bind_props($$props, { ref, value });
  pop();
}
function Pin_input_cell($$payload, $$props) {
  push();
  const uid = props_id($$payload);
  let {
    id = createId(uid),
    ref = null,
    cell,
    child,
    children,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const cellState = PinInputCellState.create({
    id: box.with(() => id),
    ref: box.with(() => ref, (v) => ref = v),
    cell: box.with(() => cell)
  });
  const mergedProps = mergeProps(restProps, cellState.props);
  if (child) {
    $$payload.out.push("<!--[-->");
    child($$payload, { props: mergedProps });
    $$payload.out.push(`<!---->`);
  } else {
    $$payload.out.push("<!--[!-->");
    $$payload.out.push(`<div${spread_attributes({ ...mergedProps }, null)}>`);
    children?.($$payload);
    $$payload.out.push(`<!----></div>`);
  }
  $$payload.out.push(`<!--]-->`);
  bind_props($$props, { ref });
  pop();
}
function Sheet_trigger($$payload, $$props) {
  push();
  let { ref = null, $$slots, $$events, ...restProps } = $$props;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out.push(`<!---->`);
    Dialog_trigger($$payload2, spread_props([
      { "data-slot": "sheet-trigger" },
      restProps,
      {
        get ref() {
          return ref;
        },
        set ref($$value) {
          ref = $$value;
          $$settled = false;
        }
      }
    ]));
    $$payload2.out.push(`<!---->`);
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref });
  pop();
}
function Sheet_header($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    children,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  $$payload.out.push(`<div${spread_attributes(
    {
      "data-slot": "sheet-header",
      class: clsx(cn$1("flex flex-col gap-1.5 p-4", className)),
      ...restProps
    },
    null
  )}>`);
  children?.($$payload);
  $$payload.out.push(`<!----></div>`);
  bind_props($$props, { ref });
  pop();
}
function Sheet_title($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out.push(`<!---->`);
    Dialog_title($$payload2, spread_props([
      {
        "data-slot": "sheet-title",
        class: cn$1("text-foreground font-semibold", className)
      },
      restProps,
      {
        get ref() {
          return ref;
        },
        set ref($$value) {
          ref = $$value;
          $$settled = false;
        }
      }
    ]));
    $$payload2.out.push(`<!---->`);
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref });
  pop();
}
function Sheet_description($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out.push(`<!---->`);
    Dialog_description($$payload2, spread_props([
      {
        "data-slot": "sheet-description",
        class: cn$1("text-muted-foreground text-sm", className)
      },
      restProps,
      {
        get ref() {
          return ref;
        },
        set ref($$value) {
          ref = $$value;
          $$settled = false;
        }
      }
    ]));
    $$payload2.out.push(`<!---->`);
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref });
  pop();
}
const Root$1 = Dialog;
function context() {
  return getContext("__request__");
}
const page$1 = {
  get data() {
    return context().page.data;
  },
  get route() {
    return context().page.route;
  },
  get url() {
    return context().page.url;
  }
};
const page = page$1;
function Sonner_1($$payload, $$props) {
  push();
  let { $$slots, $$events, ...restProps } = $$props;
  Toaster($$payload, spread_props([
    {
      theme: derivedMode.current,
      class: "toaster group",
      style: "--normal-bg: var(--popover); --normal-text: var(--popover-foreground); --normal-border: var(--border);"
    },
    restProps
  ]));
  pop();
}
function SvelteKitTopLoader($$payload, $$props) {
  push();
  head($$payload, ($$payload2) => {
    $$payload2.out.push(`<style>
		{@html styles}
	</style>`);
  });
  pop();
}
function Layout_wrapper($$payload, $$props) {
  push();
  let { children } = $$props;
  let pageTitle = `SpenDeed | ${page.data.title}`;
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>${escape_html(pageTitle)}</title>`;
  });
  SvelteKitTopLoader($$payload);
  $$payload.out.push(`<!----> `);
  children?.($$payload);
  $$payload.out.push(`<!----> `);
  Sonner_1($$payload, { richColors: true, expand: true, position: "top-right" });
  $$payload.out.push(`<!---->`);
  pop();
}
function Footer($$payload, $$props) {
  push();
  const copyRight = getCopyRight();
  $$payload.out.push(`<footer class="flex w-full shrink-0 flex-col items-center gap-2 border-t px-4 py-6 sm:flex-row md:px-6"><p class="text-muted-foreground text-xs">${escape_html(copyRight)}</p> <nav class="flex gap-4 sm:ml-auto sm:gap-6"><a class="text-xs underline-offset-4 hover:underline" href="/tos">Terms of Service</a> <a class="text-xs underline-offset-4 hover:underline" href="/privacy">Privacy</a></nav></footer>`);
  pop();
}
function Header($$payload, $$props) {
  push();
  const utilsStore = getUtilsStore();
  const doLogin = () => {
    utilsStore.setHomeSheet(SheetTypes.Login);
  };
  const doSignUp = () => {
    utilsStore.setHomeSheet(SheetTypes.SignUp);
  };
  $$payload.out.push(`<header class="bg-background sticky -top-1 z-50 border-b"><div class="container mx-auto flex h-16 items-center justify-between px-2 lg:px-4">`);
  Logo($$payload, {});
  $$payload.out.push(`<!----> `);
  Button($$payload, {
    variant: "ghost",
    size: "icon",
    class: "md:hidden",
    children: ($$payload2) => {
      Menu($$payload2, { class: "size-6" });
      $$payload2.out.push(`<!----> <span class="sr-only">Toggle menu</span>`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!----> <nav class="hidden flex-1 justify-center gap-4 sm:gap-6 md:flex"><a class="text-sm font-medium underline-offset-4 hover:underline" href="#features">Features</a> <a class="text-sm font-medium underline-offset-4 hover:underline" href="#how-it-works">How It Works</a> <a class="text-sm font-medium underline-offset-4 hover:underline" href="#pricing">Pricing</a></nav> <div class="flex items-center gap-2">`);
  Button($$payload, {
    variant: "outline",
    size: "sm",
    onclick: doLogin,
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->Login`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!----> `);
  Button($$payload, {
    size: "sm",
    onclick: doSignUp,
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->Sign Up`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!----> `);
  Light_switch($$payload);
  $$payload.out.push(`<!----></div></div></header>`);
  pop();
}
function How($$payload) {
  $$payload.out.push(`<section id="how-it-works" class="xl:py-26 bg-background w-full py-4 md:py-12 lg:py-20"><div class="container px-4 md:px-4"><p class="section-heading mb-8 text-center font-bold">How It Works</p> <div class="grid gap-6 lg:grid-cols-3"><div class="bg-card text-card-foreground rounded-lg border shadow-sm"><div class="flex flex-col items-center space-y-4 p-6 text-center"><div class="bg-primary text-primary-foreground rounded-full p-3">`);
  Shopping_bag($$payload, { class: "size-6" });
  $$payload.out.push(`<!----></div> <h3 class="text-lg font-semibold">1. Set Up Your Store</h3> <p>Add your products or services and customize your rewards program.</p></div></div> <div class="bg-card text-card-foreground rounded-lg border shadow-sm"><div class="flex flex-col items-center space-y-4 p-6 text-center"><div class="bg-primary text-primary-foreground rounded-full p-3">`);
  Message_square($$payload, { class: "size-6" });
  $$payload.out.push(`<!----></div> <h3 class="text-lg font-semibold">2. Engage Customers</h3> <p>Use AI-powered chat to interact with customers and showcase products.</p></div></div> <div class="bg-card text-card-foreground rounded-lg border shadow-sm"><div class="flex flex-col items-center space-y-4 p-6 text-center"><div class="bg-primary text-primary-foreground rounded-full p-3">`);
  Credit_card($$payload, { class: "size-6" });
  $$payload.out.push(`<!----></div> <h3 class="text-lg font-semibold">3. Grow Your Business</h3> <p>Increase sales through subscriptions and AI-recommended products.</p></div></div></div></div></section>`);
}
function Cta($$payload, $$props) {
  push();
  const utilsStore = getUtilsStore();
  const openSignupModel = () => {
    utilsStore.setHomeSheet(SheetTypes.SignUp);
  };
  $$payload.out.push(`<section class="bg-muted/40 w-full py-8 md:py-20 lg:py-28"><div class="container px-4 md:px-6"><div class="flex flex-col items-center space-y-4 text-center"><div class="space-y-2"><p class="section-heading">Transform Your Business Today</p> <p class="text-muted-foreground mx-auto max-w-[600px] md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">Start using SpenDeed now and see the difference in your customer engagement and
					sales.</p></div> `);
  Button($$payload, {
    onclick: openSignupModel,
    size: "lg",
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->Sign Up Now `);
      Arrow_right($$payload2, { class: "ml-2 size-4" });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!----></div></div></section>`);
  pop();
}
function Pricing_card($$payload, $$props) {
  let { title, price, features, buttonText } = $$props;
  const each_array = ensure_array_like(features);
  $$payload.out.push(`<div class="bg-card text-card-foreground rounded-lg border shadow-sm"><div class="flex flex-col items-center p-6"><h3 class="mb-2 text-2xl font-bold">${escape_html(title)}</h3> <p class="mb-4 text-4xl font-bold">${escape_html(price)}</p> <ul class="mb-6 space-y-2 text-sm"><!--[-->`);
  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
    let feature = each_array[$$index];
    $$payload.out.push(`<li>${escape_html(feature)}</li>`);
  }
  $$payload.out.push(`<!--]--></ul> `);
  Button($$payload, {
    class: "w-full",
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->${escape_html(buttonText)}`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!----></div></div>`);
}
function Pricing($$payload) {
  const pricingPlans = [
    {
      title: "Starter",
      price: "R529/mo",
      features: [
        "Up to 500 customers",
        "Basic reward programs",
        "AI chat support"
      ],
      buttonText: "Get Started"
    },
    {
      title: "Pro",
      price: "R1,439/mo",
      features: [
        "Up to 2,000 customers",
        "Advanced reward programs",
        "AI chat with product recommendations"
      ],
      buttonText: "Get Started"
    },
    {
      title: "Enterprise",
      price: "Custom",
      features: [
        "Unlimited customers",
        "Custom reward programs",
        "Advanced AI features"
      ],
      buttonText: "Contact Sales"
    }
  ];
  const each_array = ensure_array_like(pricingPlans);
  $$payload.out.push(`<section id="pricing" class="bg-background w-full py-4 md:py-12 lg:py-26 xl:py-20"><div class="container px-4 md:px-6"><p class="section-heading mb-8 text-center">Simple, Transparent Pricing</p> <div class="grid gap-6 lg:grid-cols-3"><!--[-->`);
  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
    let plan = each_array[$$index];
    Pricing_card($$payload, {
      title: plan.title,
      price: plan.price,
      features: plan.features,
      buttonText: plan.buttonText
    });
  }
  $$payload.out.push(`<!--]--></div></div></section>`);
}
function Hero($$payload) {
  $$payload.out.push(`<section class="bg-background lg:py-26 w-full py-4 md:py-12 xl:py-20"><div class="container px-4 md:px-6"><div class="flex flex-col items-center space-y-4 text-center"><div class="space-y-2"><h1 class="section-heading">Empower Your Business with AI-Driven Customer Engagement</h1> <p class="text-muted-foreground mx-auto max-w-[700px] md:text-lg">SpenDeed helps small businesses boost customer loyalty, increase sales, and provide
					personalized experiences through AI-powered rewards, subscriptions, and chat.</p> <p class="text-muted-foreground mx-auto max-w-[700px] md:text-lg">A Good Spend In Deed</p></div> <div class="space-x-4">`);
  Button($$payload, {
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->Get Started `);
      Arrow_right($$payload2, { class: "ml-2 size-4" });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!----> `);
  Button($$payload, {
    variant: "outline",
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->Learn More`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!----></div></div></div></section>`);
}
function Features($$payload) {
  const featureList = [
    {
      icon: Gift,
      title: "Customizable Rewards",
      description: "Create and manage tailored reward programs to boost customer loyalty."
    },
    {
      icon: Credit_card,
      title: "Subscription Management",
      description: "Easily set up and manage subscriptions for products or services."
    },
    {
      icon: Message_square,
      title: "AI Sales Assistant",
      description: "Engage customers with an intelligent AI assistant for support and sales."
    },
    {
      icon: Clock,
      title: "Appointment Scheduling",
      description: "Allow customers to easily schedule appointments with your business."
    },
    {
      icon: Mail,
      title: "Email Marketing",
      description: "Reach all your customers with targeted email marketing campaigns."
    },
    {
      icon: Users,
      title: "Fluence",
      description: "A unique affiliate system that empowers your customers to promote your business."
    }
  ];
  const each_array = ensure_array_like(featureList);
  $$payload.out.push(`<section id="features" class="lg:py-26 w-full bg-muted/40 py-4 md:py-12 xl:py-20"><div class="container px-4 md:px-6"><p class="section-heading mb-8 text-center">Key Features</p> <div class="grid gap-10 sm:grid-cols-2 lg:grid-cols-3"><!--[-->`);
  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
    let feature = each_array[$$index];
    $$payload.out.push(`<div class="flex flex-col items-center text-center"><!---->`);
    feature.icon($$payload, { class: "mb-4 h-10 w-10 text-primary" });
    $$payload.out.push(`<!----> <h3 class="mb-2 text-lg font-semibold">${escape_html(feature.title)}</h3> <p class="text-muted-foreground">${escape_html(feature.description)}</p></div>`);
  }
  $$payload.out.push(`<!--]--></div></div></section>`);
}
function Nudge($$payload) {
  $$payload.out.push(`<section class="bg- bg-muted/40 lg:py-26 w-full py-4 md:py-12 xl:py-20"><div class="container px-4 md:px-6"><div class="flex flex-col items-center space-y-4 text-center"><div class="space-y-2"><p class="section-heading">Ready to Grow Your Business?</p> <p class="text-muted-foreground mx-auto max-w-[600px] md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">Join thousands of small businesses using SpenDeed to boost customer engagement and
					increase sales.</p></div> <div class="w-full max-w-sm space-y-2"><form class="flex space-x-2">`);
  Input($$payload, {
    class: "bg-primary max-w-lg flex-1",
    placeholder: "Enter your email",
    type: "email"
  });
  $$payload.out.push(`<!----> `);
  Button($$payload, {
    type: "submit",
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->Get Started `);
      Arrow_right($$payload2, { class: "ml-2 size-4" });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!----></form> <p class="mt-4 text-sm">Start your 14-day free trial. No credit card required.</p></div></div></div></section>`);
}
function Marketing_sheet($$payload) {
  Header($$payload);
  $$payload.out.push(`<!----> <main class="flex-1 pt-16">`);
  Hero($$payload);
  $$payload.out.push(`<!----> `);
  Features($$payload);
  $$payload.out.push(`<!----> `);
  How($$payload);
  $$payload.out.push(`<!----> `);
  Cta($$payload);
  $$payload.out.push(`<!----> `);
  Pricing($$payload);
  $$payload.out.push(`<!----> `);
  Nudge($$payload);
  $$payload.out.push(`<!----></main> `);
  Footer($$payload);
  $$payload.out.push(`<!---->`);
}
function setError(form, path, error, options) {
  if (options === void 0)
    options = {};
  const errArr = Array.isArray(error) ? error : [error];
  if (!form.errors)
    form.errors = {};
  {
    if (!form.errors._errors)
      form.errors._errors = [];
    form.errors._errors = options.overwrite ? errArr : form.errors._errors.concat(errArr);
  }
  form.valid = false;
  const output = options.removeFiles === false ? { form } : withFiles({ form });
  return fail(options.status ?? 400, output);
}
function withFiles(obj) {
  if (typeof obj !== "object")
    return obj;
  for (const key in obj) {
    const value = obj[key];
    if (value instanceof File)
      delete obj[key];
    else if (value && typeof value === "object")
      withFiles(value);
  }
  return obj;
}
function Auth_wrapper($$payload, $$props) {
  push();
  let {
    children,
    pageTitle,
    pageDescription,
    containerWidth = "w-[350px]"
  } = $$props;
  const utilsStore = getUtilsStore();
  const switchToHome = () => {
    utilsStore.setHomeSheet(SheetTypes.Marketing);
  };
  $$payload.out.push(`<div class="container grid h-screen w-full flex-col items-center justify-center lg:max-w-none lg:px-0">`);
  Button($$payload, {
    onclick: switchToHome,
    variant: "ghost",
    class: "absolute top-4 left-4 md:top-8 md:left-8",
    children: ($$payload2) => {
      Chevron_left($$payload2, { className: "mr-2 size-4" });
      $$payload2.out.push(`<!----> Home`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!----> <div class="!fixed right-8 bottom-2 z-500">`);
  Light_switch($$payload);
  $$payload.out.push(`<!----></div> <div class="lg:p-8"><div${attr_class(clsx(cn$1("mx-auto flex w-full flex-col justify-center space-y-6", containerWidth)))}><div class="flex flex-col space-y-2 text-center">`);
  Logo($$payload, { onclick: switchToHome });
  $$payload.out.push(`<!----> <h1 class="mt-3 text-lg font-semibold tracking-tight">${escape_html(pageTitle)}</h1> <p class="text-muted-foreground text-sm">${escape_html(pageDescription)}</p></div> `);
  children?.($$payload);
  $$payload.out.push(`<!----></div></div></div>`);
  pop();
}
function Login($$payload, $$props) {
  push();
  var $$store_subs;
  let passKeyEnabled = false;
  let currentRoute = page.url.pathname ?? "/";
  const utilsStore = getUtilsStore();
  const authStore = getAuthStore();
  const form = superForm(defaults(valibot(loginFormSchema)), {
    id: "login-form",
    validators: valibot(loginFormSchema),
    multipleSubmits: "prevent",
    SPA: true,
    onUpdate: async ({ form: form2, cancel }) => {
      if (!form2.valid) return;
      try {
        const formData2 = form2.data;
        if (authStrategy === "passkey" && passKeyEnabled) ;
        else {
          await authClient.emailOtp.sendVerificationOtp({ email: formData2.email, type: "sign-in" }, {
            onSuccess(context2) {
              utilsStore.setCurrentAuthEmail(formData2.email);
              utilsStore.st.homeSheet = {
                authingUserEmail: formData2.email,
                authingUserId: void 0,
                passkeyEnabled: passKeyEnabled
                // Store passkey preference for potential registration
              };
              utilsStore.setHomeSheet(SheetTypes.LoginCode);
              toast.success("Please check your email for your sign-in code.");
            },
            onError(context2) {
              console.error(context2.error.message);
              toast.error(`Failed to send OTP: ${context2.error.message}`);
              cancel();
            }
          });
        }
      } catch (err) {
        console.error("Login error:", err);
        toast.error(`Login failed: ${err.message}`);
        cancel();
      }
    },
    ...getFlashModule()
  });
  const { form: formData, enhance, submitting, message, allErrors } = form;
  const switchToSignUp = () => {
    utilsStore.setHomeSheet(SheetTypes.SignUp);
  };
  let loginButtonText = "Log In";
  let authStrategy = "email";
  let submittingPasskey = (() => store_get($$store_subs ??= {}, "$submitting", submitting) && authStrategy === "passkey")();
  let readonlyEmail = /* @__PURE__ */ (() => submittingPasskey || void 0)();
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    Auth_wrapper($$payload2, {
      pageTitle: "Welcome back",
      pageDescription: "Sign in to your account",
      children: ($$payload3) => {
        $$payload3.out.push(`<div class="grid gap-6"><!---->`);
        Tabs($$payload3, {
          class: "w-full",
          get value() {
            return authStrategy;
          },
          set value($$value) {
            authStrategy = $$value;
            $$settled = false;
          },
          children: ($$payload4) => {
            $$payload4.out.push(`<!---->`);
            Tabs_list($$payload4, {
              class: "grid w-full grid-cols-2",
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->`);
                Tabs_trigger($$payload5, {
                  value: "email",
                  class: "flex items-center gap-2",
                  children: ($$payload6) => {
                    Mail($$payload6, { class: "h-4 w-4" });
                    $$payload6.out.push(`<!----> Email OTP`);
                  },
                  $$slots: { default: true }
                });
                $$payload5.out.push(`<!----> <!---->`);
                Tabs_trigger($$payload5, {
                  value: "passkey",
                  disabled: !passKeyEnabled,
                  class: `flex items-center gap-2 ${stringify("opacity-50")}`,
                  children: ($$payload6) => {
                    Key_round($$payload6, { class: "h-4 w-4" });
                    $$payload6.out.push(`<!----> Passkey`);
                  },
                  $$slots: { default: true }
                });
                $$payload5.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!---->`);
          },
          $$slots: { default: true }
        });
        $$payload3.out.push(`<!----> <form method="POST" class="space-y-6">`);
        Form_alerts($$payload3, { message });
        $$payload3.out.push(`<!----> <fieldset${attr("disabled", store_get($$store_subs ??= {}, "$submitting", submitting), true)} class="grid gap-2"><!---->`);
        Form_field($$payload3, {
          form,
          name: "email",
          children: ($$payload4) => {
            $$payload4.out.push(`<!---->`);
            {
              let children = function($$payload5, { props }) {
                $$payload5.out.push(`<!---->`);
                Form_label($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out.push(`<!---->Email`);
                  },
                  $$slots: { default: true }
                });
                $$payload5.out.push(`<!----> `);
                Input($$payload5, spread_props([
                  props,
                  {
                    autocomplete: "email webauthn",
                    readonly: readonlyEmail,
                    get value() {
                      return store_get($$store_subs ??= {}, "$formData", formData).email;
                    },
                    set value($$value) {
                      store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).email = $$value);
                      $$settled = false;
                    }
                  }
                ]));
                $$payload5.out.push(`<!---->`);
              };
              Control($$payload4, { children });
            }
            $$payload4.out.push(`<!----> <!---->`);
            Form_field_errors($$payload4, {});
            $$payload4.out.push(`<!---->`);
          },
          $$slots: { default: true }
        });
        $$payload3.out.push(`<!----> <!---->`);
        Form_field($$payload3, {
          form,
          name: "returnUrl",
          children: ($$payload4) => {
            $$payload4.out.push(`<!---->`);
            {
              let children = function($$payload5, { props }) {
                Hidden_input($$payload5, { props, value: currentRoute });
              };
              Control($$payload4, { children });
            }
            $$payload4.out.push(`<!----> <!---->`);
            Form_field_errors($$payload4, {});
            $$payload4.out.push(`<!---->`);
          },
          $$slots: { default: true }
        });
        $$payload3.out.push(`<!----> <!---->`);
        Form_button($$payload3, {
          disabled: submittingPasskey || store_get($$store_subs ??= {}, "$allErrors", allErrors)?.length > 0,
          children: ($$payload4) => {
            if (submittingPasskey) {
              $$payload4.out.push("<!--[-->");
              Loader_circle($$payload4, { class: "mr-2 size-4 animate-spin" });
            } else {
              $$payload4.out.push("<!--[!-->");
            }
            $$payload4.out.push(`<!--]--> ${escape_html(submittingPasskey ? "Please wait..." : loginButtonText)}`);
          },
          $$slots: { default: true }
        });
        $$payload3.out.push(`<!----></fieldset></form> <div class="relative"><p class="text-muted-foreground px-8 text-center text-sm">`);
        Button($$payload3, {
          variant: "link",
          onclick: switchToSignUp,
          class: "hover:text-brand no-underline",
          children: ($$payload4) => {
            $$payload4.out.push(`<!---->Don't have an account? Sign Up`);
          },
          $$slots: { default: true }
        });
        $$payload3.out.push(`<!----></p></div></div>`);
      }
    });
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
function Passkey($$payload, $$props) {
  let { $$slots, $$events, ...rest } = $$props;
  $$payload.out.push(`<svg${spread_attributes({ role: "img", viewBox: "0 0 24 24", ...rest }, null, void 0, void 0, 3)}><circle cx="10.5" cy="6" r="4.5"></circle><path d="M22.5 10.5a3.5 3.5 0 1 0-5 3.15V19l1.5 1.5 2.5-2.5-1.5-1.5 1.5-1.5-1.24-1.24a3.5 3.5 0 0 0 2.24-3.26m-3.5 0a1 1 0 1 1 1-1 1 1 0 0 1-1 1M14.44 12.52A6 6 0 0 0 12 12H9a6 6 0 0 0-6 6v2h13v-5.51a5.16 5.16 0 0 1-1.56-1.97"></path></svg>`);
}
function Textarea($$payload, $$props) {
  push();
  let {
    ref = null,
    value = void 0,
    class: className,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  $$payload.out.push(`<textarea${spread_attributes(
    {
      "data-slot": "textarea",
      class: clsx(cn$1("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 field-sizing-content shadow-xs flex min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base outline-none transition-[color,box-shadow] focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm", className)),
      ...restProps
    },
    null
  )}>`);
  const $$body = escape_html(value);
  if ($$body) {
    $$payload.out.push(`${$$body}`);
  }
  $$payload.out.push(`</textarea>`);
  bind_props($$props, { ref, value });
  pop();
}
function Signup($$payload, $$props) {
  push();
  var $$store_subs;
  const utilsStore = getUtilsStore();
  const form = superForm(defaults({}, valibotClient(signUpFormSchema)), {
    id: "signup-form",
    validators: valibotClient(signUpFormSchema),
    multipleSubmits: "prevent",
    dataType: "json",
    SPA: true,
    onUpdate: async ({ form: form2, cancel }) => {
      form2.data.authType = "email";
      form2.data.acceptTerms = true;
      if (!form2.valid) {
        setError(form2, "", "Please fill in all fields");
        cancel();
      }
      const formData2 = form2.data;
      utilsStore.setCurrentAuthEmail(formData2.email);
      try {
        const avatarUrl = await image(formData2.email, {
          size: 200,
          defaultImage: "blank",
          includeExtention: true,
          rating: "g"
        });
        const organizationId = uuidv7();
        const metadata = {
          businessName: formData2.businessName,
          businessEmail: formData2.businessEmail,
          organizationId,
          businessAddress: formData2.businessAddress
        };
        const signUpPayload = {
          email: formData2.email,
          password: generateRandomPassword(9),
          name: `${formData2.givenName} ${formData2.familyName}`,
          image: avatarUrl,
          callbackURL: window.location.origin + "/signup-code",
          // Additional user fields
          givenName: formData2.givenName,
          familyName: formData2.familyName,
          displayName: `${formData2.givenName} ${formData2.familyName}`,
          phoneNumber: formData2.phoneNumber || "",
          userStatus: "Active",
          roles: [RolesType.User, RolesType.OrgAdmin],
          phoneNumberVerified: false,
          acceptTerms: formData2.acceptTerms,
          userType: UserAccountTypes.Staff,
          authType: "passkey",
          avatarUrl,
          locale: "en",
          metadata: JSON.stringify(metadata)
        };
        await authClient.signUp.email(signUpPayload, {
          onSuccess: (signUpData) => {
            if (signUpData) {
              utilsStore.st.homeSheet = {
                authingUserId: signUpData?.user?.id,
                authingUserEmail: formData2.email,
                passkeyEnabled: false
              };
              saveEmailLocally(formData2.email);
              utilsStore.setHomeSheet(SheetTypes.SignUpCode);
              toast.success("Signup successful! Please check your email for verification code.");
            } else {
              toast.error("Signup failed: No signup data received");
              cancel();
            }
          },
          onError: (signUpError) => {
            console.error(signUpError.error.message);
            toast.error(`Signup failed: ${signUpError.error.message}`);
            cancel();
          }
        });
      } catch (err) {
        console.error("Signup error:", err);
        toast.error(`Signup failed: ${err?.message}`);
        cancel();
      }
    },
    ...getFlashModule()
  });
  const { form: formData, enhance, submitting, message, allErrors } = form;
  const switchToLogin = () => {
    utilsStore.setHomeSheet(SheetTypes.Login);
  };
  let signupButtonText = "Sign Up";
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    Auth_wrapper($$payload2, {
      pageTitle: "Sign Up",
      pageDescription: "Create your account",
      children: ($$payload3) => {
        $$payload3.out.push(`<div class="grid gap-6"><form method="POST" class="space-y-6">`);
        Form_alerts($$payload3, { message });
        $$payload3.out.push(`<!----> <fieldset${attr("disabled", store_get($$store_subs ??= {}, "$submitting", submitting), true)} class="grid gap-2"><!---->`);
        Form_field($$payload3, {
          form,
          name: "givenName",
          children: ($$payload4) => {
            $$payload4.out.push(`<!---->`);
            {
              let children = function($$payload5, { props }) {
                $$payload5.out.push(`<!---->`);
                Form_label($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out.push(`<!---->First Name`);
                  },
                  $$slots: { default: true }
                });
                $$payload5.out.push(`<!----> `);
                Input($$payload5, spread_props([
                  props,
                  {
                    placeholder: "First Name",
                    get value() {
                      return store_get($$store_subs ??= {}, "$formData", formData).givenName;
                    },
                    set value($$value) {
                      store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).givenName = $$value);
                      $$settled = false;
                    }
                  }
                ]));
                $$payload5.out.push(`<!---->`);
              };
              Control($$payload4, { children });
            }
            $$payload4.out.push(`<!----> <!---->`);
            Form_field_errors($$payload4, {});
            $$payload4.out.push(`<!---->`);
          },
          $$slots: { default: true }
        });
        $$payload3.out.push(`<!----> <!---->`);
        Form_field($$payload3, {
          form,
          name: "familyName",
          children: ($$payload4) => {
            $$payload4.out.push(`<!---->`);
            {
              let children = function($$payload5, { props }) {
                $$payload5.out.push(`<!---->`);
                Form_label($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out.push(`<!---->Last Name`);
                  },
                  $$slots: { default: true }
                });
                $$payload5.out.push(`<!----> `);
                Input($$payload5, spread_props([
                  props,
                  {
                    placeholder: "Last Name",
                    autocomplete: "family-name",
                    get value() {
                      return store_get($$store_subs ??= {}, "$formData", formData).familyName;
                    },
                    set value($$value) {
                      store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).familyName = $$value);
                      $$settled = false;
                    }
                  }
                ]));
                $$payload5.out.push(`<!---->`);
              };
              Control($$payload4, { children });
            }
            $$payload4.out.push(`<!----> <!---->`);
            Form_field_errors($$payload4, {});
            $$payload4.out.push(`<!---->`);
          },
          $$slots: { default: true }
        });
        $$payload3.out.push(`<!----> <!---->`);
        Form_field($$payload3, {
          form,
          name: "email",
          children: ($$payload4) => {
            $$payload4.out.push(`<!---->`);
            {
              let children = function($$payload5, { props }) {
                $$payload5.out.push(`<!---->`);
                Form_label($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out.push(`<!---->Email`);
                  },
                  $$slots: { default: true }
                });
                $$payload5.out.push(`<!----> `);
                Input($$payload5, spread_props([
                  { type: "email" },
                  props,
                  {
                    placeholder: "Email",
                    autocomplete: "email",
                    get value() {
                      return store_get($$store_subs ??= {}, "$formData", formData).email;
                    },
                    set value($$value) {
                      store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).email = $$value);
                      $$settled = false;
                    }
                  }
                ]));
                $$payload5.out.push(`<!---->`);
              };
              Control($$payload4, { children });
            }
            $$payload4.out.push(`<!----> <!---->`);
            Form_field_errors($$payload4, {});
            $$payload4.out.push(`<!---->`);
          },
          $$slots: { default: true }
        });
        $$payload3.out.push(`<!----> <!---->`);
        Form_field($$payload3, {
          form,
          name: "businessName",
          children: ($$payload4) => {
            $$payload4.out.push(`<!---->`);
            {
              let children = function($$payload5, { props }) {
                $$payload5.out.push(`<!---->`);
                Form_label($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out.push(`<!---->Business Name`);
                  },
                  $$slots: { default: true }
                });
                $$payload5.out.push(`<!----> `);
                Input($$payload5, spread_props([
                  props,
                  {
                    placeholder: "Business Name",
                    get value() {
                      return store_get($$store_subs ??= {}, "$formData", formData).businessName;
                    },
                    set value($$value) {
                      store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).businessName = $$value);
                      $$settled = false;
                    }
                  }
                ]));
                $$payload5.out.push(`<!---->`);
              };
              Control($$payload4, { children });
            }
            $$payload4.out.push(`<!----> <!---->`);
            Form_field_errors($$payload4, {});
            $$payload4.out.push(`<!---->`);
          },
          $$slots: { default: true }
        });
        $$payload3.out.push(`<!----> <!---->`);
        Form_field($$payload3, {
          form,
          name: "businessEmail",
          children: ($$payload4) => {
            $$payload4.out.push(`<!---->`);
            {
              let children = function($$payload5, { props }) {
                $$payload5.out.push(`<!---->`);
                Form_label($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out.push(`<!---->Business Email`);
                  },
                  $$slots: { default: true }
                });
                $$payload5.out.push(`<!----> `);
                Input($$payload5, spread_props([
                  { type: "email" },
                  props,
                  {
                    placeholder: "Email",
                    autocomplete: "email",
                    get value() {
                      return store_get($$store_subs ??= {}, "$formData", formData).businessEmail;
                    },
                    set value($$value) {
                      store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).businessEmail = $$value);
                      $$settled = false;
                    }
                  }
                ]));
                $$payload5.out.push(`<!---->`);
              };
              Control($$payload4, { children });
            }
            $$payload4.out.push(`<!----> <!---->`);
            Form_field_errors($$payload4, {});
            $$payload4.out.push(`<!---->`);
          },
          $$slots: { default: true }
        });
        $$payload3.out.push(`<!----> <!---->`);
        Form_field($$payload3, {
          form,
          name: "phoneNumber",
          children: ($$payload4) => {
            $$payload4.out.push(`<!---->`);
            {
              let children = function($$payload5, { props }) {
                $$payload5.out.push(`<!---->`);
                Form_label($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out.push(`<!---->Phone Number`);
                  },
                  $$slots: { default: true }
                });
                $$payload5.out.push(`<!----> `);
                Input($$payload5, spread_props([
                  props,
                  {
                    placeholder: "Phone Number",
                    get value() {
                      return store_get($$store_subs ??= {}, "$formData", formData).phoneNumber;
                    },
                    set value($$value) {
                      store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).phoneNumber = $$value);
                      $$settled = false;
                    }
                  }
                ]));
                $$payload5.out.push(`<!---->`);
              };
              Control($$payload4, { children });
            }
            $$payload4.out.push(`<!----> <!---->`);
            Form_field_errors($$payload4, {});
            $$payload4.out.push(`<!---->`);
          },
          $$slots: { default: true }
        });
        $$payload3.out.push(`<!----> <!---->`);
        Form_field($$payload3, {
          form,
          name: "businessAddress",
          children: ($$payload4) => {
            $$payload4.out.push(`<!---->`);
            {
              let children = function($$payload5, { props }) {
                $$payload5.out.push(`<!---->`);
                Form_label($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out.push(`<!---->Business Address`);
                  },
                  $$slots: { default: true }
                });
                $$payload5.out.push(`<!----> `);
                Textarea($$payload5, spread_props([
                  props,
                  {
                    placeholder: "Business Address",
                    get value() {
                      return store_get($$store_subs ??= {}, "$formData", formData).businessAddress;
                    },
                    set value($$value) {
                      store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).businessAddress = $$value);
                      $$settled = false;
                    }
                  }
                ]));
                $$payload5.out.push(`<!---->`);
              };
              Control($$payload4, { children });
            }
            $$payload4.out.push(`<!----> <!---->`);
            Form_field_errors($$payload4, {});
            $$payload4.out.push(`<!---->`);
          },
          $$slots: { default: true }
        });
        $$payload3.out.push(`<!----></fieldset> <!---->`);
        Form_button($$payload3, {
          disabled: store_get($$store_subs ??= {}, "$submitting", submitting) || store_get($$store_subs ??= {}, "$allErrors", allErrors)?.length > 0,
          class: "mt-4 w-full",
          children: ($$payload4) => {
            if (store_get($$store_subs ??= {}, "$submitting", submitting)) {
              $$payload4.out.push("<!--[-->");
              Passkey($$payload4, { class: "animate-caret-blink size-5 fill-current" });
            } else {
              $$payload4.out.push("<!--[!-->");
            }
            $$payload4.out.push(`<!--]--> ${escape_html(store_get($$store_subs ??= {}, "$submitting", submitting) ? "Please wait..." : signupButtonText)}`);
          },
          $$slots: { default: true }
        });
        $$payload3.out.push(`<!----></form> <div class="relative"><p class="text-muted-foreground px-8 text-center text-sm">`);
        Button($$payload3, {
          variant: "link",
          onclick: switchToLogin,
          class: "hover:text-brand no-underline",
          children: ($$payload4) => {
            $$payload4.out.push(`<!---->Already have an account? Sign In`);
          },
          $$slots: { default: true }
        });
        $$payload3.out.push(`<!----></p></div></div>`);
      }
    });
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
function Input_otp($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    value = "",
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out.push(`<!---->`);
    Pin_input($$payload2, spread_props([
      {
        "data-slot": "input-otp",
        class: cn$1("has-disabled:opacity-50 flex items-center gap-2 [&_input]:disabled:cursor-not-allowed", className)
      },
      restProps,
      {
        get ref() {
          return ref;
        },
        set ref($$value) {
          ref = $$value;
          $$settled = false;
        },
        get value() {
          return value;
        },
        set value($$value) {
          value = $$value;
          $$settled = false;
        }
      }
    ]));
    $$payload2.out.push(`<!---->`);
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref, value });
  pop();
}
function Input_otp_group($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    children,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  $$payload.out.push(`<div${spread_attributes(
    {
      "data-slot": "input-otp-group",
      class: clsx(cn$1("flex items-center", className)),
      ...restProps
    },
    null
  )}>`);
  children?.($$payload);
  $$payload.out.push(`<!----></div>`);
  bind_props($$props, { ref });
  pop();
}
function Input_otp_slot($$payload, $$props) {
  push();
  let {
    ref = null,
    cell,
    class: className,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out.push(`<!---->`);
    Pin_input_cell($$payload2, spread_props([
      {
        cell,
        "data-slot": "input-otp-slot",
        class: cn$1("border-input aria-invalid:border-destructive dark:bg-input/30 relative flex size-10 items-center justify-center border-y border-r text-sm outline-none transition-all first:rounded-l-md first:border-l last:rounded-r-md", cell.isActive && "border-ring ring-ring/50 aria-invalid:border-destructive dark:aria-invalid:ring-destructive/40 aria-invalid:ring-destructive/20 ring-offset-background z-10 ring-[3px]", className)
      },
      restProps,
      {
        get ref() {
          return ref;
        },
        set ref($$value) {
          ref = $$value;
          $$settled = false;
        },
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->${escape_html(cell.char)} `);
          if (cell.hasFakeCaret) {
            $$payload3.out.push("<!--[-->");
            $$payload3.out.push(`<div class="pointer-events-none absolute inset-0 flex items-center justify-center"><div class="animate-caret-blink bg-foreground h-4 w-px duration-1000"></div></div>`);
          } else {
            $$payload3.out.push("<!--[!-->");
          }
          $$payload3.out.push(`<!--]-->`);
        },
        $$slots: { default: true }
      }
    ]));
    $$payload2.out.push(`<!---->`);
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref });
  pop();
}
function Input_otp_separator($$payload, $$props) {
  push();
  let { ref = null, children, $$slots, $$events, ...restProps } = $$props;
  $$payload.out.push(`<div${spread_attributes(
    {
      "data-slot": "input-otp-separator",
      role: "separator",
      ...restProps
    },
    null
  )}>`);
  if (children) {
    $$payload.out.push("<!--[-->");
    children?.($$payload);
    $$payload.out.push(`<!---->`);
  } else {
    $$payload.out.push("<!--[!-->");
    Dot($$payload, {});
  }
  $$payload.out.push(`<!--]--></div>`);
  bind_props($$props, { ref });
  pop();
}
function Signup_code($$payload, $$props) {
  push();
  var $$store_subs;
  const utilsStore = getUtilsStore();
  const form = superForm(defaults(valibot(signUpCodeFormSchema)), {
    id: "signup-code-form",
    delayMs: 0,
    invalidateAll: true,
    multipleSubmits: "prevent",
    SPA: true,
    validators: valibot(signUpCodeFormSchema),
    onUpdate: async ({ form: form2, cancel }) => {
      if (!form2.valid) return;
      const formData2 = form2.data;
      const email = utilsStore.st.currentAuthEmail || formData2.email;
      await authClient.emailOtp.verifyEmail({ email: email || "", otp: formData2.code }, {
        onSuccess: (verifyEmailData) => {
          if (verifyEmailData) {
            utilsStore.setHomeSheet(SheetTypes.Login);
            toast.success("SignUp successful, please log in!");
          } else {
            toast.error("Signup failed: No signup data received");
            cancel();
          }
        },
        onError: (verifyEmailError) => {
          console.error(verifyEmailError.error.message);
          toast.error(`Signup failed: ${verifyEmailError.error.message}`);
          cancel();
        }
      });
    },
    ...getFlashModule()
  });
  const { form: formData, enhance, submitting, message, allErrors } = form;
  const signIn = () => {
    utilsStore.setHomeSheet(SheetTypes.Login);
  };
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    Auth_wrapper($$payload2, {
      pageTitle: "Sign Up",
      pageDescription: "Verify your Email",
      children: ($$payload3) => {
        $$payload3.out.push(`<div class="grid gap-6"><form method="POST">`);
        Form_alerts($$payload3, { message });
        $$payload3.out.push(`<!----> <!---->`);
        Form_field($$payload3, {
          form,
          name: "code",
          class: "mx-auto justify-items-center",
          children: ($$payload4) => {
            $$payload4.out.push(`<!---->`);
            {
              let children = function($$payload5, { props }) {
                $$payload5.out.push(`<!---->`);
                {
                  let children2 = function($$payload6, { cells }) {
                    $$payload6.out.push(`<!---->`);
                    Input_otp_group($$payload6, {
                      children: ($$payload7) => {
                        const each_array = ensure_array_like(cells.slice(0, 2));
                        $$payload7.out.push(`<!--[-->`);
                        for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
                          let cell = each_array[$$index];
                          $$payload7.out.push(`<!---->`);
                          Input_otp_slot($$payload7, { cell });
                          $$payload7.out.push(`<!---->`);
                        }
                        $$payload7.out.push(`<!--]-->`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out.push(`<!----> <!---->`);
                    Input_otp_separator($$payload6, {});
                    $$payload6.out.push(`<!----> <!---->`);
                    Input_otp_group($$payload6, {
                      children: ($$payload7) => {
                        const each_array_1 = ensure_array_like(cells.slice(2, 4));
                        $$payload7.out.push(`<!--[-->`);
                        for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
                          let cell = each_array_1[$$index_1];
                          $$payload7.out.push(`<!---->`);
                          Input_otp_slot($$payload7, { cell });
                          $$payload7.out.push(`<!---->`);
                        }
                        $$payload7.out.push(`<!--]-->`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out.push(`<!----> <!---->`);
                    Input_otp_separator($$payload6, {});
                    $$payload6.out.push(`<!----> <!---->`);
                    Input_otp_group($$payload6, {
                      children: ($$payload7) => {
                        const each_array_2 = ensure_array_like(cells.slice(4, 6));
                        $$payload7.out.push(`<!--[-->`);
                        for (let $$index_2 = 0, $$length = each_array_2.length; $$index_2 < $$length; $$index_2++) {
                          let cell = each_array_2[$$index_2];
                          $$payload7.out.push(`<!---->`);
                          Input_otp_slot($$payload7, { cell });
                          $$payload7.out.push(`<!---->`);
                        }
                        $$payload7.out.push(`<!--]-->`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out.push(`<!---->`);
                  };
                  Input_otp($$payload5, spread_props([
                    { maxlength: 6 },
                    props,
                    {
                      get value() {
                        return store_get($$store_subs ??= {}, "$formData", formData).code;
                      },
                      set value($$value) {
                        store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).code = $$value);
                        $$settled = false;
                      },
                      children: children2,
                      $$slots: { default: true }
                    }
                  ]));
                }
                $$payload5.out.push(`<!---->`);
              };
              Control($$payload4, { children });
            }
            $$payload4.out.push(`<!----> <!---->`);
            Form_field_errors($$payload4, {});
            $$payload4.out.push(`<!---->`);
          },
          $$slots: { default: true }
        });
        $$payload3.out.push(`<!----> <!---->`);
        Form_button($$payload3, {
          disabled: store_get($$store_subs ??= {}, "$submitting", submitting) || store_get($$store_subs ??= {}, "$allErrors", allErrors)?.length > 0,
          class: "mt-4 w-full",
          children: ($$payload4) => {
            if (store_get($$store_subs ??= {}, "$submitting", submitting)) {
              $$payload4.out.push("<!--[-->");
              Loader_circle($$payload4, { class: "mr-2 size-4 animate-spin" });
            } else {
              $$payload4.out.push("<!--[!-->");
            }
            $$payload4.out.push(`<!--]--> ${escape_html(store_get($$store_subs ??= {}, "$submitting", submitting) ? "Please wait..." : "Verify Code")}`);
          },
          $$slots: { default: true }
        });
        $$payload3.out.push(`<!----></form> <div class="relative"><p class="text-muted-foreground px-8 text-center text-sm">`);
        {
          $$payload3.out.push("<!--[!-->");
          $$payload3.out.push(`Still waiting? <button type="button" class="hover:underline">Resend code</button> `);
          Button($$payload3, {
            variant: "link",
            onclick: signIn,
            class: "hover:text-brand no-underline",
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Already have an account? Sign In`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!---->`);
        }
        $$payload3.out.push(`<!--]--></p></div></div>`);
      }
    });
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
function Login_code($$payload, $$props) {
  push();
  var $$store_subs;
  const utilsStore = getUtilsStore();
  const authStore = getAuthStore();
  const form = superForm(defaults(valibot(signUpCodeFormSchema)), {
    id: "signup-code-form",
    delayMs: 0,
    invalidateAll: true,
    multipleSubmits: "prevent",
    SPA: true,
    validators: valibot(signUpCodeFormSchema),
    onUpdate: async ({ form: form2, cancel }) => {
      if (!form2.valid) return;
      try {
        const formData2 = form2.data;
        const email = utilsStore.st.currentAuthEmail || formData2.email;
        const { data: _signInResult, error: signInError } = await authClient.signIn.emailOtp({ email: email || "", otp: formData2.code });
        if (signInError) {
          console.error(signInError.message);
          toast.error(`Sign-in failed: ${signInError.message}`);
          cancel();
          return;
        }
        await setUpOrganization();
        await setupPasskey();
        authStore.checkAuth();
        toast.success("Sign-in successful!");
      } catch (err) {
        console.error("Verification error:", err);
        toast.error(`Verification failed: ${err.message}`);
        cancel();
      }
    },
    ...getFlashModule()
  });
  const { form: formData, enhance, submitting, message, allErrors } = form;
  const signUp = () => {
    utilsStore.setHomeSheet(SheetTypes.SignUp);
  };
  const setUpOrganization = async () => {
    const { data: sessionData } = await authClient.getSession();
    if (!sessionData) return;
    const { user, session } = sessionData;
    if (user && user.userType === UserAccountTypes.Staff && isUserInRole(user, RolesType.OrgAdmin)) {
      const { data, error: _ } = await authClient.organization.list();
      if ((data ?? []).length > 0) {
        return;
      }
      let businessEmail = user.email;
      let businessAddress = "";
      let businessName = "";
      try {
        if (user.metadata) {
          let metadata = user.metadata;
          if (typeof metadata === "string") {
            metadata = JSON.parse(metadata);
          }
          businessEmail = metadata.businessEmail || user.email;
          businessAddress = metadata.businessAddress || "";
          businessName = metadata.businessName || user.name;
        }
      } catch (error) {
        console.warn("Failed to parse user metadata:", error);
      }
      const { data: organizationResponse, error: orgError } = await authClient.organization.create({
        id: user["organizationId"],
        name: businessName,
        slug: businessName.toLowerCase().replace(/[^a-z0-9]/g, "-").replace(/-+/g, "-"),
        logo: await image(businessEmail, {
          size: 200,
          defaultImage: "blank",
          includeExtention: true,
          rating: "g"
        }),
        businessEmail,
        businessAddress
      });
    }
  };
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    Auth_wrapper($$payload2, {
      pageTitle: "Login",
      pageDescription: "Verify OTP",
      children: ($$payload3) => {
        $$payload3.out.push(`<div class="grid gap-6"><form method="POST">`);
        Form_alerts($$payload3, { message });
        $$payload3.out.push(`<!----> <!---->`);
        Form_field($$payload3, {
          form,
          name: "code",
          class: "mx-auto justify-items-center",
          children: ($$payload4) => {
            $$payload4.out.push(`<!---->`);
            {
              let children = function($$payload5, { props }) {
                $$payload5.out.push(`<!---->`);
                {
                  let children2 = function($$payload6, { cells }) {
                    $$payload6.out.push(`<!---->`);
                    Input_otp_group($$payload6, {
                      children: ($$payload7) => {
                        const each_array = ensure_array_like(cells.slice(0, 2));
                        $$payload7.out.push(`<!--[-->`);
                        for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
                          let cell = each_array[$$index];
                          $$payload7.out.push(`<!---->`);
                          Input_otp_slot($$payload7, { cell });
                          $$payload7.out.push(`<!---->`);
                        }
                        $$payload7.out.push(`<!--]-->`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out.push(`<!----> <!---->`);
                    Input_otp_separator($$payload6, {});
                    $$payload6.out.push(`<!----> <!---->`);
                    Input_otp_group($$payload6, {
                      children: ($$payload7) => {
                        const each_array_1 = ensure_array_like(cells.slice(2, 4));
                        $$payload7.out.push(`<!--[-->`);
                        for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
                          let cell = each_array_1[$$index_1];
                          $$payload7.out.push(`<!---->`);
                          Input_otp_slot($$payload7, { cell });
                          $$payload7.out.push(`<!---->`);
                        }
                        $$payload7.out.push(`<!--]-->`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out.push(`<!----> <!---->`);
                    Input_otp_separator($$payload6, {});
                    $$payload6.out.push(`<!----> <!---->`);
                    Input_otp_group($$payload6, {
                      children: ($$payload7) => {
                        const each_array_2 = ensure_array_like(cells.slice(4, 6));
                        $$payload7.out.push(`<!--[-->`);
                        for (let $$index_2 = 0, $$length = each_array_2.length; $$index_2 < $$length; $$index_2++) {
                          let cell = each_array_2[$$index_2];
                          $$payload7.out.push(`<!---->`);
                          Input_otp_slot($$payload7, { cell });
                          $$payload7.out.push(`<!---->`);
                        }
                        $$payload7.out.push(`<!--]-->`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out.push(`<!---->`);
                  };
                  Input_otp($$payload5, spread_props([
                    { maxlength: 6 },
                    props,
                    {
                      get value() {
                        return store_get($$store_subs ??= {}, "$formData", formData).code;
                      },
                      set value($$value) {
                        store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).code = $$value);
                        $$settled = false;
                      },
                      children: children2,
                      $$slots: { default: true }
                    }
                  ]));
                }
                $$payload5.out.push(`<!---->`);
              };
              Control($$payload4, { children });
            }
            $$payload4.out.push(`<!----> <!---->`);
            Form_field_errors($$payload4, {});
            $$payload4.out.push(`<!---->`);
          },
          $$slots: { default: true }
        });
        $$payload3.out.push(`<!----> <!---->`);
        Form_button($$payload3, {
          disabled: store_get($$store_subs ??= {}, "$submitting", submitting) || store_get($$store_subs ??= {}, "$allErrors", allErrors)?.length > 0,
          class: "mt-4 w-full",
          children: ($$payload4) => {
            if (store_get($$store_subs ??= {}, "$submitting", submitting)) {
              $$payload4.out.push("<!--[-->");
              Loader_circle($$payload4, { class: "mr-2 size-4 animate-spin" });
            } else {
              $$payload4.out.push("<!--[!-->");
            }
            $$payload4.out.push(`<!--]--> ${escape_html(store_get($$store_subs ??= {}, "$submitting", submitting) ? "Please wait..." : "Verify Code")}`);
          },
          $$slots: { default: true }
        });
        $$payload3.out.push(`<!----></form> <div class="relative"><p class="text-muted-foreground px-8 text-center text-sm">`);
        {
          $$payload3.out.push("<!--[!-->");
          $$payload3.out.push(`Still waiting? <button type="button" class="hover:underline">Resend code</button> `);
          Button($$payload3, {
            variant: "link",
            onclick: signUp,
            class: "hover:text-brand no-underline",
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Dont have an account? Sign Up`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!---->`);
        }
        $$payload3.out.push(`<!--]--></p></div></div>`);
      }
    });
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
function Home_sheet($$payload, $$props) {
  push();
  const utilsStore = getUtilsStore();
  let sheetSide = "right";
  const authStore = getAuthStore();
  const componentCollection = [
    {
      componentName: SheetTypes.Marketing,
      component: Marketing_sheet,
      dialogClass: "",
      componentProps: {}
    },
    {
      componentName: SheetTypes.Login,
      component: Login,
      dialogClass: "",
      componentProps: {}
    },
    {
      componentName: SheetTypes.SignUp,
      component: Signup,
      dialogClass: "",
      componentProps: {}
    },
    {
      componentName: SheetTypes.SignUpCode,
      component: Signup_code,
      dialogClose: "",
      componentProps: {}
    },
    {
      componentName: SheetTypes.LoginCode,
      component: Login_code,
      dialogClose: "",
      componentProps: {}
    }
  ];
  let { CurrentComponent, componentProps } = (() => {
    const componentEntry = componentCollection.firstOrNull((k) => k.componentName === utilsStore.currentHomeSheet);
    return {
      CurrentComponent: componentEntry?.component,
      componentProps: componentEntry?.componentProps
    };
  })();
  $$payload.out.push(`<!---->`);
  Root$1($$payload, {
    open: !authStore.isAuthenticated,
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->`);
      Sheet_trigger($$payload2, {});
      $$payload2.out.push(`<!----> <!---->`);
      Sheet_content($$payload2, {
        escapeKeydownBehavior: "ignore",
        interactOutsideBehavior: "ignore",
        class: "w-full overflow-y-auto p-0 sm:max-w-xl sm:p-1 md:max-w-2xl lg:max-w-3xl xl:max-w-4xl",
        side: sheetSide,
        showCloseButton: false,
        onOpenAutoFocus: (e) => {
          e.preventDefault();
        },
        children: ($$payload3) => {
          $$payload3.out.push(`<div class="flex min-h-screen flex-col"><!---->`);
          CurrentComponent($$payload3, spread_props([componentProps]));
          $$payload3.out.push(`<!----></div>`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!---->`);
  pop();
}
function Sidebar_content($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    children,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  $$payload.out.push(`<div${spread_attributes(
    {
      "data-slot": "sidebar-content",
      "data-sidebar": "content",
      class: clsx(cn$1("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden", className)),
      ...restProps
    },
    null
  )}>`);
  children?.($$payload);
  $$payload.out.push(`<!----></div>`);
  bind_props($$props, { ref });
  pop();
}
function Sidebar_footer($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    children,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  $$payload.out.push(`<div${spread_attributes(
    {
      "data-slot": "sidebar-footer",
      "data-sidebar": "footer",
      class: clsx(cn$1("flex flex-col gap-2 p-2", className)),
      ...restProps
    },
    null
  )}>`);
  children?.($$payload);
  $$payload.out.push(`<!----></div>`);
  bind_props($$props, { ref });
  pop();
}
function Sidebar_group_label($$payload, $$props) {
  push();
  let {
    ref = null,
    children,
    child,
    class: className,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const mergedProps = {
    class: cn$1("text-sidebar-foreground/70 ring-sidebar-ring outline-hidden flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0", "group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0", className),
    "data-slot": "sidebar-group-label",
    "data-sidebar": "group-label",
    ...restProps
  };
  if (child) {
    $$payload.out.push("<!--[-->");
    child($$payload, { props: mergedProps });
    $$payload.out.push(`<!---->`);
  } else {
    $$payload.out.push("<!--[!-->");
    $$payload.out.push(`<div${spread_attributes({ ...mergedProps }, null)}>`);
    children?.($$payload);
    $$payload.out.push(`<!----></div>`);
  }
  $$payload.out.push(`<!--]-->`);
  bind_props($$props, { ref });
  pop();
}
function Sidebar_group($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    children,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  $$payload.out.push(`<div${spread_attributes(
    {
      "data-slot": "sidebar-group",
      "data-sidebar": "group",
      class: clsx(cn$1("relative flex w-full min-w-0 flex-col p-2", className)),
      ...restProps
    },
    null
  )}>`);
  children?.($$payload);
  $$payload.out.push(`<!----></div>`);
  bind_props($$props, { ref });
  pop();
}
function Sidebar_header($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    children,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  $$payload.out.push(`<div${spread_attributes(
    {
      "data-slot": "sidebar-header",
      "data-sidebar": "header",
      class: clsx(cn$1("flex flex-col gap-2 p-2", className)),
      ...restProps
    },
    null
  )}>`);
  children?.($$payload);
  $$payload.out.push(`<!----></div>`);
  bind_props($$props, { ref });
  pop();
}
function Sidebar_inset($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    children,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  $$payload.out.push(`<main${spread_attributes(
    {
      "data-slot": "sidebar-inset",
      class: clsx(cn$1("bg-background relative flex w-full flex-1 flex-col", "md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm", className)),
      ...restProps
    },
    null
  )}>`);
  children?.($$payload);
  $$payload.out.push(`<!----></main>`);
  bind_props($$props, { ref });
  pop();
}
function Sidebar_menu_item($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    children,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  $$payload.out.push(`<li${spread_attributes(
    {
      "data-slot": "sidebar-menu-item",
      "data-sidebar": "menu-item",
      class: clsx(cn$1("group/menu-item relative", className)),
      ...restProps
    },
    null
  )}>`);
  children?.($$payload);
  $$payload.out.push(`<!----></li>`);
  bind_props($$props, { ref });
  pop();
}
function Sidebar_menu_sub_button($$payload, $$props) {
  push();
  let {
    ref = null,
    children,
    child,
    class: className,
    size = "md",
    isActive = false,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const mergedProps = {
    class: cn$1("text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground outline-hidden flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0", "data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground", size === "sm" && "text-xs", size === "md" && "text-sm", "group-data-[collapsible=icon]:hidden", className),
    "data-slot": "sidebar-menu-sub-button",
    "data-sidebar": "menu-sub-button",
    "data-size": size,
    "data-active": isActive,
    ...restProps
  };
  if (child) {
    $$payload.out.push("<!--[-->");
    child($$payload, { props: mergedProps });
    $$payload.out.push(`<!---->`);
  } else {
    $$payload.out.push("<!--[!-->");
    $$payload.out.push(`<a${spread_attributes({ ...mergedProps }, null)}>`);
    children?.($$payload);
    $$payload.out.push(`<!----></a>`);
  }
  $$payload.out.push(`<!--]-->`);
  bind_props($$props, { ref });
  pop();
}
function Sidebar_menu_sub_item($$payload, $$props) {
  push();
  let {
    ref = null,
    children,
    class: className,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  $$payload.out.push(`<li${spread_attributes(
    {
      "data-slot": "sidebar-menu-sub-item",
      "data-sidebar": "menu-sub-item",
      class: clsx(cn$1("group/menu-sub-item relative", className)),
      ...restProps
    },
    null
  )}>`);
  children?.($$payload);
  $$payload.out.push(`<!----></li>`);
  bind_props($$props, { ref });
  pop();
}
function Sidebar_menu_sub($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    children,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  $$payload.out.push(`<ul${spread_attributes(
    {
      "data-slot": "sidebar-menu-sub",
      "data-sidebar": "menu-sub",
      class: clsx(cn$1("border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5", "group-data-[collapsible=icon]:hidden", className)),
      ...restProps
    },
    null
  )}>`);
  children?.($$payload);
  $$payload.out.push(`<!----></ul>`);
  bind_props($$props, { ref });
  pop();
}
function Sidebar_menu($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    children,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  $$payload.out.push(`<ul${spread_attributes(
    {
      "data-slot": "sidebar-menu",
      "data-sidebar": "menu",
      class: clsx(cn$1("flex w-full min-w-0 flex-col gap-1", className)),
      ...restProps
    },
    null
  )}>`);
  children?.($$payload);
  $$payload.out.push(`<!----></ul>`);
  bind_props($$props, { ref });
  pop();
}
function Sidebar_provider($$payload, $$props) {
  push();
  let {
    ref = null,
    open = true,
    onOpenChange = () => {
    },
    class: className,
    style,
    children,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  setSidebar({
    open: () => open,
    setOpen: (value) => {
      open = value;
      onOpenChange(value);
      document.cookie = `${SIDEBAR_COOKIE_NAME}=${open}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`;
    }
  });
  $$payload.out.push(`<!---->`);
  Provider($$payload, {
    delayDuration: 0,
    children: ($$payload2) => {
      $$payload2.out.push(`<div${spread_attributes(
        {
          "data-slot": "sidebar-wrapper",
          style: `--sidebar-width: ${stringify(SIDEBAR_WIDTH)}; --sidebar-width-icon: ${stringify(SIDEBAR_WIDTH_ICON)}; ${stringify(style)}`,
          class: clsx(cn$1("group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full", className)),
          ...restProps
        },
        null
      )}>`);
      children?.($$payload2);
      $$payload2.out.push(`<!----></div>`);
    }
  });
  $$payload.out.push(`<!---->`);
  bind_props($$props, { ref, open });
  pop();
}
function Sidebar_rail($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    children,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  useSidebar();
  $$payload.out.push(`<button${spread_attributes(
    {
      "data-sidebar": "rail",
      "data-slot": "sidebar-rail",
      "aria-label": "Toggle Sidebar",
      tabindex: -1,
      title: "Toggle Sidebar",
      class: clsx(cn$1("hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] group-data-[side=left]:-right-4 group-data-[side=right]:left-0 sm:flex", "in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize", "[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize", "hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full", "[[data-side=left][data-collapsible=offcanvas]_&]:-right-2", "[[data-side=right][data-collapsible=offcanvas]_&]:-left-2", className)),
      ...restProps
    },
    null
  )}>`);
  children?.($$payload);
  $$payload.out.push(`<!----></button>`);
  bind_props($$props, { ref });
  pop();
}
function Sidebar_trigger($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    onclick,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const sidebar = useSidebar();
  Button($$payload, spread_props([
    {
      "data-sidebar": "trigger",
      "data-slot": "sidebar-trigger",
      variant: "ghost",
      size: "icon",
      class: cn$1("size-7", className),
      type: "button",
      onclick: (e) => {
        onclick?.(e);
        sidebar.toggle();
      }
    },
    restProps,
    {
      children: ($$payload2) => {
        Panel_left($$payload2, {});
        $$payload2.out.push(`<!----> <span class="sr-only">Toggle Sidebar</span>`);
      },
      $$slots: { default: true }
    }
  ]));
  bind_props($$props, { ref });
  pop();
}
function Sidebar($$payload, $$props) {
  push();
  let {
    ref = null,
    side = "left",
    variant = "sidebar",
    collapsible = "offcanvas",
    class: className,
    children,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const sidebar = useSidebar();
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    if (collapsible === "none") {
      $$payload2.out.push("<!--[-->");
      $$payload2.out.push(`<div${spread_attributes(
        {
          class: clsx(cn$1("bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col", className)),
          ...restProps
        },
        null
      )}>`);
      children?.($$payload2);
      $$payload2.out.push(`<!----></div>`);
    } else {
      $$payload2.out.push("<!--[!-->");
      if (sidebar.isMobile) {
        $$payload2.out.push("<!--[-->");
        var bind_get = () => sidebar.openMobile;
        var bind_set = (v) => sidebar.setOpenMobile(v);
        $$payload2.out.push(`<!---->`);
        Root$1($$payload2, spread_props([
          {
            get open() {
              return bind_get();
            },
            set open($$value) {
              bind_set($$value);
            }
          },
          restProps,
          {
            children: ($$payload3) => {
              $$payload3.out.push(`<!---->`);
              Sheet_content($$payload3, {
                showCloseButton: true,
                "data-sidebar": "sidebar",
                "data-slot": "sidebar",
                "data-mobile": "true",
                class: "bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden",
                style: `--sidebar-width: ${stringify(SIDEBAR_WIDTH_MOBILE)};`,
                side,
                children: ($$payload4) => {
                  $$payload4.out.push(`<!---->`);
                  Sheet_header($$payload4, {
                    class: "sr-only",
                    children: ($$payload5) => {
                      $$payload5.out.push(`<!---->`);
                      Sheet_title($$payload5, {
                        children: ($$payload6) => {
                          $$payload6.out.push(`<!---->Sidebar`);
                        },
                        $$slots: { default: true }
                      });
                      $$payload5.out.push(`<!----> <!---->`);
                      Sheet_description($$payload5, {
                        children: ($$payload6) => {
                          $$payload6.out.push(`<!---->Displays the mobile sidebar.`);
                        },
                        $$slots: { default: true }
                      });
                      $$payload5.out.push(`<!---->`);
                    },
                    $$slots: { default: true }
                  });
                  $$payload4.out.push(`<!----> <div class="flex h-full w-full flex-col">`);
                  children?.($$payload4);
                  $$payload4.out.push(`<!----></div>`);
                },
                $$slots: { default: true }
              });
              $$payload3.out.push(`<!---->`);
            },
            $$slots: { default: true }
          }
        ]));
        $$payload2.out.push(`<!---->`);
      } else {
        $$payload2.out.push("<!--[!-->");
        $$payload2.out.push(`<div class="text-sidebar-foreground group peer hidden md:block"${attr("data-state", sidebar.state)}${attr("data-collapsible", sidebar.state === "collapsed" ? collapsible : "")}${attr("data-variant", variant)}${attr("data-side", side)} data-slot="sidebar"><div data-slot="sidebar-gap"${attr_class(clsx(cn$1("relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear", "group-data-[collapsible=offcanvas]:w-0", "group-data-[side=right]:rotate-180", variant === "floating" || variant === "inset" ? "group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]" : "group-data-[collapsible=icon]:w-(--sidebar-width-icon)")))}></div> <div${spread_attributes(
          {
            "data-slot": "sidebar-container",
            class: clsx(cn$1(
              "fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex",
              side === "left" ? "left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]" : "right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]",
              variant === "floating" || variant === "inset" ? "p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]" : "group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l",
              className
            )),
            ...restProps
          },
          null
        )}><div data-sidebar="sidebar" data-slot="sidebar-inner" class="bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm">`);
        children?.($$payload2);
        $$payload2.out.push(`<!----></div></div></div>`);
      }
      $$payload2.out.push(`<!--]-->`);
    }
    $$payload2.out.push(`<!--]-->`);
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref });
  pop();
}
function Avatar($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out.push(`<!---->`);
    Avatar$1($$payload2, spread_props([
      {
        "data-slot": "avatar",
        class: cn$1("relative flex size-8 shrink-0 overflow-hidden rounded-full", className)
      },
      restProps,
      {
        get ref() {
          return ref;
        },
        set ref($$value) {
          ref = $$value;
          $$settled = false;
        }
      }
    ]));
    $$payload2.out.push(`<!---->`);
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref });
  pop();
}
function Avatar_image($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out.push(`<!---->`);
    Avatar_image$1($$payload2, spread_props([
      {
        "data-slot": "avatar-image",
        class: cn$1("aspect-square size-full", className)
      },
      restProps,
      {
        get ref() {
          return ref;
        },
        set ref($$value) {
          ref = $$value;
          $$settled = false;
        }
      }
    ]));
    $$payload2.out.push(`<!---->`);
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref });
  pop();
}
function Avatar_fallback($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out.push(`<!---->`);
    Avatar_fallback$1($$payload2, spread_props([
      {
        "data-slot": "avatar-fallback",
        class: cn$1("bg-muted flex size-full items-center justify-center rounded-full", className)
      },
      restProps,
      {
        get ref() {
          return ref;
        },
        set ref($$value) {
          ref = $$value;
          $$settled = false;
        }
      }
    ]));
    $$payload2.out.push(`<!---->`);
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref });
  pop();
}
function App_org_name($$payload, $$props) {
  push();
  let orgStore = getOrgStore();
  const teamInitials = orgStore.org?.name ? getInitials(orgStore.org?.name, "") : "";
  $$payload.out.push(`<span class="font-semibold">${escape_html(teamInitials)}</span>`);
  pop();
}
function Team_switcher($$payload, $$props) {
  push();
  const orgStore = getOrgStore();
  $$payload.out.push(`<!---->`);
  Sidebar_menu($$payload, {
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->`);
      Sidebar_menu_item($$payload2, {
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Sidebar_menu_button($$payload3, {
            size: "lg",
            class: "data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground",
            children: ($$payload4) => {
              $$payload4.out.push(`<div class="flex aspect-square size-8 items-center justify-center rounded-none">`);
              App_org_name($$payload4);
              $$payload4.out.push(`<!----></div> <div class="grid flex-1 text-left text-sm leading-tight"><span class="truncate font-semibold">${escape_html(orgStore.org?.name)}</span> <span class="truncate text-xs">${escape_html(orgStore.org?.currentPlan)}</span></div>`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!---->`);
  pop();
}
const Root = Collapsible;
const Trigger = Collapsible_trigger;
const Content = Collapsible_content;
function Nav_main($$payload, $$props) {
  let {
    items
    // this should be `Component` after @lucide/svelte updates types
  } = $$props;
  const dashboard = {
    title: "Dashboard",
    url: "/",
    icon: Building,
    isActive: false
  };
  $$payload.out.push(`<!---->`);
  Sidebar_group($$payload, {
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->`);
      Sidebar_group_label($$payload2, {
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->Modules`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!----> <!---->`);
      Sidebar_menu($$payload2, {
        children: ($$payload3) => {
          const each_array = ensure_array_like(items);
          $$payload3.out.push(`<!---->`);
          Sidebar_menu_item($$payload3, {
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->`);
              Sidebar_menu_button($$payload4, {
                children: ($$payload5) => {
                  $$payload5.out.push(`<!---->`);
                  dashboard.icon($$payload5, {});
                  $$payload5.out.push(`<!----> <a${attr("href", dashboard.url)}><span>${escape_html(dashboard.title)}</span></a>`);
                },
                $$slots: { default: true }
              });
              $$payload4.out.push(`<!---->`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> <!--[-->`);
          for (let $$index_1 = 0, $$length = each_array.length; $$index_1 < $$length; $$index_1++) {
            let mainItem = each_array[$$index_1];
            $$payload3.out.push(`<!---->`);
            {
              let child = function($$payload4, { props }) {
                $$payload4.out.push(`<!---->`);
                Sidebar_menu_item($$payload4, spread_props([
                  props,
                  {
                    children: ($$payload5) => {
                      $$payload5.out.push(`<!---->`);
                      {
                        let child2 = function($$payload6, { props: props2 }) {
                          $$payload6.out.push(`<!---->`);
                          Sidebar_menu_button($$payload6, spread_props([
                            { tooltipContent: mainItem.title },
                            props2,
                            {
                              children: ($$payload7) => {
                                if (mainItem.icon) {
                                  $$payload7.out.push("<!--[-->");
                                  $$payload7.out.push(`<!---->`);
                                  mainItem.icon($$payload7, {});
                                  $$payload7.out.push(`<!---->`);
                                } else {
                                  $$payload7.out.push("<!--[!-->");
                                }
                                $$payload7.out.push(`<!--]--> <span>${escape_html(mainItem.title)}</span> `);
                                Chevron_right($$payload7, {
                                  class: "ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90"
                                });
                                $$payload7.out.push(`<!---->`);
                              },
                              $$slots: { default: true }
                            }
                          ]));
                          $$payload6.out.push(`<!---->`);
                        };
                        Trigger($$payload5, { child: child2, $$slots: { child: true } });
                      }
                      $$payload5.out.push(`<!----> <!---->`);
                      Content($$payload5, {
                        children: ($$payload6) => {
                          if (mainItem.items) {
                            $$payload6.out.push("<!--[-->");
                            $$payload6.out.push(`<!---->`);
                            Sidebar_menu_sub($$payload6, {
                              children: ($$payload7) => {
                                const each_array_1 = ensure_array_like(mainItem.items ?? []);
                                $$payload7.out.push(`<!--[-->`);
                                for (let $$index = 0, $$length2 = each_array_1.length; $$index < $$length2; $$index++) {
                                  let subItem = each_array_1[$$index];
                                  $$payload7.out.push(`<!---->`);
                                  Sidebar_menu_sub_item($$payload7, {
                                    children: ($$payload8) => {
                                      $$payload8.out.push(`<!---->`);
                                      {
                                        let child2 = function($$payload9, { props: props2 }) {
                                          $$payload9.out.push(`<a${spread_attributes({ href: subItem.url, ...props2 }, null)}><span>${escape_html(subItem.title)}</span></a>`);
                                        };
                                        Sidebar_menu_sub_button($$payload8, { child: child2, $$slots: { child: true } });
                                      }
                                      $$payload8.out.push(`<!---->`);
                                    },
                                    $$slots: { default: true }
                                  });
                                  $$payload7.out.push(`<!---->`);
                                }
                                $$payload7.out.push(`<!--]-->`);
                              },
                              $$slots: { default: true }
                            });
                            $$payload6.out.push(`<!---->`);
                          } else {
                            $$payload6.out.push("<!--[!-->");
                          }
                          $$payload6.out.push(`<!--]-->`);
                        },
                        $$slots: { default: true }
                      });
                      $$payload5.out.push(`<!---->`);
                    },
                    $$slots: { default: true }
                  }
                ]));
                $$payload4.out.push(`<!---->`);
              };
              Root($$payload3, {
                open: mainItem.isActive,
                class: "group/collapsible",
                child,
                $$slots: { child: true }
              });
            }
            $$payload3.out.push(`<!---->`);
          }
          $$payload3.out.push(`<!--]-->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!---->`);
}
function Dropdown_menu_label($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    inset,
    children,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  $$payload.out.push(`<div${spread_attributes(
    {
      "data-slot": "dropdown-menu-label",
      "data-inset": inset,
      class: clsx(cn$1("px-2 py-1.5 text-sm font-semibold data-[inset]:pl-8", className)),
      ...restProps
    },
    null
  )}>`);
  children?.($$payload);
  $$payload.out.push(`<!----></div>`);
  bind_props($$props, { ref });
  pop();
}
function Nav_user($$payload, $$props) {
  push();
  var $$store_subs;
  const sidebar = useSidebar();
  const authStore = getAuthStore();
  const orgStore = getOrgStore();
  const form = superForm(defaults(valibot(logOutFormSchema)), {
    SPA: true,
    validators: valibot(logOutFormSchema),
    multipleSubmits: "prevent",
    async onUpdate({ form: form2 }) {
      if (form2.valid) {
        try {
          await authClient.signOut();
          authStore.clearStore();
          await goto(PAGES._ROOT);
        } catch (error) {
        }
      }
    },
    ...getFlashModule()
  });
  const { enhance, message, submitting } = form;
  $$payload.out.push(`<!---->`);
  Sidebar_menu($$payload, {
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->`);
      Sidebar_menu_item($$payload2, {
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Root$2($$payload3, {
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->`);
              {
                let child = function($$payload5, { props }) {
                  $$payload5.out.push(`<!---->`);
                  Sidebar_menu_button($$payload5, spread_props([
                    {
                      size: "lg",
                      class: "data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
                    },
                    props,
                    {
                      children: ($$payload6) => {
                        $$payload6.out.push(`<!---->`);
                        Avatar($$payload6, {
                          class: "h-8 w-8 rounded-lg",
                          children: ($$payload7) => {
                            $$payload7.out.push(`<!---->`);
                            Avatar_image($$payload7, { src: authStore.user?.avatarUrl, alt: authStore.fullName });
                            $$payload7.out.push(`<!----> <!---->`);
                            Avatar_fallback($$payload7, {
                              class: "rounded-lg",
                              children: ($$payload8) => {
                                $$payload8.out.push(`<!---->${escape_html(orgStore?.orgNameInitials)}`);
                              },
                              $$slots: { default: true }
                            });
                            $$payload7.out.push(`<!---->`);
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out.push(`<!----> <div class="grid flex-1 text-left text-sm leading-tight"><span class="truncate font-semibold">${escape_html(authStore.fullName)}</span> <span class="truncate text-xs">${escape_html(authStore.user?.email ?? "")}</span></div> `);
                        Chevrons_up_down($$payload6, { class: "ml-auto size-4" });
                        $$payload6.out.push(`<!---->`);
                      },
                      $$slots: { default: true }
                    }
                  ]));
                  $$payload5.out.push(`<!---->`);
                };
                Dropdown_menu_trigger($$payload4, { child, $$slots: { child: true } });
              }
              $$payload4.out.push(`<!----> <!---->`);
              Dropdown_menu_content($$payload4, {
                class: "w-[--bits-dropdown-menu-anchor-width] min-w-56 rounded-lg",
                side: sidebar.isMobile ? "bottom" : "right",
                align: "end",
                sideOffset: 4,
                children: ($$payload5) => {
                  $$payload5.out.push(`<!---->`);
                  Dropdown_menu_label($$payload5, {
                    class: "p-0 font-normal",
                    children: ($$payload6) => {
                      $$payload6.out.push(`<div class="flex items-center gap-2 px-1 py-1.5 text-left text-sm"><!---->`);
                      Avatar($$payload6, {
                        class: "h-8 w-8 rounded-lg",
                        children: ($$payload7) => {
                          $$payload7.out.push(`<!---->`);
                          Avatar_image($$payload7, { src: authStore.user?.avatarUrl, alt: authStore.fullName });
                          $$payload7.out.push(`<!----> <!---->`);
                          Avatar_fallback($$payload7, {
                            class: "rounded-lg",
                            children: ($$payload8) => {
                              $$payload8.out.push(`<!---->${escape_html(orgStore?.orgNameInitials)}`);
                            },
                            $$slots: { default: true }
                          });
                          $$payload7.out.push(`<!---->`);
                        },
                        $$slots: { default: true }
                      });
                      $$payload6.out.push(`<!----> <div class="grid flex-1 text-left text-sm leading-tight"><span class="truncate font-semibold">${escape_html(authStore.fullName)}</span> <span class="truncate text-xs">${escape_html(authStore.user?.email)}</span></div></div>`);
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out.push(`<!----> <!---->`);
                  Dropdown_menu_separator($$payload5, {});
                  $$payload5.out.push(`<!----> `);
                  if (orgStore.org?.currentPlan === TeamPlans.Free) {
                    $$payload5.out.push("<!--[-->");
                    $$payload5.out.push(`<!---->`);
                    Dropdown_menu_group($$payload5, {
                      children: ($$payload6) => {
                        $$payload6.out.push(`<!---->`);
                        Dropdown_menu_item($$payload6, {
                          children: ($$payload7) => {
                            Sparkles($$payload7, {});
                            $$payload7.out.push(`<!----> Upgrade to Pro`);
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out.push(`<!---->`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out.push(`<!---->`);
                  } else {
                    $$payload5.out.push("<!--[!-->");
                  }
                  $$payload5.out.push(`<!--]--> <!---->`);
                  Dropdown_menu_separator($$payload5, {});
                  $$payload5.out.push(`<!----> <!---->`);
                  Dropdown_menu_group($$payload5, {
                    children: ($$payload6) => {
                      $$payload6.out.push(`<!---->`);
                      Dropdown_menu_item($$payload6, {
                        children: ($$payload7) => {
                          Badge_check($$payload7, {});
                          $$payload7.out.push(`<!----> Account`);
                        },
                        $$slots: { default: true }
                      });
                      $$payload6.out.push(`<!----> <!---->`);
                      Dropdown_menu_item($$payload6, {
                        children: ($$payload7) => {
                          Credit_card($$payload7, {});
                          $$payload7.out.push(`<!----> Billing`);
                        },
                        $$slots: { default: true }
                      });
                      $$payload6.out.push(`<!----> <!---->`);
                      Dropdown_menu_item($$payload6, {
                        children: ($$payload7) => {
                          Bell($$payload7, {});
                          $$payload7.out.push(`<!----> Notifications`);
                        },
                        $$slots: { default: true }
                      });
                      $$payload6.out.push(`<!---->`);
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out.push(`<!----> <!---->`);
                  Dropdown_menu_separator($$payload5, {});
                  $$payload5.out.push(`<!----> <!---->`);
                  Dropdown_menu_item($$payload5, {
                    children: ($$payload6) => {
                      $$payload6.out.push(`<form method="POST">`);
                      Form_alerts($$payload6, { message });
                      $$payload6.out.push(`<!----> `);
                      Button($$payload6, {
                        type: "submit",
                        variant: "ghost",
                        disabled: store_get($$store_subs ??= {}, "$submitting", submitting),
                        children: ($$payload7) => {
                          Log_out($$payload7, {});
                          $$payload7.out.push(`<!----> Log out`);
                        },
                        $$slots: { default: true }
                      });
                      $$payload6.out.push(`<!----></form>`);
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out.push(`<!---->`);
                },
                $$slots: { default: true }
              });
              $$payload4.out.push(`<!---->`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!---->`);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
function App_sidebar($$payload, $$props) {
  push();
  let {
    ref = null,
    collapsible = "icon",
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out.push(`<!---->`);
    Sidebar($$payload2, spread_props([
      { collapsible },
      restProps,
      {
        class: "text-lg",
        get ref() {
          return ref;
        },
        set ref($$value) {
          ref = $$value;
          $$settled = false;
        },
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Sidebar_header($$payload3, {
            children: ($$payload4) => {
              Logo($$payload4, { clsName: "px-2 flex items-center justify-start" });
              $$payload4.out.push(`<!----> `);
              Team_switcher($$payload4);
              $$payload4.out.push(`<!---->`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> <!---->`);
          Sidebar_content($$payload3, {
            children: ($$payload4) => {
              Nav_main($$payload4, { items: menuData.navMain });
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> <!---->`);
          Sidebar_footer($$payload3, {
            children: ($$payload4) => {
              Nav_user($$payload4);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> <!---->`);
          Sidebar_rail($$payload3, {});
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      }
    ]));
    $$payload2.out.push(`<!---->`);
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref });
  pop();
}
function Breadcrumb($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    children,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  $$payload.out.push(`<nav${spread_attributes(
    {
      "data-slot": "breadcrumb",
      class: clsx(className),
      "aria-label": "breadcrumb",
      ...restProps
    },
    null
  )}>`);
  children?.($$payload);
  $$payload.out.push(`<!----></nav>`);
  bind_props($$props, { ref });
  pop();
}
function Breadcrumb_item($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    children,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  $$payload.out.push(`<li${spread_attributes(
    {
      "data-slot": "breadcrumb-item",
      class: clsx(cn$1("inline-flex items-center gap-1.5", className)),
      ...restProps
    },
    null
  )}>`);
  children?.($$payload);
  $$payload.out.push(`<!----></li>`);
  bind_props($$props, { ref });
  pop();
}
function Breadcrumb_separator($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    children,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  $$payload.out.push(`<li${spread_attributes(
    {
      "data-slot": "breadcrumb-separator",
      role: "presentation",
      "aria-hidden": "true",
      class: clsx(cn$1("[&>svg]:size-3.5", className)),
      ...restProps
    },
    null
  )}>`);
  if (children) {
    $$payload.out.push("<!--[-->");
    children?.($$payload);
    $$payload.out.push(`<!---->`);
  } else {
    $$payload.out.push("<!--[!-->");
    Chevron_right($$payload, {});
  }
  $$payload.out.push(`<!--]--></li>`);
  bind_props($$props, { ref });
  pop();
}
function Breadcrumb_link($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    href = void 0,
    child,
    children,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const attrs = {
    "data-slot": "breadcrumb-link",
    class: cn$1("hover:text-foreground transition-colors", className),
    href,
    ...restProps
  };
  if (child) {
    $$payload.out.push("<!--[-->");
    child($$payload, { props: attrs });
    $$payload.out.push(`<!---->`);
  } else {
    $$payload.out.push("<!--[!-->");
    $$payload.out.push(`<a${spread_attributes({ ...attrs }, null)}>`);
    children?.($$payload);
    $$payload.out.push(`<!----></a>`);
  }
  $$payload.out.push(`<!--]-->`);
  bind_props($$props, { ref });
  pop();
}
function Breadcrumb_list($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    children,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  $$payload.out.push(`<ol${spread_attributes(
    {
      "data-slot": "breadcrumb-list",
      class: clsx(cn$1("text-muted-foreground flex flex-wrap items-center gap-1.5 break-words text-sm sm:gap-2.5", className)),
      ...restProps
    },
    null
  )}>`);
  children?.($$payload);
  $$payload.out.push(`<!----></ol>`);
  bind_props($$props, { ref });
  pop();
}
function Main($$payload, $$props) {
  let { children } = $$props;
  $$payload.out.push(`<!---->`);
  Sidebar_provider($$payload, {
    children: ($$payload2) => {
      App_sidebar($$payload2, {});
      $$payload2.out.push(`<!----> <!---->`);
      Sidebar_inset($$payload2, {
        children: ($$payload3) => {
          $$payload3.out.push(`<header class="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12"><div class="flex flex-grow items-center gap-2 px-4"><!---->`);
          Sidebar_trigger($$payload3, { class: "-ml-1" });
          $$payload3.out.push(`<!----> `);
          Separator($$payload3, { orientation: "vertical", class: "mr-2 h-4" });
          $$payload3.out.push(`<!----> `);
          App_breadcrumbs($$payload3);
          $$payload3.out.push(`<!----></div> <nav class="mr-2 flex flex-row items-center gap-6 px-4">`);
          Light_switch($$payload3);
          $$payload3.out.push(`<!----></nav></header> <div class="flex flex-1 flex-col gap-4 p-4 pt-0"><div class="bg-muted/50 min-h-[100vh] flex-1 rounded-none px-4 py-4 md:min-h-min">`);
          children?.($$payload3);
          $$payload3.out.push(`<!----></div></div>`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!---->`);
}
function Breadcrumbs($$payload, $$props) {
  push();
  function titleSanitizer(title) {
    return title.replace(/([A-Z])/g, " $1").replace(/^./, (str) => str.toUpperCase());
  }
  let {
    relPathToRoutes = "/src/routes/",
    routeId,
    url,
    crumbs = void 0,
    routeModules = void 0,
    pageData,
    skipRoutesWithNoPage,
    children
  } = $$props;
  function getPageTitleFromModule(module) {
    if (module?.pageTitle) {
      return module.pageTitle;
    }
    if (module?.getPageTitle) {
      return module.getPageTitle(pageData);
    }
    return void 0;
  }
  let _crumbs = (() => {
    let tmpCrumbs = [];
    if (crumbs != void 0) {
      return crumbs;
    } else if (routeId) {
      let completeUrl = "";
      let completeRoute = relPathToRoutes + (relPathToRoutes.slice(-1) == "/" ? "" : "/");
      const routes = routeId.split(/(?<!\))\//).filter((p) => p != "");
      const paths = url.pathname.split("/").filter((p) => p != "");
      for (let i = 0; i < paths.length; i++) {
        let path = paths[i];
        let route = routes[i];
        completeUrl += `/${path}`;
        completeRoute += `${route}/`;
        const routeModule = routeModules === void 0 ? void 0 : routeModules[`${completeRoute}+page.svelte`];
        let url2 = completeUrl;
        if (i == paths.length - 1) {
          url2 = void 0;
        }
        if (routeModule == void 0) {
          url2 = void 0;
          if (skipRoutesWithNoPage) {
            continue;
          }
        }
        tmpCrumbs.push({
          // Last crumb gets no url as it is the current page
          url: url2,
          title: getPageTitleFromModule(routeModule) || titleSanitizer(path)
        });
      }
    } else {
      let completeUrl = "";
      const paths = url.pathname.split("/").filter((p) => p != "");
      for (let i = 0; i < paths.length; i++) {
        let path = paths[i];
        completeUrl += `/${path}`;
        tmpCrumbs.push({
          title: titleSanitizer(path),
          url: i == paths.length - 1 ? void 0 : completeUrl
        });
      }
    }
    return tmpCrumbs;
  })();
  children?.($$payload, { crumbs: _crumbs, routeModules });
  $$payload.out.push(`<!---->`);
  bind_props($$props, { routeModules, titleSanitizer });
  pop();
}
function App_breadcrumbs($$payload, $$props) {
  push();
  {
    let children = function($$payload2, { crumbs }) {
      $$payload2.out.push(`<!---->`);
      Breadcrumb($$payload2, {
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Breadcrumb_list($$payload3, {
            children: ($$payload4) => {
              const each_array = ensure_array_like(crumbs);
              $$payload4.out.push(`<!---->`);
              Breadcrumb_item($$payload4, {
                class: "hidden md:block",
                children: ($$payload5) => {
                  $$payload5.out.push(`<!---->`);
                  Breadcrumb_link($$payload5, {
                    href: "#",
                    children: ($$payload6) => {
                      $$payload6.out.push(`<!---->SpenDeed`);
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out.push(`<!---->`);
                },
                $$slots: { default: true }
              });
              $$payload4.out.push(`<!----> <!--[-->`);
              for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
                let c = each_array[$$index];
                $$payload4.out.push(`<!---->`);
                Breadcrumb_separator($$payload4, { class: "hidden md:block" });
                $$payload4.out.push(`<!----> <!---->`);
                Breadcrumb_item($$payload4, {
                  children: ($$payload5) => {
                    $$payload5.out.push(`<!---->`);
                    Breadcrumb_link($$payload5, {
                      href: c.url,
                      class: "decoration-none",
                      children: ($$payload6) => {
                        $$payload6.out.push(`<!---->${escape_html(c.title)}`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out.push(`<!---->`);
                  },
                  $$slots: { default: true }
                });
                $$payload4.out.push(`<!---->`);
              }
              $$payload4.out.push(`<!--]-->`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    };
    Breadcrumbs($$payload, {
      url: page.url,
      routeId: page.route.id,
      pageData: void 0,
      skipRoutesWithNoPage: true,
      children,
      $$slots: { default: true }
    });
  }
  pop();
}
function _layout($$payload, $$props) {
  push();
  let { children } = $$props;
  getAuthStore();
  getUtilsStore();
  Layout_wrapper($$payload, {
    children: ($$payload2) => {
      Main($$payload2, {
        children: ($$payload3) => {
          children?.($$payload3);
          $$payload3.out.push(`<!---->`);
        }
      });
      $$payload2.out.push(`<!----> `);
      Home_sheet($$payload2);
      $$payload2.out.push(`<!---->`);
    }
  });
  pop();
}
export {
  _layout as default
};
