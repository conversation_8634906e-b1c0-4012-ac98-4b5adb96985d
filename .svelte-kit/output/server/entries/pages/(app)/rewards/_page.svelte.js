import "clsx";
import "../../../../chunks/client2.js";
import { I as Icon } from "../../../../chunks/states.svelte.js";
import "linq-extensions";
import "@sveltejs/kit/internal";
import "../../../../chunks/exports.js";
import "../../../../chunks/state.svelte.js";
import "../../../../chunks/nprogress.js";
import { C as Card_shell } from "../../../../chunks/card_shell.js";
import { B as Button } from "../../../../chunks/button.js";
import { G as spread_props, y as pop, w as push, E as ensure_array_like, B as escape_html } from "../../../../chunks/index2.js";
import { C as Card, a as Card_content } from "../../../../chunks/card-content.js";
import { C as Card_header, a as Card_title, b as Card_description } from "../../../../chunks/card-title.js";
import { G as Gift } from "../../../../chunks/gift.js";
import { C as Chart_container, a as Chart_tooltip } from "../../../../chunks/chart-tooltip.js";
import "../../../../chunks/index4.js";
import { P as Progress, B as BarChart } from "../../../../chunks/progress.js";
import { T as Trending_up } from "../../../../chunks/trending-up.js";
import { P as Plus } from "../../../../chunks/plus.js";
function Award($$payload, $$props) {
  push();
  /**
   * @license @lucide/svelte v0.539.0 - ISC
   *
   * ISC License
   *
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   *
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   *
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   *
   */
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    [
      "path",
      {
        "d": "m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526"
      }
    ],
    ["circle", { "cx": "12", "cy": "8", "r": "6" }]
  ];
  Icon($$payload, spread_props([
    { name: "award" },
    /**
     * @component @name Award
     * @description Lucide SVG icon component, renders SVG Element with children.
     *
     * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTUuNDc3IDEyLjg5IDEuNTE1IDguNTI2YS41LjUgMCAwIDEtLjgxLjQ3bC0zLjU4LTIuNjg3YTEgMSAwIDAgMC0xLjE5NyAwbC0zLjU4NiAyLjY4NmEuNS41IDAgMCAxLS44MS0uNDY5bDEuNTE0LTguNTI2IiAvPgogIDxjaXJjbGUgY3g9IjEyIiBjeT0iOCIgcj0iNiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/award
     * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
     *
     * @param {Object} props - Lucide icons props and any valid SVG attribute
     * @returns {FunctionalComponent} Svelte component
     *
     */
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function Chart_column($$payload, $$props) {
  push();
  /**
   * @license @lucide/svelte v0.539.0 - ISC
   *
   * ISC License
   *
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   *
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   *
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   *
   */
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    ["path", { "d": "M3 3v16a2 2 0 0 0 2 2h16" }],
    ["path", { "d": "M18 17V9" }],
    ["path", { "d": "M13 17V5" }],
    ["path", { "d": "M8 17v-3" }]
  ];
  Icon($$payload, spread_props([
    { name: "chart-column" },
    /**
     * @component @name ChartColumn
     * @description Lucide SVG icon component, renders SVG Element with children.
     *
     * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyAzdjE2YTIgMiAwIDAgMCAyIDJoMTYiIC8+CiAgPHBhdGggZD0iTTE4IDE3VjkiIC8+CiAgPHBhdGggZD0iTTEzIDE3VjUiIC8+CiAgPHBhdGggZD0iTTggMTd2LTMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/chart-column
     * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
     *
     * @param {Object} props - Lucide icons props and any valid SVG attribute
     * @returns {FunctionalComponent} Svelte component
     *
     */
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function Star($$payload, $$props) {
  push();
  /**
   * @license @lucide/svelte v0.539.0 - ISC
   *
   * ISC License
   *
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   *
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   *
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   *
   */
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    [
      "path",
      {
        "d": "M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z"
      }
    ]
  ];
  Icon($$payload, spread_props([
    { name: "star" },
    /**
     * @component @name Star
     * @description Lucide SVG icon component, renders SVG Element with children.
     *
     * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTEuNTI1IDIuMjk1YS41My41MyAwIDAgMSAuOTUgMGwyLjMxIDQuNjc5YTIuMTIzIDIuMTIzIDAgMCAwIDEuNTk1IDEuMTZsNS4xNjYuNzU2YS41My41MyAwIDAgMSAuMjk0LjkwNGwtMy43MzYgMy42MzhhMi4xMjMgMi4xMjMgMCAwIDAtLjYxMSAxLjg3OGwuODgyIDUuMTRhLjUzLjUzIDAgMCAxLS43NzEuNTZsLTQuNjE4LTIuNDI4YTIuMTIyIDIuMTIyIDAgMCAwLTEuOTczIDBMNi4zOTYgMjEuMDFhLjUzLjUzIDAgMCAxLS43Ny0uNTZsLjg4MS01LjEzOWEyLjEyMiAyLjEyMiAwIDAgMC0uNjExLTEuODc5TDIuMTYgOS43OTVhLjUzLjUzIDAgMCAxIC4yOTQtLjkwNmw1LjE2NS0uNzU1YTIuMTIyIDIuMTIyIDAgMCAwIDEuNTk3LTEuMTZ6IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/star
     * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
     *
     * @param {Object} props - Lucide icons props and any valid SVG attribute
     * @returns {FunctionalComponent} Svelte component
     *
     */
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function Zap($$payload, $$props) {
  push();
  /**
   * @license @lucide/svelte v0.539.0 - ISC
   *
   * ISC License
   *
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   *
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   *
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   *
   */
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    [
      "path",
      {
        "d": "M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z"
      }
    ]
  ];
  Icon($$payload, spread_props([
    { name: "zap" },
    /**
     * @component @name Zap
     * @description Lucide SVG icon component, renders SVG Element with children.
     *
     * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNCAxNGExIDEgMCAwIDEtLjc4LTEuNjNsOS45LTEwLjJhLjUuNSAwIDAgMSAuODYuNDZsLTEuOTIgNi4wMkExIDEgMCAwIDAgMTMgMTBoN2ExIDEgMCAwIDEgLjc4IDEuNjNsLTkuOSAxMC4yYS41LjUgMCAwIDEtLjg2LS40NmwxLjkyLTYuMDJBMSAxIDAgMCAwIDExIDE0eiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/zap
     * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
     *
     * @param {Object} props - Lucide icons props and any valid SVG attribute
     * @returns {FunctionalComponent} Svelte component
     *
     */
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function Program_performance($$payload, $$props) {
  let { data } = $$props;
  $$payload.out.push(`<!---->`);
  Card($$payload, {
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->`);
      Card_header($$payload2, {
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Card_title($$payload3, {
            class: "flex items-center gap-2",
            children: ($$payload4) => {
              Chart_column($$payload4, { class: "h-5 w-5" });
              $$payload4.out.push(`<!----> Program Performance`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> <!---->`);
          Card_description($$payload3, {
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Redemption rates across different reward programs`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!----> <!---->`);
      Card_content($$payload2, {
        children: ($$payload3) => {
          const each_array = ensure_array_like(data);
          $$payload3.out.push(`<div class="space-y-4"><!--[-->`);
          for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
            let program = each_array[$$index];
            $$payload3.out.push(`<div class="space-y-2"><div class="flex items-center justify-between"><div class="space-y-1"><p class="text-sm font-medium leading-none">${escape_html(program.program)}</p> <p class="text-sm text-muted-foreground">${escape_html(program.participants.toLocaleString())} participants</p></div> <div class="text-right"><p class="text-sm font-medium">${escape_html(program.rate)}%</p> <p class="text-sm text-muted-foreground">${escape_html(program.redemptions.toLocaleString())} redeemed</p></div></div> `);
            Progress($$payload3, { value: program.rate, class: "h-2" });
            $$payload3.out.push(`<!----></div>`);
          }
          $$payload3.out.push(`<!--]--></div>`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!---->`);
}
function Rewards_analytics_chart($$payload, $$props) {
  let { data } = $$props;
  const chartConfig = {
    earned: { label: "Earned", color: "var(--chart-1)" },
    redeemed: { label: "Redeemed", color: "var(--chart-2)" }
  };
  $$payload.out.push(`<!---->`);
  Card($$payload, {
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->`);
      Card_header($$payload2, {
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Card_title($$payload3, {
            class: "flex items-center gap-2",
            children: ($$payload4) => {
              Trending_up($$payload4, { class: "h-5 w-5" });
              $$payload4.out.push(`<!----> Rewards Analytics`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> <!---->`);
          Card_description($$payload3, {
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Rewards earned vs redeemed over the last 6 months`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!----> <!---->`);
      Card_content($$payload2, {
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Chart_container($$payload3, {
            config: chartConfig,
            class: "h-[300px] w-full overflow-hidden",
            children: ($$payload4) => {
              {
                let tooltip = function($$payload5) {
                  $$payload5.out.push(`<!---->`);
                  Chart_tooltip($$payload5, {
                    indicator: "dashed",
                    labelFormatter: (label) => `${label} 2024`
                  });
                  $$payload5.out.push(`<!---->`);
                };
                BarChart($$payload4, {
                  data,
                  x: "month",
                  y: ["earned", "redeemed"],
                  padding: { left: 20, right: 20, top: 20, bottom: 40 },
                  yPadding: [0, 25],
                  props: { bars: { "fill-opacity": 0.8, motion: "tween" } },
                  tooltip,
                  $$slots: { tooltip: true }
                });
              }
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!---->`);
}
function Rewards_stats($$payload, $$props) {
  push();
  let { stats } = $$props;
  $$payload.out.push(`<div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4"><!---->`);
  Card($$payload, {
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->`);
      Card_header($$payload2, {
        class: "flex flex-row items-center justify-between space-y-0 pb-2",
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Card_title($$payload3, {
            class: "text-sm font-medium",
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Total Rewards`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> `);
          Gift($$payload3, { class: "h-4 w-4 text-muted-foreground" });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!----> <!---->`);
      Card_content($$payload2, {
        children: ($$payload3) => {
          $$payload3.out.push(`<div class="text-2xl font-bold">${escape_html(stats.totalRewards.toLocaleString())}</div> <p class="text-xs text-muted-foreground">+${escape_html(stats.growthRate)}% from last month</p>`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!----> <!---->`);
  Card($$payload, {
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->`);
      Card_header($$payload2, {
        class: "flex flex-row items-center justify-between space-y-0 pb-2",
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Card_title($$payload3, {
            class: "text-sm font-medium",
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Active Programs`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> `);
          Star($$payload3, { class: "h-4 w-4 text-muted-foreground" });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!----> <!---->`);
      Card_content($$payload2, {
        children: ($$payload3) => {
          $$payload3.out.push(`<div class="text-2xl font-bold">${escape_html(stats.activePrograms)}</div> <p class="text-xs text-muted-foreground">Across all categories</p>`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!----> <!---->`);
  Card($$payload, {
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->`);
      Card_header($$payload2, {
        class: "flex flex-row items-center justify-between space-y-0 pb-2",
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Card_title($$payload3, {
            class: "text-sm font-medium",
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Redemptions Today`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> `);
          Zap($$payload3, { class: "h-4 w-4 text-muted-foreground" });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!----> <!---->`);
      Card_content($$payload2, {
        children: ($$payload3) => {
          $$payload3.out.push(`<div class="text-2xl font-bold">${escape_html(stats.redemptionsToday.toLocaleString())}</div> <p class="text-xs text-muted-foreground">+${escape_html(stats.growthRate)}% from yesterday</p>`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!----> <!---->`);
  Card($$payload, {
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->`);
      Card_header($$payload2, {
        class: "flex flex-row items-center justify-between space-y-0 pb-2",
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Card_title($$payload3, {
            class: "text-sm font-medium",
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Total Value`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> `);
          Award($$payload3, { class: "h-4 w-4 text-muted-foreground" });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!----> <!---->`);
      Card_content($$payload2, {
        children: ($$payload3) => {
          $$payload3.out.push(`<div class="text-2xl font-bold">$${escape_html(stats.totalValue.toLocaleString())}</div> <p class="text-xs text-muted-foreground">Rewards distributed</p>`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!----></div>`);
  pop();
}
function _page($$payload) {
  const rewardsStats = {
    totalRewards: 15420,
    activePrograms: 8,
    redemptionsToday: 234,
    totalValue: 125680,
    growthRate: 18.5
  };
  const rewardsAnalyticsData = [
    { month: "Jul", earned: 18500, redeemed: 12300 },
    { month: "Aug", earned: 21200, redeemed: 14800 },
    { month: "Sep", earned: 19800, redeemed: 13900 },
    { month: "Oct", earned: 23400, redeemed: 16200 },
    { month: "Nov", earned: 25100, redeemed: 17800 },
    { month: "Dec", earned: 27300, redeemed: 19400 }
  ];
  const programPerformanceData = [
    {
      program: "Cashback",
      participants: 2456,
      redemptions: 1890,
      rate: 77
    },
    {
      program: "Points",
      participants: 1890,
      redemptions: 1234,
      rate: 65
    },
    {
      program: "Discounts",
      participants: 1567,
      redemptions: 1123,
      rate: 72
    },
    {
      program: "Gifts",
      participants: 890,
      redemptions: 567,
      rate: 64
    }
  ];
  const handleCreateReward = () => {
  };
  {
    let rightSlot = function($$payload2) {
      Button($$payload2, {
        onclick: handleCreateReward,
        class: "gap-2",
        children: ($$payload3) => {
          Plus($$payload3, { class: "h-4 w-4" });
          $$payload3.out.push(`<!----> Create Reward`);
        },
        $$slots: { default: true }
      });
    };
    Card_shell($$payload, {
      heading: "Rewards Dashboard",
      subHeading: "Manage customer reward programs and track redemption analytics",
      rightSlot,
      children: ($$payload2) => {
        $$payload2.out.push(`<div class="space-y-6">`);
        Rewards_stats($$payload2, { stats: rewardsStats });
        $$payload2.out.push(`<!----> <div class="grid gap-6 lg:grid-cols-2">`);
        Rewards_analytics_chart($$payload2, { data: rewardsAnalyticsData });
        $$payload2.out.push(`<!----> `);
        Program_performance($$payload2, { data: programPerformanceData });
        $$payload2.out.push(`<!----></div></div>`);
      }
    });
  }
}
export {
  _page as default
};
