import "clsx";
import { y as pop, w as push } from "../../../chunks/index2.js";
import "../../../chunks/client2.js";
import "../../../chunks/states.svelte.js";
import "linq-extensions";
import "@sveltejs/kit/internal";
import "../../../chunks/exports.js";
import "../../../chunks/state.svelte.js";
import "../../../chunks/nprogress.js";
import { C as Card_shell } from "../../../chunks/card_shell.js";
import { e as getOrgStore } from "../../../chunks/sheet-content.js";
import "../../../chunks/auth-client.js";
import "superjson";
import "@oslojs/crypto/sha2";
import "decimal.js";
import "remult";
import "../../../chunks/button.js";
import "../../../chunks/string.js";
import "@sveltejs/kit";
import "gravatar-gen";
import "../../../chunks/sidebar-menu-button.js";
import "../../../chunks/badge.js";
/* empty css                                                  */
import "../../../chunks/index4.js";
/* empty css                   */
function Main_module_dashboard($$payload) {
  $$payload.out.push(`<div><div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4"><div class="rounded-lg border bg-card text-card-foreground shadow-sm p-4"><h3 class="font-semibold">Appointments</h3> <p class="text-2xl font-bold">0</p> <p class="text-sm text-muted-foreground">Total Appointments</p></div> <div class="rounded-lg border bg-card text-card-foreground shadow-sm p-4"><h3 class="font-semibold">Campaigns</h3> <p class="text-2xl font-bold">0</p> <p class="text-sm text-muted-foreground">Active Campaigns</p></div> <div class="rounded-lg border bg-card text-card-foreground shadow-sm p-4"><h3 class="font-semibold">Chat Store</h3> <p class="text-2xl font-bold">0</p> <p class="text-sm text-muted-foreground">Active Chats</p></div> <div class="rounded-lg border bg-card text-card-foreground shadow-sm p-4"><h3 class="font-semibold">Laybye</h3> <p class="text-2xl font-bold">0</p> <p class="text-sm text-muted-foreground">Active Laybyes</p></div> <div class="rounded-lg border bg-card text-card-foreground shadow-sm p-4"><h3 class="font-semibold">Memberships</h3> <p class="text-2xl font-bold">0</p> <p class="text-sm text-muted-foreground">Total Members</p></div> <div class="rounded-lg border bg-card text-card-foreground shadow-sm p-4"><h3 class="font-semibold">Rewards</h3> <p class="text-2xl font-bold">0</p> <p class="text-sm text-muted-foreground">Active Rewards</p></div> <div class="rounded-lg border bg-card text-card-foreground shadow-sm p-4"><h3 class="font-semibold">Subscriptions</h3> <p class="text-2xl font-bold">0</p> <p class="text-sm text-muted-foreground">Active Subscriptions</p></div></div></div>`);
}
function _page($$payload, $$props) {
  push();
  const orgStore = getOrgStore();
  const heading = `${orgStore?.org?.name ?? "SpenDeed"} Dashboard`;
  const subHeading = "Birds Eye View of Your Business";
  $$payload.out.push(`<div class="space-y-6"><div class="flex items-center justify-between"><h2 class="text-3xl font-bold tracking-tight">Dashboard</h2></div> <div class="flex-1">`);
  Card_shell($$payload, {
    heading,
    subHeading,
    children: ($$payload2) => {
      Main_module_dashboard($$payload2);
    }
  });
  $$payload.out.push(`<!----></div></div>`);
  pop();
}
export {
  _page as default
};
