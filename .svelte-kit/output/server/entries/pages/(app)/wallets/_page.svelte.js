import "clsx";
import "../../../../chunks/client2.js";
import "../../../../chunks/states.svelte.js";
import "linq-extensions";
import "@sveltejs/kit/internal";
import "../../../../chunks/exports.js";
import "../../../../chunks/state.svelte.js";
import "../../../../chunks/nprogress.js";
import { C as Card_shell } from "../../../../chunks/card_shell.js";
import { B as Button } from "../../../../chunks/button.js";
import { B as escape_html, y as pop, w as push } from "../../../../chunks/index2.js";
import { C as Card, a as Card_content } from "../../../../chunks/card-content.js";
import { C as Card_header, a as Card_title } from "../../../../chunks/card-title.js";
import { W as Wallet } from "../../../../chunks/wallet.js";
import { T as Trending_up } from "../../../../chunks/trending-up.js";
import { D as Dollar_sign } from "../../../../chunks/dollar-sign.js";
import { C as Credit_card } from "../../../../chunks/credit-card.js";
import { P as Plus } from "../../../../chunks/plus.js";
function Wallet_stats($$payload, $$props) {
  push();
  let { stats } = $$props;
  $$payload.out.push(`<div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4"><!---->`);
  Card($$payload, {
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->`);
      Card_header($$payload2, {
        class: "flex flex-row items-center justify-between space-y-0 pb-2",
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Card_title($$payload3, {
            class: "text-sm font-medium",
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Total Wallets`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> `);
          Wallet($$payload3, { class: "h-4 w-4 text-muted-foreground" });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!----> <!---->`);
      Card_content($$payload2, {
        children: ($$payload3) => {
          $$payload3.out.push(`<div class="text-2xl font-bold">${escape_html(stats.totalWallets.toLocaleString())}</div> <p class="text-xs text-muted-foreground">+${escape_html(stats.growthRate)}% from last month</p>`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!----> <!---->`);
  Card($$payload, {
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->`);
      Card_header($$payload2, {
        class: "flex flex-row items-center justify-between space-y-0 pb-2",
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Card_title($$payload3, {
            class: "text-sm font-medium",
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Active Wallets`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> `);
          Trending_up($$payload3, { class: "h-4 w-4 text-muted-foreground" });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!----> <!---->`);
      Card_content($$payload2, {
        children: ($$payload3) => {
          $$payload3.out.push(`<div class="text-2xl font-bold">${escape_html(stats.activeWallets.toLocaleString())}</div> <p class="text-xs text-muted-foreground">${escape_html((stats.activeWallets / stats.totalWallets * 100).toFixed(1))}% active rate</p>`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!----> <!---->`);
  Card($$payload, {
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->`);
      Card_header($$payload2, {
        class: "flex flex-row items-center justify-between space-y-0 pb-2",
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Card_title($$payload3, {
            class: "text-sm font-medium",
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Total Balance`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> `);
          Dollar_sign($$payload3, { class: "h-4 w-4 text-muted-foreground" });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!----> <!---->`);
      Card_content($$payload2, {
        children: ($$payload3) => {
          $$payload3.out.push(`<div class="text-2xl font-bold">$${escape_html(stats.totalBalance.toLocaleString())}</div> <p class="text-xs text-muted-foreground">Across all wallets</p>`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!----> <!---->`);
  Card($$payload, {
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->`);
      Card_header($$payload2, {
        class: "flex flex-row items-center justify-between space-y-0 pb-2",
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Card_title($$payload3, {
            class: "text-sm font-medium",
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Transactions Today`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> `);
          Credit_card($$payload3, { class: "h-4 w-4 text-muted-foreground" });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!----> <!---->`);
      Card_content($$payload2, {
        children: ($$payload3) => {
          $$payload3.out.push(`<div class="text-2xl font-bold">${escape_html(stats.transactionsToday.toLocaleString())}</div> <p class="text-xs text-muted-foreground">+${escape_html(stats.growthRate)}% from yesterday</p>`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!----></div>`);
  pop();
}
function _page($$payload) {
  const walletStats = {
    totalWallets: 4567,
    activeWallets: 4234,
    totalBalance: 125e4,
    transactionsToday: 892,
    growthRate: 22.3
  };
  const handleCreateWallet = () => {
  };
  {
    let rightSlot = function($$payload2) {
      Button($$payload2, {
        onclick: handleCreateWallet,
        class: "gap-2",
        children: ($$payload3) => {
          Plus($$payload3, { class: "h-4 w-4" });
          $$payload3.out.push(`<!----> Create Wallet`);
        },
        $$slots: { default: true }
      });
    };
    Card_shell($$payload, {
      heading: "Wallets Dashboard",
      subHeading: "Manage customer digital wallets and track transaction activity",
      rightSlot,
      children: ($$payload2) => {
        $$payload2.out.push(`<div class="space-y-6">`);
        Wallet_stats($$payload2, { stats: walletStats });
        $$payload2.out.push(`<!----> <div class="text-muted-foreground py-12 text-center"><p>Additional wallet management components will be added here</p></div></div>`);
      }
    });
  }
}
export {
  _page as default
};
