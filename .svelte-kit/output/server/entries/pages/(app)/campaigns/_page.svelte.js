import "clsx";
import "../../../../chunks/client2.js";
import "../../../../chunks/states.svelte.js";
import "linq-extensions";
import "@sveltejs/kit/internal";
import "../../../../chunks/exports.js";
import "../../../../chunks/state.svelte.js";
import "../../../../chunks/nprogress.js";
import { C as Card_shell } from "../../../../chunks/card_shell.js";
import { B as Button } from "../../../../chunks/button.js";
import { P as Plus } from "../../../../chunks/plus.js";
function _page($$payload) {
  const handleCreateCampaign = () => {
  };
  {
    let rightSlot = function($$payload2) {
      Button($$payload2, {
        onclick: handleCreateCampaign,
        class: "gap-2",
        children: ($$payload3) => {
          Plus($$payload3, { class: "h-4 w-4" });
          $$payload3.out.push(`<!----> Create Campaign`);
        },
        $$slots: { default: true }
      });
    };
    Card_shell($$payload, {
      heading: "Marketing Campaigns",
      subHeading: "Create and manage marketing campaigns to drive customer engagement",
      rightSlot,
      children: ($$payload2) => {
        $$payload2.out.push(`<div class="space-y-6"><div class="text-muted-foreground py-12 text-center"><p>Create, manage, and track the performance of your marketing campaigns.</p></div></div>`);
      }
    });
  }
}
export {
  _page as default
};
