import "clsx";
import { C as Card, a as Card_content } from "../../../../../chunks/card-content.js";
import { C as Card_header, a as Card_title, b as Card_description } from "../../../../../chunks/card-title.js";
import { c as TabsContentState, T as Tabs, a as Tabs_list, b as Tabs_trigger } from "../../../../../chunks/tabs-trigger.js";
import { w as push, G as spread_props, y as pop, O as props_id, F as spread_attributes, P as bind_props, Q as copy_payload, R as assign_payload, B as escape_html } from "../../../../../chunks/index2.js";
import { c as cn } from "../../../../../chunks/shadcn.utils.js";
import { b as createId, d as box, m as mergeProps } from "../../../../../chunks/create-id.js";
import { I as Icon } from "../../../../../chunks/states.svelte.js";
import { g as getUtilsStore, e as getOrgStore } from "../../../../../chunks/sheet-content.js";
import { B as Button } from "../../../../../chunks/button.js";
import { N as No_data } from "../../../../../chunks/no-data.js";
import { L as Label } from "../../../../../chunks/label.js";
import { P as Pencil } from "../../../../../chunks/pencil.js";
import { C as Credit_card } from "../../../../../chunks/credit-card.js";
import "../../../../../chunks/string.js";
import "@sveltejs/kit";
import "@sveltejs/kit/internal";
import "../../../../../chunks/exports.js";
import "../../../../../chunks/state.svelte.js";
import "../../../../../chunks/auth-client.js";
import "superjson";
import "@oslojs/crypto/sha2";
import "decimal.js";
import "linq-extensions";
import "remult";
function Landmark($$payload, $$props) {
  push();
  /**
   * @license @lucide/svelte v0.539.0 - ISC
   *
   * ISC License
   *
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   *
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   *
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   *
   */
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    ["path", { "d": "M10 18v-7" }],
    [
      "path",
      {
        "d": "M11.12 2.198a2 2 0 0 1 1.76.006l7.866 3.847c.476.233.31.949-.22.949H3.474c-.53 0-.695-.716-.22-.949z"
      }
    ],
    ["path", { "d": "M14 18v-7" }],
    ["path", { "d": "M18 18v-7" }],
    ["path", { "d": "M3 22h18" }],
    ["path", { "d": "M6 18v-7" }]
  ];
  Icon($$payload, spread_props([
    { name: "landmark" },
    /**
     * @component @name Landmark
     * @description Lucide SVG icon component, renders SVG Element with children.
     *
     * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgMTh2LTciIC8+CiAgPHBhdGggZD0iTTExLjEyIDIuMTk4YTIgMiAwIDAgMSAxLjc2LjAwNmw3Ljg2NiAzLjg0N2MuNDc2LjIzMy4zMS45NDktLjIyLjk0OUgzLjQ3NGMtLjUzIDAtLjY5NS0uNzE2LS4yMi0uOTQ5eiIgLz4KICA8cGF0aCBkPSJNMTQgMTh2LTciIC8+CiAgPHBhdGggZD0iTTE4IDE4di03IiAvPgogIDxwYXRoIGQ9Ik0zIDIyaDE4IiAvPgogIDxwYXRoIGQ9Ik02IDE4di03IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/landmark
     * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
     *
     * @param {Object} props - Lucide icons props and any valid SVG attribute
     * @returns {FunctionalComponent} Svelte component
     *
     */
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function Tabs_content$1($$payload, $$props) {
  push();
  const uid = props_id($$payload);
  let {
    children,
    child,
    id = createId(uid),
    ref = null,
    value,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const contentState = TabsContentState.create({
    value: box.with(() => value),
    id: box.with(() => id),
    ref: box.with(() => ref, (v) => ref = v)
  });
  const mergedProps = mergeProps(restProps, contentState.props);
  if (child) {
    $$payload.out.push("<!--[-->");
    child($$payload, { props: mergedProps });
    $$payload.out.push(`<!---->`);
  } else {
    $$payload.out.push("<!--[!-->");
    $$payload.out.push(`<div${spread_attributes({ ...mergedProps }, null)}>`);
    children?.($$payload);
    $$payload.out.push(`<!----></div>`);
  }
  $$payload.out.push(`<!--]-->`);
  bind_props($$props, { ref });
  pop();
}
function Tabs_content($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out.push(`<!---->`);
    Tabs_content$1($$payload2, spread_props([
      {
        "data-slot": "tabs-content",
        class: cn("flex-1 outline-none", className)
      },
      restProps,
      {
        get ref() {
          return ref;
        },
        set ref($$value) {
          ref = $$value;
          $$settled = false;
        }
      }
    ]));
    $$payload2.out.push(`<!---->`);
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref });
  pop();
}
function Bank_details($$payload, $$props) {
  push();
  const uStore = getUtilsStore();
  const orgStore = getOrgStore();
  let hasBankDetails = !!orgStore.orgBankDetail;
  let noDataText = hasBankDetails ? "You haven't added any bank account details yet. Add your bank details to start managing payments." : "You may not have the permissions required to view this data. Please contact your Team administrator.";
  const startAddBankDetails = () => {
    const bankDetailsForm = {
      canClose: false,
      title: "Add Bank Details",
      componentName: "AddBankDetails",
      size: "lg",
      color: "default",
      description: "Add your bank account details for payment processing",
      componentProps: {
        bankDetailsForm: {}
        // Add your form schema here
      }
    };
    uStore.openDynamicModal(bankDetailsForm);
  };
  if (hasBankDetails) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="grid auto-cols-auto grid-flow-col gap-2 max-md:grid-rows-2"><!---->`);
    Card($$payload, {
      class: "w-full",
      children: ($$payload2) => {
        $$payload2.out.push(`<!---->`);
        Card_header($$payload2, {
          children: ($$payload3) => {
            $$payload3.out.push(`<!---->`);
            Card_title($$payload3, {
              class: "flex items-center justify-between",
              children: ($$payload4) => {
                $$payload4.out.push(`<!---->Bank Account Details `);
                Button($$payload4, {
                  variant: "ghost",
                  size: "icon",
                  onclick: startAddBankDetails,
                  children: ($$payload5) => {
                    Pencil($$payload5, { class: "size-4" });
                  },
                  $$slots: { default: true }
                });
                $$payload4.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload3.out.push(`<!---->`);
          },
          $$slots: { default: true }
        });
        $$payload2.out.push(`<!----> <!---->`);
        Card_content($$payload2, {
          children: ($$payload3) => {
            $$payload3.out.push(`<div class="grid grid-cols-1 gap-4 space-y-4 md:grid-cols-3"><div class="space-y-1">`);
            Label($$payload3, {
              children: ($$payload4) => {
                $$payload4.out.push(`<!---->Account Name/alias`);
              },
              $$slots: { default: true }
            });
            $$payload3.out.push(`<!----> <p class="text-muted-foreground text-sm">${escape_html(orgStore.orgBankDetail?.accountName)}</p></div> <div class="space-y-1">`);
            Label($$payload3, {
              children: ($$payload4) => {
                $$payload4.out.push(`<!---->Bank Name`);
              },
              $$slots: { default: true }
            });
            $$payload3.out.push(`<!----> <p class="text-muted-foreground text-sm">${escape_html(orgStore.orgBankDetail?.bankName)}</p></div> <div class="space-y-1">`);
            Label($$payload3, {
              children: ($$payload4) => {
                $$payload4.out.push(`<!---->Account Number`);
              },
              $$slots: { default: true }
            });
            $$payload3.out.push(`<!----> <p class="text-muted-foreground text-sm">${escape_html(orgStore.orgBankDetail?.accountNumber)}</p></div> <div class="space-y-1">`);
            Label($$payload3, {
              children: ($$payload4) => {
                $$payload4.out.push(`<!---->Branch Code`);
              },
              $$slots: { default: true }
            });
            $$payload3.out.push(`<!----> <p class="text-muted-foreground text-sm">${escape_html(orgStore.orgBankDetail?.branchCode)}</p></div> <div class="space-y-1">`);
            Label($$payload3, {
              children: ($$payload4) => {
                $$payload4.out.push(`<!---->Swift Code`);
              },
              $$slots: { default: true }
            });
            $$payload3.out.push(`<!----> <p class="text-muted-foreground text-sm">${escape_html(orgStore.orgBankDetail?.swiftCode)}</p></div> <div class="space-y-1">`);
            Label($$payload3, {
              children: ($$payload4) => {
                $$payload4.out.push(`<!---->Created At`);
              },
              $$slots: { default: true }
            });
            $$payload3.out.push(`<!----> <p class="text-muted-foreground text-sm">${escape_html(orgStore.orgBankDetail?.createdAt)}</p></div> <div class="space-y-1">`);
            Label($$payload3, {
              children: ($$payload4) => {
                $$payload4.out.push(`<!---->Last Update:`);
              },
              $$slots: { default: true }
            });
            $$payload3.out.push(`<!----> <p class="text-muted-foreground text-sm">${escape_html(orgStore.orgBankDetail?.updatedAt)}</p></div></div>`);
          },
          $$slots: { default: true }
        });
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    });
    $$payload.out.push(`<!----></div>`);
  } else {
    let bankIconSnippet = function($$payload2) {
      Landmark($$payload2, { class: "h-10 w-10" });
    };
    $$payload.out.push("<!--[!-->");
    No_data($$payload, {
      noDataTitle: "No Bank Details Found",
      noDataDescription: noDataText,
      snippet: bankIconSnippet,
      children: ($$payload2) => {
        $$payload2.out.push(`<div class="flex justify-between space-x-2">`);
        Button($$payload2, {
          variant: "default",
          onclick: startAddBankDetails,
          children: ($$payload3) => {
            $$payload3.out.push(`<!---->Add Bank Details`);
          },
          $$slots: { default: true }
        });
        $$payload2.out.push(`<!----></div>`);
      }
    });
  }
  $$payload.out.push(`<!--]-->`);
  pop();
}
function Payment_provider($$payload, $$props) {
  push();
  const uStore = getUtilsStore();
  const orgStore = getOrgStore();
  let hasPaymentProvider = !!orgStore.orgPaymentProvider;
  const startConfigurePaymentEngine = () => {
    const paymentEngineForm = {
      canClose: false,
      title: "Configure Payment Engine",
      componentName: "AddPaymentProvider",
      size: "lg",
      color: "default",
      description: "Set up your payment engine configuration",
      componentProps: {}
    };
    uStore.openDynamicModal(paymentEngineForm);
  };
  if (hasPaymentProvider) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<!---->`);
    Card($$payload, {
      children: ($$payload2) => {
        $$payload2.out.push(`<!---->`);
        Card_header($$payload2, {
          children: ($$payload3) => {
            $$payload3.out.push(`<!---->`);
            Card_title($$payload3, {
              class: "flex items-center justify-between",
              children: ($$payload4) => {
                $$payload4.out.push(`<!---->Payment Provider `);
                Button($$payload4, {
                  variant: "ghost",
                  size: "icon",
                  onclick: startConfigurePaymentEngine,
                  children: ($$payload5) => {
                    Pencil($$payload5, { class: "size-4" });
                  },
                  $$slots: { default: true }
                });
                $$payload4.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload3.out.push(`<!----> <!---->`);
            Card_description($$payload3, {
              children: ($$payload4) => {
                $$payload4.out.push(`<!---->Manage your Payment Provider for receiving payments. Please note that this information
				is encrypted and stored. Only the Team admin's credential can decrypt and view the
				information`);
              },
              $$slots: { default: true }
            });
            $$payload3.out.push(`<!---->`);
          },
          $$slots: { default: true }
        });
        $$payload2.out.push(`<!----> <!---->`);
        Card_content($$payload2, {
          children: ($$payload3) => {
            $$payload3.out.push(`<div class="grid grid-cols-1 gap-4 space-y-4 md:grid-cols-3"><div class="space-y-1">`);
            Label($$payload3, {
              children: ($$payload4) => {
                $$payload4.out.push(`<!---->Provider Name`);
              },
              $$slots: { default: true }
            });
            $$payload3.out.push(`<!----> <p class="text-muted-foreground text-sm">${escape_html(orgStore.orgPaymentProvider?.providerName)}</p></div> <div class="space-y-1">`);
            Label($$payload3, {
              children: ($$payload4) => {
                $$payload4.out.push(`<!---->Api URL`);
              },
              $$slots: { default: true }
            });
            $$payload3.out.push(`<!----> <p class="text-muted-foreground text-sm">${escape_html(orgStore.orgPaymentProvider?.providerApiUrl)}</p></div> <div class="space-y-1">`);
            Label($$payload3, {
              children: ($$payload4) => {
                $$payload4.out.push(`<!---->Api Key`);
              },
              $$slots: { default: true }
            });
            $$payload3.out.push(`<!----> <p class="text-muted-foreground text-sm">${escape_html(orgStore.orgPaymentProvider?.providerApiKey)}</p></div> <div class="space-y-1">`);
            Label($$payload3, {
              children: ($$payload4) => {
                $$payload4.out.push(`<!---->Api Secret`);
              },
              $$slots: { default: true }
            });
            $$payload3.out.push(`<!----> <p class="text-muted-foreground text-sm">${escape_html(orgStore.orgPaymentProvider?.providerApiSecret)}</p></div> <div class="space-y-1">`);
            Label($$payload3, {
              children: ($$payload4) => {
                $$payload4.out.push(`<!---->Date Created`);
              },
              $$slots: { default: true }
            });
            $$payload3.out.push(`<!----> <p class="text-muted-foreground text-sm">${escape_html(orgStore.orgPaymentProvider?.createdAt)}</p></div> <div class="space-y-1">`);
            Label($$payload3, {
              children: ($$payload4) => {
                $$payload4.out.push(`<!---->Date Last Updated`);
              },
              $$slots: { default: true }
            });
            $$payload3.out.push(`<!----> <p class="text-muted-foreground text-sm">${escape_html(orgStore.orgPaymentProvider?.updatedAt)}</p></div></div>`);
          },
          $$slots: { default: true }
        });
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    });
    $$payload.out.push(`<!---->`);
  } else {
    let paymentIconSnippet = function($$payload2) {
      Credit_card($$payload2, { class: "h-10 w-10" });
    };
    $$payload.out.push("<!--[!-->");
    No_data($$payload, {
      noDataTitle: "No Payment Engine Configured",
      noDataDescription: "You haven't set up your payment engine yet. Configure your payment settings to start accepting payments.",
      snippet: paymentIconSnippet,
      children: ($$payload2) => {
        $$payload2.out.push(`<div class="flex justify-between space-x-2">`);
        Button($$payload2, {
          variant: "default",
          onclick: startConfigurePaymentEngine,
          children: ($$payload3) => {
            $$payload3.out.push(`<!---->Configure Payment Engine`);
          },
          $$slots: { default: true }
        });
        $$payload2.out.push(`<!----></div>`);
      }
    });
  }
  $$payload.out.push(`<!--]-->`);
  pop();
}
function _page($$payload) {
  $$payload.out.push(`<!---->`);
  Card($$payload, {
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->`);
      Card_header($$payload2, {
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Card_title($$payload3, {
            class: "flex items-center justify-between",
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Financial Info Settings`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!----> <!---->`);
      Card_content($$payload2, {
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Tabs($$payload3, {
            value: "bank-details",
            class: "w-full",
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->`);
              Tabs_list($$payload4, {
                children: ($$payload5) => {
                  $$payload5.out.push(`<!---->`);
                  Tabs_trigger($$payload5, {
                    value: "bank-details",
                    children: ($$payload6) => {
                      $$payload6.out.push(`<!---->Bank Details`);
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out.push(`<!----> <!---->`);
                  Tabs_trigger($$payload5, {
                    value: "payment-engine",
                    children: ($$payload6) => {
                      $$payload6.out.push(`<!---->Payment Engine`);
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out.push(`<!---->`);
                },
                $$slots: { default: true }
              });
              $$payload4.out.push(`<!----> <!---->`);
              Tabs_content($$payload4, {
                value: "bank-details",
                children: ($$payload5) => {
                  Bank_details($$payload5);
                },
                $$slots: { default: true }
              });
              $$payload4.out.push(`<!----> <!---->`);
              Tabs_content($$payload4, {
                value: "payment-engine",
                children: ($$payload5) => {
                  Payment_provider($$payload5);
                },
                $$slots: { default: true }
              });
              $$payload4.out.push(`<!---->`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!---->`);
}
export {
  _page as default
};
