import "clsx";
import "../../../../chunks/states.svelte.js";
import { B as Button } from "../../../../chunks/button.js";
import { d as getAuthStore, e as getOrgStore, g as getUtilsStore } from "../../../../chunks/sheet-content.js";
import { C as Card_footer, F as Form_description, D as Dynamic_modal } from "../../../../chunks/form-description.js";
import "../../../../chunks/badge.js";
import "@sveltejs/kit/internal";
import "../../../../chunks/exports.js";
import "../../../../chunks/state.svelte.js";
import { b as authClient, o as addMemberFormSchema, G as GenderTypes, R as RolesType, O as OrgPaymentProviderController, p as paymentProviderFormSchema, q as OrgBankDetailController, r as bankAccountFormSchema } from "../../../../chunks/auth-client.js";
import "superjson";
import "@oslojs/crypto/sha2";
import "decimal.js";
import "linq-extensions";
import "remult";
import { w as push, Q as copy_payload, R as assign_payload, y as pop, G as spread_props, V as store_mutate, T as store_get, B as escape_html, z as attr, E as ensure_array_like, U as unsubscribe_stores } from "../../../../chunks/index2.js";
import { s as superForm } from "../../../../chunks/string.js";
import { a as valibotClient, d as defaults } from "../../../../chunks/valibot.js";
import "@sveltejs/kit";
import { g as getFlashModule } from "../../../../chunks/dialog.js";
import { F as Form_alerts, a as Form_field, C as Control, b as Form_field_errors } from "../../../../chunks/index5.js";
import { C as Card, a as Card_content } from "../../../../chunks/card-content.js";
import { R as Root, S as Select_trigger, a as Select_content, b as Select_item } from "../../../../chunks/index8.js";
import { I as Input } from "../../../../chunks/input.js";
import { F as Form_label, a as Form_button, L as Loader_circle } from "../../../../chunks/form-button.js";
import { C as Card_header, a as Card_title, b as Card_description } from "../../../../chunks/card-title.js";
import { S as Switch } from "../../../../chunks/switch.js";
import { uuidv7 } from "@kripod/uuidv7";
import { E as Eye } from "../../../../chunks/eye.js";
function Add_team_member($$payload, $$props) {
  push();
  var $$store_subs;
  let { data = {} } = $$props;
  getAuthStore();
  const orgStore = getOrgStore();
  const form = superForm(defaults(data, valibotClient(addMemberFormSchema)), {
    SPA: true,
    resetForm: true,
    clearOnSubmit: "errors-and-message",
    validators: valibotClient(addMemberFormSchema),
    async onUpdate({ form: form2 }) {
      try {
        const { data: formData2 } = form2;
        const teamLocation = orgStore.teams.firstOrNull((k) => k.id === formData2.location);
        const { data: data2, error } = await authClient.organization.inviteMember({
          email: formData2.email,
          role: formData2.role,
          organizationId: orgStore.org?.id,
          teamId: teamLocation?.id,
          resend: true
        });
        if (error) {
          throw error;
        }
        form2.message = {
          text: "Team member invite sent successfully!",
          status: "success"
        };
      } catch (error) {
        console.error(error);
        form2.message = { text: "Failed to send team member invite!", status: "error" };
      }
    },
    ...getFlashModule()
  });
  const {
    form: formData,
    enhance,
    submitting,
    allErrors,
    message,
    validateForm
  } = form;
  let utilsStore = getUtilsStore();
  const capitalizeWords = (word) => {
    return word.split("-").map((w) => w.charAt(0).toUpperCase() + w.slice(1)).join(" ");
  };
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out.push(`<form method="POST">`);
    Form_alerts($$payload2, { message });
    $$payload2.out.push(`<!----> <!---->`);
    Card($$payload2, {
      class: "rounded-none",
      children: ($$payload3) => {
        $$payload3.out.push(`<!---->`);
        Card_content($$payload3, {
          class: " mb-4 grid grid-cols-1 gap-3  md:grid-cols-3",
          children: ($$payload4) => {
            $$payload4.out.push(`<!---->`);
            Form_field($$payload4, {
              form,
              name: "givenName",
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->`);
                {
                  let children = function($$payload6, { props }) {
                    $$payload6.out.push(`<!---->`);
                    Form_label($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out.push(`<!---->First Name`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out.push(`<!----> `);
                    Input($$payload6, spread_props([
                      props,
                      {
                        placeholder: "First Name",
                        get value() {
                          return store_get($$store_subs ??= {}, "$formData", formData).givenName;
                        },
                        set value($$value) {
                          store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).givenName = $$value);
                          $$settled = false;
                        }
                      }
                    ]));
                    $$payload6.out.push(`<!---->`);
                  };
                  Control($$payload5, { children });
                }
                $$payload5.out.push(`<!----> <!---->`);
                Form_field_errors($$payload5, {});
                $$payload5.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!----> <!---->`);
            Form_field($$payload4, {
              form,
              name: "familyName",
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->`);
                {
                  let children = function($$payload6, { props }) {
                    $$payload6.out.push(`<!---->`);
                    Form_label($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out.push(`<!---->First Name`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out.push(`<!----> `);
                    Input($$payload6, spread_props([
                      props,
                      {
                        placeholder: "First Name",
                        get value() {
                          return store_get($$store_subs ??= {}, "$formData", formData).familyName;
                        },
                        set value($$value) {
                          store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).familyName = $$value);
                          $$settled = false;
                        }
                      }
                    ]));
                    $$payload6.out.push(`<!---->`);
                  };
                  Control($$payload5, { children });
                }
                $$payload5.out.push(`<!----> <!---->`);
                Form_field_errors($$payload5, {});
                $$payload5.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!----> <!---->`);
            Form_field($$payload4, {
              form,
              name: "email",
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->`);
                {
                  let children = function($$payload6, { props }) {
                    $$payload6.out.push(`<!---->`);
                    Form_label($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out.push(`<!---->Email`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out.push(`<!----> `);
                    Input($$payload6, spread_props([
                      props,
                      {
                        placeholder: "Email",
                        get value() {
                          return store_get($$store_subs ??= {}, "$formData", formData).email;
                        },
                        set value($$value) {
                          store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).email = $$value);
                          $$settled = false;
                        }
                      }
                    ]));
                    $$payload6.out.push(`<!---->`);
                  };
                  Control($$payload5, { children });
                }
                $$payload5.out.push(`<!----> <!---->`);
                Form_field_errors($$payload5, {});
                $$payload5.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!----> <!---->`);
            Form_field($$payload4, {
              form,
              name: "phoneNumber",
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->`);
                {
                  let children = function($$payload6, { props }) {
                    $$payload6.out.push(`<!---->`);
                    Form_label($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out.push(`<!---->Phone No.`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out.push(`<!----> `);
                    Input($$payload6, spread_props([
                      props,
                      {
                        placeholder: "Phone Number",
                        get value() {
                          return store_get($$store_subs ??= {}, "$formData", formData).phoneNumber;
                        },
                        set value($$value) {
                          store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).phoneNumber = $$value);
                          $$settled = false;
                        }
                      }
                    ]));
                    $$payload6.out.push(`<!---->`);
                  };
                  Control($$payload5, { children });
                }
                $$payload5.out.push(`<!----> <!---->`);
                Form_field_errors($$payload5, {});
                $$payload5.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!----> <!---->`);
            Form_field($$payload4, {
              form,
              name: "idDocumentNumber",
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->`);
                {
                  let children = function($$payload6, { props }) {
                    $$payload6.out.push(`<!---->`);
                    Form_label($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out.push(`<!---->ID Number`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out.push(`<!----> `);
                    Input($$payload6, spread_props([
                      props,
                      {
                        placeholder: "ID Number",
                        get value() {
                          return store_get($$store_subs ??= {}, "$formData", formData).idDocumentNumber;
                        },
                        set value($$value) {
                          store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).idDocumentNumber = $$value);
                          $$settled = false;
                        }
                      }
                    ]));
                    $$payload6.out.push(`<!---->`);
                  };
                  Control($$payload5, { children });
                }
                $$payload5.out.push(`<!----> <!---->`);
                Form_field_errors($$payload5, {});
                $$payload5.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!----> <!---->`);
            Form_field($$payload4, {
              form,
              name: "gender",
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->`);
                {
                  let children = function($$payload6, { props }) {
                    $$payload6.out.push(`<!---->`);
                    Form_label($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out.push(`<!---->Gender`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out.push(`<!----> <!---->`);
                    Root($$payload6, {
                      type: "single",
                      name: props.name,
                      get value() {
                        return store_get($$store_subs ??= {}, "$formData", formData).gender;
                      },
                      set value($$value) {
                        store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).gender = $$value);
                        $$settled = false;
                      },
                      children: ($$payload7) => {
                        $$payload7.out.push(`<!---->`);
                        Select_trigger($$payload7, spread_props([
                          props,
                          {
                            children: ($$payload8) => {
                              $$payload8.out.push(`<!---->${escape_html(store_get($$store_subs ??= {}, "$formData", formData).gender ? store_get($$store_subs ??= {}, "$formData", formData).gender : "Select a Gender")}`);
                            },
                            $$slots: { default: true }
                          }
                        ]));
                        $$payload7.out.push(`<!----> <!---->`);
                        Select_content($$payload7, {
                          children: ($$payload8) => {
                            $$payload8.out.push(`<!---->`);
                            Select_item($$payload8, { value: GenderTypes.Male, label: GenderTypes.Male });
                            $$payload8.out.push(`<!----> <!---->`);
                            Select_item($$payload8, { value: GenderTypes.Female, label: GenderTypes.Female });
                            $$payload8.out.push(`<!----> <!---->`);
                            Select_item($$payload8, { value: GenderTypes.Other, label: GenderTypes.Other });
                            $$payload8.out.push(`<!---->`);
                          },
                          $$slots: { default: true }
                        });
                        $$payload7.out.push(`<!---->`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out.push(`<!----> <input${attr("name", props.name)}${attr("value", store_get($$store_subs ??= {}, "$formData", formData).gender)} hidden/>`);
                  };
                  Control($$payload5, { children });
                }
                $$payload5.out.push(`<!----> <!---->`);
                Form_field_errors($$payload5, {});
                $$payload5.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!----> <!---->`);
            Form_field($$payload4, {
              form,
              name: "role",
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->`);
                {
                  let children = function($$payload6, { props }) {
                    $$payload6.out.push(`<!---->`);
                    Form_label($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out.push(`<!---->Role`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out.push(`<!----> <!---->`);
                    Root($$payload6, {
                      type: "single",
                      name: props.name,
                      get value() {
                        return store_get($$store_subs ??= {}, "$formData", formData).role;
                      },
                      set value($$value) {
                        store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).role = $$value);
                        $$settled = false;
                      },
                      children: ($$payload7) => {
                        $$payload7.out.push(`<!---->`);
                        Select_trigger($$payload7, spread_props([
                          props,
                          {
                            children: ($$payload8) => {
                              $$payload8.out.push(`<!---->${escape_html(store_get($$store_subs ??= {}, "$formData", formData).role ? store_get($$store_subs ??= {}, "$formData", formData).role : "Select a Role")}`);
                            },
                            $$slots: { default: true }
                          }
                        ]));
                        $$payload7.out.push(`<!----> <!---->`);
                        Select_content($$payload7, {
                          children: ($$payload8) => {
                            $$payload8.out.push(`<!---->`);
                            Select_item($$payload8, {
                              value: RolesType.TeamMember,
                              label: capitalizeWords(RolesType.TeamMember)
                            });
                            $$payload8.out.push(`<!----> <!---->`);
                            Select_item($$payload8, {
                              value: RolesType.TeamLocationAdmin,
                              label: capitalizeWords(RolesType.TeamLocationAdmin)
                            });
                            $$payload8.out.push(`<!----> <!---->`);
                            Select_item($$payload8, {
                              value: RolesType.OrgAdmin,
                              label: capitalizeWords(RolesType.OrgAdmin)
                            });
                            $$payload8.out.push(`<!---->`);
                          },
                          $$slots: { default: true }
                        });
                        $$payload7.out.push(`<!---->`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out.push(`<!----> <input${attr("name", props.name)}${attr("value", store_get($$store_subs ??= {}, "$formData", formData).gender)} hidden/>`);
                  };
                  Control($$payload5, { children });
                }
                $$payload5.out.push(`<!----> <!---->`);
                Form_field_errors($$payload5, {});
                $$payload5.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!----> <!---->`);
            Form_field($$payload4, {
              form,
              name: "location",
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->`);
                {
                  let children = function($$payload6, { props }) {
                    $$payload6.out.push(`<!---->`);
                    Form_label($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out.push(`<!---->Assigned Location`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out.push(`<!----> <!---->`);
                    Root($$payload6, {
                      type: "single",
                      name: props.name,
                      get value() {
                        return store_get($$store_subs ??= {}, "$formData", formData).location;
                      },
                      set value($$value) {
                        store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).location = $$value);
                        $$settled = false;
                      },
                      children: ($$payload7) => {
                        $$payload7.out.push(`<!---->`);
                        Select_trigger($$payload7, {
                          children: ($$payload8) => {
                            $$payload8.out.push(`<!---->${escape_html(store_get($$store_subs ??= {}, "$formData", formData).location ? store_get($$store_subs ??= {}, "$formData", formData).location : "Select a Location")}`);
                          },
                          $$slots: { default: true }
                        });
                        $$payload7.out.push(`<!----> <!---->`);
                        Select_content($$payload7, {
                          children: ($$payload8) => {
                            const each_array = ensure_array_like(orgStore.teams);
                            $$payload8.out.push(`<!--[-->`);
                            for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
                              let teamLocation = each_array[$$index];
                              $$payload8.out.push(`<!---->`);
                              Select_item($$payload8, { value: teamLocation.id, label: teamLocation.name });
                              $$payload8.out.push(`<!---->`);
                            }
                            $$payload8.out.push(`<!--]-->`);
                          },
                          $$slots: { default: true }
                        });
                        $$payload7.out.push(`<!---->`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out.push(`<!----> <input${attr("name", props.name)}${attr("value", store_get($$store_subs ??= {}, "$formData", formData).gender)} hidden/>`);
                  };
                  Control($$payload5, { children });
                }
                $$payload5.out.push(`<!----> <!---->`);
                Form_field_errors($$payload5, {});
                $$payload5.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!---->`);
          },
          $$slots: { default: true }
        });
        $$payload3.out.push(`<!----> <!---->`);
        Card_footer($$payload3, {
          class: "flex justify-between",
          children: ($$payload4) => {
            Button($$payload4, {
              disabled: store_get($$store_subs ??= {}, "$submitting", submitting),
              variant: "outline",
              class: "mt-4 w-1/3",
              onclick: () => utilsStore.clearDynamicModal(),
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->Cancel`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!----> <!---->`);
            Form_button($$payload4, {
              disabled: store_get($$store_subs ??= {}, "$allErrors", allErrors).length > 0 || store_get($$store_subs ??= {}, "$submitting", submitting),
              class: "mt-4 w-4/10",
              children: ($$payload5) => {
                if (store_get($$store_subs ??= {}, "$submitting", submitting)) {
                  $$payload5.out.push("<!--[-->");
                  Loader_circle($$payload5, { class: "mr-2 size-4 animate-spin" });
                } else {
                  $$payload5.out.push("<!--[!-->");
                }
                $$payload5.out.push(`<!--]--> ${escape_html(store_get($$store_subs ??= {}, "$submitting", submitting) ? "Please wait..." : "Invite Team Member")}`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!---->`);
          },
          $$slots: { default: true }
        });
        $$payload3.out.push(`<!---->`);
      },
      $$slots: { default: true }
    });
    $$payload2.out.push(`<!----></form>`);
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
function Add_payment_provider($$payload, $$props) {
  push();
  var $$store_subs;
  let { data = {} } = $$props;
  const orgStore = getOrgStore();
  let hasPaymentProvider = !!orgStore.orgPaymentProvider;
  const form = superForm(defaults(orgStore.orgPaymentProvider, valibotClient(paymentProviderFormSchema)), {
    SPA: true,
    resetForm: true,
    clearOnSubmit: "errors-and-message",
    validators: valibotClient(paymentProviderFormSchema),
    async onUpdate({ form: form2 }) {
      if (!form2.valid) return;
      try {
        const { data: formData2 } = form2;
        const paymentProvider = {
          id: uuidv7(),
          organizationId: orgStore.org?.id,
          providerName: formData2.providerName,
          providerApiKey: formData2.providerApiKey ?? "",
          providerApiSecret: formData2.providerApiSecret ?? "",
          providerApiUrl: formData2.providerApiUrl,
          providerClientId: formData2.providerClientId,
          providerWebhookUrl: formData2.providerWebhookUrl
        };
        let providerMutResp;
        if (!hasPaymentProvider) {
          providerMutResp = await OrgPaymentProviderController.insert(paymentProvider);
        } else {
          providerMutResp = await OrgPaymentProviderController.update(orgStore.orgPaymentProvider?.id, paymentProvider);
        }
        if (!providerMutResp.id) {
          throw new Error("Failed to save payment provider");
        } else {
          utilsStore.toastSuccess("Payment Provider SDK details updated successfully!");
          utilsStore.clearDynamicModal();
          form2.message = {
            status: "success",
            text: "Payment provider added successfully"
          };
        }
      } catch (err) {
        form2.message = {
          status: "error",
          text: `Error adding payment provider ${err.message}`
        };
      }
    },
    ...getFlashModule()
  });
  const {
    form: formData,
    enhance,
    submitting,
    allErrors,
    message,
    validateForm
  } = form;
  let utilsStore = getUtilsStore();
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out.push(`<form method="POST">`);
    Form_alerts($$payload2, { message });
    $$payload2.out.push(`<!----> <!---->`);
    Card($$payload2, {
      class: "mx-4 rounded-none",
      children: ($$payload3) => {
        $$payload3.out.push(`<!---->`);
        Card_header($$payload3, {
          children: ($$payload4) => {
            $$payload4.out.push(`<!---->`);
            Card_title($$payload4, {
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->${escape_html(data?.providerName ? "Edit" : "Add")} Payment Provider`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!----> <!---->`);
            Card_description($$payload4, {
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->Configure payment provider details to enable payment processing`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!---->`);
          },
          $$slots: { default: true }
        });
        $$payload3.out.push(`<!----> <!---->`);
        Card_content($$payload3, {
          class: "mb-4 grid grid-cols-1 gap-3 md:grid-cols-2",
          children: ($$payload4) => {
            $$payload4.out.push(`<!---->`);
            Form_field($$payload4, {
              form,
              name: "providerName",
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->`);
                {
                  let children = function($$payload6, { props }) {
                    $$payload6.out.push(`<!---->`);
                    Form_label($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out.push(`<!---->Provider Name`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out.push(`<!----> `);
                    Input($$payload6, spread_props([
                      props,
                      {
                        placeholder: "e.g.,PayStack,Stitch,etc",
                        get value() {
                          return store_get($$store_subs ??= {}, "$formData", formData).providerName;
                        },
                        set value($$value) {
                          store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).providerName = $$value);
                          $$settled = false;
                        }
                      }
                    ]));
                    $$payload6.out.push(`<!---->`);
                  };
                  Control($$payload5, { children });
                }
                $$payload5.out.push(`<!----> <!---->`);
                Form_field_errors($$payload5, {});
                $$payload5.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!----> <!---->`);
            Form_field($$payload4, {
              form,
              name: "providerApiUrl",
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->`);
                {
                  let children = function($$payload6, { props }) {
                    $$payload6.out.push(`<!---->`);
                    Form_label($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out.push(`<!---->API URL`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out.push(`<!----> `);
                    Input($$payload6, spread_props([
                      props,
                      {
                        placeholder: "https://api.provider.com",
                        get value() {
                          return store_get($$store_subs ??= {}, "$formData", formData).providerApiUrl;
                        },
                        set value($$value) {
                          store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).providerApiUrl = $$value);
                          $$settled = false;
                        }
                      }
                    ]));
                    $$payload6.out.push(`<!---->`);
                  };
                  Control($$payload5, { children });
                }
                $$payload5.out.push(`<!----> <!---->`);
                Form_field_errors($$payload5, {});
                $$payload5.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!----> <!---->`);
            Form_field($$payload4, {
              form,
              name: "providerApiKey",
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->`);
                {
                  let children = function($$payload6, { props }) {
                    $$payload6.out.push(`<!---->`);
                    Form_label($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out.push(`<!---->API Key`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out.push(`<!----> <div class="relative">`);
                    Input($$payload6, spread_props([
                      props,
                      {
                        type: "password",
                        placeholder: "Enter API Key",
                        get value() {
                          return store_get($$store_subs ??= {}, "$formData", formData).providerApiKey;
                        },
                        set value($$value) {
                          store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).providerApiKey = $$value);
                          $$settled = false;
                        }
                      }
                    ]));
                    $$payload6.out.push(`<!----> <button type="button" class="absolute top-1/2 right-3 -translate-y-1/2">`);
                    {
                      $$payload6.out.push("<!--[!-->");
                      Eye($$payload6, { class: "size-4" });
                    }
                    $$payload6.out.push(`<!--]--></button></div>`);
                  };
                  Control($$payload5, { children });
                }
                $$payload5.out.push(`<!----> <!---->`);
                Form_field_errors($$payload5, {});
                $$payload5.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!----> <!---->`);
            Form_field($$payload4, {
              form,
              name: "providerClientId",
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->`);
                {
                  let children = function($$payload6, { props }) {
                    $$payload6.out.push(`<!---->`);
                    Form_label($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out.push(`<!---->Client ID (Optional)`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out.push(`<!----> `);
                    Input($$payload6, spread_props([
                      props,
                      {
                        placeholder: "Enter Client ID",
                        get value() {
                          return store_get($$store_subs ??= {}, "$formData", formData).providerClientId;
                        },
                        set value($$value) {
                          store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).providerClientId = $$value);
                          $$settled = false;
                        }
                      }
                    ]));
                    $$payload6.out.push(`<!---->`);
                  };
                  Control($$payload5, { children });
                }
                $$payload5.out.push(`<!----> <!---->`);
                Form_field_errors($$payload5, {});
                $$payload5.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!----> <!---->`);
            Form_field($$payload4, {
              form,
              name: "providerApiSecret",
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->`);
                {
                  let children = function($$payload6, { props }) {
                    $$payload6.out.push(`<!---->`);
                    Form_label($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out.push(`<!---->Client Secret (Optional)`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out.push(`<!----> <div class="relative">`);
                    Input($$payload6, spread_props([
                      props,
                      {
                        type: "password",
                        placeholder: "Enter Client Secret",
                        get value() {
                          return store_get($$store_subs ??= {}, "$formData", formData).providerApiSecret;
                        },
                        set value($$value) {
                          store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).providerApiSecret = $$value);
                          $$settled = false;
                        }
                      }
                    ]));
                    $$payload6.out.push(`<!----> <button type="button" class="absolute top-1/2 right-3 -translate-y-1/2">`);
                    {
                      $$payload6.out.push("<!--[!-->");
                      Eye($$payload6, { class: "size-4" });
                    }
                    $$payload6.out.push(`<!--]--></button></div>`);
                  };
                  Control($$payload5, { children });
                }
                $$payload5.out.push(`<!----> <!---->`);
                Form_field_errors($$payload5, {});
                $$payload5.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!----> <!---->`);
            Form_field($$payload4, {
              form,
              name: "providerWebhookUrl",
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->`);
                {
                  let children = function($$payload6, { props }) {
                    $$payload6.out.push(`<!---->`);
                    Form_label($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out.push(`<!---->Webhook URL (Optional)`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out.push(`<!----> `);
                    Input($$payload6, spread_props([
                      props,
                      {
                        placeholder: "https://your-webhook-endpoint.com",
                        get value() {
                          return store_get($$store_subs ??= {}, "$formData", formData).providerWebhookUrl;
                        },
                        set value($$value) {
                          store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).providerWebhookUrl = $$value);
                          $$settled = false;
                        }
                      }
                    ]));
                    $$payload6.out.push(`<!---->`);
                  };
                  Control($$payload5, { children });
                }
                $$payload5.out.push(`<!----> <!---->`);
                Form_field_errors($$payload5, {});
                $$payload5.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!----> <!---->`);
            Form_field($$payload4, {
              form,
              name: "isActive",
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->`);
                {
                  let children = function($$payload6, { props }) {
                    $$payload6.out.push(`<div class="flex items-center space-x-2">`);
                    Switch($$payload6, spread_props([
                      props,
                      {
                        checked: store_get($$store_subs ??= {}, "$formData", formData).isActive ?? false,
                        onCheckedChange: (checked) => store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).isActive = checked)
                      }
                    ]));
                    $$payload6.out.push(`<!----> <!---->`);
                    Form_label($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out.push(`<!---->Active Provider`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out.push(`<!----></div>`);
                  };
                  Control($$payload5, { children });
                }
                $$payload5.out.push(`<!----> <!---->`);
                Form_field_errors($$payload5, {});
                $$payload5.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!---->`);
          },
          $$slots: { default: true }
        });
        $$payload3.out.push(`<!----> <!---->`);
        Card_footer($$payload3, {
          class: "flex justify-between",
          children: ($$payload4) => {
            Button($$payload4, {
              disabled: store_get($$store_subs ??= {}, "$submitting", submitting),
              variant: "outline",
              class: "mt-4 w-1/3",
              onclick: () => utilsStore.clearDynamicModal(),
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->Cancel`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!----> <!---->`);
            Form_button($$payload4, {
              disabled: store_get($$store_subs ??= {}, "$allErrors", allErrors).length > 0 || store_get($$store_subs ??= {}, "$submitting", submitting),
              class: "mt-4 w-1/3",
              children: ($$payload5) => {
                if (store_get($$store_subs ??= {}, "$submitting", submitting)) {
                  $$payload5.out.push("<!--[-->");
                  Loader_circle($$payload5, { class: "mr-2 size-4 animate-spin" });
                } else {
                  $$payload5.out.push("<!--[!-->");
                }
                $$payload5.out.push(`<!--]--> ${escape_html(store_get($$store_subs ??= {}, "$submitting", submitting) ? "Please wait..." : data?.providerName ? "Update Provider" : "Add Provider")}`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!---->`);
          },
          $$slots: { default: true }
        });
        $$payload3.out.push(`<!---->`);
      },
      $$slots: { default: true }
    });
    $$payload2.out.push(`<!----></form>`);
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
function Add_bank_detail($$payload, $$props) {
  push();
  var $$store_subs;
  let { data = {} } = $$props;
  let utilsStore = getUtilsStore();
  const orgStore = getOrgStore();
  const hasDetail = !!orgStore.orgBankDetail;
  const form = superForm(defaults(orgStore.orgBankDetail, valibotClient(bankAccountFormSchema)), {
    SPA: true,
    resetForm: true,
    clearOnSubmit: "errors-and-message",
    validators: valibotClient(bankAccountFormSchema),
    async onUpdate({ form: form2 }) {
      if (!form2.valid) return;
      try {
        const { data: formData2 } = form2;
        const bankDetail = {
          accountName: formData2.accountName,
          accountNumber: formData2.accountNumber,
          bankName: formData2.bankName,
          branchCode: formData2.branchCode,
          swiftCode: formData2.swiftCode
        };
        let orgBankDetailResp;
        if (!hasDetail) {
          orgBankDetailResp = await OrgBankDetailController.insert(bankDetail);
        } else {
          if (!orgStore.orgBankDetail?.id) {
            throw new Error("Bank detail ID is required for update");
          }
          orgBankDetailResp = await OrgBankDetailController.update(orgStore.orgBankDetail.id, bankDetail);
        }
        if (orgBankDetailResp?.id) {
          utilsStore.toastSuccess("Bank details added successfully!");
          utilsStore.clearDynamicModal();
          form2.message = { status: "success", text: "Bank details added successfully" };
        } else {
          utilsStore.toastError("Failed to add bank details");
          form2.message = { status: "error", text: "Failed to add bank details" };
        }
      } catch (err) {
        form2.message = {
          status: "error",
          text: `Error adding bank details ${err.message}`
        };
      }
    },
    ...getFlashModule()
  });
  const {
    form: formData,
    enhance,
    submitting,
    allErrors,
    message,
    validateForm
  } = form;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out.push(`<form method="POST">`);
    Form_alerts($$payload2, { message });
    $$payload2.out.push(`<!----> <!---->`);
    Card($$payload2, {
      class: "mx-4 rounded-none",
      children: ($$payload3) => {
        $$payload3.out.push(`<!---->`);
        Card_header($$payload3, {
          children: ($$payload4) => {
            $$payload4.out.push(`<!---->`);
            Card_title($$payload4, {
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->${escape_html(orgStore.orgBankDetail?.accountNumber ? "Edit" : "Add")} Bank Account`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!----> <!---->`);
            Card_description($$payload4, {
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->Please provide your bank account details for receiving payments`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!---->`);
          },
          $$slots: { default: true }
        });
        $$payload3.out.push(`<!----> <!---->`);
        Card_content($$payload3, {
          class: "mb-4 grid grid-cols-1 gap-3 md:grid-cols-2",
          children: ($$payload4) => {
            $$payload4.out.push(`<!---->`);
            Form_field($$payload4, {
              form,
              name: "accountName",
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->`);
                {
                  let children = function($$payload6, { props }) {
                    $$payload6.out.push(`<!---->`);
                    Form_label($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out.push(`<!---->Account Name`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out.push(`<!----> `);
                    Input($$payload6, spread_props([
                      props,
                      {
                        placeholder: "Account Name",
                        get value() {
                          return store_get($$store_subs ??= {}, "$formData", formData).accountName;
                        },
                        set value($$value) {
                          store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).accountName = $$value);
                          $$settled = false;
                        }
                      }
                    ]));
                    $$payload6.out.push(`<!----> <!---->`);
                    Form_description($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out.push(`<!---->Nickname for the account`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out.push(`<!---->`);
                  };
                  Control($$payload5, { children });
                }
                $$payload5.out.push(`<!----> <!---->`);
                Form_field_errors($$payload5, {});
                $$payload5.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!----> <!---->`);
            Form_field($$payload4, {
              form,
              name: "accountNumber",
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->`);
                {
                  let children = function($$payload6, { props }) {
                    $$payload6.out.push(`<!---->`);
                    Form_label($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out.push(`<!---->Account Number`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out.push(`<!----> `);
                    Input($$payload6, spread_props([
                      props,
                      {
                        placeholder: "Account Number",
                        get value() {
                          return store_get($$store_subs ??= {}, "$formData", formData).accountNumber;
                        },
                        set value($$value) {
                          store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).accountNumber = $$value);
                          $$settled = false;
                        }
                      }
                    ]));
                    $$payload6.out.push(`<!---->`);
                  };
                  Control($$payload5, { children });
                }
                $$payload5.out.push(`<!----> <!---->`);
                Form_field_errors($$payload5, {});
                $$payload5.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!----> <!---->`);
            Form_field($$payload4, {
              form,
              name: "bankName",
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->`);
                {
                  let children = function($$payload6, { props }) {
                    $$payload6.out.push(`<!---->`);
                    Form_label($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out.push(`<!---->Bank Name`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out.push(`<!----> `);
                    Input($$payload6, spread_props([
                      props,
                      {
                        placeholder: "Bank Name",
                        get value() {
                          return store_get($$store_subs ??= {}, "$formData", formData).bankName;
                        },
                        set value($$value) {
                          store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).bankName = $$value);
                          $$settled = false;
                        }
                      }
                    ]));
                    $$payload6.out.push(`<!---->`);
                  };
                  Control($$payload5, { children });
                }
                $$payload5.out.push(`<!----> <!---->`);
                Form_field_errors($$payload5, {});
                $$payload5.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!----> <!---->`);
            Form_field($$payload4, {
              form,
              name: "branchCode",
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->`);
                {
                  let children = function($$payload6, { props }) {
                    $$payload6.out.push(`<!---->`);
                    Form_label($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out.push(`<!---->Branch Code`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out.push(`<!----> `);
                    Input($$payload6, spread_props([
                      props,
                      {
                        placeholder: "Branch Code",
                        get value() {
                          return store_get($$store_subs ??= {}, "$formData", formData).branchCode;
                        },
                        set value($$value) {
                          store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).branchCode = $$value);
                          $$settled = false;
                        }
                      }
                    ]));
                    $$payload6.out.push(`<!---->`);
                  };
                  Control($$payload5, { children });
                }
                $$payload5.out.push(`<!----> <!---->`);
                Form_field_errors($$payload5, {});
                $$payload5.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!----> <!---->`);
            Form_field($$payload4, {
              form,
              name: "swiftCode",
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->`);
                {
                  let children = function($$payload6, { props }) {
                    $$payload6.out.push(`<!---->`);
                    Form_label($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out.push(`<!---->SWIFT Code`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out.push(`<!----> `);
                    Input($$payload6, spread_props([
                      props,
                      {
                        placeholder: "SWIFT Code",
                        get value() {
                          return store_get($$store_subs ??= {}, "$formData", formData).swiftCode;
                        },
                        set value($$value) {
                          store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).swiftCode = $$value);
                          $$settled = false;
                        }
                      }
                    ]));
                    $$payload6.out.push(`<!---->`);
                  };
                  Control($$payload5, { children });
                }
                $$payload5.out.push(`<!----> <!---->`);
                Form_field_errors($$payload5, {});
                $$payload5.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!---->`);
          },
          $$slots: { default: true }
        });
        $$payload3.out.push(`<!----> <!---->`);
        Card_footer($$payload3, {
          class: "flex justify-between",
          children: ($$payload4) => {
            Button($$payload4, {
              disabled: store_get($$store_subs ??= {}, "$submitting", submitting),
              variant: "outline",
              class: "mt-4 w-1/3",
              onclick: () => utilsStore.clearDynamicModal(),
              children: ($$payload5) => {
                $$payload5.out.push(`<!---->Cancel`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!----> <!---->`);
            Form_button($$payload4, {
              disabled: store_get($$store_subs ??= {}, "$allErrors", allErrors).length > 0 || store_get($$store_subs ??= {}, "$submitting", submitting),
              type: "submit",
              class: "px-auto mt-4 w-4/10",
              children: ($$payload5) => {
                if (store_get($$store_subs ??= {}, "$submitting", submitting)) {
                  $$payload5.out.push("<!--[-->");
                  Loader_circle($$payload5, { class: "mr-2 size-4 animate-spin" });
                } else {
                  $$payload5.out.push("<!--[!-->");
                }
                $$payload5.out.push(`<!--]--> ${escape_html(store_get($$store_subs ??= {}, "$submitting", submitting) ? "Please wait..." : orgStore.orgBankDetail?.accountNumber ? "Update Bank Account" : "Add Bank Account")}`);
              },
              $$slots: { default: true }
            });
            $$payload4.out.push(`<!---->`);
          },
          $$slots: { default: true }
        });
        $$payload3.out.push(`<!---->`);
      },
      $$slots: { default: true }
    });
    $$payload2.out.push(`<!----></form>`);
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
function _layout($$payload, $$props) {
  let { children } = $$props;
  const componentsCollection = [
    {
      componentName: "AddTeamMember",
      component: Add_team_member,
      dialogClass: "md:min-w-[920px]"
    },
    {
      componentName: "AddBankDetails",
      component: Add_bank_detail,
      dialogClass: "md:min-w-[620px]"
    },
    {
      componentName: "AddPaymentProvider",
      component: Add_payment_provider,
      dialogClass: "md:min-w-[620px]"
    }
  ];
  $$payload.out.push(`<div class="space-y-6"><div class="flex items-center justify-between"><h2 class="text-3xl font-bold tracking-tight">Settings</h2></div> <div class="flex-1">`);
  children?.($$payload);
  $$payload.out.push(`<!----></div></div> `);
  Dynamic_modal($$payload, { componentsCollection });
  $$payload.out.push(`<!---->`);
}
export {
  _layout as default
};
