import "clsx";
import { y as pop, w as push } from "../../../../../chunks/index2.js";
import "../../../../../chunks/client2.js";
import "../../../../../chunks/states.svelte.js";
import "linq-extensions";
import "@sveltejs/kit/internal";
import "../../../../../chunks/exports.js";
import "../../../../../chunks/state.svelte.js";
import "../../../../../chunks/nprogress.js";
import { A as App_shell, a as App_shell_header } from "../../../../../chunks/app_shell_header.js";
import { C as Card, a as Card_content } from "../../../../../chunks/card-content.js";
import { C as Card_header, a as Card_title, b as Card_description } from "../../../../../chunks/card-title.js";
import "../../../../../chunks/string.js";
import { d as defaults, a as valibotClient } from "../../../../../chunks/valibot.js";
import "@sveltejs/kit";
import { B as Button } from "../../../../../chunks/button.js";
import { g as getUtilsStore, e as getOrgStore } from "../../../../../chunks/sheet-content.js";
import { N as No_data } from "../../../../../chunks/no-data.js";
import { L as List } from "../../../../../chunks/list.js";
import { o as addMemberFormSchema } from "../../../../../chunks/auth-client.js";
import "superjson";
import "@oslojs/crypto/sha2";
import "decimal.js";
import "remult";
function Invites_table($$payload) {
  $$payload.out.push(`<p>Invites Table and processing</p>`);
}
function Invite_list($$payload, $$props) {
  push();
  let { addTeamMemberForm } = $$props;
  const uStore = getUtilsStore();
  const orgStore = getOrgStore();
  let hasInvites = orgStore.staffMemberInvites.length > 0;
  const startCreateTeamMember = () => {
    const newTeamMemberForm = {
      canClose: false,
      title: "Invite new Team Member",
      componentName: "AddTeamMember",
      size: "lg",
      color: "default",
      description: "Send Team member invite to your team",
      componentProps: { addTeamMemberForm }
    };
    uStore.openDynamicModal(newTeamMemberForm);
  };
  if (hasInvites) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="space-y-4">`);
    Invites_table($$payload);
    $$payload.out.push(`<!----></div>`);
  } else {
    let listSnippet = function($$payload2) {
      List($$payload2, { class: "h-10 w-10" });
    };
    $$payload.out.push("<!--[!-->");
    No_data($$payload, {
      noDataTitle: "No Pending Invites",
      noDataDescription: "You do not have any pending invites. start creating employee Data",
      snippet: listSnippet,
      children: ($$payload2) => {
        $$payload2.out.push(`<div class="flex justify-between space-x-2">`);
        Button($$payload2, {
          variant: "default",
          onclick: startCreateTeamMember,
          children: ($$payload3) => {
            $$payload3.out.push(`<!---->New Invite`);
          },
          $$slots: { default: true }
        });
        $$payload2.out.push(`<!----></div>`);
      }
    });
  }
  $$payload.out.push(`<!--]-->`);
  pop();
}
function _page($$payload, $$props) {
  push();
  const heading = "Team Manager";
  const subHeading = "Manage all Team members/Employee Data. Change State, Add new , Edit and Disable Team members";
  const addTeamMemberForm = defaults({}, valibotClient(addMemberFormSchema));
  App_shell($$payload, {
    children: ($$payload2) => {
      App_shell_header($$payload2, { heading, subHeading });
      $$payload2.out.push(`<!----> <div class="grid gap-10"><!---->`);
      Card($$payload2, {
        class: "rounded-none",
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Card_header($$payload3, {
            class: "px-7",
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->`);
              Card_title($$payload4, {
                children: ($$payload5) => {
                  $$payload5.out.push(`<!---->Team Member's Invites`);
                },
                $$slots: { default: true }
              });
              $$payload4.out.push(`<!----> <!---->`);
              Card_description($$payload4, {
                children: ($$payload5) => {
                  $$payload5.out.push(`<!---->Setup and Manage New Team Members`);
                },
                $$slots: { default: true }
              });
              $$payload4.out.push(`<!---->`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> <!---->`);
          Card_content($$payload3, {
            children: ($$payload4) => {
              Invite_list($$payload4, { addTeamMemberForm });
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!----></div>`);
    }
  });
  pop();
}
export {
  _page as default
};
