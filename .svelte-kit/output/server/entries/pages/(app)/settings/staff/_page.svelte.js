import "clsx";
import { w as push, G as spread_props, y as pop, O as props_id, F as spread_attributes, P as bind_props, Q as copy_payload, R as assign_payload, E as ensure_array_like, I as attr_class, J as clsx, B as escape_html } from "../../../../../chunks/index2.js";
import "../../../../../chunks/client2.js";
import { I as Icon, f as SvelteSet } from "../../../../../chunks/states.svelte.js";
import "linq-extensions";
import "@sveltejs/kit/internal";
import "../../../../../chunks/exports.js";
import "../../../../../chunks/state.svelte.js";
import "../../../../../chunks/nprogress.js";
import { A as App_shell, a as App_shell_header } from "../../../../../chunks/app_shell_header.js";
import { C as Card, a as Card_content } from "../../../../../chunks/card-content.js";
import { C as Card_header, a as Card_title, b as Card_description } from "../../../../../chunks/card-title.js";
import "../../../../../chunks/string.js";
import { d as defaults, a as valibotClient } from "../../../../../chunks/valibot.js";
import "@sveltejs/kit";
import { ah as TeamMemberStatuses, a7 as UserController, R as RolesType, o as addMemberFormSchema } from "../../../../../chunks/auth-client.js";
import "superjson";
import "@oslojs/crypto/sha2";
import "decimal.js";
import "remult";
import { B as Button } from "../../../../../chunks/button.js";
import { g as getUtilsStore, e as getOrgStore } from "../../../../../chunks/sheet-content.js";
import { N as No_data } from "../../../../../chunks/no-data.js";
import { D as Dropdown_menu_shortcut, a as Data_table_checkbox, b as Data_table_cell, c as Data_table_column_header, d as Data_table_toolbar, e as Data_table_pagination } from "../../../../../chunks/data_table_column_header.js";
import { f as CommandSeparatorState, C as Command, a as Command_input, b as Command_empty, c as Command_group, d as Command_item } from "../../../../../chunks/command-input.js";
import { c as cn } from "../../../../../chunks/shadcn.utils.js";
import { C as Command_list } from "../../../../../chunks/command-list.js";
import { b as createId, d as box, m as mergeProps } from "../../../../../chunks/create-id.js";
import { R as Root, a as Popover_trigger, P as Popover_content } from "../../../../../chunks/index7.js";
import { S as Separator } from "../../../../../chunks/separator.js";
import { B as Badge } from "../../../../../chunks/badge.js";
import { C as Check } from "../../../../../chunks/check.js";
import { r as renderComponent, c as createSvelteTable, T as Table, a as Table_header, b as Table_row, d as Table_head, F as Flex_render, e as Table_body, f as Table_cell } from "../../../../../chunks/data-table.svelte.js";
import { getFacetedUniqueValues, getFacetedRowModel, getSortedRowModel, getPaginationRowModel, getFilteredRowModel, getCoreRowModel } from "@tanstack/table-core";
import { R as Root$1, D as Dropdown_menu_trigger, a as Dropdown_menu_content } from "../../../../../chunks/index6.js";
import { b as Dropdown_menu_item, D as Dropdown_menu_separator } from "../../../../../chunks/dropdown-menu-separator.js";
import { E as Ellipsis } from "../../../../../chunks/ellipsis.js";
import { U as User_plus } from "../../../../../chunks/user-plus.js";
import { U as Users } from "../../../../../chunks/users.js";
import { X } from "../../../../../chunks/x.js";
function Bluetooth_connected($$payload, $$props) {
  push();
  /**
   * @license @lucide/svelte v0.539.0 - ISC
   *
   * ISC License
   *
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   *
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   *
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   *
   */
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    ["path", { "d": "m7 7 10 10-5 5V2l5 5L7 17" }],
    ["line", { "x1": "18", "x2": "21", "y1": "12", "y2": "12" }],
    ["line", { "x1": "3", "x2": "6", "y1": "12", "y2": "12" }]
  ];
  Icon($$payload, spread_props([
    { name: "bluetooth-connected" },
    /**
     * @component @name BluetoothConnected
     * @description Lucide SVG icon component, renders SVG Element with children.
     *
     * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtNyA3IDEwIDEwLTUgNVYybDUgNUw3IDE3IiAvPgogIDxsaW5lIHgxPSIxOCIgeDI9IjIxIiB5MT0iMTIiIHkyPSIxMiIgLz4KICA8bGluZSB4MT0iMyIgeDI9IjYiIHkxPSIxMiIgeTI9IjEyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/bluetooth-connected
     * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
     *
     * @param {Object} props - Lucide icons props and any valid SVG attribute
     * @returns {FunctionalComponent} Svelte component
     *
     */
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function Circle_plus($$payload, $$props) {
  push();
  /**
   * @license @lucide/svelte v0.539.0 - ISC
   *
   * ISC License
   *
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   *
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   *
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   *
   */
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    ["circle", { "cx": "12", "cy": "12", "r": "10" }],
    ["path", { "d": "M8 12h8" }],
    ["path", { "d": "M12 8v8" }]
  ];
  Icon($$payload, spread_props([
    { name: "circle-plus" },
    /**
     * @component @name CirclePlus
     * @description Lucide SVG icon component, renders SVG Element with children.
     *
     * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8cGF0aCBkPSJNOCAxMmg4IiAvPgogIDxwYXRoIGQ9Ik0xMiA4djgiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/circle-plus
     * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
     *
     * @param {Object} props - Lucide icons props and any valid SVG attribute
     * @returns {FunctionalComponent} Svelte component
     *
     */
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function File_text($$payload, $$props) {
  push();
  /**
   * @license @lucide/svelte v0.539.0 - ISC
   *
   * ISC License
   *
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   *
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   *
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   *
   */
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    [
      "path",
      {
        "d": "M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"
      }
    ],
    ["path", { "d": "M14 2v4a2 2 0 0 0 2 2h4" }],
    ["path", { "d": "M10 9H8" }],
    ["path", { "d": "M16 13H8" }],
    ["path", { "d": "M16 17H8" }]
  ];
  Icon($$payload, spread_props([
    { name: "file-text" },
    /**
     * @component @name FileText
     * @description Lucide SVG icon component, renders SVG Element with children.
     *
     * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUgMkg2YTIgMiAwIDAgMC0yIDJ2MTZhMiAyIDAgMCAwIDIgMmgxMmEyIDIgMCAwIDAgMi0yVjdaIiAvPgogIDxwYXRoIGQ9Ik0xNCAydjRhMiAyIDAgMCAwIDIgMmg0IiAvPgogIDxwYXRoIGQ9Ik0xMCA5SDgiIC8+CiAgPHBhdGggZD0iTTE2IDEzSDgiIC8+CiAgPHBhdGggZD0iTTE2IDE3SDgiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/file-text
     * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
     *
     * @param {Object} props - Lucide icons props and any valid SVG attribute
     * @returns {FunctionalComponent} Svelte component
     *
     */
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function Plug_zap($$payload, $$props) {
  push();
  /**
   * @license @lucide/svelte v0.539.0 - ISC
   *
   * ISC License
   *
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   *
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   *
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   *
   */
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    [
      "path",
      {
        "d": "M6.3 20.3a2.4 2.4 0 0 0 3.4 0L12 18l-6-6-2.3 2.3a2.4 2.4 0 0 0 0 3.4Z"
      }
    ],
    ["path", { "d": "m2 22 3-3" }],
    ["path", { "d": "M7.5 13.5 10 11" }],
    ["path", { "d": "M10.5 16.5 13 14" }],
    ["path", { "d": "m18 3-4 4h6l-4 4" }]
  ];
  Icon($$payload, spread_props([
    { name: "plug-zap" },
    /**
     * @component @name PlugZap
     * @description Lucide SVG icon component, renders SVG Element with children.
     *
     * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNi4zIDIwLjNhMi40IDIuNCAwIDAgMCAzLjQgMEwxMiAxOGwtNi02LTIuMyAyLjNhMi40IDIuNCAwIDAgMCAwIDMuNFoiIC8+CiAgPHBhdGggZD0ibTIgMjIgMy0zIiAvPgogIDxwYXRoIGQ9Ik03LjUgMTMuNSAxMCAxMSIgLz4KICA8cGF0aCBkPSJNMTAuNSAxNi41IDEzIDE0IiAvPgogIDxwYXRoIGQ9Im0xOCAzLTQgNGg2bC00IDQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/plug-zap
     * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
     *
     * @param {Object} props - Lucide icons props and any valid SVG attribute
     * @returns {FunctionalComponent} Svelte component
     *
     */
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function Screen_share_off($$payload, $$props) {
  push();
  /**
   * @license @lucide/svelte v0.539.0 - ISC
   *
   * ISC License
   *
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   *
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   *
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   *
   */
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    [
      "path",
      {
        "d": "M13 3H4a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-3"
      }
    ],
    ["path", { "d": "M8 21h8" }],
    ["path", { "d": "M12 17v4" }],
    ["path", { "d": "m22 3-5 5" }],
    ["path", { "d": "m17 3 5 5" }]
  ];
  Icon($$payload, spread_props([
    { name: "screen-share-off" },
    /**
     * @component @name ScreenShareOff
     * @description Lucide SVG icon component, renders SVG Element with children.
     *
     * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTMgM0g0YTIgMiAwIDAgMC0yIDJ2MTBhMiAyIDAgMCAwIDIgMmgxNmEyIDIgMCAwIDAgMi0ydi0zIiAvPgogIDxwYXRoIGQ9Ik04IDIxaDgiIC8+CiAgPHBhdGggZD0iTTEyIDE3djQiIC8+CiAgPHBhdGggZD0ibTIyIDMtNSA1IiAvPgogIDxwYXRoIGQ9Im0xNyAzIDUgNSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/screen-share-off
     * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
     *
     * @param {Object} props - Lucide icons props and any valid SVG attribute
     * @returns {FunctionalComponent} Svelte component
     *
     */
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function Unplug($$payload, $$props) {
  push();
  /**
   * @license @lucide/svelte v0.539.0 - ISC
   *
   * ISC License
   *
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   *
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   *
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   *
   */
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    ["path", { "d": "m19 5 3-3" }],
    ["path", { "d": "m2 22 3-3" }],
    [
      "path",
      {
        "d": "M6.3 20.3a2.4 2.4 0 0 0 3.4 0L12 18l-6-6-2.3 2.3a2.4 2.4 0 0 0 0 3.4Z"
      }
    ],
    ["path", { "d": "M7.5 13.5 10 11" }],
    ["path", { "d": "M10.5 16.5 13 14" }],
    [
      "path",
      {
        "d": "m12 6 6 6 2.3-2.3a2.4 2.4 0 0 0 0-3.4l-2.6-2.6a2.4 2.4 0 0 0-3.4 0Z"
      }
    ]
  ];
  Icon($$payload, spread_props([
    { name: "unplug" },
    /**
     * @component @name Unplug
     * @description Lucide SVG icon component, renders SVG Element with children.
     *
     * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTkgNSAzLTMiIC8+CiAgPHBhdGggZD0ibTIgMjIgMy0zIiAvPgogIDxwYXRoIGQ9Ik02LjMgMjAuM2EyLjQgMi40IDAgMCAwIDMuNCAwTDEyIDE4bC02LTYtMi4zIDIuM2EyLjQgMi40IDAgMCAwIDAgMy40WiIgLz4KICA8cGF0aCBkPSJNNy41IDEzLjUgMTAgMTEiIC8+CiAgPHBhdGggZD0iTTEwLjUgMTYuNSAxMyAxNCIgLz4KICA8cGF0aCBkPSJtMTIgNiA2IDYgMi4zLTIuM2EyLjQgMi40IDAgMCAwIDAtMy40bC0yLjYtMi42YTIuNCAyLjQgMCAwIDAtMy40IDBaIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/unplug
     * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
     *
     * @param {Object} props - Lucide icons props and any valid SVG attribute
     * @returns {FunctionalComponent} Svelte component
     *
     */
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function User_check($$payload, $$props) {
  push();
  /**
   * @license @lucide/svelte v0.539.0 - ISC
   *
   * ISC License
   *
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   *
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   *
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   *
   */
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    ["path", { "d": "m16 11 2 2 4-4" }],
    ["path", { "d": "M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" }],
    ["circle", { "cx": "9", "cy": "7", "r": "4" }]
  ];
  Icon($$payload, spread_props([
    { name: "user-check" },
    /**
     * @component @name UserCheck
     * @description Lucide SVG icon component, renders SVG Element with children.
     *
     * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTYgMTEgMiAyIDQtNCIgLz4KICA8cGF0aCBkPSJNMTYgMjF2LTJhNCA0IDAgMCAwLTQtNEg2YTQgNCAwIDAgMC00IDR2MiIgLz4KICA8Y2lyY2xlIGN4PSI5IiBjeT0iNyIgcj0iNCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/user-check
     * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
     *
     * @param {Object} props - Lucide icons props and any valid SVG attribute
     * @returns {FunctionalComponent} Svelte component
     *
     */
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function User_round_plus($$payload, $$props) {
  push();
  /**
   * @license @lucide/svelte v0.539.0 - ISC
   *
   * ISC License
   *
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   *
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   *
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   *
   */
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    ["path", { "d": "M2 21a8 8 0 0 1 13.292-6" }],
    ["circle", { "cx": "10", "cy": "8", "r": "5" }],
    ["path", { "d": "M19 16v6" }],
    ["path", { "d": "M22 19h-6" }]
  ];
  Icon($$payload, spread_props([
    { name: "user-round-plus" },
    /**
     * @component @name UserRoundPlus
     * @description Lucide SVG icon component, renders SVG Element with children.
     *
     * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMiAyMWE4IDggMCAwIDEgMTMuMjkyLTYiIC8+CiAgPGNpcmNsZSBjeD0iMTAiIGN5PSI4IiByPSI1IiAvPgogIDxwYXRoIGQ9Ik0xOSAxNnY2IiAvPgogIDxwYXRoIGQ9Ik0yMiAxOWgtNiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/user-round-plus
     * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
     *
     * @param {Object} props - Lucide icons props and any valid SVG attribute
     * @returns {FunctionalComponent} Svelte component
     *
     */
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function Command_separator$1($$payload, $$props) {
  push();
  const uid = props_id($$payload);
  let {
    id = createId(uid),
    ref = null,
    forceMount = false,
    children,
    child,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const separatorState = CommandSeparatorState.create({
    id: box.with(() => id),
    ref: box.with(() => ref, (v) => ref = v),
    forceMount: box.with(() => forceMount)
  });
  const mergedProps = mergeProps(restProps, separatorState.props);
  if (separatorState.shouldRender) {
    $$payload.out.push("<!--[-->");
    if (child) {
      $$payload.out.push("<!--[-->");
      child($$payload, { props: mergedProps });
      $$payload.out.push(`<!---->`);
    } else {
      $$payload.out.push("<!--[!-->");
      $$payload.out.push(`<div${spread_attributes({ ...mergedProps }, null)}>`);
      children?.($$payload);
      $$payload.out.push(`<!----></div>`);
    }
    $$payload.out.push(`<!--]-->`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]-->`);
  bind_props($$props, { ref });
  pop();
}
function Command_separator($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out.push(`<!---->`);
    Command_separator$1($$payload2, spread_props([
      {
        "data-slot": "command-separator",
        class: cn("bg-border -mx-1 h-px", className)
      },
      restProps,
      {
        get ref() {
          return ref;
        },
        set ref($$value) {
          ref = $$value;
          $$settled = false;
        }
      }
    ]));
    $$payload2.out.push(`<!---->`);
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref });
  pop();
}
function Data_table_faceted_filter($$payload, $$props) {
  push();
  let { column, title, options } = $$props;
  const facets = column?.getFacetedUniqueValues();
  const selectedValues = new SvelteSet(column?.getFilterValue());
  $$payload.out.push(`<!---->`);
  Root($$payload, {
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->`);
      {
        let child = function($$payload3, { props }) {
          Button($$payload3, spread_props([
            props,
            {
              variant: "outline",
              size: "sm",
              class: "h-8 border-dashed",
              children: ($$payload4) => {
                Circle_plus($$payload4, {});
                $$payload4.out.push(`<!----> ${escape_html(title)} `);
                if (selectedValues.size > 0) {
                  $$payload4.out.push("<!--[-->");
                  Separator($$payload4, { orientation: "vertical", class: "mx-2 h-4" });
                  $$payload4.out.push(`<!----> `);
                  Badge($$payload4, {
                    variant: "secondary",
                    class: "rounded-sm px-1 font-normal lg:hidden",
                    children: ($$payload5) => {
                      $$payload5.out.push(`<!---->${escape_html(selectedValues.size)}`);
                    },
                    $$slots: { default: true }
                  });
                  $$payload4.out.push(`<!----> <div class="hidden space-x-1 lg:flex">`);
                  if (selectedValues.size > 2) {
                    $$payload4.out.push("<!--[-->");
                    Badge($$payload4, {
                      variant: "secondary",
                      class: "rounded-sm px-1 font-normal",
                      children: ($$payload5) => {
                        $$payload5.out.push(`<!---->${escape_html(selectedValues.size)} selected`);
                      },
                      $$slots: { default: true }
                    });
                  } else {
                    $$payload4.out.push("<!--[!-->");
                    const each_array = ensure_array_like(options.filter((opt) => selectedValues.has(opt.value)));
                    $$payload4.out.push(`<!--[-->`);
                    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
                      let option = each_array[$$index];
                      Badge($$payload4, {
                        variant: "secondary",
                        class: "rounded-sm px-1 font-normal",
                        children: ($$payload5) => {
                          $$payload5.out.push(`<!---->${escape_html(option.label)}`);
                        },
                        $$slots: { default: true }
                      });
                    }
                    $$payload4.out.push(`<!--]-->`);
                  }
                  $$payload4.out.push(`<!--]--></div>`);
                } else {
                  $$payload4.out.push("<!--[!-->");
                }
                $$payload4.out.push(`<!--]-->`);
              },
              $$slots: { default: true }
            }
          ]));
        };
        Popover_trigger($$payload2, { child, $$slots: { child: true } });
      }
      $$payload2.out.push(`<!----> <!---->`);
      Popover_content($$payload2, {
        class: "w-[200px] p-0",
        align: "start",
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Command($$payload3, {
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->`);
              Command_input($$payload4, { placeholder: title });
              $$payload4.out.push(`<!----> <!---->`);
              Command_list($$payload4, {
                children: ($$payload5) => {
                  $$payload5.out.push(`<!---->`);
                  Command_empty($$payload5, {
                    children: ($$payload6) => {
                      $$payload6.out.push(`<!---->No results found.`);
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out.push(`<!----> <!---->`);
                  Command_group($$payload5, {
                    children: ($$payload6) => {
                      const each_array_1 = ensure_array_like(options);
                      $$payload6.out.push(`<!--[-->`);
                      for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
                        let option = each_array_1[$$index_1];
                        const isSelected = selectedValues.has(option.value);
                        $$payload6.out.push(`<!---->`);
                        Command_item($$payload6, {
                          onSelect: () => {
                            if (isSelected) {
                              selectedValues.delete(option.value);
                            } else {
                              selectedValues.add(option.value);
                            }
                            const filterValues = Array.from(selectedValues);
                            column?.setFilterValue(filterValues.length ? filterValues : void 0);
                          },
                          children: ($$payload7) => {
                            $$payload7.out.push(`<div${attr_class(clsx(cn("border-primary mr-2 flex size-4 items-center justify-center rounded-sm border", isSelected ? "bg-primary text-primary-foreground" : "opacity-50 [&_svg]:invisible")))}>`);
                            Check($$payload7, { class: "size-4" });
                            $$payload7.out.push(`<!----></div> `);
                            if (option.icon) {
                              $$payload7.out.push("<!--[-->");
                              const Icon2 = option.icon;
                              $$payload7.out.push(`<!---->`);
                              Icon2($$payload7, { class: "text-muted-foreground" });
                              $$payload7.out.push(`<!---->`);
                            } else {
                              $$payload7.out.push("<!--[!-->");
                            }
                            $$payload7.out.push(`<!--]--> <span>${escape_html(option.label)}</span> `);
                            if (facets?.get(option.value)) {
                              $$payload7.out.push("<!--[-->");
                              $$payload7.out.push(`<span class="ml-auto flex size-4 items-center justify-center font-mono text-xs">${escape_html(facets.get(option.value))}</span>`);
                            } else {
                              $$payload7.out.push("<!--[!-->");
                            }
                            $$payload7.out.push(`<!--]-->`);
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out.push(`<!---->`);
                      }
                      $$payload6.out.push(`<!--]-->`);
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out.push(`<!----> `);
                  if (selectedValues.size > 0) {
                    $$payload5.out.push("<!--[-->");
                    $$payload5.out.push(`<!---->`);
                    Command_separator($$payload5, {});
                    $$payload5.out.push(`<!----> <!---->`);
                    Command_group($$payload5, {
                      children: ($$payload6) => {
                        $$payload6.out.push(`<!---->`);
                        Command_item($$payload6, {
                          onSelect: () => column?.setFilterValue(void 0),
                          class: "justify-center text-center",
                          children: ($$payload7) => {
                            $$payload7.out.push(`<!---->Clear filters`);
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out.push(`<!---->`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out.push(`<!---->`);
                  } else {
                    $$payload5.out.push("<!--[!-->");
                  }
                  $$payload5.out.push(`<!--]-->`);
                },
                $$slots: { default: true }
              });
              $$payload4.out.push(`<!---->`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!---->`);
  pop();
}
function Data_table_status_cell($$payload, $$props) {
  push();
  let { value, statuses: statuses2 } = $$props;
  const status = statuses2.find((status2) => status2.value === value);
  const Icon2 = status?.icon;
  if (status) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="flex w-[100px] items-center">`);
    if (Icon2) {
      $$payload.out.push("<!--[-->");
      $$payload.out.push(`<!---->`);
      Icon2($$payload, { class: "mr-2 size-4 text-muted-foreground" });
      $$payload.out.push(`<!---->`);
    } else {
      $$payload.out.push("<!--[!-->");
    }
    $$payload.out.push(`<!--]--> <span>${escape_html(status.label)}</span></div>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]-->`);
  pop();
}
function Staff_table_row_actions($$payload, $$props) {
  push();
  let { row } = $$props;
  const uStore = getUtilsStore();
  let toggeStatusText = row.original.userStatus === TeamMemberStatuses.Active ? "Deactivate User" : "Activate User";
  const toggleUserStatus = async () => {
    const status = row.original.userStatus === TeamMemberStatuses.Active ? TeamMemberStatuses.InActive : TeamMemberStatuses.Active;
    await updateMemberStatus(status);
  };
  const editTeamMember = () => {
    const newTeamMemberForm = {
      canClose: false,
      title: "Edit Team Member",
      componentName: "AddTeamMember",
      size: "lg",
      color: "default",
      description: "Edit Team member details",
      componentProps: { data: row.original }
    };
    uStore.openDynamicModal(newTeamMemberForm);
  };
  const setMemberOnLeave = async () => {
    await updateMemberStatus(TeamMemberStatuses.OnLeave);
  };
  const updateMemberStatus = async (status) => {
    try {
      const updateResp = await UserController.update(row.original.id, { userStatus: status });
      if (!updateResp.id) throw new Error("Failed to update user status");
      return uStore.toastSuccess("Status updated successfully");
    } catch (error) {
      console.error(error);
      return uStore.toastError(`Failed to update status: ${error.message}`);
    }
  };
  $$payload.out.push(`<!---->`);
  Root$1($$payload, {
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->`);
      {
        let child = function($$payload3, { props }) {
          Button($$payload3, spread_props([
            props,
            {
              variant: "ghost",
              class: "data-[state=open]:bg-muted flex h-8 w-8 p-0",
              children: ($$payload4) => {
                Ellipsis($$payload4, {});
                $$payload4.out.push(`<!----> <span class="sr-only">Open Menu</span>`);
              },
              $$slots: { default: true }
            }
          ]));
        };
        Dropdown_menu_trigger($$payload2, { child, $$slots: { child: true } });
      }
      $$payload2.out.push(`<!----> <!---->`);
      Dropdown_menu_content($$payload2, {
        class: "w-[160px]",
        align: "end",
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Dropdown_menu_item($$payload3, {
            onclick: editTeamMember,
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Edit`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> <!---->`);
          Dropdown_menu_item($$payload3, {
            onclick: setMemberOnLeave,
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Set On Leave`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> <!---->`);
          Dropdown_menu_separator($$payload3, {});
          $$payload3.out.push(`<!----> <!---->`);
          Dropdown_menu_item($$payload3, {
            onclick: () => toggleUserStatus(),
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->${escape_html(toggeStatusText)} <!---->`);
              Dropdown_menu_shortcut($$payload4, {
                children: ($$payload5) => {
                  $$payload5.out.push(`<!---->⌘⌫`);
                },
                $$slots: { default: true }
              });
              $$payload4.out.push(`<!---->`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!---->`);
  pop();
}
const columns = [
  {
    id: "select",
    header: ({ table }) => renderComponent(Data_table_checkbox, {
      checked: table.getIsAllPageRowsSelected(),
      onCheckedChange: (value) => table.toggleAllPageRowsSelected(!!value),
      "aria-label": "Select all",
      class: "translate-y-[2px]"
    }),
    cell: ({ row }) => renderComponent(Data_table_checkbox, {
      checked: row.getIsSelected(),
      onCheckedChange: (value) => row.toggleSelected(!!value),
      "aria-label": "Select row",
      class: "translate-y-[2px]"
    }),
    enableSorting: false,
    enableHiding: false
  },
  {
    accessorKey: "fullName",
    header: ({ column }) => renderComponent(Data_table_column_header, {
      column,
      title: "Full Name"
    }),
    cell: ({ row }) => {
      return renderComponent(Data_table_cell, {
        value: `${row.original.profile.givenName} ${row.original.profile.familyName}`
      });
    },
    enableSorting: true,
    enableColumnFilter: true
  },
  {
    accessorKey: "memberStatus",
    header: ({ column }) => renderComponent(Data_table_column_header, {
      column,
      title: "User Status"
    }),
    cell: ({ row }) => {
      return renderComponent(Data_table_status_cell, {
        value: row.original.profile.userStatus || void 0,
        statuses
      });
    },
    enableSorting: true,
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id));
    }
  },
  {
    accessorKey: "phoneNumber",
    header: ({ column }) => renderComponent(Data_table_column_header, {
      column,
      title: "Phone"
    }),
    cell: ({ row }) => {
      return renderComponent(Data_table_cell, {
        value: row.original?.profile?.phoneNumber || ""
      });
    },
    enableSorting: true,
    enableColumnFilter: true
  },
  {
    accessorKey: "email",
    header: ({ column }) => renderComponent(Data_table_column_header, {
      column,
      title: "Email"
    }),
    cell: ({ row }) => {
      return renderComponent(Data_table_cell, {
        value: row.original.profile?.email || ""
      });
    },
    enableSorting: true,
    enableColumnFilter: true
  },
  {
    accessorKey: "gender",
    header: ({ column }) => renderComponent(Data_table_column_header, {
      column,
      title: "Gender"
    }),
    cell: ({ row }) => {
      return renderComponent(Data_table_cell, {
        value: row.original.profile?.gender?.toString()
      });
    },
    enableColumnFilter: false,
    enableSorting: true
  },
  {
    accessorKey: "teamName",
    header: ({ column }) => {
      return renderComponent(Data_table_column_header, {
        title: "Branch",
        column
      });
    },
    cell: ({ row }) => {
      return renderComponent(Data_table_cell, {
        value: row.original.location.name
      });
    },
    enableColumnFilter: true,
    enableSorting: true,
    enableGrouping: true
  },
  {
    accessorKey: "roles",
    header: ({ column }) => {
      return renderComponent(Data_table_column_header, {
        title: "Roles",
        column
      });
    },
    cell: ({ row }) => {
      return renderComponent(Data_table_cell, {
        value: Array.isArray(row.original.profile.roles) ? row.original.profile.roles.join(", ") : row.original.profile.roles
      });
    },
    enableColumnFilter: true,
    enableSorting: true,
    enableGrouping: true
  },
  {
    id: "actions",
    cell: ({ row }) => renderComponent(Staff_table_row_actions, { row })
  }
];
const roles = [
  {
    label: "Admin",
    value: RolesType.Admin,
    icon: User_check
  },
  {
    label: "Org Admin",
    value: RolesType.OrgAdmin,
    icon: User_round_plus
  },
  {
    label: "Location Admin",
    value: RolesType.TeamLocationAdmin,
    icon: User_plus
  },
  {
    label: "Team Member",
    value: RolesType.TeamMember,
    icon: Users
  }
];
const statuses = [
  {
    value: TeamMemberStatuses.Active,
    label: "Active",
    icon: Bluetooth_connected
  },
  {
    value: TeamMemberStatuses.InActive,
    label: "Inactive",
    icon: Screen_share_off
  },
  {
    value: TeamMemberStatuses.OnLeave,
    label: "On Leave",
    icon: Unplug
  },
  {
    value: TeamMemberStatuses.Resigned,
    label: "Resigned",
    icon: Plug_zap
  }
];
function Team_list_table($$payload, $$props) {
  push();
  let { data } = $$props;
  let rowSelection = {};
  let columnVisibility = {};
  let columnFilters = [];
  let sorting = [];
  let pagination = { pageIndex: 0, pageSize: 10 };
  const table = createSvelteTable({
    get data() {
      return data || [];
    },
    state: {
      get sorting() {
        return sorting;
      },
      get columnVisibility() {
        return columnVisibility;
      },
      get rowSelection() {
        return rowSelection;
      },
      get columnFilters() {
        return columnFilters;
      },
      get pagination() {
        return pagination;
      }
    },
    columns,
    enableRowSelection: true,
    onRowSelectionChange: (updater) => {
      if (typeof updater === "function") {
        rowSelection = updater(rowSelection);
      } else {
        rowSelection = updater;
      }
    },
    onSortingChange: (updater) => {
      if (typeof updater === "function") {
        sorting = updater(sorting);
      } else {
        sorting = updater;
      }
    },
    onColumnFiltersChange: (updater) => {
      if (typeof updater === "function") {
        columnFilters = updater(columnFilters);
      } else {
        columnFilters = updater;
      }
    },
    onColumnVisibilityChange: (updater) => {
      if (typeof updater === "function") {
        columnVisibility = updater(columnVisibility);
      } else {
        columnVisibility = updater;
      }
    },
    onPaginationChange: (updater) => {
      if (typeof updater === "function") {
        pagination = updater(pagination);
      } else {
        pagination = updater;
      }
    },
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues()
  });
  const isFiltered = table.getState().columnFilters.length > 0;
  const statusCol = table.getColumn("memberStatus");
  const roleCol = table.getColumn("roles");
  $$payload.out.push(`<div class="space-y-4">`);
  Data_table_toolbar($$payload, {
    table,
    filterColumn: "fullName",
    filterPlaceholder: "Filter by name...",
    children: ($$payload2) => {
      if (statusCol) {
        $$payload2.out.push("<!--[-->");
        Data_table_faceted_filter($$payload2, { column: statusCol, title: "Status", options: statuses });
      } else {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]--> `);
      if (roleCol) {
        $$payload2.out.push("<!--[-->");
        Data_table_faceted_filter($$payload2, { column: roleCol, title: "Roles", options: roles });
      } else {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]--> `);
      if (isFiltered) {
        $$payload2.out.push("<!--[-->");
        Button($$payload2, {
          variant: "ghost",
          onclick: () => table.resetColumnFilters(),
          class: "h-8 px-2 lg:px-3",
          children: ($$payload3) => {
            $$payload3.out.push(`<!---->Reset `);
            X($$payload3, {});
            $$payload3.out.push(`<!---->`);
          },
          $$slots: { default: true }
        });
      } else {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]-->`);
    }
  });
  $$payload.out.push(`<!----> <div class="rounded-md border"><!---->`);
  Table($$payload, {
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->`);
      Table_header($$payload2, {
        children: ($$payload3) => {
          const each_array = ensure_array_like(table.getHeaderGroups());
          $$payload3.out.push(`<!--[-->`);
          for (let $$index_1 = 0, $$length = each_array.length; $$index_1 < $$length; $$index_1++) {
            let headerGroup = each_array[$$index_1];
            $$payload3.out.push(`<!---->`);
            Table_row($$payload3, {
              children: ($$payload4) => {
                const each_array_1 = ensure_array_like(headerGroup.headers);
                $$payload4.out.push(`<!--[-->`);
                for (let $$index = 0, $$length2 = each_array_1.length; $$index < $$length2; $$index++) {
                  let header = each_array_1[$$index];
                  $$payload4.out.push(`<!---->`);
                  Table_head($$payload4, {
                    colspan: header.colSpan,
                    children: ($$payload5) => {
                      if (!header.isPlaceholder) {
                        $$payload5.out.push("<!--[-->");
                        Flex_render($$payload5, {
                          content: header.column.columnDef.header,
                          context: header.getContext()
                        });
                      } else {
                        $$payload5.out.push("<!--[!-->");
                      }
                      $$payload5.out.push(`<!--]-->`);
                    },
                    $$slots: { default: true }
                  });
                  $$payload4.out.push(`<!---->`);
                }
                $$payload4.out.push(`<!--]-->`);
              },
              $$slots: { default: true }
            });
            $$payload3.out.push(`<!---->`);
          }
          $$payload3.out.push(`<!--]-->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!----> <!---->`);
      Table_body($$payload2, {
        children: ($$payload3) => {
          const each_array_2 = ensure_array_like(table.getRowModel().rows);
          if (each_array_2.length !== 0) {
            $$payload3.out.push("<!--[-->");
            for (let $$index_3 = 0, $$length = each_array_2.length; $$index_3 < $$length; $$index_3++) {
              let row = each_array_2[$$index_3];
              $$payload3.out.push(`<!---->`);
              Table_row($$payload3, {
                "data-state": row.getIsSelected() && "selected",
                children: ($$payload4) => {
                  const each_array_3 = ensure_array_like(row.getVisibleCells());
                  $$payload4.out.push(`<!--[-->`);
                  for (let $$index_2 = 0, $$length2 = each_array_3.length; $$index_2 < $$length2; $$index_2++) {
                    let cell = each_array_3[$$index_2];
                    $$payload4.out.push(`<!---->`);
                    Table_cell($$payload4, {
                      children: ($$payload5) => {
                        Flex_render($$payload5, {
                          content: cell.column.columnDef.cell,
                          context: cell.getContext()
                        });
                      },
                      $$slots: { default: true }
                    });
                    $$payload4.out.push(`<!---->`);
                  }
                  $$payload4.out.push(`<!--]-->`);
                },
                $$slots: { default: true }
              });
              $$payload3.out.push(`<!---->`);
            }
          } else {
            $$payload3.out.push("<!--[!-->");
            $$payload3.out.push(`<!---->`);
            Table_row($$payload3, {
              children: ($$payload4) => {
                $$payload4.out.push(`<!---->`);
                Table_cell($$payload4, {
                  colspan: columns.length,
                  class: "h-24 text-center",
                  children: ($$payload5) => {
                    $$payload5.out.push(`<!---->No results.`);
                  },
                  $$slots: { default: true }
                });
                $$payload4.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload3.out.push(`<!---->`);
          }
          $$payload3.out.push(`<!--]-->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!----></div> `);
  Data_table_pagination($$payload, { table });
  $$payload.out.push(`<!----></div>`);
  pop();
}
function Staff_list($$payload, $$props) {
  push();
  let { addTeamMemberForm } = $$props;
  const uStore = getUtilsStore();
  const orgStore = getOrgStore();
  let hasStaffMembers = orgStore.staffMembers?.length ?? 0 > 0;
  let searchTerm = "";
  let filteredMembers = (() => {
    if (!orgStore.staffMembers || !Array.isArray(orgStore.staffMembers)) {
      return [];
    }
    return orgStore.staffMembers.where((k) => (k.profile?.name?.includes(searchTerm) ?? false) || (k.profile?.email?.includes(searchTerm) ?? false) || (k.profile?.familyName?.includes(searchTerm) ?? false) || (k.location?.name?.includes(searchTerm) ?? false)).toArray().map((k) => {
      const { location, ...profile } = k;
      return { profile, location };
    });
  })();
  const startCreateTeamMember = () => {
    const newTeamMemberForm = {
      canClose: true,
      title: "Invite new Team Member",
      componentName: "AddTeamMember",
      size: "lg",
      color: "default",
      description: "Send Team member invite to your team",
      componentProps: { addTeamMemberForm }
    };
    uStore.openDynamicModal(newTeamMemberForm);
  };
  if (hasStaffMembers) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<div class="space-y-4">`);
    Team_list_table($$payload, { data: filteredMembers });
    $$payload.out.push(`<!----></div>`);
  } else {
    let iconSnippet = function($$payload2) {
      File_text($$payload2, { class: "h-10 w-10" });
    };
    $$payload.out.push("<!--[!-->");
    No_data($$payload, {
      noDataTitle: "No Staff Data Found",
      noDataDescription: "You do not have any staff/employee data captured yet. start creating employee Data",
      snippet: iconSnippet,
      children: ($$payload2) => {
        $$payload2.out.push(`<div class="flex justify-between space-x-2">`);
        Button($$payload2, {
          variant: "default",
          onclick: startCreateTeamMember,
          children: ($$payload3) => {
            $$payload3.out.push(`<!---->New Invite`);
          },
          $$slots: { default: true }
        });
        $$payload2.out.push(`<!----></div>`);
      }
    });
  }
  $$payload.out.push(`<!--]-->`);
  pop();
}
function _page($$payload, $$props) {
  push();
  const heading = "Staff Manager";
  const subHeading = "Manage all Team members/Employee Data. Change State, Add new , Edit and Disable Team members";
  const addTeamMemberForm = defaults({}, valibotClient(addMemberFormSchema));
  App_shell($$payload, {
    children: ($$payload2) => {
      App_shell_header($$payload2, { heading, subHeading });
      $$payload2.out.push(`<!----> <div class="grid gap-10"><!---->`);
      Card($$payload2, {
        class: "rounded-none",
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Card_header($$payload3, {
            class: "px-7",
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->`);
              Card_title($$payload4, {
                children: ($$payload5) => {
                  $$payload5.out.push(`<!---->Team Members`);
                },
                $$slots: { default: true }
              });
              $$payload4.out.push(`<!----> <!---->`);
              Card_description($$payload4, {
                children: ($$payload5) => {
                  $$payload5.out.push(`<!---->Setup and Manage Team Members`);
                },
                $$slots: { default: true }
              });
              $$payload4.out.push(`<!---->`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> <!---->`);
          Card_content($$payload3, {
            children: ($$payload4) => {
              Staff_list($$payload4, { addTeamMemberForm });
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!----></div>`);
    }
  });
  pop();
}
export {
  _page as default
};
