import { w as push, G as spread_props, y as pop, T as store_get, Q as copy_payload, R as assign_payload, U as unsubscribe_stores, B as escape_html, V as store_mutate, z as attr } from "../../../../chunks/index2.js";
import "@sveltejs/kit/internal";
import "../../../../chunks/exports.js";
import "../../../../chunks/state.svelte.js";
import { af as settingsFormSchema, ag as OrganizationController } from "../../../../chunks/auth-client.js";
import "superjson";
import "@oslojs/crypto/sha2";
import "decimal.js";
import "linq-extensions";
import "remult";
import { C as Calendar_1 } from "../../../../chunks/calendar-prev-button.js";
import { c as cn } from "../../../../chunks/shadcn.utils.js";
import { B as Button, b as buttonVariants } from "../../../../chunks/button.js";
import { s as superForm } from "../../../../chunks/string.js";
import { v as valibot, d as defaults } from "../../../../chunks/valibot.js";
import "@sveltejs/kit";
import { C as Card, a as Card_content } from "../../../../chunks/card-content.js";
import { C as Card_header, a as Card_title } from "../../../../chunks/card-title.js";
import { F as Form_alerts, a as Form_field, C as Control, b as Form_field_errors } from "../../../../chunks/index5.js";
import { R as Root, a as Popover_trigger, P as Popover_content } from "../../../../chunks/index7.js";
import { I as Input } from "../../../../chunks/input.js";
import { L as Label } from "../../../../chunks/label.js";
import { I as Icon } from "../../../../chunks/states.svelte.js";
import "clsx";
import { H as Hidden_input } from "../../../../chunks/hidden-input.js";
import { N as No_data } from "../../../../chunks/no-data.js";
import { e as getOrgStore } from "../../../../chunks/sheet-content.js";
import { DateFormatter, parseDate, today, getLocalTimeZone, CalendarDate } from "@internationalized/date";
import "../../../../chunks/client2.js";
import "../../../../chunks/nprogress.js";
import { C as Card_shell } from "../../../../chunks/card_shell.js";
import { P as Pencil } from "../../../../chunks/pencil.js";
import { F as Form_label, a as Form_button, L as Loader_circle } from "../../../../chunks/form-button.js";
import { C as Calendar } from "../../../../chunks/calendar.js";
function Hard_drive_upload($$payload, $$props) {
  push();
  /**
   * @license @lucide/svelte v0.539.0 - ISC
   *
   * ISC License
   *
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   *
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   *
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   *
   */
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    ["path", { "d": "m16 6-4-4-4 4" }],
    ["path", { "d": "M12 2v8" }],
    [
      "rect",
      { "width": "20", "height": "8", "x": "2", "y": "14", "rx": "2" }
    ],
    ["path", { "d": "M6 18h.01" }],
    ["path", { "d": "M10 18h.01" }]
  ];
  Icon($$payload, spread_props([
    { name: "hard-drive-upload" },
    /**
     * @component @name HardDriveUpload
     * @description Lucide SVG icon component, renders SVG Element with children.
     *
     * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTYgNi00LTQtNCA0IiAvPgogIDxwYXRoIGQ9Ik0xMiAydjgiIC8+CiAgPHJlY3Qgd2lkdGg9IjIwIiBoZWlnaHQ9IjgiIHg9IjIiIHk9IjE0IiByeD0iMiIgLz4KICA8cGF0aCBkPSJNNiAxOGguMDEiIC8+CiAgPHBhdGggZD0iTTEwIDE4aC4wMSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/hard-drive-upload
     * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
     *
     * @param {Object} props - Lucide icons props and any valid SVG attribute
     * @returns {FunctionalComponent} Svelte component
     *
     */
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out.push(`<!---->`);
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function _page($$payload, $$props) {
  push();
  var $$store_subs;
  const orgStore = getOrgStore();
  const form = superForm(defaults(orgStore.org, valibot(settingsFormSchema)), {
    SPA: true,
    resetForm: true,
    clearOnSubmit: "errors-and-message",
    dataType: "json",
    validators: valibot(settingsFormSchema),
    async onUpdate({ form: form2 }) {
      if (!form2.valid) return;
      try {
        const { data: formData } = form2;
        const settingsObj = {
          phoneNumber: formData.phoneNumber,
          businessEmail: formData.businessEmail,
          name: formData.name,
          businessRegNo: formData.businessRegNo,
          registrationDate: regDateValue
        };
        if (!orgStore.org?.id) {
          throw new Error("Organization ID is required");
        }
        const orgUpdateResp = await OrganizationController.update(orgStore.org.id, settingsObj);
        if (orgUpdateResp?.id) form2.message = { status: "success", text: "Settings updated successfully!" };
        else form2.message = { status: "error", text: "Failed to update settings" };
      } catch (error) {
        console.error(error);
        form2.message = {
          text: "An error occurred while updating the settings",
          status: "error"
        };
      }
    }
  });
  const {
    form: formValues,
    enhance,
    message,
    submitting,
    allErrors,
    delayed
  } = form;
  let editSettings = false;
  const df = new DateFormatter("en-GB", { dateStyle: "long" });
  orgStore.org?.registrationDate ? new Date(orgStore.org?.registrationDate).toString() : "";
  let regDateValue = store_get($$store_subs ??= {}, "$formValues", formValues).registrationDate ? parseDate(store_get($$store_subs ??= {}, "$formValues", formValues).registrationDate) : void 0;
  let placeholder = today(getLocalTimeZone());
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    Card_shell($$payload2, {
      heading: "Organization Settings Dashboard",
      subHeading: "Manage organization details",
      children: ($$payload3) => {
        if (orgStore.org?.id) {
          $$payload3.out.push("<!--[-->");
          $$payload3.out.push(`<div class="grid auto-cols-auto grid-flow-col gap-2 max-md:grid-rows-2"><!---->`);
          Card($$payload3, {
            class: "w-full rounded-none",
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->`);
              Card_header($$payload4, {
                children: ($$payload5) => {
                  $$payload5.out.push(`<!---->`);
                  Card_title($$payload5, {
                    class: "flex items-center justify-between",
                    children: ($$payload6) => {
                      $$payload6.out.push(`<!---->Current Organization Settings `);
                      Button($$payload6, {
                        variant: "ghost",
                        size: "icon",
                        onclick: () => editSettings = true,
                        children: ($$payload7) => {
                          Pencil($$payload7, { class: "size-4" });
                        },
                        $$slots: { default: true }
                      });
                      $$payload6.out.push(`<!---->`);
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out.push(`<!---->`);
                },
                $$slots: { default: true }
              });
              $$payload4.out.push(`<!----> <!---->`);
              Card_content($$payload4, {
                children: ($$payload5) => {
                  $$payload5.out.push(`<div class="grid grid-cols-1 gap-4 space-y-4 md:grid-cols-3"><div class="space-y-1">`);
                  Label($$payload5, {
                    children: ($$payload6) => {
                      $$payload6.out.push(`<!---->Team Name`);
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out.push(`<!----> <p class="text-muted-foreground text-sm">${escape_html(orgStore.org?.name)}</p></div> <div class="space-y-1">`);
                  Label($$payload5, {
                    children: ($$payload6) => {
                      $$payload6.out.push(`<!---->Email`);
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out.push(`<!----> <p class="text-muted-foreground text-sm">${escape_html(orgStore.org?.businessEmail)}</p></div> <div class="space-y-1">`);
                  Label($$payload5, {
                    children: ($$payload6) => {
                      $$payload6.out.push(`<!---->Phone Number`);
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out.push(`<!----> <p class="text-muted-foreground text-sm">${escape_html(orgStore.org?.phoneNumber)}</p></div> <div class="space-y-1">`);
                  Label($$payload5, {
                    children: ($$payload6) => {
                      $$payload6.out.push(`<!---->Registration No.`);
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out.push(`<!----> <p class="text-muted-foreground text-sm">${escape_html(orgStore.org?.businessRegNo)}</p></div> <div class="space-y-1">`);
                  Label($$payload5, {
                    children: ($$payload6) => {
                      $$payload6.out.push(`<!---->Registration Date:`);
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out.push(`<!----> <p class="text-muted-foreground text-sm">${escape_html(new Date(orgStore.org?.registrationDate ?? "").toLocaleDateString())}</p></div></div>`);
                },
                $$slots: { default: true }
              });
              $$payload4.out.push(`<!---->`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> `);
          if (editSettings) {
            $$payload3.out.push("<!--[-->");
            $$payload3.out.push(`<!---->`);
            Card($$payload3, {
              class: "w-full",
              children: ($$payload4) => {
                $$payload4.out.push(`<!---->`);
                Card_header($$payload4, {
                  children: ($$payload5) => {
                    $$payload5.out.push(`<!---->`);
                    Card_title($$payload5, {
                      children: ($$payload6) => {
                        $$payload6.out.push(`<!---->Edit Settings`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out.push(`<!---->`);
                  },
                  $$slots: { default: true }
                });
                $$payload4.out.push(`<!----> <!---->`);
                Card_content($$payload4, {
                  children: ($$payload5) => {
                    $$payload5.out.push(`<form method="POST" class="space-y-4">`);
                    Form_alerts($$payload5, { message });
                    $$payload5.out.push(`<!----> <!---->`);
                    Form_field($$payload5, {
                      form,
                      name: "name",
                      class: "space-y-2",
                      children: ($$payload6) => {
                        $$payload6.out.push(`<!---->`);
                        {
                          let children = function($$payload7, { props }) {
                            $$payload7.out.push(`<!---->`);
                            Form_label($$payload7, {
                              children: ($$payload8) => {
                                $$payload8.out.push(`<!---->Business Name`);
                              },
                              $$slots: { default: true }
                            });
                            $$payload7.out.push(`<!----> `);
                            Input($$payload7, spread_props([
                              props,
                              {
                                placeholder: "Business Name",
                                get value() {
                                  return store_get($$store_subs ??= {}, "$formValues", formValues).name;
                                },
                                set value($$value) {
                                  store_mutate($$store_subs ??= {}, "$formValues", formValues, store_get($$store_subs ??= {}, "$formValues", formValues).name = $$value);
                                  $$settled = false;
                                }
                              }
                            ]));
                            $$payload7.out.push(`<!---->`);
                          };
                          Control($$payload6, { children });
                        }
                        $$payload6.out.push(`<!----> <!---->`);
                        Form_field_errors($$payload6, {});
                        $$payload6.out.push(`<!---->`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out.push(`<!----> <!---->`);
                    Form_field($$payload5, {
                      form,
                      name: "businessEmail",
                      class: "space-y-2",
                      children: ($$payload6) => {
                        $$payload6.out.push(`<!---->`);
                        {
                          let children = function($$payload7, { props }) {
                            $$payload7.out.push(`<!---->`);
                            Form_label($$payload7, {
                              children: ($$payload8) => {
                                $$payload8.out.push(`<!---->Email`);
                              },
                              $$slots: { default: true }
                            });
                            $$payload7.out.push(`<!----> `);
                            Input($$payload7, spread_props([
                              props,
                              {
                                placeholder: "Business Email",
                                get value() {
                                  return store_get($$store_subs ??= {}, "$formValues", formValues).businessEmail;
                                },
                                set value($$value) {
                                  store_mutate($$store_subs ??= {}, "$formValues", formValues, store_get($$store_subs ??= {}, "$formValues", formValues).businessEmail = $$value);
                                  $$settled = false;
                                }
                              }
                            ]));
                            $$payload7.out.push(`<!---->`);
                          };
                          Control($$payload6, { children });
                        }
                        $$payload6.out.push(`<!----> <!---->`);
                        Form_field_errors($$payload6, {});
                        $$payload6.out.push(`<!---->`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out.push(`<!----> <!---->`);
                    Form_field($$payload5, {
                      form,
                      name: "phoneNumber",
                      class: "space-y-2",
                      children: ($$payload6) => {
                        $$payload6.out.push(`<!---->`);
                        {
                          let children = function($$payload7, { props }) {
                            $$payload7.out.push(`<!---->`);
                            Form_label($$payload7, {
                              children: ($$payload8) => {
                                $$payload8.out.push(`<!---->Phone Number`);
                              },
                              $$slots: { default: true }
                            });
                            $$payload7.out.push(`<!----> `);
                            Input($$payload7, spread_props([
                              props,
                              {
                                placeholder: "Phone Number",
                                get value() {
                                  return store_get($$store_subs ??= {}, "$formValues", formValues).phoneNumber;
                                },
                                set value($$value) {
                                  store_mutate($$store_subs ??= {}, "$formValues", formValues, store_get($$store_subs ??= {}, "$formValues", formValues).phoneNumber = $$value);
                                  $$settled = false;
                                }
                              }
                            ]));
                            $$payload7.out.push(`<!---->`);
                          };
                          Control($$payload6, { children });
                        }
                        $$payload6.out.push(`<!----> <!---->`);
                        Form_field_errors($$payload6, {});
                        $$payload6.out.push(`<!---->`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out.push(`<!----> <!---->`);
                    Form_field($$payload5, {
                      form,
                      name: "businessRegNo",
                      class: "space-y-2",
                      children: ($$payload6) => {
                        $$payload6.out.push(`<!---->`);
                        {
                          let children = function($$payload7, { props }) {
                            $$payload7.out.push(`<!---->`);
                            Form_label($$payload7, {
                              children: ($$payload8) => {
                                $$payload8.out.push(`<!---->Registration No:`);
                              },
                              $$slots: { default: true }
                            });
                            $$payload7.out.push(`<!----> `);
                            Input($$payload7, spread_props([
                              props,
                              {
                                placeholder: "Registration No",
                                get value() {
                                  return store_get($$store_subs ??= {}, "$formValues", formValues).businessRegNo;
                                },
                                set value($$value) {
                                  store_mutate($$store_subs ??= {}, "$formValues", formValues, store_get($$store_subs ??= {}, "$formValues", formValues).businessRegNo = $$value);
                                  $$settled = false;
                                }
                              }
                            ]));
                            $$payload7.out.push(`<!---->`);
                          };
                          Control($$payload6, { children });
                        }
                        $$payload6.out.push(`<!----> <!---->`);
                        Form_field_errors($$payload6, {});
                        $$payload6.out.push(`<!---->`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out.push(`<!----> <!---->`);
                    Form_field($$payload5, {
                      form,
                      name: "registrationDate",
                      class: "flex flex-col",
                      children: ($$payload6) => {
                        $$payload6.out.push(`<!---->`);
                        {
                          let children = function($$payload7, { props }) {
                            $$payload7.out.push(`<!---->`);
                            Form_label($$payload7, {
                              children: ($$payload8) => {
                                $$payload8.out.push(`<!---->Date Incorporated`);
                              },
                              $$slots: { default: true }
                            });
                            $$payload7.out.push(`<!----> <!---->`);
                            Root($$payload7, {
                              children: ($$payload8) => {
                                $$payload8.out.push(`<!---->`);
                                Popover_trigger($$payload8, spread_props([
                                  props,
                                  {
                                    class: cn(buttonVariants({ variant: "outline" }), "w-[280px] justify-start pl-4 text-left font-normal", !regDateValue && "text-muted-foreground"),
                                    children: ($$payload9) => {
                                      $$payload9.out.push(`<!---->${escape_html(regDateValue ? df.format(regDateValue.toDate(getLocalTimeZone())) : "Pick a date")} `);
                                      Calendar($$payload9, { class: "ml-auto size-4 opacity-50" });
                                      $$payload9.out.push(`<!---->`);
                                    },
                                    $$slots: { default: true }
                                  }
                                ]));
                                $$payload8.out.push(`<!----> <!---->`);
                                Popover_content($$payload8, {
                                  class: "w-auto p-0",
                                  side: "bottom",
                                  children: ($$payload9) => {
                                    Calendar_1($$payload9, {
                                      type: "single",
                                      value: regDateValue,
                                      minValue: new CalendarDate(1900, 1, 1),
                                      maxValue: today(getLocalTimeZone()),
                                      calendarLabel: "Date of birth",
                                      onValueChange: (v) => {
                                        if (v) {
                                          store_mutate($$store_subs ??= {}, "$formValues", formValues, store_get($$store_subs ??= {}, "$formValues", formValues).registrationDate = v.toString());
                                        } else {
                                          store_mutate($$store_subs ??= {}, "$formValues", formValues, store_get($$store_subs ??= {}, "$formValues", formValues).registrationDate = "");
                                        }
                                      },
                                      get placeholder() {
                                        return placeholder;
                                      },
                                      set placeholder($$value) {
                                        placeholder = $$value;
                                        $$settled = false;
                                      }
                                    });
                                  },
                                  $$slots: { default: true }
                                });
                                $$payload8.out.push(`<!---->`);
                              },
                              $$slots: { default: true }
                            });
                            $$payload7.out.push(`<!----> <input hidden${attr("name", props.name)}${attr("value", store_get($$store_subs ??= {}, "$formValues", formValues).registrationDate)}/>`);
                          };
                          Control($$payload6, { children });
                        }
                        $$payload6.out.push(`<!----> <!---->`);
                        Form_field_errors($$payload6, {});
                        $$payload6.out.push(`<!---->`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out.push(`<!----> <!---->`);
                    Form_field($$payload5, {
                      form,
                      name: "id",
                      children: ($$payload6) => {
                        $$payload6.out.push(`<!---->`);
                        {
                          let children = function($$payload7, { props }) {
                            Hidden_input($$payload7, {
                              props,
                              value: store_get($$store_subs ??= {}, "$formValues", formValues).id
                            });
                          };
                          Control($$payload6, { children });
                        }
                        $$payload6.out.push(`<!----> <!---->`);
                        Form_field_errors($$payload6, {});
                        $$payload6.out.push(`<!---->`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out.push(`<!----> <!---->`);
                    Form_button($$payload5, {
                      disabled: store_get($$store_subs ??= {}, "$allErrors", allErrors).length > 0 || store_get($$store_subs ??= {}, "$submitting", submitting),
                      class: "mt-4 w-full",
                      children: ($$payload6) => {
                        if (store_get($$store_subs ??= {}, "$delayed", delayed)) {
                          $$payload6.out.push("<!--[-->");
                          Loader_circle($$payload6, { class: "mr-2 size-4 animate-spin" });
                        } else {
                          $$payload6.out.push("<!--[!-->");
                        }
                        $$payload6.out.push(`<!--]--> ${escape_html(store_get($$store_subs ??= {}, "$submitting", submitting) ? "Please wait..." : "Update Settings")}`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out.push(`<!----></form>`);
                  },
                  $$slots: { default: true }
                });
                $$payload4.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload3.out.push(`<!---->`);
          } else {
            $$payload3.out.push("<!--[!-->");
          }
          $$payload3.out.push(`<!--]--></div>`);
        } else {
          let iconSnippet = function($$payload4) {
            Hard_drive_upload($$payload4, { class: "h-10 w-10" });
          };
          $$payload3.out.push("<!--[!-->");
          No_data($$payload3, {
            noDataTitle: "No Teams Settings Data",
            noDataDescription: "You may not have the permissions required to view this data",
            snippet: iconSnippet,
            children: ($$payload4) => {
              $$payload4.out.push(`<div class="flex justify-between space-x-2"><p>You may not have the permissions required to view this data. Please contact your Team administrator.</p></div>`);
            }
          });
        }
        $$payload3.out.push(`<!--]-->`);
      }
    });
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
export {
  _page as default
};
