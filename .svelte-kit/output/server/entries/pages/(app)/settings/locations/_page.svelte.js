import "clsx";
import { w as push, B as escape_html, G as spread_props, y as pop, E as ensure_array_like } from "../../../../../chunks/index2.js";
import "../../../../../chunks/states.svelte.js";
import { B as Button } from "../../../../../chunks/button.js";
import { g as getUtilsStore, e as getOrgStore } from "../../../../../chunks/sheet-content.js";
import { N as No_data } from "../../../../../chunks/no-data.js";
import "../../../../../chunks/client2.js";
import "linq-extensions";
import "@sveltejs/kit/internal";
import "../../../../../chunks/exports.js";
import "../../../../../chunks/state.svelte.js";
import "../../../../../chunks/nprogress.js";
import { C as Card_shell } from "../../../../../chunks/card_shell.js";
import { C as Card, a as Card_content } from "../../../../../chunks/card-content.js";
import { a8 as TeamController, R as RolesType } from "../../../../../chunks/auth-client.js";
import "superjson";
import "@oslojs/crypto/sha2";
import "decimal.js";
import "remult";
import { D as Dropdown_menu_shortcut, a as Data_table_checkbox, b as Data_table_cell, c as Data_table_column_header, d as Data_table_toolbar, e as Data_table_pagination } from "../../../../../chunks/data_table_column_header.js";
import "../../../../../chunks/badge.js";
import { r as renderComponent, c as createSvelteTable, T as Table, a as Table_header, b as Table_row, d as Table_head, F as Flex_render, e as Table_body, f as Table_cell } from "../../../../../chunks/data-table.svelte.js";
import { getFacetedUniqueValues, getFacetedRowModel, getSortedRowModel, getPaginationRowModel, getFilteredRowModel, getCoreRowModel } from "@tanstack/table-core";
import { R as Root, D as Dropdown_menu_trigger, a as Dropdown_menu_content } from "../../../../../chunks/index6.js";
import { b as Dropdown_menu_item, D as Dropdown_menu_separator } from "../../../../../chunks/dropdown-menu-separator.js";
import { E as Ellipsis } from "../../../../../chunks/ellipsis.js";
import { X } from "../../../../../chunks/x.js";
import { L as List } from "../../../../../chunks/list.js";
function Locations_row_actions($$payload, $$props) {
  push();
  let { row } = $$props;
  const uStore = getUtilsStore();
  let toggeStatusText = row.original?.isActive === true ? "Deactivate Location" : "Activate Location";
  const toggleTeamStatus = async () => {
    const status = !row.original?.isActive;
    await updateTeamStatus(status);
  };
  const editTeamLocation = () => {
    const newTeamLocationForm = {
      canClose: false,
      title: "Edit Team/Branch Location",
      componentName: "AddTeamLocation",
      size: "lg",
      color: "default",
      description: "Edit Team/Branch details",
      componentProps: { data: row.original }
    };
    uStore.openDynamicModal(newTeamLocationForm);
  };
  const setInactiveLocation = async () => {
    await updateTeamStatus(false);
  };
  const updateTeamStatus = async (isActive) => {
    try {
      const updateResp = await TeamController.update(row.original.id, { isActive });
      if (updateResp.id) return uStore.toastSuccess("Team location status updated successfully");
      else {
        uStore.toastError("Failed to update team location status");
      }
    } catch (err) {
      uStore.toastError(`Failed to update status: ${err.message}`);
    }
  };
  $$payload.out.push(`<!---->`);
  Root($$payload, {
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->`);
      {
        let child = function($$payload3, { props }) {
          Button($$payload3, spread_props([
            props,
            {
              variant: "ghost",
              class: "data-[state=open]:bg-muted flex h-8 w-8 p-0",
              children: ($$payload4) => {
                Ellipsis($$payload4, {});
                $$payload4.out.push(`<!----> <span class="sr-only">Open Menu</span>`);
              },
              $$slots: { default: true }
            }
          ]));
        };
        Dropdown_menu_trigger($$payload2, { child, $$slots: { child: true } });
      }
      $$payload2.out.push(`<!----> <!---->`);
      Dropdown_menu_content($$payload2, {
        class: "w-[160px]",
        align: "end",
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Dropdown_menu_item($$payload3, {
            onclick: editTeamLocation,
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Edit`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> <!---->`);
          Dropdown_menu_item($$payload3, {
            onclick: setInactiveLocation,
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->Set InActive`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!----> <!---->`);
          Dropdown_menu_separator($$payload3, {});
          $$payload3.out.push(`<!----> <!---->`);
          Dropdown_menu_item($$payload3, {
            onclick: () => toggleTeamStatus(),
            children: ($$payload4) => {
              $$payload4.out.push(`<!---->${escape_html(toggeStatusText)} <!---->`);
              Dropdown_menu_shortcut($$payload4, {
                children: ($$payload5) => {
                  $$payload5.out.push(`<!---->⌘⌫`);
                },
                $$slots: { default: true }
              });
              $$payload4.out.push(`<!---->`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!---->`);
  pop();
}
const columns = [
  {
    id: "select",
    header: ({ table }) => renderComponent(Data_table_checkbox, {
      checked: table.getIsAllPageRowsSelected(),
      onCheckedChange: (value) => table.toggleAllPageRowsSelected(!!value),
      "aria-label": "Select all",
      class: "translate-y-[2px]"
    }),
    cell: ({ row }) => renderComponent(Data_table_checkbox, {
      checked: row.getIsSelected(),
      onCheckedChange: (value) => row.toggleSelected(!!value),
      "aria-label": "Select row",
      class: "translate-y-[2px]"
    }),
    enableSorting: false,
    enableHiding: false
  },
  {
    accessorKey: "locationName",
    header: ({ column }) => renderComponent(
      Data_table_column_header,
      {
        column,
        title: "Location Name"
      }
    ),
    cell: ({ row }) => {
      return renderComponent(Data_table_cell, {
        value: row.original?.location?.name
      });
    },
    enableSorting: true,
    enableColumnFilter: true
  },
  {
    accessorKey: "isActive",
    header: ({ column }) => renderComponent(
      Data_table_column_header,
      {
        column,
        title: "Active ?"
      }
    ),
    cell: ({ row }) => {
      return renderComponent(Data_table_cell, {
        value: row.original?.location?.isActive ? "Yes" : "No"
      });
    },
    enableSorting: true,
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id));
    }
  },
  {
    accessorKey: "locationAddress",
    header: ({ column }) => renderComponent(
      Data_table_column_header,
      {
        column,
        title: "Address"
      }
    ),
    cell: ({ row }) => {
      return renderComponent(Data_table_cell, {
        value: row.original?.location?.locationAddress ?? ""
      });
    },
    enableSorting: true,
    enableColumnFilter: true
  },
  {
    accessorKey: "locationEmail",
    header: ({ column }) => renderComponent(
      Data_table_column_header,
      {
        column,
        title: "Email"
      }
    ),
    cell: ({ row }) => {
      return renderComponent(Data_table_cell, {
        value: row.original?.location?.locationEmail ?? ""
      });
    },
    enableSorting: true,
    enableColumnFilter: true
  },
  {
    accessorKey: "locationPhoneNumber",
    header: ({ column }) => renderComponent(
      Data_table_column_header,
      {
        column,
        title: "Phone"
      }
    ),
    cell: ({ row }) => {
      return renderComponent(Data_table_cell, {
        value: row.original?.location?.locationPhoneNumber ?? ""
      });
    },
    enableColumnFilter: false,
    enableSorting: true
  },
  {
    accessorKey: "teamAdminName",
    header: ({ column }) => {
      return renderComponent(
        Data_table_column_header,
        {
          title: "Team Manager",
          column
        }
      );
    },
    cell: ({ row }) => {
      return renderComponent(Data_table_cell, {
        value: `${row.original?.profile?.givenName} ${row.original?.profile?.familyName}`
      });
    },
    enableColumnFilter: true,
    enableSorting: true,
    enableGrouping: true
  },
  {
    accessorKey: "isDefault",
    header: ({ column }) => {
      return renderComponent(
        Data_table_column_header,
        {
          title: "HQ ?",
          column
        }
      );
    },
    cell: ({ row }) => {
      return renderComponent(Data_table_cell, {
        value: row.original?.location?.isDefault ? "Yes" : "No"
      });
    },
    enableColumnFilter: true,
    enableSorting: true,
    enableGrouping: true
  },
  {
    id: "actions",
    cell: ({ row }) => renderComponent(Locations_row_actions, { row })
  }
];
function Locations_table($$payload, $$props) {
  push();
  let { data } = $$props;
  let rowSelection = {};
  let columnVisibility = {};
  let columnFilters = [];
  let sorting = [];
  let pagination = { pageIndex: 0, pageSize: 10 };
  getOrgStore();
  const table = createSvelteTable({
    get data() {
      return data || [];
    },
    state: {
      get sorting() {
        return sorting;
      },
      get columnVisibility() {
        return columnVisibility;
      },
      get rowSelection() {
        return rowSelection;
      },
      get columnFilters() {
        return columnFilters;
      },
      get pagination() {
        return pagination;
      }
    },
    columns,
    enableRowSelection: true,
    onRowSelectionChange: (updater) => {
      if (typeof updater === "function") {
        rowSelection = updater(rowSelection);
      } else {
        rowSelection = updater;
      }
    },
    onSortingChange: (updater) => {
      if (typeof updater === "function") {
        sorting = updater(sorting);
      } else {
        sorting = updater;
      }
    },
    onColumnFiltersChange: (updater) => {
      if (typeof updater === "function") {
        columnFilters = updater(columnFilters);
      } else {
        columnFilters = updater;
      }
    },
    onColumnVisibilityChange: (updater) => {
      if (typeof updater === "function") {
        columnVisibility = updater(columnVisibility);
      } else {
        columnVisibility = updater;
      }
    },
    onPaginationChange: (updater) => {
      if (typeof updater === "function") {
        pagination = updater(pagination);
      } else {
        pagination = updater;
      }
    },
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues()
  });
  const isFiltered = table.getState().columnFilters.length > 0;
  $$payload.out.push(`<div class="space-y-4">`);
  Data_table_toolbar($$payload, {
    table,
    filterColumn: "locationName",
    filterPlaceholder: "Filter locations...",
    children: ($$payload2) => {
      if (isFiltered) {
        $$payload2.out.push("<!--[-->");
        Button($$payload2, {
          variant: "ghost",
          onclick: () => table.resetColumnFilters(),
          class: "h-8 px-2 lg:px-3",
          children: ($$payload3) => {
            $$payload3.out.push(`<!---->Reset `);
            X($$payload3, {});
            $$payload3.out.push(`<!---->`);
          },
          $$slots: { default: true }
        });
      } else {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]-->`);
    }
  });
  $$payload.out.push(`<!----> <div class="rounded-md border"><!---->`);
  Table($$payload, {
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->`);
      Table_header($$payload2, {
        children: ($$payload3) => {
          const each_array = ensure_array_like(table.getHeaderGroups());
          $$payload3.out.push(`<!--[-->`);
          for (let $$index_1 = 0, $$length = each_array.length; $$index_1 < $$length; $$index_1++) {
            let headerGroup = each_array[$$index_1];
            $$payload3.out.push(`<!---->`);
            Table_row($$payload3, {
              children: ($$payload4) => {
                const each_array_1 = ensure_array_like(headerGroup.headers);
                $$payload4.out.push(`<!--[-->`);
                for (let $$index = 0, $$length2 = each_array_1.length; $$index < $$length2; $$index++) {
                  let header = each_array_1[$$index];
                  $$payload4.out.push(`<!---->`);
                  Table_head($$payload4, {
                    colspan: header.colSpan,
                    children: ($$payload5) => {
                      if (!header.isPlaceholder) {
                        $$payload5.out.push("<!--[-->");
                        Flex_render($$payload5, {
                          content: header.column.columnDef.header,
                          context: header.getContext()
                        });
                      } else {
                        $$payload5.out.push("<!--[!-->");
                      }
                      $$payload5.out.push(`<!--]-->`);
                    },
                    $$slots: { default: true }
                  });
                  $$payload4.out.push(`<!---->`);
                }
                $$payload4.out.push(`<!--]-->`);
              },
              $$slots: { default: true }
            });
            $$payload3.out.push(`<!---->`);
          }
          $$payload3.out.push(`<!--]-->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!----> <!---->`);
      Table_body($$payload2, {
        children: ($$payload3) => {
          const each_array_2 = ensure_array_like(table.getRowModel().rows);
          if (each_array_2.length !== 0) {
            $$payload3.out.push("<!--[-->");
            for (let $$index_3 = 0, $$length = each_array_2.length; $$index_3 < $$length; $$index_3++) {
              let row = each_array_2[$$index_3];
              $$payload3.out.push(`<!---->`);
              Table_row($$payload3, {
                "data-state": row.getIsSelected() && "selected",
                children: ($$payload4) => {
                  const each_array_3 = ensure_array_like(row.getVisibleCells());
                  $$payload4.out.push(`<!--[-->`);
                  for (let $$index_2 = 0, $$length2 = each_array_3.length; $$index_2 < $$length2; $$index_2++) {
                    let cell = each_array_3[$$index_2];
                    $$payload4.out.push(`<!---->`);
                    Table_cell($$payload4, {
                      children: ($$payload5) => {
                        Flex_render($$payload5, {
                          content: cell.column.columnDef.cell,
                          context: cell.getContext()
                        });
                      },
                      $$slots: { default: true }
                    });
                    $$payload4.out.push(`<!---->`);
                  }
                  $$payload4.out.push(`<!--]-->`);
                },
                $$slots: { default: true }
              });
              $$payload3.out.push(`<!---->`);
            }
          } else {
            $$payload3.out.push("<!--[!-->");
            $$payload3.out.push(`<!---->`);
            Table_row($$payload3, {
              children: ($$payload4) => {
                $$payload4.out.push(`<!---->`);
                Table_cell($$payload4, {
                  colspan: columns.length,
                  class: "h-24 text-center",
                  children: ($$payload5) => {
                    $$payload5.out.push(`<!---->No results.`);
                  },
                  $$slots: { default: true }
                });
                $$payload4.out.push(`<!---->`);
              },
              $$slots: { default: true }
            });
            $$payload3.out.push(`<!---->`);
          }
          $$payload3.out.push(`<!--]-->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    },
    $$slots: { default: true }
  });
  $$payload.out.push(`<!----></div> `);
  Data_table_pagination($$payload, { table });
  $$payload.out.push(`<!----></div>`);
  pop();
}
function _page($$payload, $$props) {
  push();
  const uStore = getUtilsStore();
  const orgStore = getOrgStore();
  let hasLocations = orgStore.teams.length > 0;
  const heading = "Locations Manager";
  const subHeading = "Manage team (business) locations. Change State, Add new , Edit and Disable Locations";
  const teamData = (() => {
    return orgStore.teams?.map((tl) => {
      const teamAdmin = orgStore.staffMembers?.firstOrNull((sm) => sm.profile?.teamId === tl.id && sm.profile?.roles?.includes(RolesType.TeamLocationAdmin));
      return { location: tl, profile: teamAdmin?.profile };
    });
  })();
  const startCreateTeamLocation = () => {
    const newBranchLocation = {
      canClose: false,
      title: "New team Location",
      componentName: "AddTeamLocation",
      size: "lg",
      color: "default",
      description: "Create new Team/Branch Location",
      componentProps: {}
    };
    uStore.openDynamicModal(newBranchLocation);
  };
  Card_shell($$payload, {
    heading,
    subHeading,
    children: ($$payload2) => {
      $$payload2.out.push(`<!---->`);
      Card($$payload2, {
        class: "rounded-none",
        children: ($$payload3) => {
          $$payload3.out.push(`<!---->`);
          Card_content($$payload3, {
            children: ($$payload4) => {
              if (hasLocations) {
                $$payload4.out.push("<!--[-->");
                Locations_table($$payload4, { data: teamData });
              } else {
                let listSnippet = function($$payload5) {
                  List($$payload5, { class: "h-10 w-10" });
                };
                $$payload4.out.push("<!--[!-->");
                No_data($$payload4, {
                  noDataTitle: "No Team Location",
                  noDataDescription: "You do not have any location created",
                  snippet: listSnippet,
                  children: ($$payload5) => {
                    $$payload5.out.push(`<div class="flex justify-between space-x-2">`);
                    Button($$payload5, {
                      variant: "default",
                      onclick: startCreateTeamLocation,
                      children: ($$payload6) => {
                        $$payload6.out.push(`<!---->Create Location`);
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out.push(`<!----></div>`);
                  }
                });
              }
              $$payload4.out.push(`<!--]-->`);
            },
            $$slots: { default: true }
          });
          $$payload3.out.push(`<!---->`);
        },
        $$slots: { default: true }
      });
      $$payload2.out.push(`<!---->`);
    }
  });
  pop();
}
export {
  _page as default
};
