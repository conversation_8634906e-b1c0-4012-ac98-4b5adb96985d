import { remult } from "remult";
const trailingSlash = "always";
const load = async ({ url }) => {
  const baseMetaTags = Object.freeze({
    title: "Default",
    titleTemplate: "%s | SpenDeed",
    description: "Ai Driven small business platform for running everything from AI assisted sales to rewards and appointments",
    canonical: new URL(url.pathname, url.origin).href,
    openGraph: {
      type: "website",
      url: new URL(url.pathname, url.origin).href,
      locale: "en_GB",
      title: "Spendeed",
      description: "Ai Driven small business platform for running everything from AI assisted sales to rewards and appointments",
      siteName: "spendeed.com",
      images: [
        {
          url: "https://www.example.ie/og-image.jpg",
          alt: "Og Image Alt",
          width: 800,
          height: 600,
          secureUrl: "https://www.example.ie/og-image.jpg",
          type: "image/jpeg"
        }
      ]
    }
  });
  return {
    baseMetaTags,
    user: remult.user
  };
};
export {
  load,
  trailingSlash
};
