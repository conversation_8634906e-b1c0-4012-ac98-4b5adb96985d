import { z as attr, y as pop, w as push, A as head, B as escape_html, D as stringify, E as ensure_array_like, F as spread_attributes, G as spread_props } from "../../chunks/index2.js";
import "linq-extensions";
/* empty css                */
import { m as modeStorageKey, t as themeStorageKey, d as darkClassNames, l as lightClassNames, a as disableTransitions, b as themeColors, s as synchronousModeChanges, c as createSubscriber } from "../../chunks/states.svelte.js";
import "clsx";
import { s as setInitialMode, d as defineConfig } from "../../chunks/mode.js";
import { h as html } from "../../chunks/html.js";
import "../../chunks/button.js";
import { s as setUtilsStoreContext, a as setAuthStoreContext, b as setOrgStoreContext, c as setAppointmentStoreContext } from "../../chunks/sheet-content.js";
import "@sveltejs/kit/internal";
import "../../chunks/exports.js";
import "../../chunks/state.svelte.js";
import { i as initSuperJson } from "../../chunks/auth-client.js";
import "superjson";
import { remult, Remult } from "remult";
function Mode_watcher_lite($$payload, $$props) {
  push();
  let { themeColors: themeColors2 } = $$props;
  if (themeColors2) {
    $$payload.out.push("<!--[-->");
    $$payload.out.push(`<meta name="theme-color"${attr("content", themeColors2.dark)}/>`);
  } else {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]-->`);
  pop();
}
function Mode_watcher_full($$payload, $$props) {
  push();
  let { trueNonce = "", initConfig, themeColors: themeColors2 } = $$props;
  head($$payload, ($$payload2) => {
    if (themeColors2) {
      $$payload2.out.push("<!--[-->");
      $$payload2.out.push(`<meta name="theme-color"${attr("content", themeColors2.dark)}/>`);
    } else {
      $$payload2.out.push("<!--[!-->");
    }
    $$payload2.out.push(`<!--]--> ${html(`<script${trueNonce ? ` nonce=${trueNonce}` : ""}>(` + setInitialMode.toString() + `)(` + JSON.stringify(initConfig) + `);<\/script>`)}`);
  });
  pop();
}
function Mode_watcher($$payload, $$props) {
  push();
  let {
    defaultMode = "system",
    themeColors: themeColorsProp,
    disableTransitions: disableTransitionsProp = true,
    darkClassNames: darkClassNamesProp = ["dark"],
    lightClassNames: lightClassNamesProp = [],
    defaultTheme = "",
    nonce = "",
    themeStorageKey: themeStorageKeyProp = "mode-watcher-theme",
    modeStorageKey: modeStorageKeyProp = "mode-watcher-mode",
    disableHeadScriptInjection = false,
    synchronousModeChanges: synchronousModeChangesProp = false
  } = $$props;
  modeStorageKey.current = modeStorageKeyProp;
  themeStorageKey.current = themeStorageKeyProp;
  darkClassNames.current = darkClassNamesProp;
  lightClassNames.current = lightClassNamesProp;
  disableTransitions.current = disableTransitionsProp;
  themeColors.current = themeColorsProp;
  synchronousModeChanges.current = synchronousModeChangesProp;
  const initConfig = defineConfig({
    defaultMode,
    themeColors: themeColorsProp,
    darkClassNames: darkClassNamesProp,
    lightClassNames: lightClassNamesProp,
    defaultTheme,
    modeStorageKey: modeStorageKeyProp,
    themeStorageKey: themeStorageKeyProp
  });
  const trueNonce = typeof window === "undefined" ? nonce : "";
  if (disableHeadScriptInjection) {
    $$payload.out.push("<!--[-->");
    Mode_watcher_lite($$payload, { themeColors: themeColors.current });
  } else {
    $$payload.out.push("<!--[!-->");
    Mode_watcher_full($$payload, { trueNonce, initConfig, themeColors: themeColors.current });
  }
  $$payload.out.push(`<!--]-->`);
  pop();
}
function MetaTags($$payload, $$props) {
  push();
  let {
    title = void 0,
    titleTemplate = void 0,
    robots = "index,follow",
    additionalRobotsProps = void 0,
    description = void 0,
    mobileAlternate = void 0,
    languageAlternates = void 0,
    twitter = void 0,
    facebook = void 0,
    openGraph = void 0,
    canonical = void 0,
    keywords = void 0,
    additionalMetaTags = void 0,
    additionalLinkTags = void 0
  } = $$props;
  let updatedTitle = titleTemplate ? title ? titleTemplate.replace(/%s/g, title) : title : title;
  let robotsParams = "";
  if (additionalRobotsProps) {
    const {
      nosnippet,
      maxSnippet,
      maxImagePreview,
      maxVideoPreview,
      noarchive,
      noimageindex,
      notranslate,
      unavailableAfter
    } = additionalRobotsProps;
    robotsParams = `${nosnippet ? ",nosnippet" : ""}${maxSnippet ? `,max-snippet:${maxSnippet}` : ""}${maxImagePreview ? `,max-image-preview:${maxImagePreview}` : ""}${noarchive ? ",noarchive" : ""}${unavailableAfter ? `,unavailable_after:${unavailableAfter}` : ""}${noimageindex ? ",noimageindex" : ""}${maxVideoPreview ? `,max-video-preview:${maxVideoPreview}` : ""}${notranslate ? ",notranslate" : ""}`;
  }
  head($$payload, ($$payload2) => {
    if (updatedTitle) {
      $$payload2.out.push("<!--[-->");
      $$payload2.title = `<title>${escape_html(updatedTitle)}</title>`;
    } else {
      $$payload2.out.push("<!--[!-->");
    }
    $$payload2.out.push(`<!--]--> `);
    if (robots !== false) {
      $$payload2.out.push("<!--[-->");
      $$payload2.out.push(`<meta name="robots"${attr("content", `${stringify(robots)}${stringify(robotsParams)}`)}/>`);
    } else {
      $$payload2.out.push("<!--[!-->");
    }
    $$payload2.out.push(`<!--]--> `);
    if (description) {
      $$payload2.out.push("<!--[-->");
      $$payload2.out.push(`<meta name="description"${attr("content", description)}/>`);
    } else {
      $$payload2.out.push("<!--[!-->");
    }
    $$payload2.out.push(`<!--]--> `);
    if (canonical) {
      $$payload2.out.push("<!--[-->");
      $$payload2.out.push(`<link rel="canonical"${attr("href", canonical)}/>`);
    } else {
      $$payload2.out.push("<!--[!-->");
    }
    $$payload2.out.push(`<!--]--> `);
    if (keywords?.length) {
      $$payload2.out.push("<!--[-->");
      $$payload2.out.push(`<meta name="keywords"${attr("content", keywords.join(", "))}/>`);
    } else {
      $$payload2.out.push("<!--[!-->");
    }
    $$payload2.out.push(`<!--]--> `);
    if (mobileAlternate) {
      $$payload2.out.push("<!--[-->");
      $$payload2.out.push(`<link rel="alternate"${attr("media", mobileAlternate.media)}${attr("href", mobileAlternate.href)}/>`);
    } else {
      $$payload2.out.push("<!--[!-->");
    }
    $$payload2.out.push(`<!--]--> `);
    if (languageAlternates && languageAlternates.length > 0) {
      $$payload2.out.push("<!--[-->");
      const each_array = ensure_array_like(languageAlternates);
      $$payload2.out.push(`<!--[-->`);
      for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
        let languageAlternate = each_array[$$index];
        $$payload2.out.push(`<link rel="alternate"${attr("hreflang", languageAlternate.hrefLang)}${attr("href", languageAlternate.href)}/>`);
      }
      $$payload2.out.push(`<!--]-->`);
    } else {
      $$payload2.out.push("<!--[!-->");
    }
    $$payload2.out.push(`<!--]--> `);
    if (twitter) {
      $$payload2.out.push("<!--[-->");
      if (twitter.cardType) {
        $$payload2.out.push("<!--[-->");
        $$payload2.out.push(`<meta name="twitter:card"${attr("content", twitter.cardType)}/>`);
      } else {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]--> `);
      if (twitter.site) {
        $$payload2.out.push("<!--[-->");
        $$payload2.out.push(`<meta name="twitter:site"${attr("content", twitter.site)}/>`);
      } else {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]--> `);
      if (twitter.title) {
        $$payload2.out.push("<!--[-->");
        $$payload2.out.push(`<meta name="twitter:title"${attr("content", twitter.title)}/>`);
      } else {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]--> `);
      if (twitter.description) {
        $$payload2.out.push("<!--[-->");
        $$payload2.out.push(`<meta name="twitter:description"${attr("content", twitter.description)}/>`);
      } else {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]--> `);
      if (twitter.creator) {
        $$payload2.out.push("<!--[-->");
        $$payload2.out.push(`<meta name="twitter:creator"${attr("content", twitter.creator)}/>`);
      } else {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]--> `);
      if (twitter.creatorId) {
        $$payload2.out.push("<!--[-->");
        $$payload2.out.push(`<meta name="twitter:creator:id"${attr("content", twitter.creatorId)}/>`);
      } else {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]--> `);
      if (twitter.image) {
        $$payload2.out.push("<!--[-->");
        $$payload2.out.push(`<meta name="twitter:image"${attr("content", twitter.image)}/>`);
      } else {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]--> `);
      if (twitter.imageAlt) {
        $$payload2.out.push("<!--[-->");
        $$payload2.out.push(`<meta name="twitter:image:alt"${attr("content", twitter.imageAlt)}/>`);
      } else {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]--> `);
      if (twitter.player) {
        $$payload2.out.push("<!--[-->");
        $$payload2.out.push(`<meta name="twitter:player"${attr("content", twitter.player)}/>`);
      } else {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]--> `);
      if (twitter.playerWidth) {
        $$payload2.out.push("<!--[-->");
        $$payload2.out.push(`<meta name="twitter:player:width"${attr("content", twitter.playerWidth.toString())}/>`);
      } else {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]--> `);
      if (twitter.playerHeight) {
        $$payload2.out.push("<!--[-->");
        $$payload2.out.push(`<meta name="twitter:player:height"${attr("content", twitter.playerHeight.toString())}/>`);
      } else {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]--> `);
      if (twitter.playerStream) {
        $$payload2.out.push("<!--[-->");
        $$payload2.out.push(`<meta name="twitter:player:stream"${attr("content", twitter.playerStream)}/>`);
      } else {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]--> `);
      if (twitter.appNameIphone) {
        $$payload2.out.push("<!--[-->");
        $$payload2.out.push(`<meta name="twitter:app:name:iphone"${attr("content", twitter.appNameIphone)}/>`);
      } else {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]--> `);
      if (twitter.appIdIphone) {
        $$payload2.out.push("<!--[-->");
        $$payload2.out.push(`<meta name="twitter:app:id:iphone"${attr("content", twitter.appIdIphone)}/>`);
      } else {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]--> `);
      if (twitter.appUrlIphone) {
        $$payload2.out.push("<!--[-->");
        $$payload2.out.push(`<meta name="twitter:app:url:iphone"${attr("content", twitter.appUrlIphone)}/>`);
      } else {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]--> `);
      if (twitter.appNameIpad) {
        $$payload2.out.push("<!--[-->");
        $$payload2.out.push(`<meta name="twitter:app:name:ipad"${attr("content", twitter.appNameIpad)}/>`);
      } else {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]--> `);
      if (twitter.appIdIpad) {
        $$payload2.out.push("<!--[-->");
        $$payload2.out.push(`<meta name="twitter:app:id:ipad"${attr("content", twitter.appIdIpad)}/>`);
      } else {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]--> `);
      if (twitter.appUrlIpad) {
        $$payload2.out.push("<!--[-->");
        $$payload2.out.push(`<meta name="twitter:app:url:ipad"${attr("content", twitter.appUrlIpad)}/>`);
      } else {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]--> `);
      if (twitter.appNameGoogleplay) {
        $$payload2.out.push("<!--[-->");
        $$payload2.out.push(`<meta name="twitter:app:name:googleplay"${attr("content", twitter.appNameGoogleplay)}/>`);
      } else {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]--> `);
      if (twitter.appIdGoogleplay) {
        $$payload2.out.push("<!--[-->");
        $$payload2.out.push(`<meta name="twitter:app:id:googleplay"${attr("content", twitter.appIdGoogleplay)}/>`);
      } else {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]--> `);
      if (twitter.appUrlGoogleplay) {
        $$payload2.out.push("<!--[-->");
        $$payload2.out.push(`<meta name="twitter:app:url:googleplay"${attr("content", twitter.appUrlGoogleplay)}/>`);
      } else {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]-->`);
    } else {
      $$payload2.out.push("<!--[!-->");
    }
    $$payload2.out.push(`<!--]--> `);
    if (facebook) {
      $$payload2.out.push("<!--[-->");
      $$payload2.out.push(`<meta property="fb:app_id"${attr("content", facebook.appId)}/>`);
    } else {
      $$payload2.out.push("<!--[!-->");
    }
    $$payload2.out.push(`<!--]--> `);
    if (openGraph) {
      $$payload2.out.push("<!--[-->");
      if (openGraph.url || canonical) {
        $$payload2.out.push("<!--[-->");
        $$payload2.out.push(`<meta property="og:url"${attr("content", openGraph.url || canonical)}/>`);
      } else {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]--> `);
      if (openGraph.type) {
        $$payload2.out.push("<!--[-->");
        $$payload2.out.push(`<meta property="og:type"${attr("content", openGraph.type.toLowerCase())}/> `);
        if (openGraph.type.toLowerCase() === "profile" && openGraph.profile) {
          $$payload2.out.push("<!--[-->");
          if (openGraph.profile.firstName) {
            $$payload2.out.push("<!--[-->");
            $$payload2.out.push(`<meta property="profile:first_name"${attr("content", openGraph.profile.firstName)}/>`);
          } else {
            $$payload2.out.push("<!--[!-->");
          }
          $$payload2.out.push(`<!--]--> `);
          if (openGraph.profile.lastName) {
            $$payload2.out.push("<!--[-->");
            $$payload2.out.push(`<meta property="profile:last_name"${attr("content", openGraph.profile.lastName)}/>`);
          } else {
            $$payload2.out.push("<!--[!-->");
          }
          $$payload2.out.push(`<!--]--> `);
          if (openGraph.profile.username) {
            $$payload2.out.push("<!--[-->");
            $$payload2.out.push(`<meta property="profile:username"${attr("content", openGraph.profile.username)}/>`);
          } else {
            $$payload2.out.push("<!--[!-->");
          }
          $$payload2.out.push(`<!--]--> `);
          if (openGraph.profile.gender) {
            $$payload2.out.push("<!--[-->");
            $$payload2.out.push(`<meta property="profile:gender"${attr("content", openGraph.profile.gender)}/>`);
          } else {
            $$payload2.out.push("<!--[!-->");
          }
          $$payload2.out.push(`<!--]-->`);
        } else {
          $$payload2.out.push("<!--[!-->");
          if (openGraph.type.toLowerCase() === "book" && openGraph.book) {
            $$payload2.out.push("<!--[-->");
            if (openGraph.book.authors && openGraph.book.authors.length) {
              $$payload2.out.push("<!--[-->");
              const each_array_1 = ensure_array_like(openGraph.book.authors);
              $$payload2.out.push(`<!--[-->`);
              for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
                let author = each_array_1[$$index_1];
                $$payload2.out.push(`<meta property="book:author"${attr("content", author)}/>`);
              }
              $$payload2.out.push(`<!--]-->`);
            } else {
              $$payload2.out.push("<!--[!-->");
            }
            $$payload2.out.push(`<!--]--> `);
            if (openGraph.book.isbn) {
              $$payload2.out.push("<!--[-->");
              $$payload2.out.push(`<meta property="book:isbn"${attr("content", openGraph.book.isbn)}/>`);
            } else {
              $$payload2.out.push("<!--[!-->");
            }
            $$payload2.out.push(`<!--]--> `);
            if (openGraph.book.releaseDate) {
              $$payload2.out.push("<!--[-->");
              $$payload2.out.push(`<meta property="book:release_date"${attr("content", openGraph.book.releaseDate)}/>`);
            } else {
              $$payload2.out.push("<!--[!-->");
            }
            $$payload2.out.push(`<!--]--> `);
            if (openGraph.book.tags && openGraph.book.tags.length) {
              $$payload2.out.push("<!--[-->");
              const each_array_2 = ensure_array_like(openGraph.book.tags);
              $$payload2.out.push(`<!--[-->`);
              for (let $$index_2 = 0, $$length = each_array_2.length; $$index_2 < $$length; $$index_2++) {
                let tag = each_array_2[$$index_2];
                $$payload2.out.push(`<meta property="book:tag"${attr("content", tag)}/>`);
              }
              $$payload2.out.push(`<!--]-->`);
            } else {
              $$payload2.out.push("<!--[!-->");
            }
            $$payload2.out.push(`<!--]-->`);
          } else {
            $$payload2.out.push("<!--[!-->");
            if (openGraph.type.toLowerCase() === "article" && openGraph.article) {
              $$payload2.out.push("<!--[-->");
              if (openGraph.article.publishedTime) {
                $$payload2.out.push("<!--[-->");
                $$payload2.out.push(`<meta property="article:published_time"${attr("content", openGraph.article.publishedTime)}/>`);
              } else {
                $$payload2.out.push("<!--[!-->");
              }
              $$payload2.out.push(`<!--]--> `);
              if (openGraph.article.modifiedTime) {
                $$payload2.out.push("<!--[-->");
                $$payload2.out.push(`<meta property="article:modified_time"${attr("content", openGraph.article.modifiedTime)}/>`);
              } else {
                $$payload2.out.push("<!--[!-->");
              }
              $$payload2.out.push(`<!--]--> `);
              if (openGraph.article.expirationTime) {
                $$payload2.out.push("<!--[-->");
                $$payload2.out.push(`<meta property="article:expiration_time"${attr("content", openGraph.article.expirationTime)}/>`);
              } else {
                $$payload2.out.push("<!--[!-->");
              }
              $$payload2.out.push(`<!--]--> `);
              if (openGraph.article.authors && openGraph.article.authors.length) {
                $$payload2.out.push("<!--[-->");
                const each_array_3 = ensure_array_like(openGraph.article.authors);
                $$payload2.out.push(`<!--[-->`);
                for (let $$index_3 = 0, $$length = each_array_3.length; $$index_3 < $$length; $$index_3++) {
                  let author = each_array_3[$$index_3];
                  $$payload2.out.push(`<meta property="article:author"${attr("content", author)}/>`);
                }
                $$payload2.out.push(`<!--]-->`);
              } else {
                $$payload2.out.push("<!--[!-->");
              }
              $$payload2.out.push(`<!--]--> `);
              if (openGraph.article.section) {
                $$payload2.out.push("<!--[-->");
                $$payload2.out.push(`<meta property="article:section"${attr("content", openGraph.article.section)}/>`);
              } else {
                $$payload2.out.push("<!--[!-->");
              }
              $$payload2.out.push(`<!--]--> `);
              if (openGraph.article.tags && openGraph.article.tags.length) {
                $$payload2.out.push("<!--[-->");
                const each_array_4 = ensure_array_like(openGraph.article.tags);
                $$payload2.out.push(`<!--[-->`);
                for (let $$index_4 = 0, $$length = each_array_4.length; $$index_4 < $$length; $$index_4++) {
                  let tag = each_array_4[$$index_4];
                  $$payload2.out.push(`<meta property="article:tag"${attr("content", tag)}/>`);
                }
                $$payload2.out.push(`<!--]-->`);
              } else {
                $$payload2.out.push("<!--[!-->");
              }
              $$payload2.out.push(`<!--]-->`);
            } else {
              $$payload2.out.push("<!--[!-->");
              if (openGraph.type.toLowerCase() === "video.movie" || openGraph.type.toLowerCase() === "video.episode" || openGraph.type.toLowerCase() === "video.tv_show" || openGraph.type.toLowerCase() === "video.other" && openGraph.video) {
                $$payload2.out.push("<!--[-->");
                if (openGraph.video?.actors && openGraph.video.actors.length) {
                  $$payload2.out.push("<!--[-->");
                  const each_array_5 = ensure_array_like(openGraph.video.actors);
                  $$payload2.out.push(`<!--[-->`);
                  for (let $$index_5 = 0, $$length = each_array_5.length; $$index_5 < $$length; $$index_5++) {
                    let actor = each_array_5[$$index_5];
                    if (actor.profile) {
                      $$payload2.out.push("<!--[-->");
                      $$payload2.out.push(`<meta property="video:actor"${attr("content", actor.profile)}/>`);
                    } else {
                      $$payload2.out.push("<!--[!-->");
                    }
                    $$payload2.out.push(`<!--]--> `);
                    if (actor.role) {
                      $$payload2.out.push("<!--[-->");
                      $$payload2.out.push(`<meta property="video:actor:role"${attr("content", actor.role)}/>`);
                    } else {
                      $$payload2.out.push("<!--[!-->");
                    }
                    $$payload2.out.push(`<!--]-->`);
                  }
                  $$payload2.out.push(`<!--]-->`);
                } else {
                  $$payload2.out.push("<!--[!-->");
                }
                $$payload2.out.push(`<!--]--> `);
                if (openGraph.video?.directors && openGraph.video.directors.length) {
                  $$payload2.out.push("<!--[-->");
                  const each_array_6 = ensure_array_like(openGraph.video.directors);
                  $$payload2.out.push(`<!--[-->`);
                  for (let $$index_6 = 0, $$length = each_array_6.length; $$index_6 < $$length; $$index_6++) {
                    let director = each_array_6[$$index_6];
                    $$payload2.out.push(`<meta property="video:director"${attr("content", director)}/>`);
                  }
                  $$payload2.out.push(`<!--]-->`);
                } else {
                  $$payload2.out.push("<!--[!-->");
                }
                $$payload2.out.push(`<!--]--> `);
                if (openGraph.video?.writers && openGraph.video.writers.length) {
                  $$payload2.out.push("<!--[-->");
                  const each_array_7 = ensure_array_like(openGraph.video.writers);
                  $$payload2.out.push(`<!--[-->`);
                  for (let $$index_7 = 0, $$length = each_array_7.length; $$index_7 < $$length; $$index_7++) {
                    let writer = each_array_7[$$index_7];
                    $$payload2.out.push(`<meta property="video:writer"${attr("content", writer)}/>`);
                  }
                  $$payload2.out.push(`<!--]-->`);
                } else {
                  $$payload2.out.push("<!--[!-->");
                }
                $$payload2.out.push(`<!--]--> `);
                if (openGraph.video?.duration) {
                  $$payload2.out.push("<!--[-->");
                  $$payload2.out.push(`<meta property="video:duration"${attr("content", openGraph.video.duration.toString())}/>`);
                } else {
                  $$payload2.out.push("<!--[!-->");
                }
                $$payload2.out.push(`<!--]--> `);
                if (openGraph.video?.releaseDate) {
                  $$payload2.out.push("<!--[-->");
                  $$payload2.out.push(`<meta property="video:release_date"${attr("content", openGraph.video.releaseDate)}/>`);
                } else {
                  $$payload2.out.push("<!--[!-->");
                }
                $$payload2.out.push(`<!--]--> `);
                if (openGraph.video?.tags && openGraph.video.tags.length) {
                  $$payload2.out.push("<!--[-->");
                  const each_array_8 = ensure_array_like(openGraph.video.tags);
                  $$payload2.out.push(`<!--[-->`);
                  for (let $$index_8 = 0, $$length = each_array_8.length; $$index_8 < $$length; $$index_8++) {
                    let tag = each_array_8[$$index_8];
                    $$payload2.out.push(`<meta property="video:tag"${attr("content", tag)}/>`);
                  }
                  $$payload2.out.push(`<!--]-->`);
                } else {
                  $$payload2.out.push("<!--[!-->");
                }
                $$payload2.out.push(`<!--]--> `);
                if (openGraph.video?.series) {
                  $$payload2.out.push("<!--[-->");
                  $$payload2.out.push(`<meta property="video:series"${attr("content", openGraph.video.series)}/>`);
                } else {
                  $$payload2.out.push("<!--[!-->");
                }
                $$payload2.out.push(`<!--]-->`);
              } else {
                $$payload2.out.push("<!--[!-->");
              }
              $$payload2.out.push(`<!--]-->`);
            }
            $$payload2.out.push(`<!--]-->`);
          }
          $$payload2.out.push(`<!--]-->`);
        }
        $$payload2.out.push(`<!--]-->`);
      } else {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]--> `);
      if (openGraph.title || updatedTitle) {
        $$payload2.out.push("<!--[-->");
        $$payload2.out.push(`<meta property="og:title"${attr("content", openGraph.title || updatedTitle)}/>`);
      } else {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]--> `);
      if (openGraph.description || description) {
        $$payload2.out.push("<!--[-->");
        $$payload2.out.push(`<meta property="og:description"${attr("content", openGraph.description || description)}/>`);
      } else {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]--> `);
      if (openGraph.images && openGraph.images.length) {
        $$payload2.out.push("<!--[-->");
        const each_array_9 = ensure_array_like(openGraph.images);
        $$payload2.out.push(`<!--[-->`);
        for (let $$index_9 = 0, $$length = each_array_9.length; $$index_9 < $$length; $$index_9++) {
          let image = each_array_9[$$index_9];
          $$payload2.out.push(`<meta property="og:image"${attr("content", image.url)}/> `);
          if (image.alt) {
            $$payload2.out.push("<!--[-->");
            $$payload2.out.push(`<meta property="og:image:alt"${attr("content", image.alt)}/>`);
          } else {
            $$payload2.out.push("<!--[!-->");
          }
          $$payload2.out.push(`<!--]--> `);
          if (image.width) {
            $$payload2.out.push("<!--[-->");
            $$payload2.out.push(`<meta property="og:image:width"${attr("content", image.width.toString())}/>`);
          } else {
            $$payload2.out.push("<!--[!-->");
          }
          $$payload2.out.push(`<!--]--> `);
          if (image.height) {
            $$payload2.out.push("<!--[-->");
            $$payload2.out.push(`<meta property="og:image:height"${attr("content", image.height.toString())}/>`);
          } else {
            $$payload2.out.push("<!--[!-->");
          }
          $$payload2.out.push(`<!--]--> `);
          if (image.secureUrl) {
            $$payload2.out.push("<!--[-->");
            $$payload2.out.push(`<meta property="og:image:secure_url"${attr("content", image.secureUrl.toString())}/>`);
          } else {
            $$payload2.out.push("<!--[!-->");
          }
          $$payload2.out.push(`<!--]--> `);
          if (image.type) {
            $$payload2.out.push("<!--[-->");
            $$payload2.out.push(`<meta property="og:image:type"${attr("content", image.type.toString())}/>`);
          } else {
            $$payload2.out.push("<!--[!-->");
          }
          $$payload2.out.push(`<!--]-->`);
        }
        $$payload2.out.push(`<!--]-->`);
      } else {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]--> `);
      if (openGraph.videos && openGraph.videos.length) {
        $$payload2.out.push("<!--[-->");
        const each_array_10 = ensure_array_like(openGraph.videos);
        $$payload2.out.push(`<!--[-->`);
        for (let $$index_10 = 0, $$length = each_array_10.length; $$index_10 < $$length; $$index_10++) {
          let video = each_array_10[$$index_10];
          $$payload2.out.push(`<meta property="og:video"${attr("content", video.url)}/> `);
          if (video.width) {
            $$payload2.out.push("<!--[-->");
            $$payload2.out.push(`<meta property="og:video:width"${attr("content", video.width.toString())}/>`);
          } else {
            $$payload2.out.push("<!--[!-->");
          }
          $$payload2.out.push(`<!--]--> `);
          if (video.height) {
            $$payload2.out.push("<!--[-->");
            $$payload2.out.push(`<meta property="og:video:height"${attr("content", video.height.toString())}/>`);
          } else {
            $$payload2.out.push("<!--[!-->");
          }
          $$payload2.out.push(`<!--]--> `);
          if (video.secureUrl) {
            $$payload2.out.push("<!--[-->");
            $$payload2.out.push(`<meta property="og:video:secure_url"${attr("content", video.secureUrl.toString())}/>`);
          } else {
            $$payload2.out.push("<!--[!-->");
          }
          $$payload2.out.push(`<!--]--> `);
          if (video.type) {
            $$payload2.out.push("<!--[-->");
            $$payload2.out.push(`<meta property="og:video:type"${attr("content", video.type.toString())}/>`);
          } else {
            $$payload2.out.push("<!--[!-->");
          }
          $$payload2.out.push(`<!--]-->`);
        }
        $$payload2.out.push(`<!--]-->`);
      } else {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]--> `);
      if (openGraph.audio && openGraph.audio.length) {
        $$payload2.out.push("<!--[-->");
        const each_array_11 = ensure_array_like(openGraph.audio);
        $$payload2.out.push(`<!--[-->`);
        for (let $$index_11 = 0, $$length = each_array_11.length; $$index_11 < $$length; $$index_11++) {
          let audio = each_array_11[$$index_11];
          $$payload2.out.push(`<meta property="og:audio"${attr("content", audio.url)}/> `);
          if (audio.secureUrl) {
            $$payload2.out.push("<!--[-->");
            $$payload2.out.push(`<meta property="og:audio:secure_url"${attr("content", audio.secureUrl.toString())}/>`);
          } else {
            $$payload2.out.push("<!--[!-->");
          }
          $$payload2.out.push(`<!--]--> `);
          if (audio.type) {
            $$payload2.out.push("<!--[-->");
            $$payload2.out.push(`<meta property="og:audio:type"${attr("content", audio.type.toString())}/>`);
          } else {
            $$payload2.out.push("<!--[!-->");
          }
          $$payload2.out.push(`<!--]-->`);
        }
        $$payload2.out.push(`<!--]-->`);
      } else {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]--> `);
      if (openGraph.locale) {
        $$payload2.out.push("<!--[-->");
        $$payload2.out.push(`<meta property="og:locale"${attr("content", openGraph.locale)}/>`);
      } else {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]--> `);
      if (openGraph.siteName) {
        $$payload2.out.push("<!--[-->");
        $$payload2.out.push(`<meta property="og:site_name"${attr("content", openGraph.siteName)}/>`);
      } else {
        $$payload2.out.push("<!--[!-->");
      }
      $$payload2.out.push(`<!--]-->`);
    } else {
      $$payload2.out.push("<!--[!-->");
    }
    $$payload2.out.push(`<!--]--> `);
    if (additionalMetaTags && Array.isArray(additionalMetaTags)) {
      $$payload2.out.push("<!--[-->");
      const each_array_12 = ensure_array_like(additionalMetaTags);
      $$payload2.out.push(`<!--[-->`);
      for (let $$index_12 = 0, $$length = each_array_12.length; $$index_12 < $$length; $$index_12++) {
        let tag = each_array_12[$$index_12];
        $$payload2.out.push(`<meta${spread_attributes(
          {
            ...tag.httpEquiv ? { ...tag, "http-equiv": tag.httpEquiv } : tag
          },
          null
        )}/>`);
      }
      $$payload2.out.push(`<!--]-->`);
    } else {
      $$payload2.out.push("<!--[!-->");
    }
    $$payload2.out.push(`<!--]--> `);
    if (additionalLinkTags?.length) {
      $$payload2.out.push("<!--[-->");
      const each_array_13 = ensure_array_like(additionalLinkTags);
      $$payload2.out.push(`<!--[-->`);
      for (let $$index_13 = 0, $$length = each_array_13.length; $$index_13 < $$length; $$index_13++) {
        let tag = each_array_13[$$index_13];
        $$payload2.out.push(`<link${spread_attributes({ ...tag }, null)} onload="this.__e=event" onerror="this.__e=event"/>`);
      }
      $$payload2.out.push(`<!--]-->`);
    } else {
      $$payload2.out.push("<!--[!-->");
    }
    $$payload2.out.push(`<!--]-->`);
  });
  pop();
}
function _layout($$payload, $$props) {
  push();
  let { children, data } = $$props;
  let metaTags = data.baseMetaTags;
  initSuperJson();
  setUtilsStoreContext();
  setAuthStoreContext();
  setOrgStoreContext();
  setAppointmentStoreContext();
  const initRemultSvelteReactivity = () => {
    {
      let update = () => {
      };
      let s = createSubscriber();
      remult.subscribeAuth({ reportObserved: () => s(), reportChanged: () => update() });
    }
    {
      Remult.entityRefInit = (x) => {
        let update = () => {
        };
        let s = createSubscriber();
        x.subscribe({ reportObserved: () => s(), reportChanged: () => update() });
      };
    }
  };
  initRemultSvelteReactivity();
  remult.user = data.user;
  MetaTags($$payload, spread_props([metaTags]));
  $$payload.out.push(`<!----> `);
  Mode_watcher($$payload, {});
  $$payload.out.push(`<!----> `);
  children?.($$payload);
  $$payload.out.push(`<!----> `);
  {
    $$payload.out.push("<!--[!-->");
  }
  $$payload.out.push(`<!--]-->`);
  pop();
}
export {
  _layout as default
};
