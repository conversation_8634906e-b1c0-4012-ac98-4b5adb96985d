export const manifest = (() => {
function __memo(fn) {
	let value;
	return () => value ??= (value = fn());
}

return {
	appDir: "_app",
	appPath: "_app",
	assets: new Set([".DS_Store","apple-touch-icon.png","avatars/shadcn.jpg","favicon-96x96.png","favicon.ico","favicon.svg","images/bg-hero.jpg","robots.txt","site.webmanifest","web-app-manifest-192x192.png","web-app-manifest-512x512.png"]),
	mimeTypes: {".png":"image/png",".jpg":"image/jpeg",".svg":"image/svg+xml",".txt":"text/plain",".webmanifest":"application/manifest+json"},
	_: {
		client: null,
		nodes: [
			__memo(() => import('./nodes/0.js')),
			__memo(() => import('./nodes/1.js')),
			__memo(() => import('./nodes/2.js')),
			__memo(() => import('./nodes/3.js')),
			__memo(() => import('./nodes/4.js')),
			__memo(() => import('./nodes/5.js')),
			__memo(() => import('./nodes/6.js')),
			__memo(() => import('./nodes/7.js')),
			__memo(() => import('./nodes/8.js')),
			__memo(() => import('./nodes/9.js')),
			__memo(() => import('./nodes/10.js')),
			__memo(() => import('./nodes/11.js')),
			__memo(() => import('./nodes/12.js')),
			__memo(() => import('./nodes/13.js')),
			__memo(() => import('./nodes/14.js')),
			__memo(() => import('./nodes/15.js')),
			__memo(() => import('./nodes/16.js')),
			__memo(() => import('./nodes/17.js')),
			__memo(() => import('./nodes/18.js')),
			__memo(() => import('./nodes/19.js')),
			__memo(() => import('./nodes/20.js')),
			__memo(() => import('./nodes/21.js')),
			__memo(() => import('./nodes/22.js')),
			__memo(() => import('./nodes/23.js')),
			__memo(() => import('./nodes/24.js')),
			__memo(() => import('./nodes/25.js')),
			__memo(() => import('./nodes/26.js')),
			__memo(() => import('./nodes/27.js')),
			__memo(() => import('./nodes/28.js')),
			__memo(() => import('./nodes/29.js'))
		],
		remotes: {
			
		},
		routes: [
			{
				id: "/(app)",
				pattern: /^\/?$/,
				params: [],
				page: { layouts: [0,2,], errors: [1,,], leaf: 12 },
				endpoint: null
			},
			{
				id: "/api/[...remult]",
				pattern: /^\/api(?:\/([^]*))?\/?$/,
				params: [{"name":"remult","optional":false,"rest":true,"chained":true}],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/_...remult_/_server.ts.js'))
			},
			{
				id: "/(app)/appointments",
				pattern: /^\/appointments\/?$/,
				params: [],
				page: { layouts: [0,2,3,], errors: [1,,,], leaf: 13 },
				endpoint: null
			},
			{
				id: "/(app)/appointments/appointment-templates",
				pattern: /^\/appointments\/appointment-templates\/?$/,
				params: [],
				page: { layouts: [0,2,3,], errors: [1,,,], leaf: 14 },
				endpoint: null
			},
			{
				id: "/(app)/appointments/calendar-blocks",
				pattern: /^\/appointments\/calendar-blocks\/?$/,
				params: [],
				page: { layouts: [0,2,3,], errors: [1,,,], leaf: 15 },
				endpoint: null
			},
			{
				id: "/(app)/campaigns",
				pattern: /^\/campaigns\/?$/,
				params: [],
				page: { layouts: [0,2,4,], errors: [1,,,], leaf: 16 },
				endpoint: null
			},
			{
				id: "/(app)/chat-store",
				pattern: /^\/chat-store\/?$/,
				params: [],
				page: { layouts: [0,2,5,], errors: [1,,,], leaf: 17 },
				endpoint: null
			},
			{
				id: "/(app)/invitation/[id]",
				pattern: /^\/invitation\/([^/]+?)\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,2,], errors: [1,,], leaf: 18 },
				endpoint: null
			},
			{
				id: "/(app)/laybye",
				pattern: /^\/laybye\/?$/,
				params: [],
				page: { layouts: [0,2,6,], errors: [1,,,], leaf: 19 },
				endpoint: null
			},
			{
				id: "/(app)/memberships",
				pattern: /^\/memberships\/?$/,
				params: [],
				page: { layouts: [0,2,7,], errors: [1,,,], leaf: 20 },
				endpoint: null
			},
			{
				id: "/(app)/rewards",
				pattern: /^\/rewards\/?$/,
				params: [],
				page: { layouts: [0,2,8,], errors: [1,,,], leaf: 21 },
				endpoint: null
			},
			{
				id: "/(app)/settings",
				pattern: /^\/settings\/?$/,
				params: [],
				page: { layouts: [0,2,9,], errors: [1,,,], leaf: 22 },
				endpoint: null
			},
			{
				id: "/(app)/settings/invites",
				pattern: /^\/settings\/invites\/?$/,
				params: [],
				page: { layouts: [0,2,9,], errors: [1,,,], leaf: 23 },
				endpoint: null
			},
			{
				id: "/(app)/settings/locations",
				pattern: /^\/settings\/locations\/?$/,
				params: [],
				page: { layouts: [0,2,9,], errors: [1,,,], leaf: 24 },
				endpoint: null
			},
			{
				id: "/(app)/settings/payment-engine",
				pattern: /^\/settings\/payment-engine\/?$/,
				params: [],
				page: { layouts: [0,2,9,], errors: [1,,,], leaf: 25 },
				endpoint: null
			},
			{
				id: "/(app)/settings/staff-invites",
				pattern: /^\/settings\/staff-invites\/?$/,
				params: [],
				page: { layouts: [0,2,9,], errors: [1,,,], leaf: 27 },
				endpoint: null
			},
			{
				id: "/(app)/settings/staff",
				pattern: /^\/settings\/staff\/?$/,
				params: [],
				page: { layouts: [0,2,9,], errors: [1,,,], leaf: 26 },
				endpoint: null
			},
			{
				id: "/(app)/subscriptions",
				pattern: /^\/subscriptions\/?$/,
				params: [],
				page: { layouts: [0,2,10,], errors: [1,,,], leaf: 28 },
				endpoint: null
			},
			{
				id: "/(app)/wallets",
				pattern: /^\/wallets\/?$/,
				params: [],
				page: { layouts: [0,2,11,], errors: [1,,,], leaf: 29 },
				endpoint: null
			}
		],
		prerendered_routes: new Set([]),
		matchers: async () => {
			
			return {  };
		},
		server_assets: {}
	}
}
})();
