export { matchers } from './matchers.js';

export const nodes = [
	() => import('./nodes/0'),
	() => import('./nodes/1'),
	() => import('./nodes/2'),
	() => import('./nodes/3'),
	() => import('./nodes/4'),
	() => import('./nodes/5'),
	() => import('./nodes/6'),
	() => import('./nodes/7'),
	() => import('./nodes/8'),
	() => import('./nodes/9'),
	() => import('./nodes/10'),
	() => import('./nodes/11'),
	() => import('./nodes/12'),
	() => import('./nodes/13'),
	() => import('./nodes/14'),
	() => import('./nodes/15'),
	() => import('./nodes/16'),
	() => import('./nodes/17'),
	() => import('./nodes/18'),
	() => import('./nodes/19'),
	() => import('./nodes/20'),
	() => import('./nodes/21'),
	() => import('./nodes/22'),
	() => import('./nodes/23'),
	() => import('./nodes/24'),
	() => import('./nodes/25'),
	() => import('./nodes/26'),
	() => import('./nodes/27'),
	() => import('./nodes/28'),
	() => import('./nodes/29')
];

export const server_loads = [0];

export const dictionary = {
		"/(app)": [12,[2]],
		"/(app)/appointments": [13,[2,3]],
		"/(app)/appointments/appointment-templates": [14,[2,3]],
		"/(app)/appointments/calendar-blocks": [15,[2,3]],
		"/(app)/campaigns": [16,[2,4]],
		"/(app)/chat-store": [17,[2,5]],
		"/(app)/invitation/[id]": [18,[2]],
		"/(app)/laybye": [19,[2,6]],
		"/(app)/memberships": [20,[2,7]],
		"/(app)/rewards": [21,[2,8]],
		"/(app)/settings": [22,[2,9]],
		"/(app)/settings/invites": [23,[2,9]],
		"/(app)/settings/locations": [24,[2,9]],
		"/(app)/settings/payment-engine": [25,[2,9]],
		"/(app)/settings/staff-invites": [27,[2,9]],
		"/(app)/settings/staff": [26,[2,9]],
		"/(app)/subscriptions": [28,[2,10]],
		"/(app)/wallets": [29,[2,11]]
	};

export const hooks = {
	handleError: (({ error }) => { console.error(error) }),
	
	reroute: (() => {}),
	transport: {}
};

export const decoders = Object.fromEntries(Object.entries(hooks.transport).map(([k, v]) => [k, v.decode]));

export const hash = false;

export const decode = (type, value) => decoders[type](value);

export { default as root } from '../root.js';