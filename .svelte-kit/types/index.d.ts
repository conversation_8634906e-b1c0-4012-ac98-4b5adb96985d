type DynamicRoutes = {
	"/api/[...remult]": { remult: string };
	"/(app)/invitation/[id]": { id: string }
};

type Layouts = {
	"/(app)/(components)": undefined;
	"/(app)": { id?: string };
	"/": { remult?: string; id?: string };
	"/api": { remult?: string };
	"/api/[...remult]": { remult: string };
	"/(app)/appointments/(components)": undefined;
	"/(app)/appointments": undefined;
	"/(app)/appointments/appointment-templates/(components)": undefined;
	"/(app)/appointments/appointment-templates": undefined;
	"/(app)/appointments/calendar-blocks": undefined;
	"/(app)/(components)/auth": undefined;
	"/(app)/campaigns": undefined;
	"/(app)/chat-store": undefined;
	"/(app)/invitation": { id?: string };
	"/(app)/invitation/[id]": { id: string };
	"/(app)/laybye/(components)": undefined;
	"/(app)/laybye": undefined;
	"/(app)/(components)/layout": undefined;
	"/(app)/(components)/marketing": undefined;
	"/(app)/memberships/(components)": undefined;
	"/(app)/memberships": undefined;
	"/(app)/rewards/(components)": undefined;
	"/(app)/rewards": undefined;
	"/(app)/settings/(components)": undefined;
	"/(app)/settings": undefined;
	"/(app)/settings/invites": undefined;
	"/(app)/settings/locations/(components)": undefined;
	"/(app)/settings/locations": undefined;
	"/(app)/settings/payment-engine/(components)": undefined;
	"/(app)/settings/payment-engine": undefined;
	"/(app)/settings/staff-invites/(components)": undefined;
	"/(app)/settings/staff-invites": undefined;
	"/(app)/settings/staff/(components)": undefined;
	"/(app)/settings/staff": undefined;
	"/(app)/settings/staff/(components)/member": undefined;
	"/(app)/subscriptions/(components)": undefined;
	"/(app)/subscriptions": undefined;
	"/(app)/wallets/(components)": undefined;
	"/(app)/wallets": undefined
};

export type RouteId = "/(app)/(components)" | "/(app)" | "/" | "/api" | "/api/[...remult]" | "/(app)/appointments/(components)" | "/(app)/appointments" | "/(app)/appointments/appointment-templates/(components)" | "/(app)/appointments/appointment-templates" | "/(app)/appointments/calendar-blocks" | "/(app)/(components)/auth" | "/(app)/campaigns" | "/(app)/chat-store" | "/(app)/invitation" | "/(app)/invitation/[id]" | "/(app)/laybye/(components)" | "/(app)/laybye" | "/(app)/(components)/layout" | "/(app)/(components)/marketing" | "/(app)/memberships/(components)" | "/(app)/memberships" | "/(app)/rewards/(components)" | "/(app)/rewards" | "/(app)/settings/(components)" | "/(app)/settings" | "/(app)/settings/invites" | "/(app)/settings/locations/(components)" | "/(app)/settings/locations" | "/(app)/settings/payment-engine/(components)" | "/(app)/settings/payment-engine" | "/(app)/settings/staff-invites/(components)" | "/(app)/settings/staff-invites" | "/(app)/settings/staff/(components)" | "/(app)/settings/staff" | "/(app)/settings/staff/(components)/member" | "/(app)/subscriptions/(components)" | "/(app)/subscriptions" | "/(app)/wallets/(components)" | "/(app)/wallets";

export type RouteParams<T extends RouteId> = T extends keyof DynamicRoutes ? DynamicRoutes[T] : Record<string, never>;

export type LayoutParams<T extends RouteId> = Layouts[T] | Record<string, never>;

export type Pathname = "/" | "/api" | `/api/${string}` & {} | "/appointments" | "/appointments/appointment-templates" | "/appointments/calendar-blocks" | "/auth" | "/campaigns" | "/chat-store" | "/invitation" | `/invitation/${string}` & {} | "/laybye" | "/layout" | "/marketing" | "/memberships" | "/rewards" | "/settings" | "/settings/invites" | "/settings/locations" | "/settings/payment-engine" | "/settings/staff-invites" | "/settings/staff" | "/settings/staff/member" | "/subscriptions" | "/wallets";

export type ResolvedPathname = `${"" | `/${string}`}${Pathname}`;

export type Asset = "/.DS_Store" | "/apple-touch-icon.png" | "/avatars/shadcn.jpg" | "/favicon-96x96.png" | "/favicon.ico" | "/favicon.svg" | "/images/bg-hero.jpg" | "/robots.txt" | "/site.webmanifest" | "/web-app-manifest-192x192.png" | "/web-app-manifest-512x512.png";