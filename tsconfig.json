{
	"extends": [
		"./.svelte-kit/tsconfig.json"
	],
	"compilerOptions": {
		"allowJs": true,
		"checkJs": true,
		"esModuleInterop": true,
		"forceConsistentCasingInFileNames": true,
		"resolveJsonModule": true,
		"skipLibCheck": true,
		"sourceMap": true,
		"strict": true,
		"jsx": "react-jsx",
		"noImplicitAny": false,
		"strictNullChecks": true,
		"moduleResolution": "bundler",
		"experimentalDecorators": true,
		"emitDecoratorMetadata": true,
		"allowSyntheticDefaultImports": true,
		"target": "ES2023",
		"verbatimModuleSyntax": true
	},
	"include": [
		"src/**/*",
		".svelte-kit/ambient.d.ts"
	],
	"exclude": [
		"node_modules"
	]
	// Path aliases are handled by https://kit.svelte.dev/docs/configuration#alias
	// except $lib which is handled by https://kit.svelte.dev/docs/configuration#files
	//
	// If you want to overwrite includes/excludes, make sure to copy over the relevant includes/excludes
	// from the referenced tsconfig.json - TypeScript does not merge them in
}