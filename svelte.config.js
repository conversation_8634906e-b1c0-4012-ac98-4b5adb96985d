import adapter from '@sveltejs/adapter-auto'
import { vitePreprocess } from '@sveltejs/vite-plugin-svelte'

/** @type {import('@sveltejs/kit').Config} */
const config = {
	// Consult https://kit.svelte.dev/docs/integrations#preprocessors
	// for more information about preprocessors
	preprocess: vitePreprocess(),
	compilerOptions: {
		runes: true
	},
	vitePlugin: {
		dynamicCompileOptions: ({ filename }) => {
			if (
				filename.includes('node_modules') ||
				filename.includes('components/ui/') ||
				filename.includes('components/common/data-table')
			) {
				return { runes: undefined }
			}
		}
	},
	kit: {
		adapter: adapter(),
		// adapter: adapter({
		// 	//fallback: '200.html'
		// }),
		alias: {
			$: './src',
			'$/*': './src/*',
			$assets: './src/assets',
			$backend: '../worker/src',
			'$backend/*': '../worker/src/*'
		},
		paths: {
			relative: false
		}
	}
}

export default config
